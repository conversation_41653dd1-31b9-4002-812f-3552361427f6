// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';

class EmptyListWidget extends StatelessWidget {
  EmptyListWidget({
    Key? key,
    required this.text,
    required this.topPadding,
    this.image = '',
    this.imageSize,
  }) : super(key: key);

  final String text;
  final double topPadding;
  final double? imageSize;
  final String image;

  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      margin: EdgeInsets.only(top: dW * topPadding),
      alignment: Alignment.center,
      child: Column(
        children: [
          if (image != '')
            Container(
              width: dW * 0.65,
              margin: EdgeInsets.only(bottom: dW * 0.1),
              child: Image.asset('assets/images/$image.png', height: imageSize),
            ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge!
                  .copyWith(fontSize: tS * 16, color: Colors.black),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
