import 'package:flutter/material.dart';

class FaqTile extends StatefulWidget {
  const FaqTile({Key? key, this.question, this.answer, this.deviceWidth})
      : super(key: key);
  final String? question;
  final String? answer;
  final double? deviceWidth;
  @override
  _FaqTileState createState() => _FaqTileState();
}

class _FaqTileState extends State<FaqTile> {
  var _expanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(top: 2.5, bottom: 10),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            width: 0,
            color: Color.fromRGBO(209, 209, 209, 1),
          ),
        ),
      ),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _expanded = !_expanded;
              });
            },
            child: AnimatedContainer(
              // width: double.infinity,
              duration: Duration(milliseconds: 300),
              height: _expanded ? 45 : 40,
              child: ListTile(
                contentPadding: EdgeInsets.all(0),
                title: Text(
                  widget.question.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Color.fromRGBO(78, 78, 82, 1),
                  ),
                ),
                trailing:
                    Icon(_expanded ? Icons.expand_less : Icons.expand_more),
              ),
            ),
          ),
          if (_expanded)
            SizedBox(
              height: 10,
            ),
          if (_expanded)
            AnimatedContainer(
              // margin: EdgeInsets.only(top: 10),
              duration: Duration(milliseconds: 300),

              child: Text(widget.answer.toString(),
                  style: TextStyle(fontWeight: FontWeight.w300),
                  softWrap: true),
            )
        ],
      ),
    );
  }
}
