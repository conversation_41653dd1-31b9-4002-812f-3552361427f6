import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? leading;
  final double dW;
  final double elevation;
  final Function? actionMethod;
  final List<Widget>? actions;
  final Color? bgColor;
  bool? centerTitle;

  CustomAppBar({
    this.title = '',
    this.leading,
    required this.dW,
    this.elevation = 0.0,
    this.actionMethod,
    this.actions,
    this.bgColor,
    this.centerTitle,
  });

  final double topMargin = Platform.isIOS ? 0 : 0.03;

  @override
  Widget build(BuildContext context) {
    final textScale = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.only(top: dW * topMargin),
      child: AppBar(
        centerTitle: centerTitle,
        backgroundColor: bgColor ?? Colors.white,
        elevation: elevation,
        leadingWidth: dW * 0.22,
        leading: leading ??
            GestureDetector(
              onTap: () {
                if (actionMethod != null) {
                  actionMethod!();
                } else {
                  Navigator.pop(context);
                }
              },
              child: Container(
                // padding:
                //     Platform.isIOS ? EdgeInsets.only(right: dW * 0.005) : null,
                margin: EdgeInsets.only(
                  left: dW * 0.05,
                  right: dW * 0.045,
                  top: dW * 0.012,
                  bottom: dW * 0.013,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xffF2F2F2),
                ),
                child: Center(
                  child: Platform.isIOS
                      ? const Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: 22,
                          color: Color(0xff114C3A),
                        )
                      : const Icon(
                          Icons.arrow_back,
                          color: Color(0xff114C3A),
                        ),
                ),
              ),
            ),
        title: Container(
          constraints: BoxConstraints(maxWidth: dW * 0.56),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: getThemeColor(),
                  fontSize: textScale * 18,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
        titleSpacing: 0,
        actions: actions ?? [],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(dW * (0.145 + topMargin));
}
