// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import '../orderModule/screens/orderScreen.dart';
// import '../../moreModule/screens/moreScreen.dart';
// import '../../../homeModule/screens/homeScreen.dart';

// class BottomNavBar extends StatefulWidget {
//   final int index;
//   BottomNavBar({this.index = 0});
//   @override
//   _BottomNavBarState createState() => _BottomNavBarState();
// }

// class _BottomNavBarState extends State<BottomNavBar> {
//   int? _currentPage;

//   @override
//   void initState() {
//     if (widget.index != 0) {
//       _currentPage = widget.index;
//     } else {
//       _currentPage = 0;
//     }
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: getPage(_currentPage),
//       bottomNavigationBar: AnimatedBottomNav(
//           currentIndex: _currentPage,
//           onChange: (index) {
//             setState(() {
//               _currentPage = index;
//             });
//           }),
//     );
//   }

//   getPage(int? page) {
//     switch (page) {
//       case 0:
//         return HomeScreen();
//       case 1:
//         return OrderScreen();
//       case 2:
//         return MoreScreen();
//     }
//   }
// }

// class AnimatedBottomNav extends StatelessWidget {
//   final int? currentIndex;
//   final Function(int)? onChange;
//   const AnimatedBottomNav({Key? key, this.currentIndex, this.onChange})
//       : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: kToolbarHeight,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         boxShadow: [
//           BoxShadow(
//             blurRadius: 3,
//             spreadRadius: 0.2,
//             color: Colors.black,
//             offset: Offset(0, 4),
//           )
//         ],
//       ),
//       child: Row(
//         children: <Widget>[
//           Expanded(
//             child: InkWell(
//               onTap: () {
//                 onChange!(0);
//               },
//               child: BottomNavItem(
//                 iconString: 'assets/svgIcons/home.svg',
//                 title: "Home",
//                 isActive: currentIndex == 0,
//               ),
//             ),
//           ),
//           Expanded(
//             child: InkWell(
//               onTap: () {
//                 onChange!(1);
//               },
//               child: BottomNavItem(
//                 iconString: 'assets/svgIcons/Bag.svg',
//                 title: "Order",
//                 isActive: currentIndex == 1,
//               ),
//             ),
//           ),
//           Expanded(
//             child: InkWell(
//               onTap: () {
//                 onChange!(2);
//               },
//               child: BottomNavItem(
//                 iconString: 'assets/svgIcons/more.svg',
//                 title: "More",
//                 isActive: currentIndex == 2,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class BottomNavItem extends StatelessWidget {
//   final bool isActive;
//   final String iconString;
//   final Color? activeColor;
//   final Color? inactiveColor;
//   final String title;
//   const BottomNavItem(
//       {Key? key,
//       this.isActive = false,
//       this.iconString = '',
//       this.activeColor,
//       this.inactiveColor,
//       this.title = ''})
//       : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return AnimatedSwitcher(
//       transitionBuilder: (child, animation) {
//         return SlideTransition(
//           position: Tween<Offset>(
//             begin: const Offset(0.0, 1.0),
//             end: Offset.zero,
//           ).animate(animation),
//           child: child,
//         );
//       },
//       duration: Duration(milliseconds: 500),
//       reverseDuration: Duration(milliseconds: 200),
//       child: isActive
//           ? Container(
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   border: Border(
//                     top: BorderSide(
//                       width: 2,
//                       color: Color(0XFF2064AB),
//                     ),
//                   ),
//                 ),
//                 padding: EdgeInsets.all(5.0),
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: <Widget>[
//                     SvgPicture.asset(
//                       iconString,
//                       height: 20,
//                       width: 20,
//                       color: Color(0XFF2064AB),
//                     ),
//                     SizedBox(height: 5.0),
//                     FittedBox(
//                       fit: BoxFit.scaleDown,
//                       child: Text(
//                         title,
//                         style: TextStyle(
//                           fontWeight: FontWeight.normal,
//                           color: Color(0XFF2064AB),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             )
//           : Container(
//               color: Colors.white,
//               padding: EdgeInsets.all(5.0),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: <Widget>[
//                   SvgPicture.asset(
//                     iconString,
//                     height: 20,
//                     width: 20,
//                   ),
//                   SizedBox(height: 5.0),
//                   FittedBox(
//                     fit: BoxFit.scaleDown,
//                     child: Text(
//                       title,
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         color: activeColor ?? Colors.black,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//     );
//   }
// }

// // SvgPicture.asset(
// //               iconString,
// //               height: 25,
// //               width: 25,
// //             )
