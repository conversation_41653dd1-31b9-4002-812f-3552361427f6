import 'package:bys_business/navigators.dart';

import 'policyWidget.dart';
import 'package:flutter/material.dart';

class PolicyTextWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final bool fromVenue;
  const PolicyTextWidget(
      {Key? key, required this.dW, required this.tS, this.fromVenue = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        constraints: BoxConstraints(maxWidth: dW * 0.8),
        width: dW,
        padding: EdgeInsets.only(top: fromVenue ? 0 : dW * 0.05),
        child: Column(
          crossAxisAlignment:
              fromVenue ? CrossAxisAlignment.start : CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: fromVenue
                  ? MainAxisAlignment.start
                  : MainAxisAlignment.center,
              children: [
                FittedBox(
                  child: Text(
                    'By continuing you agree to the',
                    style: TextStyle(
                      fontSize: tS * 9,
                      color: Colors.black,
                      letterSpacing: .48,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    push(PolicyWidget(state: 'TERMSANDCONDITION'));
                  },
                  child: FittedBox(
                    child: Text(
                      ' Terms of services ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                        fontSize: tS * 10,
                        letterSpacing: .48,
                      ),
                    ),
                  ),
                ),
                FittedBox(
                  child: Text(
                    'and ',
                    style: TextStyle(
                      fontSize: tS * 9,
                      letterSpacing: .48,
                    ),
                    softWrap: true,
                  ),
                ),
              ],
            ),
            SizedBox(height: dW * 0.005),
            GestureDetector(
              onTap: () {
                push(PolicyWidget(state: 'PRIVACYPOLICY'));
              },
              child: FittedBox(
                child: Text(
                  'Privacy policy',
                  softWrap: true,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                    fontSize: tS * 10,
                    letterSpacing: .48,
                  ),
                ),
              ),
            ),
            if (!fromVenue) SizedBox(height: dW * 0.1)
          ],
        ),
      ),
    );
  }
}
