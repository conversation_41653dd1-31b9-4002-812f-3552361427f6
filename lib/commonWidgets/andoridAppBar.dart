import 'dart:io';

import 'package:flutter/material.dart';

class AndroidAppBar extends StatelessWidget implements PreferredSizeWidget {
  final title;
  final titleColor;
  final deviceWidth;
  final backgroundColor;
  final elevation;
  List<Widget>? action;
  final TabBar? bottom;
  final Function? actionMethod;
  AndroidAppBar({
    @required this.title,
    @required this.deviceWidth,
    this.action,
    this.bottom,
    this.backgroundColor = Colors.white,
    this.titleColor = Colors.black,
    this.elevation = 0.0,
    this.actionMethod,
  });

  @override
  Widget build(BuildContext context) {
    final textScale = MediaQuery.of(context).textScaleFactor;
    return AppBar(
      toolbarHeight: deviceWidth * 0.15,
      backgroundColor: backgroundColor,
      elevation: elevation,
      actions: action,
      leading: IconButton(
        onPressed: () =>
            actionMethod != null ? actionMethod!() : Navigator.pop(context),
        icon: Icon(
          Platform.isAndroid
              ? Icons.arrow_back_outlined
              : Icons.arrow_back_ios_new,
          color: titleColor,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          color: titleColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          letterSpacing: .38,
        ),
        overflow: TextOverflow.ellipsis,
        softWrap: true,
      ),
      bottom: bottom,
      titleSpacing: 0,
      // bottom: PreferredSize(
      //   preferredSize: Size.fromHeight(30),
      //   child: TopButtonWidget(
      //     buttonText: 'Pickup Summary',
      //     actionMethod: () {
      //       Navigator.pop();
      //     },
      //     deviceHeight: 30,
      //     deviceWidth: deviceWidth,
      //   ),
      // ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(deviceWidth * 0.145);
}
