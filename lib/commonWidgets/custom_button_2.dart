// ignore_for_file: must_be_immutable

import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../authModule/providers/auth.dart';
import '../navigators.dart';
import '../new_colors.dart';
import 'asset_svg_icon.dart';

class CustomButton2 extends StatelessWidget {
  final double dW;
  final Color? buttonBackgroundColor;
  final Color? iconColor;
  final VoidCallback? onTap;
  final String? svgIcon;
  final IconData? icon;
  CustomButton2(
      {Key? key,
      required this.dW,
      this.buttonBackgroundColor,
      this.iconColor,
      this.onTap,
      this.svgIcon,
      this.icon})
      : super(key: key);

  Map language = {};

  @override
  Widget build(BuildContext context) {
    language = Provider.of<Auth>(context).selectedLanguage;

    return GestureDetector(
      onTap: onTap ?? () => pop(),
      child: Container(
        height: dW * 0.1,
        width: dW * 0.1,
        decoration: BoxDecoration(
          color: buttonBackgroundColor ?? getCustomBackIconBgColor(context),
          boxShadow: [
            BoxShadow(
                color: Colors.black.withOpacity(.15),
                offset: const Offset(0, 4),
                blurRadius: 10)
          ],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: svgIcon != null
              ? AssetSvgIcon(
                  iconName: svgIcon!,
                )
              : Platform.isIOS
                  ? Icon(icon ?? Icons.arrow_back_ios_new_rounded,
                      size: 22,
                      color: iconColor ?? getCustomBackIconColor(context))
                  : Icon(icon ?? Icons.arrow_back,
                      color: iconColor ?? getCustomBackIconColor(context)),
        ),
      ),
    );
  }
}
