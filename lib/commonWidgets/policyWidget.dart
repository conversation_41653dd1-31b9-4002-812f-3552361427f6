import 'dart:io' show Platform;
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:provider/provider.dart';
import '../../commonWidgets/andoridAppBar.dart';
import '../../commonWidgets/iOSAppBar.dart';

class PolicyWidget extends StatefulWidget {
  final String state;
  PolicyWidget({
    required this.state,
  });

  @override
  PpolicyWidgetState createState() => PpolicyWidgetState();
}

class PpolicyWidgetState extends State<PolicyWidget> {
  double textScale = 0.0;
  double deviceHeight = 0.0;
  double deviceWidth = 0.0;
  bool isLoading = true;
  var data;
  @override
  void initState() {
    super.initState();
    fetchPolicy();
  }

  fetchPolicy() async {
    data = await Provider.of<Auth>(context, listen: false)
        .fetchPolicy(widget.state);
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // user = Provider.of<Auth>(context).user;
    // addresses = user.address!;
    textScale = MediaQuery.of(context).textScaleFactor;
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title: widget.state == 'TERMSANDCONDITION'
          ? 'Terms & Condition'
          : 'Privacy Policy',
      deviceWidth: deviceWidth,
      elevation: 1.0,
    );

    return Scaffold(
      backgroundColor: Theme.of(context).canvasColor,
      appBar: androidAppBar,
      body: screenBody(),
    );
  }

  screenBody() {
    return SafeArea(
      child: isLoading
          ? Center(
              child: MaterialCircularLoader(
                  MediaQuery.of(context).size.width * 0.07),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.03),
              child: Column(
                children: [
                  SizedBox(height: deviceWidth * 0.05),
                  Html(
                    data: data['content'],
                  ),
                  SizedBox(height: deviceWidth * 0.05),
                ],
              ),
            ),
    );
  }
}
