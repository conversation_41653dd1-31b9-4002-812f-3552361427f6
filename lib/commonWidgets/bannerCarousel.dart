// import 'package:carousel_slider/carousel_controller.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:flutter/material.dart';

// class BannerCarousel extends StatefulWidget {
//   final deviceWidth;
//   final deviceHeight;
//   BannerCarousel({
//     this.deviceHeight,
//     this.deviceWidth,
//   });

//   @override
//   _BannerCarouselState createState() => _BannerCarouselState();
// }

// class _BannerCarouselState extends State<BannerCarousel> {
//   // GlobalKey<> _sliderKey = GlobalKey();
//   CarouselController _sliderController = CarouselController();
//   bool viewport = false;
//   final images = [
//     'assets/images/2.jpg',
//     'assets/images/2.jpg',
//   ];
//   int myIndex = 0;
//   String i = '';

//   @override
//   Widget build(BuildContext context) {
//     return Column(children: [
//       Container(
//         margin: EdgeInsets.only(top: widget.deviceWidth * 0.05),
//         width: widget.deviceWidth,
//         height: widget.deviceWidth * 0.5,
//         child: CarouselSlider(
//           carouselController: _sliderController,
//           items: images.map((image) {
//             i = image;
//             return Container(
//               width: widget.deviceWidth,
//               height: widget.deviceWidth * 0.45,
//               margin: EdgeInsets.only(
//                 // left:
//                 //     images.indexOf(image) == 0 ? 0 : widget.deviceWidth * 0.01,
//                 right: images.indexOf(image) == images.length - 1
//                     ? 0
//                     : widget.deviceWidth * 0.015,
//               ),
//               decoration: BoxDecoration(
//                 // color: images.indexOf(image) == 0
//                 //     ? Colors.blue
//                 //     : images.indexOf(image) == 1
//                 //         ? Colors.red
//                 //         : Colors.yellow,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               alignment: Alignment.center,
//               child: Image.asset(
//                 image,
//                 fit: BoxFit.fill,
//               ),
//             );
//           }).toList(),
//           options: CarouselOptions(
//               autoPlayAnimationDuration: Duration(milliseconds: 2),
//               autoPlay: true,
//               enlargeCenterPage: false,
//               enableInfiniteScroll: false,
//               viewportFraction: 0.92,
//               onPageChanged: (index, reason) {
//                 setState(() {
//                   myIndex = index;
//                 });
//               }),
//         ),
//       ),
//       // Row(
//       //   mainAxisAlignment: MainAxisAlignment.center,
//       //   children: images.map((url) {
//       //     int index = images.indexOf(url);
//       //     return Container(
//       //       width: index == myIndex ? 18 : 5.0,
//       //       height: 5.0,
//       //       margin: EdgeInsets.symmetric(vertical: 10.0, horizontal: 1.5),
//       //       decoration: index == myIndex
//       //           ? BoxDecoration(
//       //               borderRadius: BorderRadius.circular(20),
//       //               color: Colors.blue,
//       //             )
//       //           : BoxDecoration(
//       //               shape: BoxShape.circle,
//       //               color: Colors.grey,
//       //             ),
//       //     );
//       //   }).toList(),
//       // ),
//     ]);
//   }
// }
