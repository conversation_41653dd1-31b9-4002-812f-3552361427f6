import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../colors.dart';
import '../common_function.dart';
import 'text_widget.dart';

class CustomTextFieldWithLabel extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final FocusNode? focusNode;
  final String hintText;
  final TextInputType inputType;
  final TextInputAction inputAction;
  final int? minLines;
  final int? maxLength;
  final int? maxLines;
  final List<TextInputFormatter>? inputFormatter;
  final Function? onTap;
  final Function? onChanged;
  final Function? onFieldSubmitted;
  final Function? validator;
  final bool enabled;
  final Widget? widget;
  final TextCapitalization textCapitalization;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final TextAlign textAlign;
  final double labelFS;
  final double hintFS;
  final double textFS;
  final BoxConstraints? suffixIconConstraints;
  final BoxConstraints? prefixIconConstraints;
  final bool optional;
  final bool obscureText;
  final Color labelColor;
  final Color hintColor;
  final Color? borderColor;
  final Color? textColor;
  final Color? fillColor;
  final String? counterText;
  final double borderRadius;
  final FontWeight labelFW;
  final FontWeight textFW;
  final EdgeInsetsGeometry? contentPadding;

  const CustomTextFieldWithLabel({
    required this.label,
    required this.controller,
    this.focusNode,
    this.minLines,
    this.maxLength,
    this.maxLines,
    required this.hintText,
    this.contentPadding,
    this.inputType = TextInputType.text,
    this.inputFormatter,
    this.onTap,
    this.onChanged,
    this.onFieldSubmitted,
    this.validator,
    this.inputAction = TextInputAction.done,
    this.enabled = true,
    this.widget,
    this.suffixIcon,
    this.suffixIconConstraints,
    this.prefixIconConstraints,
    this.prefixIcon,
    this.textAlign = TextAlign.start,
    this.textCapitalization = TextCapitalization.none,
    this.labelFS = 14,
    this.textFS = 15,
    this.hintFS = 14,
    this.optional = false,
    this.obscureText = false,
    this.labelColor = blackColor3,
    this.borderColor,
    this.counterText = '',
    this.hintColor = placeholderColor,
    this.textColor = blackColor3,
    this.fillColor,
    this.borderRadius = 10,
    this.labelFW = FontWeight.normal,
    this.textFW = FontWeight.w600,
  });

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(
        color: borderColor ?? getThemeColor(),
      ),
      borderRadius: BorderRadius.circular(borderRadius),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double tS = MediaQuery.of(context).textScaleFactor;
    final double dW = MediaQuery.of(context).size.width;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (label != '')
              Row(
                children: [
                  TextWidget(
                    title: label,
                    fontSize: labelFS,
                    color: labelColor,
                    fontWeight: labelFW,
                  ),
                  if (!optional)
                    TextWidget(
                      title: '*',
                      fontSize: labelFS,
                      color: redColor,
                    ),
                ],
              ),
            if (widget != null) widget!,
          ],
        ),
        if (label != '') SizedBox(height: dW * 0.025),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          onTap: onTap != null ? () => onTap!() : null,
          inputFormatters: inputFormatter,
          textCapitalization: textCapitalization,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          enabled: enabled,
          obscureText: obscureText,
          textAlign: textAlign,
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontSize: tS * textFS,
                fontWeight: textFW,
                letterSpacing: .3,
                color: textColor,
              ),
          cursorColor: getThemeColor(),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              fontSize: tS * hintFS,
              fontWeight: FontWeight.normal,
              letterSpacing: .3,
              color: hintColor,
            ),
            contentPadding: contentPadding ??
                EdgeInsets.symmetric(
                  horizontal: dW * 0.04,
                  vertical: dW * 0.035,
                ),
            fillColor: fillColor,
            filled: fillColor != null,
            border: textFormBorder(context),
            focusedBorder: textFormBorder(context),
            enabledBorder: textFormBorder(context),
            errorBorder: textFormBorder(context),
            disabledBorder: textFormBorder(context),
            focusedErrorBorder: textFormBorder(context),
            counterText: counterText,
            suffixIcon: suffixIcon,
            suffixIconConstraints: suffixIconConstraints,
            prefixIcon: prefixIcon,
            prefixIconConstraints: prefixIconConstraints,
          ),
          minLines: minLines,
          maxLength: maxLength,
          maxLines: maxLines,
          textInputAction: inputAction,
          keyboardType: inputType,
          onChanged: onChanged != null ? (value) => onChanged!(value) : null,
          validator: validator != null ? (value) => validator!(value) : null,
          onFieldSubmitted: onFieldSubmitted != null
              ? (value) => onFieldSubmitted!(value)
              : null,
        ),
      ],
    );
  }
}
