import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';

import '../colors.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String? subTitle;
  final String noText;
  final String yesText;
  final Function yesFunction;
  final Function noFunction;
  final AlignmentGeometry? alignment;
  final TextAlign? titleAlign;
  final TextAlign? subTitleAlign;

  const CustomDialog({
    Key? key,
     this.title='',
    this.subTitle,
    required this.noText,
    this.titleAlign,
    this.subTitleAlign,
    this.alignment,
    required this.yesText,
    required this.noFunction,
    required this.yesFunction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    final double tS = MediaQuery.of(context).textScaleFactor;
    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12.0)),
      ),
      titlePadding: EdgeInsets.symmetric(
        horizontal: dW * 0.04,
        vertical: dW * 0.04,
      ),
      title: SizedBox(
        width: dW,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              alignment: alignment == null ? Alignment.center : alignment,
              child: Column(
                children: [
                  if(title!='')
                  TextWidget(
                    title: title,
                    textAlign:
                        titleAlign == null ? TextAlign.center : titleAlign,
                  ),
                  if (subTitle != null)
                    TextWidget(
                        title: subTitle!,
                        textAlign: subTitleAlign == null
                            ? TextAlign.center
                            : subTitleAlign),
                ],
              ),
            ),
            Divider(height: dW * 0.06, color: const Color(0xffEFEFEF)),
            SizedBox(height: dW * 0.02),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    noFunction();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: dW * 0.04, vertical: dW * 0.02),
                    decoration: BoxDecoration(
                        color: whiteColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          width: 1,
                          color: getThemeColor().withOpacity(.2),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            offset: const Offset(0, 0),
                            spreadRadius: 0,
                            blurRadius: 0,
                          )
                        ]),
                    child: TextWidget(title: noText),
                  ),
                ),
                SizedBox(width: dW * 0.04),
                GestureDetector(
                  onTap: () {
                    yesFunction();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: dW * 0.04, vertical: dW * 0.02),
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            offset: const Offset(0, 0),
                            spreadRadius: 0,
                            blurRadius: 0,
                          )
                        ]),
                    child: TextWidget(
                      title: yesText,
                      color: whiteColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      titleTextStyle: Theme.of(context)
          .textTheme
          .displayLarge!
          .copyWith(fontSize: tS * 14, color: blackColor2),
    );
  }
}
