import 'package:flutter/material.dart';

class MyValidator {
  String message;
  bool hasError;
  MyValidator({this.hasError = true, this.message = ''});
}

class TextFieldError extends StatelessWidget {
  final double deviceWidth;
  final String message;
  final double fontSize;
  TextFieldError(this.deviceWidth, this.message, {this.fontSize = 10});
  @override
  Widget build(BuildContext context) {
    return Container(
      width: deviceWidth * 0.8,
      padding: EdgeInsets.only(
        top: fontSize != null ? deviceWidth * 0.02 : deviceWidth * 0.01,
        left: fontSize != null ? deviceWidth * 0.01 : 0,
      ),
      child: Text(
        message,
        style: TextStyle(
            fontSize: 11,
            letterSpacing: 0.1,
            fontWeight: FontWeight.w400,
            color: Colors.red[600]),
      ),
    );
  }
}