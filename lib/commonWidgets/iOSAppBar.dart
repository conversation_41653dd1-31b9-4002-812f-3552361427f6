import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class IOSAppBar extends StatelessWidget
    implements ObstructingPreferredSizeWidget {
  final title;
  final titleColor;
  final deviceWidth;
  final backgroundColor;
  final Function? actionMethod;
  final Widget? action;

  const IOSAppBar({
    @required this.title,
    this.action,
    @required this.deviceWidth,
    this.titleColor = CupertinoColors.black,
    this.backgroundColor = CupertinoColors.white,
    this.actionMethod,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoNavigationBar(
      backgroundColor: backgroundColor,
      leading: GestureDetector(
        onTap: () => actionMethod != null ? actionMethod!() : Navigator.pop(context),
        child: Icon(
          Icons.arrow_back_ios_rounded,
          color: titleColor,
          size: 20,
        ),
      ),
      trailing: action,
      middle: Text(
        title,
        style: TextStyle(color: titleColor),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(deviceWidth * 0.145);

  @override
  bool shouldFullyObstruct(BuildContext context) => true;
}
