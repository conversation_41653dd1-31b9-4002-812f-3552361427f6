import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../common_function.dart';
import 'custom_button_2.dart';

class NewAppBar extends StatelessWidget {
  final double dW;
  final String title;
  final VoidCallback? onTap;
  final bool hideLeading;
  final String? leadingIcon;
  final Widget? suffixWidget;

  const NewAppBar({
    Key? key,
    required this.dW,
    required this.title,
    this.leadingIcon,
    this.onTap,
    this.hideLeading = false,
    this.suffixWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
      child: Row(
        children: [
          if (!hideLeading) ...[
            CustomButton2(
              dW: dW,
              onTap: onTap,
              svgIcon: leadingIcon,
            ),
            SizedBox(width: dW * 0.05),
          ],
          TextWidget(
            title: title,
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: getThemeColor(),
            maxLines: 1,
            textOverflow: TextOverflow.ellipsis,
          ),
          if (suffixWidget != null) ...[Spacer(), suffixWidget!]
        ],
      ),
    );
  }
}
