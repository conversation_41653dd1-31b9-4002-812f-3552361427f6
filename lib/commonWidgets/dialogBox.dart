import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

dialogBoxCustom({
  String? title,
  TextStyle? titleStyle,
  String? content,
  TextStyle? contentStyle,
  String? cancelBtnText,
  TextStyle? cancelBtnStyle,
  Function? cancelBtnPress,
  String? okBtnText,
  TextStyle? okBtnStyle,
  Function? okBtnPress,
  TargetPlatform? platform,
  BuildContext? context,
  bool dismissible = false,
}) {
  return platform == TargetPlatform.iOS
      ? showCupertinoDialog(
          barrierDismissible: !dismissible ? true : false,
          context: context!,
          builder: (context) => CupertinoAlertDialog(
            title: Text(
              title!,
              style: titleStyle,
            ),
            content: Text(content!, style: contentStyle),
            actions: <Widget>[
              CupertinoDialogAction(
                child: Text(
                  okBtnText!,
                  style: okBtnStyle,
                ),
                onPressed: () => okBtnPress!(),
              ),
              if (cancelBtnText != null)
                CupertinoDialogAction(
                  child: Text(
                    cancelBtnText,
                    style: cancelBtnStyle,
                  ),
                  onPressed: () => cancelBtnPress!(),
                ),
            ],
          ),
        )
      : showDialog(
          barrierDismissible: dismissible ? false : true,
          context: context!,
          builder: (ctx) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            title: Text(
              title!,
              style: titleStyle,
            ),
            content: Text(content!, style: contentStyle),
            actions: [
              TextButton(
                style: TextButton.styleFrom(padding: EdgeInsets.all(0)),
                onPressed: () => okBtnPress!(),
                child: Text(
                  okBtnText!,
                  style: okBtnStyle,
                ),
              ),
              if (cancelBtnText != null)
                TextButton(
                  style: TextButton.styleFrom(padding: EdgeInsets.all(0)),
                  onPressed: () => cancelBtnPress!(),
                  child: Text(
                    cancelBtnText,
                    style: cancelBtnStyle,
                  ),
                ),
            ],
          ),
        );
}
