import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

class OldCheckBoxWidget extends StatelessWidget {
  final deviceWidth;
  final tick;
  final argument;
  OldCheckBoxWidget(
    this.deviceWidth,
    this.tick, {
    this.argument,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: deviceWidth * 0.05,
      height: deviceWidth * 0.05,
      decoration: BoxDecoration(
        color: tick ? getThemeColor() : Colors.transparent,
        border: tick
            ? Border.all(color: getThemeColor(), width: 1)
            : Border.all(color: Color(0xff828080), width: 0.75),
        borderRadius: BorderRadius.circular(deviceWidth * 0.007),
      ),
      child: tick
          ? Icon(
              Icons.check_rounded,
              size: deviceWidth * 0.045,
              color: Colors.white,
            )
          : null,
    );
  }
}
