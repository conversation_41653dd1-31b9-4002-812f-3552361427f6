import 'package:flutter/material.dart';

import '../colors.dart';

class DividerWidget extends StatelessWidget {
  final double top;
  final double bottom;
  final Color color;
  const DividerWidget({
    this.top = 10,
    this.bottom = 10,
    this.color = dividerColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        top: top,
        bottom: bottom,
      ),
      child: Divider(color: color),
    );
  }
}
