import 'package:flutter/material.dart';

class OldTextWidget extends StatelessWidget {
  final double bottomMargin;
  final BuildContext context;
  final String text;
  final double fontSize;
  final Color textColor;
  final double letterSpacing;
  final FontWeight fontWeight;
  final TextAlign? textAlign;
  final Alignment? alignment;

  const OldTextWidget({
    Key? key,
    this.bottomMargin = 0,
    required this.context,
    required this.text,
    required this.fontSize,
    this.textColor = Colors.black,
    this.letterSpacing = 0,
    this.fontWeight = FontWeight.normal,
    this.textAlign = TextAlign.left,
    this.alignment = Alignment.topLeft,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: bottomMargin),
      alignment: alignment,
      child: Text(
        text,
        style: Theme.of(context).textTheme.displayLarge!.copyWith(
              fontSize: fontSize,
              color: textColor,
              fontWeight: fontWeight,
              letterSpacing: letterSpacing,
            ),
        textAlign: textAlign,
      ),
    );
  }
}
