import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;

import '../common_function.dart';

class CircularLoader extends StatelessWidget {
  final double android;
  final double iOS;
  final Color? color;
  const CircularLoader({
    required this.android,
    required this.iOS,
    this.color,
  });
  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? Center(child: CupertinoActivityIndicator(radius: iOS, color: color))
        : Center(
            child: SizedBox(
              height: android,
              width: android,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor:
                    AlwaysStoppedAnimation<Color>(color ?? getThemeColor()),
              ),
            ),
          );
  }
}

Widget lazyLoader(double dW) {
  return Padding(
    padding: EdgeInsets.only(top: dW * 0.06, bottom: dW * 0.1),
    child: Center(child: CircularLoader(android: dW * 0.07, iOS: 11.0)),
  );
}