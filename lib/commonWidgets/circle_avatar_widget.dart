
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../common_function.dart';

class CircleAvatarWidget extends StatelessWidget {
  final String avatar;
  final String userName;
  final double height;
  final double? width;
  final double parentRadius;
  final double childRadius;
  final double imageRadius;
  final double fontSize;

  const CircleAvatarWidget({
    required this.avatar,
    required this.userName,
    this.height = 0.23,
    this.parentRadius = 45,
    this.childRadius = 45,
    this.fontSize = 25,
    this.imageRadius = 100,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final dW = MediaQuery.of(context).size.width;
    return avatar == ''
        ? CircleAvatar(
            radius: parentRadius,
            backgroundColor: const Color(0xff1D3854),
            child: TextWidget(
              title: getInitials(userName).toUpperCase(),
              fontSize: fontSize,
              color: Colors.white,
              letterSpacing: .5,
              fontWeight: FontWeight.w600,
              textAlign: TextAlign.center,
            ),
          )
        : SizedBox(
            height: dW * height,
            width: width ?? dW * height,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(imageRadius),
              child: CachedNetworkImage(
                repeat: ImageRepeat.repeat,
                fit: BoxFit.cover,
                imageUrl: avatar,
                placeholder: (_, __) => Image.asset(
                  'assets/images/placeholder.jpg',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          );
  }
}
