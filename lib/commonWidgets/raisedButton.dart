import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../commonWidgets/materialCircularLoader.dart';

Widget buildRaisedButton(double width, double height, Function onPress,
    Widget child, TargetPlatform platform, Color backgroundColor, double radius,
    {Color? shadowColor}) {
  return Container(
    width: width,
    height: height,
    child: platform == TargetPlatform.iOS
        ? CupertinoButton.filled(
            child: child,
            borderRadius: BorderRadius.circular(radius),
            onPressed: () => onPress(),
          )
        : ElevatedButton(
            style: ElevatedButton.styleFrom(
              // primary: Color.fromRGBO(55, 95, 190, 1),
              foregroundColor: shadowColor != null ? shadowColor : null, backgroundColor: backgroundColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radius),
              ),
            ),
            onPressed: () => onPress(),
            child: child,
          ),
  );
}

Widget buildRaisedButtonWithBorder(
  double width,
  double height,
  Function onPress,
  Widget child,
  TargetPlatform platform,
  Color backgroundColor,
  Color borderColor,
  double radius, {
  Color? shadowColor,
}) {
  return Container(
    width: width,
    height: height,
    decoration: BoxDecoration(
      border: Border.all(color: borderColor),
      borderRadius: BorderRadius.circular(30),
    ),
    child: platform == TargetPlatform.iOS
        ? CupertinoButton.filled(
            child: child,
            borderRadius: BorderRadius.circular(radius),
            onPressed: () => onPress(),
          )
        : ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: shadowColor != null ? shadowColor : null, backgroundColor: backgroundColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radius),
              ),
            ),
            onPressed: () => onPress(),
            child: child,
          ),
  );
}

Widget addSubtractButton({
  double width = 0.0,
  IconData? icon,
  required Function onPress,
  double rightMargin = 0.0,
  double height = 0.0,
}) {
  return Container(
    margin: EdgeInsets.only(right: rightMargin, top: width * 0.01),
    child: InkWell(
      splashColor: Colors.grey.shade400,
      borderRadius: BorderRadius.circular(7),
      onTap: () {
        onPress();
      },
      child: Align(
        alignment: Alignment.bottomRight,
        child: Container(
          height: height,
          width: height,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(7),
          ),
          // padding: EdgeInsets.all(3.5),
          // margin: EdgeInsets.only(right: rightMargin, top: width * 0.01),
          child: Icon(
            icon,
            size: 19,
            color: Colors.white,
          ),
        ),
      ),
    ),
  );
}

// Widget quantityWidget({
//   double deviceWidth = 0.0,
//   double textScaleFactor = 0.0,
//   int total = 0,
//   required incremenetQuantity,
//   required decrementQuantity,
// }) {
//   return Container(
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         addSubtractButton(
//           width: deviceWidth,
//           icon: Icons.remove,
//           onPress: () {
//             decrementQuantity();
//           },
//           rightMargin: deviceWidth * 0.02,
//           height: deviceWidth * 0.072,
//         ),
//         Container(
//           margin: EdgeInsets.only(
//               right: deviceWidth * 0.02, top: deviceWidth * 0.008),
//           child: Text(
//             '$total',
//             style: TextStyle(
//               fontSize:  16,
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//         ),
//         addSubtractButton(
//           width: deviceWidth,
//           icon: Icons.add,
//           onPress: () {
//             incremenetQuantity();
//           },
//           rightMargin: 0,
//           height: deviceWidth * 0.072,
//         ),
//       ],
//     ),
//   );
// }

Widget quantityWidget({
  double deviceWidth = 0.0,
  double textScaleFactor = 0.0,
  int total = 0,
  required incremenetQuantity,
  required BuildContext context,
  required decrementQuantity,
}) {
  return Container(
    height: deviceWidth * 0.065,
    child: Row(
      children: [
        Container(
          height: deviceWidth * 0.065,
          width: deviceWidth * 0.07,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white, shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(7),
              ), backgroundColor: Theme.of(context).primaryColor,
              padding: EdgeInsets.all(0),
              fixedSize: Size.fromWidth(deviceWidth * 0.06),
            ),
            onPressed: decrementQuantity,
            child: Center(
              child: Icon(
                Icons.remove_rounded,
                size: 17,
              ),
            ),
          ),
        ),
        Container(
          height: deviceWidth * 0.07,
          // width: deviceWidth * 0.06,
          child: Center(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.015),
              child: Text(
                total.toString(),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        Container(
          height: deviceWidth * 0.065,
          width: deviceWidth * 0.07,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white, shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(7),
              ), backgroundColor: Theme.of(context).primaryColor,
              padding: EdgeInsets.all(0),
              fixedSize: Size.fromWidth(deviceWidth * 0.06),
            ),
            onPressed: incremenetQuantity,
            child: Center(
              child: Icon(
                Icons.add_rounded,
                size: 17,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

callToastMessage(String msg) {
  Fluttertoast.showToast(
    msg: msg,
    toastLength: Toast.LENGTH_SHORT,
    gravity: ToastGravity.values[5],
    timeInSecForIosWeb: 2,
    backgroundColor: Colors.black,
    textColor: Colors.white,
    fontSize: 16.0,
  );
}

customLaunch(command) async {
  if (await canLaunch(command)) {
    await launch(command);
    return true;
  } else {
    print('could not launch $command');
    return false;
  }
}

Widget subscribeButton({
  required double deviceWidth,
  required BuildContext context,
  required bool isLoading,
  required Function function,
}) {
  return Container(
    margin: EdgeInsets.only(top: deviceWidth * 0.02),
    child: buildRaisedButtonWithBorder(
      deviceWidth * 0.3,
      deviceWidth * 0.065,
      isLoading ? () {} : () => function(context),
      isLoading
          ? circularForButton(deviceWidth)
          : Row(
              children: [
                SvgPicture.asset(
                  'assets/svgIcons/refresh.svg',
                  height: 14,
                  width: 14,
                  // color: Theme.of(context).primaryColor,
                  color: Colors.white,
                ),
                SizedBox(width: deviceWidth * 0.02),
                Container(
                  width: deviceWidth * 0.15,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      'Subscribe',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
      TargetPlatform.android,
      Theme.of(context).primaryColor,
      Colors.white,
      30,
      shadowColor: Colors.grey,
    ),
  );
}

// createAnalytics({
//   required BuildContext context,
//   required String title,
//   required Map<String, String> parameter,
// }) async {
//   // Firebase And Facebook analytics Log
//   await Provider.of<AnalyticsAndNotifications>(context, listen: false)
//       .googleAnalyticsLogEvent(title, parameter);

//   await Provider.of<AnalyticsAndNotifications>(context, listen: false)
//       .facebookAnalyticsLogEvent(title, parameter);
//   await Provider.of<AnalyticsAndNotifications>(context, listen: false)
//       .facebookAppEventLog(title, parameter);
// }
