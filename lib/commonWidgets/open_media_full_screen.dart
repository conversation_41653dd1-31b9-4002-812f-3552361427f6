import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../navigators.dart';

class OpenMediaFullScreen extends StatefulWidget {
  final String type;
  final bool isLocal;
  final bool fileData;
  final String url;

  OpenMediaFullScreen({
    required this.type,
    required this.isLocal,
    required this.url,
    this.fileData = false,
  });

  @override
  State<OpenMediaFullScreen> createState() => _OpenMediaFullScreenState();
}

class _OpenMediaFullScreenState extends State<OpenMediaFullScreen> {
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  bool isLoading = false;

  VideoPlayerController? videoPlayerController;
  ChewieController? chewieController;

  setVideoPlayer() async {
    try {
      setState(() {
        isLoading = true;
      });
      if (widget.fileData) {
        videoPlayerController = VideoPlayerController.file(File(widget.url));
      } else {
        videoPlayerController =
            VideoPlayerController.networkUrl(Uri.parse(widget.url));
      }

      //Call Function Here
      await videoPlayerController!.initialize();

      chewieController = ChewieController(
        videoPlayerController: videoPlayerController!,
        autoPlay: true,
        looping: true,
        allowFullScreen: false,
      );
    } catch (e, stackTrace) {
      print(e);
      pop();
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.type == 'Video') {
      setVideoPlayer();
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (widget.type == 'Video') {
      chewieController!.dispose();
      videoPlayerController!.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      appBar: CustomAppBar(
        dW: dW,
        bgColor: widget.type == 'Image' ? Colors.black : Colors.white,
      ),
      body: SafeArea(
        child: Container(
          width: dW,
          height: dH,
          color: widget.type == 'Image' ? Colors.black : Colors.white,
          child: isLoading
              ? Center(child: CircularLoader(android: dW * 0.07, iOS: 12))
              : widget.type == "Image"
                  ? widget.fileData
                      ? _buildFileImage(File(widget.url))
                      : widget.isLocal
                          ? Image.asset(widget.url, fit: BoxFit.contain)
                          : CachedNetworkImage(
                              fit: BoxFit.contain,
                              imageUrl: widget.url,
                              imageBuilder: (context, imageProvioder) {
                                return Container(
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                        image: imageProvioder,
                                        fit: BoxFit.contain),
                                  ),
                                );
                              },
                              placeholder: (_, __) => CircularLoader(
                                android: dW * 0.07,
                                iOS: 12,
                                color: Colors.white,
                              ),
                              errorWidget: (context, url, error) => Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.error, color: Colors.red),
                                    SizedBox(height: 8),
                                    Text("Image not found", style: TextStyle(color: Colors.white)),
                                  ],
                                ),
                              ),
                            )
                  : chewieController != null
                      ? Chewie(controller: chewieController!)
                      : Center(
                          child: CircularLoader(android: dW * 0.07, iOS: 12)),
        ),
      ),
    );
  }

  Widget _buildFileImage(File file) {
    return FutureBuilder<bool>(
      future: file.exists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.data == true) {
            return Image.file(file, fit: BoxFit.contain);
          } else {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red),
                  SizedBox(height: 8),
                  Text("File not found", style: TextStyle(color: Colors.white)),
                ],
              ),
            );
          }
        }
        return CircularLoader(android: dW * 0.07, iOS: 12, color: Colors.white);
      },
    );
  }
}
