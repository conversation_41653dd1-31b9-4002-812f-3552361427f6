import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/custom_app_bar.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ScreenLayout extends StatefulWidget {
  const ScreenLayout({Key? key}) : super(key: key);

  @override
  ScreenLayoutState createState() => ScreenLayoutState();
}

class ScreenLayoutState extends State<ScreenLayout> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Map language = {};

  bool isLoading = false;
  late UserModal user;

  fetchData() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
              // SizedBox(height: dW * 0.05),
              NewAppBar(dW: dW, title: 'Title'),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: isLoading
                    ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                    : SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SizedBox(height: dW * 0.02),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ));
  }
}
