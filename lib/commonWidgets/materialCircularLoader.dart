import 'package:flutter/material.dart';

class MaterialCircularLoader extends StatelessWidget {
  final size;
  const MaterialCircularLoader(
    this.size,
  );
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: size,
      width: size,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor:
            AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
      ),
    );
  }
}

Widget circularForButton(double deviceWidth, {Color color = Colors.white}) {
  return Center(
    child: Container(
      height: deviceWidth * 0.05,
      width: deviceWidth * 0.05,
      child: CircularProgressIndicator(
        strokeWidth: 2.3,
        valueColor: AlwaysStoppedAnimation(color),
      ),
    ),
  );
}
