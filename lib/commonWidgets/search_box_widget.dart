// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';
import 'asset_svg_icon.dart';

class SearchBoxWidget extends StatelessWidget {
  final String leadingSvg;
  String trailingSvg;
  final Function onTap;
  final String searchBarText;
  final Color bgColor;
  final Color borderColor;
  final double vPadding;
  final double bottomMargin;
  SearchBoxWidget({
    this.leadingSvg = 'search1',
    this.trailingSvg = '',
    required this.onTap,
    this.searchBarText = 'Search....',
    this.bgColor = Colors.transparent,
    this.borderColor = const Color(0xFFBFC0C8),
    this.vPadding = 0.032,
    this.bottomMargin = 0.05,
  });

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return GestureDetector(
      onTap: () => onTap(),
      child: Container(
        width: dW,
        padding: EdgeInsets.symmetric(
            vertical: dW * vPadding, horizontal: dW * 0.04),
        margin: EdgeInsets.only(bottom: dW * bottomMargin, top: dW * 0.03),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(width: 1, color: borderColor),
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.only(right: dW * 0.03),
              child: AssetSvgIcon(iconName: leadingSvg),
            ),
            Expanded(
              child: TextWidget(
                title: searchBarText,
                fontWeight: FontWeight.w500,
                color: Color(0xffC4C4C4),
              ),
            ),
            if (trailingSvg != '') AssetSvgIcon(iconName: trailingSvg),
          ],
        ),
      ),
    );
  }
}
