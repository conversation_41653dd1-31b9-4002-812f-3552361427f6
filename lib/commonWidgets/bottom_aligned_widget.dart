// ignore_for_file: must_be_immutable
import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class BottomAlignedWidget extends StatelessWidget {
  final double dW;
  final double dH;
  final bool isLoading;
  final double keyBoardHeight;
  final Widget child;
  final List<BoxShadow>? boxShadow;

  BottomAlignedWidget({
    required this.dW,
    required this.dH,
    required this.child,
    this.keyBoardHeight = 0,
    this.isLoading = false,
    this.boxShadow,
  });

  Map language = {};

  @override
  Widget build(BuildContext context) {
    return Container(
      width: dW,
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: boxShadow ??
              [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  offset: const Offset(0, -1),
                  spreadRadius: 0,
                  blurRadius: 5,
                )
              ]),
      padding: EdgeInsets.only(
        top: dW * 0.02,
        bottom: dW *
            (keyBoardHeight > 0
                ? 0.025
                : Platform.isIOS && dH > 850
                    ? 0.06
                    : 0.03),
        left: dW * 0.04,
        right: dW * 0.04,
      ),
      child: child,
    );
  }
}
