import 'package:flutter/material.dart';

class RadioWidget extends StatelessWidget {
  final double? radius;
  final double? padding;
  final bool active;
  final Color? activeColor;
  final Color? inActiveBorderColor;
  final String? title;
  final FontWeight? textFontWeight;
  final Color? textColor;
  final double? fontSize;
  final double? inactiveBorderRadius;

  const RadioWidget({
    Key? key,
    required this.active,
    this.radius = 7,
    this.padding = 2.3,
    this.title = '',
    this.activeColor = Colors.blue,
    this.inActiveBorderColor = const Color(0xff828080),
    this.textFontWeight = FontWeight.w600,
    this.textColor = Colors.black,
    this.fontSize = 14.0,
    this.inactiveBorderRadius = 5,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    final deviceWidth = MediaQuery.of(context).size.width;

    return Container(
      color: Colors.transparent,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(padding!),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: active ? activeColor! : inActiveBorderColor!,
              ),
            ),
            child: !active
                ? Padding(
                    padding: EdgeInsets.all(inactiveBorderRadius!),
                  )
                : CircleAvatar(
                    radius: radius,
                    backgroundColor: activeColor!,
                  ),
          ),
          if (title != '')
            Container(
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.4),
              margin: EdgeInsets.only(left: deviceWidth * 0.02),
              child: Text(
                '$title',
                style: TextStyle(
                  fontWeight: textFontWeight,
                  color: textColor,
                  fontSize: textScaleFactor * fontSize!,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
        ],
      ),
    );
  }
}
