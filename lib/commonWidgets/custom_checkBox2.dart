import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../new_colors.dart';
import 'asset_svg_icon.dart';

class CustomCheckbox2 extends StatefulWidget {
  final bool value;
  final Function onChanged;
  final Color activeColor;
  final double size;
  final String title;
  final Color? textColor;
  final FontWeight fontWeight;
  final double fontSize;
  final String? svgIcon;
  final Color? iconColor;
  final double? iconHeight;
  final bool? reverseCheckBox;
  final double? spaceBetween;
  final Color? borderColor;

  CustomCheckbox2({
    required this.value,
    required this.onChanged,
    required this.title,
    this.iconColor,
    this.iconHeight,
    this.svgIcon,
    this.fontWeight = FontWeight.normal,
    this.fontSize = 14,
    this.textColor,
    this.activeColor = Colors.blue,
    this.size = 24.0,
    this.reverseCheckBox = false,
    this.spaceBetween,
    this.borderColor,
  });

  @override
  _CustomCheckbox2State createState() => _CustomCheckbox2State();
}

class _CustomCheckbox2State extends State<CustomCheckbox2> {
  double dW = 0.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;

    return GestureDetector(
      onTap: () {
        setState(() {});
        // widget.onChanged(!widget.value);
        widget.onChanged();
      },
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            if (widget.svgIcon != null)
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: dW * 0.04),
                    child: AssetSvgIcon(
                      iconName: widget.svgIcon!,
                      color: widget.iconColor,
                      height: widget.iconHeight,
                    ),
                  ),
                ],
              ),
            Expanded(
              child: Column(
                children: [
                  !widget.reverseCheckBox!
                      ? Row(
                          mainAxisAlignment: widget.spaceBetween == 0.0
                              ? MainAxisAlignment.spaceBetween
                              : MainAxisAlignment.start,
                          children: [
                            TextWidget(
                              title: widget.title,
                              fontWeight: widget.fontWeight,
                              fontSize: widget.fontSize,
                              color: widget.textColor ?? getBlackColor(context),
                            ),
                            if (widget.spaceBetween != 0.0)
                              SizedBox(
                                width: widget.spaceBetween,
                              ),
                            Container(
                              width: widget.size,
                              height: widget.size,
                              decoration: BoxDecoration(
                                // borderRadius: BorderRadius.circular(40),
                                shape: BoxShape.rectangle,
                                color: widget.value
                                    ? widget.activeColor
                                    : getWhiteColor(context),
                                border: Border.all(
                                  color: const Color(0xffACACB4),
                                  width: widget.value ? 0 : 2.0,
                                ),
                              ),
                              child: Center(
                                child: widget.value
                                    ? Icon(
                                        Icons.check,
                                        size: widget.size * 0.8,
                                        color: getWhiteColor(context),
                                      )
                                    : null,
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: widget.spaceBetween == 0.0
                              ? MainAxisAlignment.spaceBetween
                              : MainAxisAlignment.start,
                          children: [
                            Container(
                              width: widget.size,
                              height: widget.size,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                // shape: BoxShape.rectangle,
                                color: widget.value
                                    ? widget.activeColor
                                    : getWhiteColor(context),
                                border: Border.all(
                                  color: widget.borderColor ??
                                      const Color(0xffACACB4),
                                  width: widget.value ? 0 : 2.0,
                                ),
                              ),
                              child: Center(
                                child: widget.value
                                    ? Icon(
                                        Icons.check,
                                        size: widget.size * 0.8,
                                        color: getWhiteColor(context),
                                      )
                                    : null,
                              ),
                            ),
                            if (widget.spaceBetween != 0.0)
                              SizedBox(
                                width: widget.spaceBetween,
                              ),
                            TextWidget(
                              title: widget.title,
                              fontWeight: widget.fontWeight,
                              fontSize: widget.fontSize,
                              color: widget.textColor ?? getBlackColor(context),
                            ),
                          ],
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
