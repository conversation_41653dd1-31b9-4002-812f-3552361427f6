// ignore_for_file: use_key_in_widget_constructors

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'asset_svg_icon.dart';

// ignore: must_be_immutable
class LocationDisabledWidget extends StatelessWidget {
//
  final String messageContent;
  final bool showManualOption;
  LocationDisabledWidget({
    required this.messageContent,
    this.showManualOption = true,
  });

  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  late BuildContext bC;

  button({
    required double topMargin,
    required String svg,
    required String text,
    required Color color,
    required Function action,
  }) =>
      Container(
        margin: EdgeInsets.only(top: dW * topMargin),
        width: dW,
        decoration: BoxDecoration(
            border: Border(
                top: BorderSide(
          color: Colors.grey.shade300,
          width: 1,
        ))),
        child: TextButton(
          style: TextButton.styleFrom(
            foregroundColor: color, tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            elevation: 00,
            padding: EdgeInsets.symmetric(vertical: dW * 0.03),
          ),
          onPressed: () => action(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/svgIcons/$svg',
                color: color,
                height: 18,
              ),
              SizedBox(width: dW * 0.01),
              Text(text,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: tS * 13,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.4,
                    color: color,
                  )),
            ],
          ),
        ),
      );

  @override
  Widget build(BuildContext context) {
    tS = MediaQuery.of(context).textScaleFactor;
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    bC = context;
    return Container(
      width: dW * 0.85,
      padding: EdgeInsets.only(top: dW * 0.07),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              offset: Offset(0, 10),
              color: Colors.black12,
              blurRadius: 20,
              spreadRadius: 1,
            )
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: dW * 0.035),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.amber.shade50,
                    ),
                    child: Center(
                        child: AssetSvgIcon(
                      iconName: 'location-disabled',
                      height: dW * 0.11,
                      color: Theme.of(context).primaryColor,
                    )),
                  ),
                  Container(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      alignment: Alignment.center,
                      child: Text(
                        'Location permissions not enabled',
                        style: TextStyle(
                          fontSize: tS * 16,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.3,
                          wordSpacing: .3,
                        ),
                        textAlign: TextAlign.center,
                      )),
                  Container(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      alignment: Alignment.center,
                      child: Text(
                        messageContent,
                        // 'Please enable location permissions for a better delivery experience',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: tS * 14,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w400,
                          letterSpacing: 0.3,
                          wordSpacing: .3,
                        ),
                      )),
                ]),
          ),
          button(
              topMargin: 0.06,
              svg: 'navigator.svg',
              text: 'Enable device location',
              color: Theme.of(context).primaryColor,
              action: () {
                Navigator.pop(context);
                AppSettings.openAppSettings(type: AppSettingsType.location);
              }),
          // if (showManualOption)
          //   button(
          //       topMargin: 0,
          //       svg: 'search.svg',
          //       text: 'Enter location manually',
          //       color: Colors.grey.shade700,
          //       action: () {
          //         Navigator.pop(context);
          //         Navigator.of(context).push(MaterialPageRoute(
          //             builder: (context) =>
          //                 AddressScreen(selectLocationManually: true)));
          //       }),
        ],
      ),
    );
  }
}
