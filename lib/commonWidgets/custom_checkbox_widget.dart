import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../new_colors.dart';
import 'asset_svg_icon.dart';

class CustomCheckbox extends StatefulWidget {
  final bool value;
  final Function onChanged;
  final Color activeColor;
  final double size;
  final String title;
  final Color? textColor;
  final FontWeight fontWeight;
  final double fontSize;
  final String? svgIcon;
  final Color? iconColor;
  final double? iconHeight;
  final bool? reverseCheckBox;

  CustomCheckbox({
    required this.value,
    required this.onChanged,
    required this.title,
    this.iconColor,
    this.iconHeight,
    this.svgIcon,
    this.fontWeight = FontWeight.normal,
    this.fontSize = 14,
    this.textColor,
    this.activeColor = Colors.blue,
    this.size = 24.0,
    this.reverseCheckBox = false,
  });

  @override
  _CustomCheckboxState createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends State<CustomCheckbox> {
  double dW = 0.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;

    return GestureDetector(
      onTap: () {
        setState(() {});
        widget.onChanged();
      },
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            if (widget.svgIcon != null)
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: dW * 0.04),
                    child: AssetSvgIcon(
                      iconName: widget.svgIcon!,
                      color: widget.iconColor,
                      height: widget.iconHeight,
                    ),
                  ),
                ],
              ),
            Expanded(
              child: Column(
                children: [
                  !widget.reverseCheckBox!
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextWidget(
                              title: widget.title,
                              fontWeight: widget.fontWeight,
                              fontSize: widget.fontSize,
                              color: widget.textColor ?? getBlackColor(context),
                            ),
                            Container(
                              width: widget.size,
                              height: widget.size,
                              decoration: BoxDecoration(
                                shape: BoxShape.rectangle,
                                color: widget.value
                                    ? widget.activeColor
                                    : getWhiteColor(context),
                                border: Border.all(
                                  color: const Color(0xffACACB4),
                                  width: widget.value ? 0 : 2.0,
                                ),
                              ),
                              child: Center(
                                child: widget.value
                                    ? Icon(
                                        Icons.check,
                                        size: widget.size * 0.8,
                                        color: getWhiteColor(context),
                                      )
                                    : null,
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: widget.size,
                              height: widget.size,
                              decoration: BoxDecoration(
                                shape: BoxShape.rectangle,
                                color: widget.value
                                    ? widget.activeColor
                                    : getWhiteColor(context),
                                border: Border.all(
                                  color: const Color(0xffACACB4),
                                  width: widget.value ? 0 : 2.0,
                                ),
                              ),
                              child: Center(
                                child: widget.value
                                    ? Icon(
                                        Icons.check,
                                        size: widget.size * 0.8,
                                        color: getWhiteColor(context),
                                      )
                                    : null,
                              ),
                            ),
                            TextWidget(
                              title: widget.title,
                              fontWeight: widget.fontWeight,
                              fontSize: widget.fontSize,
                              color: widget.textColor ?? getBlackColor(context),
                            ),
                          ],
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
