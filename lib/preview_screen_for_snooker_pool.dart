// ignore_for_file: deprecated_member_use

import '../authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'commonWidgets/bottom_aligned_widget.dart';
import 'commonWidgets/custom_button.dart';
import 'commonWidgets/custom_container.dart';
import 'commonWidgets/divider_widget.dart';
import 'commonWidgets/new_check_box_widget.dart';
import 'commonWidgets/policy_text_widget.dart';
import 'commonWidgets/text_widget.dart';
import 'common_function.dart';
import 'navigators.dart';
import 'venueModule/models/venue_model.dart';
import 'venueModule/screens/turf_details_Screen.dart';

class PreviewScreenForSnookerPool extends StatefulWidget {
  final String venueName;
  final String venueContact;
  final String venueDescription;
  final Address venueAddress;
  final List selectedWeekDays;
  final List selectedWeekEnds;
  final int selectedSlotDuration;
  final List selectedSlots;
  const PreviewScreenForSnookerPool({
    Key? key,
    required this.venueName,
    required this.venueContact,
    required this.venueDescription,
    required this.venueAddress,
    required this.selectedWeekDays,
    required this.selectedWeekEnds,
    required this.selectedSlotDuration,
    required this.selectedSlots,
  }) : super(key: key);

  @override
  PreviewScreenForSnookerPoolState createState() =>
      PreviewScreenForSnookerPoolState();
}

class PreviewScreenForSnookerPoolState
    extends State<PreviewScreenForSnookerPool> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  //Variables
  bool isLoading = false;
  late UserModal user;
  bool policyAccepted = false;

  //Init Function
  myInit() {}

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    myInit();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        children: [
          Expanded(
              child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.07),
                  child: TextWidget(
                    title: 'Review your venue listing',
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: TextWidget(
                    title:
                        'Here’s what we’’ll show ot to the users. make sure everything looks good',
                    fontSize: 12,
                    color: Color(0xff21272A),
                  ),
                ),
                CustomContainer(
                  margin: EdgeInsets.only(top: dW * 0.1),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                          title: 'Venue Details',
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: getThemeColor()),
                      SizedBox(height: dW * 0.05),
                      TextWidget(title: 'Name'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueName,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: dW * 0.045),
                      TextWidget(title: 'Phone No.'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueContact,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: dW * 0.045),
                      TextWidget(title: 'About Venue'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueDescription,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Address'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: widget.venueAddress.fullAddress,
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Sport Category'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: 'Indoor',
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Sport Type'),
                          SizedBox(height: dW * 0.02),
                          Wrap(
                            crossAxisAlignment: WrapCrossAlignment.start,
                            runAlignment: WrapAlignment.spaceBetween,
                            children: [
                              CustomContainer(
                                width: dW * 0.32,
                                boxShadow: [],
                                borderColor: getThemeColor(),
                                vPadding: 0.02,
                                hPadding: 0.02,
                                radius: 8,
                                margin: EdgeInsets.only(
                                  bottom: dW * 0.03,
                                  right: dW * 0.03,
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Image.network(
                                      Provider.of<Auth>(context, listen: false)
                                          .getImageBySportName('Snooker'),
                                      height: 30,
                                    ),
                                    SizedBox(width: dW * .02),
                                    ConstrainedBox(
                                      constraints:
                                          BoxConstraints(maxWidth: dW * 0.18),
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: TextWidget(
                                          title: 'Snooker',
                                          fontWeight: FontWeight.w500,
                                          textAlign: TextAlign.left,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                CustomContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            title: 'Venue & Sports Configuration',
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: getThemeColor(),
                          ),
                          SizedBox(height: dW * 0.05),
                          TextWidget(title: 'Weekdays'),
                          SizedBox(height: dW * 0.03),
                          Wrap(
                            children: [
                              ...widget.selectedWeekDays
                                  .asMap()
                                  .map(
                                    (i, day) => MapEntry(
                                      i,
                                      TextWidget(
                                        title:
                                            '${getFullWeekDays(day)}${i == widget.selectedWeekDays.length - 1 ? '' : ','} ',
                                        fontWeight: FontWeight.w500,
                                        textAlign: TextAlign.center,
                                        color: Color(0xff9798A3),
                                      ),
                                    ),
                                  )
                                  .values
                                  .toList()
                            ],
                          ),
                          SizedBox(height: dW * 0.03),
                          TextWidget(title: 'Weekends'),
                          SizedBox(height: dW * 0.03),
                          Wrap(
                            children: [
                              ...widget.selectedWeekEnds
                                  .asMap()
                                  .map(
                                    (i, day) => MapEntry(
                                      i,
                                      TextWidget(
                                        title:
                                            '${getFullWeekDays(day)}${i == widget.selectedWeekEnds.length - 1 ? '' : ','} ',
                                        fontWeight: FontWeight.w500,
                                        textAlign: TextAlign.center,
                                        color: Color(0xff9798A3),
                                      ),
                                    ),
                                  )
                                  .values
                                  .toList()
                            ],
                          ),
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Time Slot Difference'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: '${widget.selectedSlotDuration} minutes',
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Selected Slots'),
                          SizedBox(height: dW * 0.03),
                          ...widget.selectedSlots
                              .asMap()
                              .map(
                                (i, slot) => MapEntry(
                                  i,
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      TextWidget(
                                        title:
                                            '${i + 1}. ${slot['title']} Slot',
                                        color: getThemeColor(),
                                        fontWeight: FontWeight.w500,
                                      ),
                                      SizedBox(height: dW * 0.03),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          TextWidget(
                                              title: 'Selected Slot Timing'),
                                          SizedBox(height: dW * 0.03),
                                          buildSlotTimingWidget(
                                            dW: dW,
                                            tS: tS,
                                            title: 'Start Time :',
                                            value:
                                                '${slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                          ),
                                          SizedBox(height: dW * 0.05),
                                          buildSlotTimingWidget(
                                            dW: dW,
                                            tS: tS,
                                            title: 'End Time :',
                                            value:
                                                '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: dW * 0.05),
                                      TextWidget(
                                        title: 'Selected Sport & Pricing',
                                        fontSize: 16,
                                        color: Color(0xff636363),
                                        fontWeight: FontWeight.w500,
                                      ),
                                      SizedBox(height: dW * 0.01),
                                      TextWidget(
                                        title:
                                            'Price for ${widget.selectedSlotDuration} minutes (Inclusive GST).',
                                        fontSize: 12,
                                        color: Color(0xff636363),
                                      ),
                                      SizedBox(height: dW * 0.03),
                                      CustomContainer(
                                        radius: 8,
                                        hPadding: 0.03,
                                        vPadding: 0.015,
                                        boxShadow: [],
                                        borderColor: getThemeColor(),
                                        child: Column(
                                          children: [
                                            SizedBox(height: dW * 0.03),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                ...[
                                                  'Sport',
                                                  'Weekdays',
                                                  'Weekends'
                                                ].map(
                                                  (type) => Container(
                                                    alignment:
                                                        Alignment.topLeft,
                                                    width: type == 'Weekends' ||
                                                            type == 'Weekdays'
                                                        ? dW * 0.21
                                                        : dW * .24,
                                                    child: FittedBox(
                                                      fit: BoxFit.scaleDown,
                                                      child: TextWidget(
                                                        title: type,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: dW * 0.03),
                                            ...slot['priceAndQuantity'].map(
                                              (sport) => Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            vertical:
                                                                dW * 0.02),
                                                    child: TextWidget(
                                                      title: 'Snooker',
                                                      color: getThemeColor(),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      ...[1, 2, 3].map(
                                                        (index) => Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  bottom: dW *
                                                                      0.03),
                                                          alignment: index == 1
                                                              ? Alignment
                                                                  .topLeft
                                                              : Alignment
                                                                  .center,
                                                          width: index != 1
                                                              ? dW * 0.21
                                                              : dW * .24,
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                                  vertical: 7),
                                                          decoration: index == 1
                                                              ? null
                                                              : BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              5),
                                                                  border: Border
                                                                      .all(
                                                                          color:
                                                                              getThemeColor()),
                                                                ),
                                                          child: FittedBox(
                                                            fit: BoxFit
                                                                .scaleDown,
                                                            child: TextWidget(
                                                              title: index == 1
                                                                  ? sport[
                                                                      'title']
                                                                  : index == 2
                                                                      ? sport['price'] ==
                                                                              0
                                                                          ? 'N.A'
                                                                          : '\u20b9 ${sport['price']}'
                                                                      : sport['weekendPrice'] ==
                                                                              0
                                                                          ? 'N.A'
                                                                          : '\u20b9 ${sport['weekendPrice']}',
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: dW * 0.05)
                                    ],
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          // SizedBox(height: dW * 0.03),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )),
          BottomAlignedWidget(
            dW: dW,
            dH: dH,
            child: Column(
              children: [
                SizedBox(height: dW * 0.01),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.005),
                      child: CheckBoxWidget(
                        activeBorderColor: getThemeColor(),
                        activeColor: getThemeColor(),
                        active: policyAccepted,
                        borderRadius: 3,
                        height: dW * 0.05,
                        iconSize: 14,
                        constraintWidth: null,
                        onTap: () {
                          policyAccepted = !policyAccepted;
                          setState(() {});
                        },
                      ),
                    ),
                    SizedBox(width: dW * 0.025),
                    GestureDetector(
                      onTap: () {
                        policyAccepted = !policyAccepted;
                        setState(() {});
                      },
                      child: PolicyTextWidget(dW: dW, tS: tS, fromVenue: true),
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.033),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomButton(
                      width: dW * 0.4,
                      height: dW * 0.12,
                      fontSize: 16,
                      radius: 7,
                      borderColor: getThemeColor(),
                      textColor: getThemeColor(),
                      buttonColor: Colors.white,
                      buttonText: 'Back',
                      onPressed: isLoading ? () {} : pop,
                    ),
                    CustomButton(
                      width: dW * 0.45,
                      height: dW * 0.12,
                      fontSize: 16,
                      radius: 7,
                      buttonText: 'Submit For Review',
                      onPressed: () {},
                      isLoading: isLoading,
                    )
                  ],
                ),
                SizedBox(height: dW * 0.04),
                TextWidget(
                  title:
                      'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
