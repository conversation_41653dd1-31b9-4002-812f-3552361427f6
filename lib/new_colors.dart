import 'package:bys_business/main.dart';
import 'package:flutter/material.dart';

Color get themeColor =>
    Theme.of(navigatorKey.currentState!.context).primaryColor;

//Splash Screen
Color getSplashTitleColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF0B2D1F); // Light mode color
  } else {
    return const Color(0xFFFFFFFF); // Dark mode color
  }
}

//OnBoarding screen
Color getCustomBackIconBgColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFFFFF); // Light mode color
  } else {
    return const Color(0xFF212B1E); // Dark mode color
  }
}

Color getCustomBackIconColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF292D32); // Light mode color
  } else {
    return const Color(0xFFFFFFFF); // Dark mode color
  }
}

Color getWhiteColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFFFFF); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF181F17); // Example dark mode color
  }
}

//Register Screen
Color getBackButtonBgColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF184D09); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

Color getLightGrey2Color(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFD9D9D9); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

//
//
//Black color
//
//
Color getBlackColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF434343); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getBlackColor2(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF37383F); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

//
//
//Grey color
//
//
Color getGreyColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF42536D); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

Color getGreyColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF9798A3); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getGreyColor5(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF636363); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getLightGreyColor7(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFBCBCBC); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFBCBCBC); // Example dark mode color
  }
}

Color getGreyColor2(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF3E3E3E); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

Color getBlueColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF1877F2); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF1877F2); // Example dark mode color
  }
}

Color getGrey3Color(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF5E5E5E); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

Color getGreyColor8(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF3E3E3E); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getGreyColor9(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF3D3D3D); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getGreyColor10(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFB9B9B9); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getLightGreenColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFE3FEDB); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF374D31); // Example dark mode color
  }
}

Color getLightGreenColor2(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFEDF7EE); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF374D31); // Example dark mode color
  }
}

Color getGreenColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF9CD989); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF374D31); // Example dark mode color
  }
}

Color getRedColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    // return const Color(0xFFEF4444); // Light mode color
    return const Color(0xFFD75C5C);
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getDisabledColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFDAF0D3); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF184D09); // Example dark mode color
  }
}

Color getLightGreyColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFACACB4); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getLightGreyColor2(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFF8F9FD); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

//otp Widget
Color getLightGreyColor3(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFE9E9E9); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getLightGreyColor4(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFD0D0D0); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getGreyColor6(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF434343); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getUnselectedLabelColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF5E5E5E); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF5E5E5E); // Example dark mode color
  }
}

Color getLightGreyColor8(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFDDDDDD); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFFFFF); // Example dark mode color
  }
}

Color getDividerColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFF0F0F0); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFF0F0F0); // Example dark mode color
  }
}

Color getLightGreyColor9(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFB1B1B1); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFB1B1B1); // Example dark mode color
  }
}

Color getLightGreyColor11(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFACACB4); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFB1B1B1); // Example dark mode color
  }
}

Color getTextFieldGreyBackgroundColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFF8F9FD); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF181F17); // Example dark mode color
  }
}

Color getDarkYellowColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFAD31); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFAD31); // Example dark mode color
  }
}

Color getYellowColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    // return const Color(0xFFFFD600); // Light mode color
    return const Color(0xFFE4B200);
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFD600); // Example dark mode color
  }
}

Color getMediumYellowColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFE796); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFF8E0); // Example dark mode color
  }
}

Color getLightYellowColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFF8E0); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFF8E0); // Example dark mode color
  }
}

Color getLightRedColor1(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFFFCBCB); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFF8E0); // Example dark mode color
  }
}

Color getRedColor2(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFD84848); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFFFF8E0); // Example dark mode color
  }
}

Color getRemainingAmountSubTitleColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFF5E5E5E); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFFACACB4); // Example dark mode color
  }
}

const Color lightGray = Color(0xFFACACB4);

const Color redColor = Color(0xFFDD4F4D);

const Color dividerColor = Color(0xFFEAEAEA);
const Color blackColor3 = Color(0xFF3E3E3E);
const Color placeholderColor = Color(0xFFAAABB5);
Color disabledColor = const Color(0xFF4A9F63).withOpacity(.5);

Color getTextBoxBorderColor(BuildContext context) {
  final themeMode = Theme.of(context).brightness;

  if (themeMode == Brightness.light) {
    return const Color(0xFFACACB4); // Light mode color
  } else {
    // Handle dark mode color here
    return const Color(0xFF53A63A); // Example dark mode color
  }
}
