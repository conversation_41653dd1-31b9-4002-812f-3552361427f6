// import '../../employeeModule/models/business_model.dart';

// import '../../authModule/modals/userModel.dart';
// import '../../commonWidgets/commonFunction.dart';
// import '../../commonWidgets/raisedButton.dart';
// import '../../homeModule/screens/BookSummaryScreen.dart';
// import '../../homeModule/widgets/selectSlotDropDown.dart';
// import '../../skeletonLoader/bookturfOfflineSkeletonLoader.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:intl/intl.dart';
// import 'package:provider/provider.dart';

// import '../../commonWidgets/andoridAppBar.dart';
// import '../../authModule/screens/registrationSummaryScreen.dart';
// import '../../fontSizes.dart';
// import '../../homeModule/widgets/sportDropDown.dart';
// import '../../homeModule/widgets/selectTurfSizeDropdown.dart';
// import '../../homeModule/widgets/selectTimeDropdown.dart';
// import '../../venueModule/models/turfModel.dart';
// import '../../venueModule/providers/turfProvider.dart';

// class OfflineBookingScreen extends StatefulWidget {
//   final UserModal user;
//   final BusinessModel? business;
//   const OfflineBookingScreen({
//     Key? key,
//     required this.user,
//     this.business,
//   }) : super(key: key);

//   @override
//   _OfflineBookingScreenState createState() => _OfflineBookingScreenState();
// }

// class _OfflineBookingScreenState extends State<OfflineBookingScreen> {
//   late DateTime selectedDate;
//   bool turfSelected = false;
//   bool isLoading = false;
//   String sportId = '';
//   String label = '';
//   Venue? selectedTurf;
//   List selectedTime = [];
//   double amount = 0.0;
//   int quantity = 1;
//   var turf;

//   List<Venue> listOfTurf = [];
//   late Slot selectedSlot;

//   GlobalKey<FormState> _formKey = GlobalKey<FormState>();
//   TextEditingController fullNameController = TextEditingController();
//   TextEditingController mobileNoController = TextEditingController();
//   TextEditingController areaController = TextEditingController();
//   TextEditingController sportController = TextEditingController();
//   TextEditingController sizeOrSportController = TextEditingController();
//   TextEditingController selectedDateController = TextEditingController();
//   TextEditingController timeController = TextEditingController();
//   TextEditingController slotController = TextEditingController();

//   getHintStyle(textScaleFactor) {
//     return Theme.of(context).textTheme.headlineSmall!.copyWith(
//           fontSize: textScaleFactor * 13,
//           color: Color(0xff737373),
//           fontWeight: FontWeight.w600,
//         );
//   }

//   getStyle(textScaleFactor) {
//     return Theme.of(context).textTheme.headlineSmall!.copyWith(
//           fontSize: textScaleFactor * displayMedium,
//           color: Colors.black,
//           fontWeight: FontWeight.w600,
//         );
//   }

//   selectSportDropDown() {
//     showModalBottomSheet(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(24),
//           topRight: Radius.circular(24),
//         ),
//       ),
//       context: (context),
//       builder: (context) => GestureDetector(
//         child: SelectSportDropdown(
//           turf: selectedTurf!,
//         ),
//         onTap: () {},
//         behavior: HitTestBehavior.opaque,
//       ),
//     ).then((value) {
//       if (value != null) {
//         sportController.text = value.sport;
//         sportId = value.id;
//       }
//     });
//   }

//   selectSlotDropDown() {
//     showModalBottomSheet(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(24),
//           topRight: Radius.circular(24),
//         ),
//       ),
//       context: (context),
//       builder: (context) => GestureDetector(
//         child: SelectSlotSizeDropdown(
//           turf: selectedTurf!,
//           selectedDate: selectedDate,
//         ),
//         onTap: () {},
//         behavior: HitTestBehavior.opaque,
//       ),
//     ).then((value) {
//       if (value != null) {
//         setState(() {
//           slotController.text = value.session;
//           sizeOrSportController.clear();
//           timeController.clear();
//           selectedSlot = value;
//         });
//       }
//     });
//   }

//   checkIsNine(List turfSizes) {
//     if (turfSizes.length == 1 && turfSizes.contains('6:6')) {
//       return false;
//     } else if (turfSizes.length == 1 && turfSizes.contains('5:5')) {
//       return false;
//     } else if (turfSizes.contains('5:5') &&
//         turfSizes.contains('6:6') &&
//         !turfSizes.contains('9:9') &&
//         !turfSizes.contains('7:7')) {
//       return false;
//     } else if ((turfSizes.contains('9:9') || turfSizes.contains('7:7')) &&
//         !turfSizes.contains('8:8')) {
//       return true;
//     } else {
//       return false;
//     }
//   }

//   selectTurfSizeDropDown() {
//     showModalBottomSheet(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(24),
//           topRight: Radius.circular(24),
//         ),
//       ),
//       context: (context),
//       builder: (context) => GestureDetector(
//         child: SelectTurfSizeDropdown(
//           selectedSlot: selectedSlot,
//           categoryName: selectedTurf!.sportCategory!.categoryName,
//           selectedDate: selectedDate,
//           selectedSport: sportController.text,
//         ),
//         onTap: () {},
//         behavior: HitTestBehavior.opaque,
//       ),
//     ).then((value) {
//       if (value != null) {
//         setState(() {
//           sizeOrSportController.text = value.title;
//           timeController.clear();
//           if (selectedDate.weekday == 6 || selectedDate.weekday == 7) {
//             amount = value.weekendPrice;
//           } else {
//             amount = value.price;
//           }
//           print(amount);
//           quantity = value.quantity;
//           label = value.label;
//         });
//       }
//     });
//   }

//   selectTimeDropDown() {
//     showModalBottomSheet(
//       enableDrag: true,
//       isScrollControlled: true,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(24),
//           topRight: Radius.circular(24),
//         ),
//       ),
//       context: (context),
//       builder: (context) => GestureDetector(
//         child: SelectTimeDropdown(
//           turf: selectedTurf!,
//           selectedDate: selectedDate,
//           selectedSlot: selectedSlot,
//           selectedTime: selectedTime,
//           sizeOrSport: sizeOrSportController.text,
//         ),
//         onTap: () {},
//         behavior: HitTestBehavior.opaque,
//       ),
//     ).then((value) {
//       if (value != null) {
//         selectedTime = value;
//         List time = [];
//         value.forEach((data) {
//           time.add(double.parse(data.replaceAll(":", ".")));
//         });
//         if (selectedSlot.session != 'Night') {
//           time.sort();
//         } else {
//           var array24 = [];
//           var array0 = [];
//           time.forEach((value) {
//             if (value >= 20 && value <= 24) {
//               // if (!array24.contains(value)) {
//               array24.add(value);
//               // }
//             } else {
//               // if (!array0.contains(value)) {
//               array0.add(value);
//               // }
//             }
//           });
//           array24.sort();
//           array0.sort();
//           time = [];
//           time.addAll(array24);
//           time.addAll(array0);
//         }
//         time.toSet();
//         selectedTime = [];
//         time.forEach((data) {
//           selectedTime.add(data.toString().replaceAll(".", ":"));
//         });

//         print(selectedTime);
//         timeController.text =
//             '${double.parse(selectedTime[0].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(selectedTime[0].split(':')[0]))} - ${double.parse(selectedTime[selectedTime.length - 1].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(selectedTime[selectedTime.length - 1].split(':')[0]))}';
//       }
//     });
//   }

//   selectDate() {
//     DateTime date = DateTime.now();

//     List days = [];

//     selectedTurf!.days!.forEach((day) {
//       days.add(getWeekNumber(day));
//     });

//     for (var i = 0; i < 7; i++) {
//       if (!days.contains(date.weekday)) {
//         date = date.add(Duration(days: 1));
//       } else {
//         break;
//       }
//     }

//     showDatePicker(
//       context: context,
//       initialDate: date,
//       firstDate: date,
//       lastDate: DateTime(2200),
//       selectableDayPredicate: (DateTime val) =>
//           days.contains(val.weekday) ? true : false,
//     ).then((date) {
//       if (date != null) {
//         if (selectedTurf!.pauseDates!
//             .contains(DateFormat('yyyy-MM-dd').format(date))) {
//           callToastMessage('Booking are not available for this date');
//           return;
//         } else {
//           setState(() {
//             selectedDate = date;
//             selectedDateController.text =
//                 DateFormat('dd MMM yyyy').format(selectedDate);
//             sizeOrSportController.clear();
//             slotController.clear();
//             timeController.clear();
//             slotController.clear();
//           });
//         }
//       }
//     });
//   }

//   @override
//   void initState() {
//     super.initState();
//     fetchTurf();
//   }

//   Future<void> fetchTurf() async {
//     try {
//       setState(() {
//         isLoading = true;
//       });
//       await Provider.of<TurfProvider>(context, listen: false)
//           .fetchTurfsByBusinessId(
//         widget.user.accessToken,
//         widget.user.businessId,
//       );
//       listOfTurf =
//           Provider.of<TurfProvider>(context, listen: false).getVerifiedTurf(1);

//       if (widget.business != null) {
//         List<String> turfIds = [];
//         widget.business!.turfs.forEach((turf) {
//           turfIds.add(turf.id);
//         });

//         listOfTurf.removeWhere((turf) => !turfIds.contains(turf.id));
//       }

//       setState(() {
//         isLoading = false;
//       });
//     } catch (e) {
//       print(e);
//       setState(() {
//         isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final deviceHeight = MediaQuery.of(context).size.height;
//     final deviceWidth = MediaQuery.of(context).size.width;
//     final textScaleFactor = MediaQuery.of(context).textScaleFactor;

//     PreferredSizeWidget? androidAppBar = AndroidAppBar(
//       title: 'Book Venue',
//       deviceWidth: deviceWidth,
//       elevation: 1.0,
//     );

//     return Scaffold(
//       appBar: androidAppBar,
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: isLoading
//             ? BookTurfOfflineSkeletonLoader(deviceWidth: deviceWidth)
//             : listOfTurf.length == 0
//                 ? Container(
//                     alignment: Alignment.center,
//                     padding:
//                         EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
//                     child: Text(
//                       'You cannot create offline booking because venue is not created or none of your venue is verified.',
//                       style: Theme.of(context).textTheme.displayLarge!.copyWith(
//                             fontSize: textScaleFactor * displayLarge,
//                             color: Colors.black,
//                             fontWeight: FontWeight.bold,
//                           ),
//                       textAlign: TextAlign.center,
//                     ),
//                   )
//                 : Container(
//                     child: Form(
//                       key: _formKey,
//                       child: SingleChildScrollView(
//                         physics: BouncingScrollPhysics(),
//                         padding: EdgeInsets.symmetric(
//                             horizontal: deviceWidth * 0.05),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             SizedBox(height: deviceWidth * 0.05),
//                             buildTitleContainer(
//                               deviceWidth,
//                               textScaleFactor,
//                               context,
//                               'Full Name',
//                             ),
//                             Container(
//                               child: TextFormField(
//                                 style: getStyle(textScaleFactor),
//                                 decoration: InputDecoration(
//                                   hintStyle: getHintStyle(textScaleFactor),

//                                   // contentPadding: EdgeInsets.symmetric(
//                                   //   horizontal: deviceWidth * 0.034,
//                                   //   vertical: deviceWidth * 0.04,
//                                   // ),
//                                   hintText: "Name",
//                                   counterText: "",
//                                   fillColor: Colors.grey.shade300,
//                                   border: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   focusedBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   enabledBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   errorBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   disabledBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   filled: false,
//                                 ),
//                                 maxLines: null,
//                                 autovalidateMode:
//                                     AutovalidateMode.onUserInteraction,
//                                 cursorColor: Colors.black,
//                                 textCapitalization: TextCapitalization.words,
//                                 controller: fullNameController,
//                                 keyboardType: TextInputType.text,
//                                 onChanged: (value) {},
//                                 validator: (value) {
//                                   if (value!.isEmpty) {
//                                     return 'Please enter full name';
//                                   }
//                                 },
//                               ),
//                             ),
//                             SizedBox(height: deviceWidth * 0.05),
//                             buildTitleContainer(
//                               deviceWidth,
//                               textScaleFactor,
//                               context,
//                               'Mobile Number',
//                             ),
//                             Container(
//                               child: TextFormField(
//                                 inputFormatters: [
//                                   FilteringTextInputFormatter.digitsOnly
//                                 ],
//                                 style: getStyle(textScaleFactor),
//                                 decoration: InputDecoration(
//                                   hintStyle: getHintStyle(textScaleFactor),
//                                   // contentPadding: EdgeInsets.symmetric(
//                                   //   horizontal: deviceWidth * 0.034,
//                                   //   vertical: deviceWidth * 0.04,
//                                   // ),
//                                   hintText: "Number",
//                                   counterText: "",
//                                   fillColor: Colors.grey.shade300,
//                                   border: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   focusedBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   enabledBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   errorBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   disabledBorder: UnderlineInputBorder(
//                                     borderSide:
//                                         BorderSide(color: Colors.grey.shade500),
//                                   ),
//                                   filled: false,
//                                 ),
//                                 cursorColor: Colors.black,
//                                 autovalidateMode:
//                                     AutovalidateMode.onUserInteraction,
//                                 controller: mobileNoController,
//                                 keyboardType: TextInputType.phone,
//                                 maxLength: 10,
//                                 onChanged: (value) {},
//                                 validator: (value) {
//                                   if (value!.isEmpty) {
//                                     return 'Please enter a phone number';
//                                   } else if (value.length < 10) {
//                                     return 'Please enter a valid phone number';
//                                   }
//                                 },
//                               ),
//                             ),
//                             SizedBox(height: deviceWidth * 0.05),
//                             buildTitleContainer(
//                               deviceWidth,
//                               textScaleFactor,
//                               context,
//                               'Select Venue',
//                             ),
//                             Container(
//                               decoration: BoxDecoration(
//                                 border: Border(
//                                   bottom:
//                                       BorderSide(color: Colors.grey.shade500),
//                                 ),
//                               ),
//                               child: DropdownButtonHideUnderline(
//                                 child: DropdownButton(
//                                   borderRadius: BorderRadius.circular(10),
//                                   value: turf,
//                                   isExpanded: true,
//                                   iconSize: 25,
//                                   icon: Icon(
//                                     Icons.keyboard_arrow_down,
//                                     color: Colors.black,
//                                   ),
//                                   iconEnabledColor: Colors.black54,
//                                   hint: Text(
//                                     'Select Venue',
//                                     style: Theme.of(context)
//                                         .textTheme
//                                         .headlineSmall!
//                                         .copyWith(
//                                           fontSize: textScaleFactor * 14,
//                                           color: Colors.black,
//                                           fontWeight: FontWeight.w500,
//                                         ),
//                                   ),
//                                   items: listOfTurf.map(
//                                     (turf) {
//                                       return DropdownMenuItem(
//                                         value: turf,
//                                         child: Container(
//                                           child: Text(
//                                             turf.name.toString(),
//                                             style: getStyle(textScaleFactor),
//                                           ),
//                                         ),
//                                       );
//                                     },
//                                   ).toList(),
//                                   onChanged: (value) {
//                                     setState(() {
//                                       turf = value;
//                                       selectedTurf = turf;
//                                       turfSelected = true;
//                                       areaController.text =
//                                           '${selectedTurf!.address.streetName}, ${selectedTurf!.address.landmark}, ${selectedTurf!.address.city}, ${selectedTurf!.address.pincode}';
//                                       sportController.clear();
//                                       timeController.clear();
//                                       selectedDateController.clear();

//                                       sizeOrSportController.clear();
//                                       slotController.clear();
//                                     });
//                                   },
//                                 ),
//                               ),
//                             ),
//                             SizedBox(height: deviceWidth * 0.05),
//                             // if (turfSelected)
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 buildTitleContainer(
//                                   deviceWidth,
//                                   textScaleFactor,
//                                   context,
//                                   'Area',
//                                 ),
//                                 Container(
//                                   child: TextFormField(
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.digitsOnly
//                                     ],
//                                     style: getStyle(textScaleFactor),
//                                     decoration: InputDecoration(
//                                       suffixIcon: Icon(
//                                         Icons.directions,
//                                         color: Colors.black54,
//                                       ),
//                                       hintStyle: getHintStyle(textScaleFactor),
//                                       // contentPadding: EdgeInsets.symmetric(
//                                       //   horizontal: deviceWidth * 0.034,
//                                       //   vertical: deviceWidth * 0.04,
//                                       // ),
//                                       hintText: "Area",
//                                       counterText: "",
//                                       fillColor: Colors.grey.shade300,
//                                       border: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       focusedBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       enabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       errorBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       disabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       filled: false,
//                                     ),
//                                     cursorColor: Colors.black,
//                                     readOnly: true,
//                                     maxLines: selectedTurf == null ? null : 2,
//                                     autovalidateMode:
//                                         AutovalidateMode.onUserInteraction,
//                                     controller: areaController,
//                                     keyboardType: TextInputType.text,
//                                     onChanged: (value) {},
//                                     validator: (value) {
//                                       if (value!.isEmpty) {
//                                         return 'Please enter venue area';
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 SizedBox(height: deviceWidth * 0.05),
//                                 if (selectedTurf != null &&
//                                     selectedTurf!.sportCategory!.categoryName ==
//                                         'Outdoor')
//                                   Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       buildTitleContainer(
//                                         deviceWidth,
//                                         textScaleFactor,
//                                         context,
//                                         'Sports',
//                                       ),
//                                       Container(
//                                         child: TextFormField(
//                                           inputFormatters: [
//                                             FilteringTextInputFormatter
//                                                 .digitsOnly
//                                           ],
//                                           style: getStyle(textScaleFactor),
//                                           decoration: InputDecoration(
//                                             suffixIcon: Icon(
//                                               Icons.keyboard_arrow_down,
//                                               color: Colors.black54,
//                                             ),
//                                             hintStyle:
//                                                 getHintStyle(textScaleFactor),
//                                             // contentPadding:
//                                             //     EdgeInsets.symmetric(
//                                             //   horizontal: deviceWidth * 0.034,
//                                             //   vertical: deviceWidth * 0.04,
//                                             // ),
//                                             hintText: "Select Sport",
//                                             counterText: "",
//                                             fillColor: Colors.grey.shade300,
//                                             border: UnderlineInputBorder(
//                                               borderSide: BorderSide(
//                                                   color: Colors.grey.shade500),
//                                             ),
//                                             focusedBorder: UnderlineInputBorder(
//                                               borderSide: BorderSide(
//                                                   color: Colors.grey.shade500),
//                                             ),
//                                             enabledBorder: UnderlineInputBorder(
//                                               borderSide: BorderSide(
//                                                   color: Colors.grey.shade500),
//                                             ),
//                                             errorBorder: UnderlineInputBorder(
//                                               borderSide: BorderSide(
//                                                   color: Colors.grey.shade500),
//                                             ),
//                                             disabledBorder:
//                                                 UnderlineInputBorder(
//                                               borderSide: BorderSide(
//                                                   color: Colors.grey.shade500),
//                                             ),
//                                             filled: false,
//                                           ),
//                                           cursorColor: Colors.black,
//                                           readOnly: true,
//                                           autovalidateMode: AutovalidateMode
//                                               .onUserInteraction,
//                                           controller: sportController,
//                                           keyboardType: TextInputType.text,
//                                           onChanged: (value) {},
//                                           onTap: () {
//                                             if (turfSelected) {
//                                               selectSportDropDown();
//                                             } else {
//                                               callToastMessage(
//                                                 'Please select venue',
//                                               );
//                                               return;
//                                             }
//                                           },
//                                           validator: (value) {
//                                             if (value!.isEmpty) {
//                                               return 'Please select sport';
//                                             }
//                                           },
//                                         ),
//                                       ),
//                                       SizedBox(height: deviceWidth * 0.05),
//                                     ],
//                                   ),
//                                 buildTitleContainer(
//                                   deviceWidth,
//                                   textScaleFactor,
//                                   context,
//                                   'Date',
//                                 ),
//                                 Container(
//                                   child: TextFormField(
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.digitsOnly
//                                     ],
//                                     style: getStyle(textScaleFactor),
//                                     decoration: InputDecoration(
//                                       suffixIcon: Icon(
//                                         Icons.calendar_today_outlined,
//                                         color: Colors.black54,
//                                         size: 20,
//                                       ),
//                                       hintStyle: getHintStyle(textScaleFactor),
//                                       // contentPadding: EdgeInsets.symmetric(
//                                       //   horizontal: deviceWidth * 0.034,
//                                       //   vertical: deviceWidth * 0.04,
//                                       // ),
//                                       hintText: "Pick a date",
//                                       counterText: "",
//                                       fillColor: Colors.grey.shade300,
//                                       border: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       focusedBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       enabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       errorBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       disabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       filled: false,
//                                     ),
//                                     cursorColor: Colors.black,
//                                     readOnly: true,
//                                     autovalidateMode:
//                                         AutovalidateMode.onUserInteraction,
//                                     controller: selectedDateController,
//                                     keyboardType: TextInputType.datetime,
//                                     onChanged: (value) {},
//                                     onTap: () {
//                                       if (turfSelected) {
//                                         selectDate();
//                                       } else {
//                                         callToastMessage('Please select venue');
//                                         return;
//                                       }
//                                     },
//                                     validator: (value) {
//                                       if (value!.isEmpty) {
//                                         return 'Please select booking date';
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 SizedBox(height: deviceWidth * 0.05),
//                                 buildTitleContainer(
//                                   deviceWidth,
//                                   textScaleFactor,
//                                   context,
//                                   'Slots',
//                                 ),
//                                 Container(
//                                   child: TextFormField(
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.digitsOnly
//                                     ],
//                                     style: getStyle(textScaleFactor),
//                                     decoration: InputDecoration(
//                                       : Icon(
//                                         Icons.keyboard_arrow_down,
//                                         color: Colors.black54,
//                                       ),
//                                       hintStyle: getHintStyle(textScaleFactor),
//                                       // contentPadding: EdgeInsets.symmetric(
//                                       //   horizontal: deviceWidth * 0.034,
//                                       //   vertical: deviceWidth * 0.04,
//                                       // ),
//                                       hintText: "Select Slot",
//                                       counterText: "",
//                                       fillColor: Colors.grey.shade300,
//                                       border: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       focusedBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       enabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       errorBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       disabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       filled: false,
//                                     ),
//                                     cursorColor: Colors.black,
//                                     readOnly: true,
//                                     autovalidateMode:
//                                         AutovalidateMode.onUserInteraction,
//                                     controller: slotController,
//                                     keyboardType: TextInputType.text,
//                                     onChanged: (value) {},
//                                     onTap: () {
//                                       if (turfSelected) {
//                                         if (selectedDateController
//                                                 .text.length ==
//                                             0) {
//                                           callToastMessage(
//                                               'Please select booking date');
//                                           return;
//                                         } else {
//                                           selectSlotDropDown();
//                                         }
//                                       } else {
//                                         callToastMessage('Please select venue');
//                                         return;
//                                       }
//                                     },
//                                     validator: (value) {
//                                       if (value!.isEmpty) {
//                                         return 'Please select slot';
//                                       }
//                                     },
//                                   ),
//                                 ),

//                                 SizedBox(height: deviceWidth * 0.05
//                                     //  slotController.text != ""
//                                     //     ? deviceWidth * 0.05
//                                     //     : 0,
//                                     ),
//                                 // if (slotController.text != "")
//                                 buildTitleContainer(
//                                   deviceWidth,
//                                   textScaleFactor,
//                                   context,
//                                   selectedTurf == null
//                                       ? 'Game'
//                                       : selectedTurf!.sportCategory!
//                                                   .categoryName !=
//                                               'Outdoor'
//                                           ? 'Game'
//                                           : 'Turf Size',
//                                 ),
//                                 // if (slotController.text != "")
//                                 Container(
//                                   child: TextFormField(
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.digitsOnly
//                                     ],
//                                     style: getStyle(textScaleFactor),
//                                     decoration: InputDecoration(
//                                       suffixIcon: Icon(
//                                         Icons.keyboard_arrow_down,
//                                         color: Colors.black54,
//                                       ),
//                                       hintStyle: getHintStyle(textScaleFactor),
//                                       // contentPadding: EdgeInsets.symmetric(
//                                       //   horizontal: deviceWidth * 0.034,
//                                       //   vertical: deviceWidth * 0.04,
//                                       // ),
//                                       hintText: selectedTurf == null
//                                           ? 'Select Game'
//                                           : selectedTurf!.sportCategory!
//                                                       .categoryName !=
//                                                   'Outdoor'
//                                               ? 'Select Game'
//                                               : "Select Turf Size",
//                                       counterText: "",
//                                       fillColor: Colors.grey.shade300,
//                                       border: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       focusedBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       enabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       errorBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       disabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       filled: false,
//                                     ),
//                                     cursorColor: Colors.black,
//                                     readOnly: true,
//                                     autovalidateMode:
//                                         AutovalidateMode.onUserInteraction,
//                                     controller: sizeOrSportController,
//                                     keyboardType: TextInputType.text,
//                                     onChanged: (value) {},
//                                     onTap: () {
//                                       if (slotController.text != '') {
//                                         selectTurfSizeDropDown();
//                                       } else {
//                                         callToastMessage('Please select slot');
//                                         return;
//                                       }
//                                     },
//                                     validator: (value) {
//                                       if (value!.isEmpty) {
//                                         return selectedTurf!.sportCategory!
//                                                     .categoryName !=
//                                                 'Outdoor'
//                                             ? 'Please select game'
//                                             : 'Please select turf size';
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 SizedBox(height: deviceWidth * 0.05),
//                                 // if (selectedDateController.text.length != 0 &&
//                                 //     slotController.text != "" &&
//                                 //     sizeOrSportController.text != "")
//                                 buildTitleContainer(
//                                   deviceWidth,
//                                   textScaleFactor,
//                                   context,
//                                   'Time',
//                                 ),
//                                 // if (selectedDateController.text.length != 0 &&
//                                 //     slotController.text != "" &&
//                                 //     sizeOrSportController.text != "")
//                                 Container(
//                                   child: TextFormField(
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.digitsOnly
//                                     ],
//                                     style: getStyle(textScaleFactor),
//                                     decoration: InputDecoration(
//                                       suffixIcon: Icon(
//                                         Icons.keyboard_arrow_down,
//                                         color: Colors.black54,
//                                       ),
//                                       hintStyle: getHintStyle(textScaleFactor),
//                                       hintText: "Pick a time",
//                                       counterText: "",
//                                       fillColor: Colors.grey.shade300,
//                                       border: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       focusedBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       enabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       errorBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       disabledBorder: UnderlineInputBorder(
//                                         borderSide: BorderSide(
//                                             color: Colors.grey.shade500),
//                                       ),
//                                       filled: false,
//                                     ),
//                                     cursorColor: Colors.black,
//                                     readOnly: true,
//                                     autovalidateMode:
//                                         AutovalidateMode.onUserInteraction,
//                                     controller: timeController,
//                                     keyboardType: TextInputType.datetime,
//                                     onChanged: (value) {},
//                                     onTap: () {
//                                       if (selectedDateController.text.length !=
//                                               0 &&
//                                           slotController.text != "" &&
//                                           sizeOrSportController.text != "") {
//                                         selectTimeDropDown();
//                                       } else {
//                                         callToastMessage(
//                                             'Please select all the above fields');
//                                         return;
//                                       }
//                                     },
//                                     validator: (value) {
//                                       if (value!.isEmpty) {
//                                         return 'Please select timing';
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 SizedBox(height: deviceWidth * 0.28),

//                                 // SizedBox(height: deviceWidth * 0.07),
//                               ],
//                             )
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//       ),
//       floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
//       floatingActionButton: isLoading ||
//               MediaQuery.of(context).viewInsets.bottom != 0 ||
//               listOfTurf.length == 0
//           ? SizedBox.shrink()
//           : Container(
//               margin: EdgeInsets.only(bottom: deviceWidth * 0.07),
//               padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
//               child: buildRaisedButton(
//                 deviceWidth,
//                 deviceWidth * 0.12,
//                 () {
//                   if (!_formKey.currentState!.validate()) {
//                     return;
//                   }
//                   Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => BookingSummaryScreen(
//                         turf: selectedTurf!,
//                         selectedTime: selectedTime,
//                         bookingDate: selectedDate,
//                         time: timeController.text,
//                         sport: SportType(
//                           id: sportId,
//                           sport: sportController.text,
//                           image: '',
//                         ),
//                         amount: amount,
//                         sizeOrSport: sizeOrSportController.text,
//                         name: fullNameController.text.trim(),
//                         phone: mobileNoController.text,
//                         quantity: quantity,
//                         // selectedSlot: selectedSlot,
//                         label: label,
//                         business: widget.business,
//                       ),
//                     ),
//                   ).then((value) {
//                     if (value != null) {
//                       setState(() {
//                         timeController.clear();
//                       });
//                       if (value['goBack']) {
//                         Navigator.of(context).pop(true);
//                       }
//                     }
//                   });
//                 },
//                 Text(
//                   'Proceed',
//                   style: Theme.of(context).textTheme.displayMedium?.copyWith(
//                         fontSize: textScaleFactor * 16,
//                         fontWeight: FontWeight.w600,
//                         letterSpacing: .50,
//                         color: Colors.white,
//                       ),
//                 ),
//                 TargetPlatform.android,
//                 Theme.of(context).primaryColor,
//                 7,
//               ),
//             ),
//     );
//   }
// }
