import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../employeeModule/screens/booking_screen.dart';
import '../../moreModule.dart/provider/moreProvider.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../homeModule/screens/homeScreen.dart';
import '../providers/homeProvider.dart';

class SuccessScreen extends StatefulWidget {
  final String text;
  final UserModal user;
  SuccessScreen({required this.text, required this.user});
  @override
  _SuccessScreenState createState() => _SuccessScreenState();
}

class _SuccessScreenState extends State<SuccessScreen> {
  navigate() {
    if (widget.user.business != null) {
      pushAndRemoveUntil(BookingScreen(user: widget.user));
    } else {
      Provider.of<HomeProvider>(context, listen: false).resetBooking();
      Provider.of<MoreProvider>(context, listen: false).emptyBooking();
      Provider.of<HomeProvider>(context, listen: false).resetBulkBooking();
      pushAndRemoveUntil(HomeScreen());
    }
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 2)).then((value) {
      Provider.of<NotificationProvider>(context, listen: false)
          .incrementAndDecrementNotificationCount(true);
      navigate();
    });
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;

    return WillPopScope(
      onWillPop: () async {
        navigate();
        return true;
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).primaryColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.only(top: width * 0.7),
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(width * 0.05),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      Icons.done,
                      size: 45,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  SizedBox(height: width * 0.05),
                  Text(
                    '${widget.text}',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
