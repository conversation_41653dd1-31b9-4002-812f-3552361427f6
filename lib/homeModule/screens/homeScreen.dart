import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/homeModule/screens/qrScannerScreen.dart';
import 'package:bys_business/homeModule/widgets/analytics_widget.dart';
import 'package:bys_business/homeModule/widgets/badge_widget.dart';
import 'package:bys_business/venueModule/widgets/add_venue_bottom_sheet.dart';
import 'package:bys_business/venueModule/widgets/turfWidget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_button_2.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../moreModule.dart/model/barChartModel.dart';
import '../../moreModule.dart/provider/moreProvider.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import '../../main.dart';
import '../../pushNotification/localNotificationService.dart';
import '../../venueModule/newVenueFlow/screens/venue_instruction_screen.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../fontSizes.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/mainDrawer.dart';
import '../../notificationModule.dart/screens/notificationScreen.dart';
import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../venueModule/models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../widgets/bookingStatusWidget.dart';
import '../widgets/bookingWidget.dart';
import '../widgets/select_venue_sheet.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  double dW = 0;
  double dH = 0;
  double tS = 0;

  Map language = {};
  TextTheme customTextTheme = const TextTheme();

  int selectedBooking = 0;

  bool isLoading = false;
  bool lazyLoading = false;
  bool isFetchingAnalytics = false;
  final LocalStorage storage = new LocalStorage('bysBusiness');

  List<BarChartModel> totalBookingData = [];
  List<PieData> individualTotalBookingOfSport = [];

  List<BookingModel> listOfBookings = [];
  List<Venue> listOfTurfs = [];
  List turfDetails = [];
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  DateTime selectedDate = DateTime.now();
  late UserModal user;
  bool addHours = false;
  bool rejected = false;

  final ScrollController _scrollController = ScrollController();

  String selectedDuration = 'Weekly';
  String selectedBookingTab = 'Upcoming';

  // final String status = 'Upcomings';

  List bookingIds = [];

  String adminContact = '';

  cancelConfirmation() {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title: 'Are you sure you want to cancel selected all bookings?',
            noText: 'Yes, Cancel',
            yesText: 'No',
            noFunction: () {
              pop();
              cancelBulkBooking();
            },
            yesFunction: () => pop(),
          )),
    );
  }

  cancelBulkBooking() async {
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .cancelBulkBooking(
      body: {
        'bookingIds': bookingIds,
      },
      accessToken: user.accessToken,
    );

    if (response['success']) {
      bookingIds = [];
      selectedBooking = 0;
      setState(() {});
      refreshBooking();
    }
  }

  Future<void> fetchTurf() async {
    try {
      setState(() {
        isLoading = true;
      });
      await fetchAppUpdate();
      await Provider.of<TurfProvider>(context, listen: false)
          .fetchTurfsByBusinessId(user.accessToken, user.businessId);
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getStatusCount(int index) {
    int count = 0;
    if (index == 1) {
      count = Provider.of<HomeProvider>(context, listen: false).newCount;
    } else if (index == 2) {
      count = Provider.of<HomeProvider>(context, listen: false).completedCount;
    } else if (index == 3) {
      count = Provider.of<HomeProvider>(context, listen: false).cancelledCount;
    }
    return count;
  }

  changeDate() {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2021),
      lastDate: DateTime(2200),
    ).then((date) {
      if (date != null) {
        setState(() {
          selectedDate = date;
          addHours = true;
          // fetchBooking();
        });
      }
    });
  }

  String? notificationId;

  handleNotificationClick(data) async {
    final notificationIdString = storage.getItem('fcmNotificationIds');
    if (notificationIdString != null) {
      notificationId = json.decode(notificationIdString);
      if (notificationId == data['notificationId']) {
        return;
      }
    }
    storage.setItem('fcmNotificationIds', json.encode(data['notificationId']));
    print('Handle  route');

    if (data == null) {
    } else if (data['type'] == 'Booking') {
      navigatorKey.currentState!.push(
        MaterialPageRoute(
          builder: (context) => BookingDescriptionScreen(
            user: user,
            bookingId: data['bookingId'],
          ),
        ),
      );
    } else if (data['type'] == 'Business Approved' ||
        data['type'] == 'Business Rejected') {
      checkForUser();
    }
  }

  checkForUser() async {
    try {
      setState(() {
        isLoading = true;
      });
      await Provider.of<Auth>(context, listen: false).isUserExists(user.phone);
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  fetchAppUpdate() async {
    try {
      setState(() {
        isLoading = true;
      });

      await Provider.of<NotificationProvider>(context, listen: false)
          .fetchUserNewNotifcation(accessToken: user.accessToken);

      if (!Provider.of<Auth>(context, listen: false).callOnces) {
        Provider.of<Auth>(context, listen: false).callOnces = true;
        await checkVersion(context);
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  scanQrCode() {
    push(QRScannerScreen()).then((value) async {
      if (value != null && value != '') {
        var data = json.decode(value);
        if (data['business'] != user.businessId) {
          showSnackbar('This booking does not belong to your venue');
          return;
        }

        var bookingId = data['bookingId'];
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BookingDescriptionScreen(
              user: user,
              comingFrom: 'qrScanner',
              bookingId: bookingId,
            ),
          ),
        );
      }
    });
  }

  fetchBookingAnalytics() async {
    try {
      setState(() => isFetchingAnalytics = true);
      await Provider.of<MoreProvider>(context, listen: false).fetchAnalyticsV2(
        accessToken: user.accessToken,
        business: user.businessId,
        title: selectedDuration,
      );

      individualTotalBookingOfSport =
          Provider.of<MoreProvider>(context, listen: false).listOfSports;
      totalBookingData =
          Provider.of<MoreProvider>(context, listen: false).listOfBooking;
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isFetchingAnalytics = false);
    }
  }

  selectBookingTab(String tab) async {
    selectedBookingTab = tab;
    setState(() {});
    await fetchBooking();
  }

  selectDuration(String duration) {
    selectedDuration = duration;
    setState(() {});
    fetchBookingAnalytics();
  }

  fetchBooking() async {
    try {
      if (Provider.of<MoreProvider>(context, listen: false)
          .getBookingByTab(selectedBookingTab)
          .isEmpty) {
        setState(() => isLoading = true);
        await Provider.of<MoreProvider>(context, listen: false)
            .fetchBusinessBookingV2(
          accessToken: user.accessToken,
          selectedTab: selectedBookingTab,
          businessId: user.businessId,
          role: user.role,
          refresh: true,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  lazyLoad() async {
    setState(() => lazyLoading = true);
    await Provider.of<MoreProvider>(context, listen: false)
        .fetchBusinessBookingV2(
      accessToken: user.accessToken,
      selectedTab: selectedBookingTab,
      businessId: user.businessId,
      role: user.role,
    );
    setState(() => lazyLoading = false);
  }

  refreshBooking() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);
      await Provider.of<MoreProvider>(context, listen: false)
          .fetchBusinessBookingV2(
        accessToken: user.accessToken,
        selectedTab: selectedBookingTab,
        businessId: user.businessId,
        role: user.role,
        refresh: true,
      );
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) lazyLoad();
    }
    return false;
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      // constraints:
      //     BoxConstraints(maxHeight: dH * 0.8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SelectVenueSheet(user: user, bookingType: ''),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  addVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddVenueBottomSheet(listOfVenues: listOfTurfs),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  featureUnavailableDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Feature Unavailable',
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: tS * 16,
                        color: const Color(0XFF1D2739),
                      ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    'This feature is not currently available to you. Please contact the admin.',
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF667185),
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    adminContact,
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: getThemeColor(),
                        ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    launchCall(adminContact);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: dW * 0.05),
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Call',
                      style: Theme.of(context).textTheme.displayLarge!.copyWith(
                            fontSize: tS * 16,
                            color: getThemeColor(),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  fetchAdminContact() {
    var auth = Provider.of<Auth>(context, listen: false);
    adminContact = auth.adminContact.toString();
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchAdminContact();
    if (user.businessStatus == 'PENDING') {
      fetchTurf();
    } else if (user.businessStatus == "REJECTED") {
      rejected = true;
    } else {
      fetchAppUpdate();
      fetchBookingAnalytics();
      fetchBooking();
    }

    Provider.of<Auth>(context, listen: false)
        .insertAppVersion(accessToken: user.accessToken);
    LocalNotificationService.initialize(
        navigatorKey.currentContext!, handleNotificationClick);
    FirebaseMessaging.onBackgroundMessage(
        (RemoteMessage message) => handleNotificationClick(message));
    FirebaseMessaging.instance.getInitialMessage().then((message) async {
      if (message != null) {
        message.data['notificationId'] = message.messageId;
        handleNotificationClick(message.data);
      }
    });

    FirebaseMessaging.onMessage.listen((message) async {
      if (message.notification != null) {
        message.data['notificationId'] = message.messageId;
        LocalNotificationService.display(message);
        if (message.data['type'] == 'Business Rejected' ||
            message.data['type'] == 'Business Approved') {
          await Provider.of<NotificationProvider>(context, listen: false)
              .incrementAndDecrementNotificationCount(true);
          checkForUser();
        }
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((message) async {
      if (message.notification != null) {
        message.data['notificationId'] = message.messageId;
        handleNotificationClick(message.data);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    user = Provider.of<Auth>(context).user;
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<Auth>(context).selectedLanguage;

    customTextTheme = Theme.of(context).textTheme;
    listOfTurfs = Provider.of<TurfProvider>(context).turfs;
    listOfBookings =
        Provider.of<MoreProvider>(context).getBookingByTab(selectedBookingTab);

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      drawer: MainDrawer(),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: user.businessStatus == "PENDING" && !isLoading
          ? Padding(
              padding: EdgeInsets.only(bottom: dW * 0.05),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomButton(
                    width: dW * 0.9,
                    height: dW * 0.12,
                    fontSize: 16,
                    buttonText: 'Add Venue Now',
                    // onPressed: () => listOfTurfs.isEmpty
                    //     ? push(VenueInstructionScreen())
                    //     : addVenueBottomSheet(),
                    buttonColor: Color(0XFFC0C0C0),
                    onPressed: () {
                      featureUnavailableDialog();
                    },
                  ),
                  SizedBox(height: dW * 0.05),
                  TextWidget(
                    title:
                        'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  )
                ],
              ),
            )
          : SizedBox.shrink(),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.04,
                right: dW * 0.04,
                top: Platform.isAndroid ? 0 : dW * 0.08),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CustomButton2(
                      dW: dW,
                      svgIcon: 'menu',
                      onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    ),
                    SizedBox(width: dW * .05),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: dW * 0.52),
                          child: TextWidget(
                            title: '${user.firstName} ${user.lastName}',
                            fontWeight: FontWeight.w600,
                            color: getGreyColor2(context),
                            maxLines: 2,
                            textOverflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          user.email,
                          style: customTextTheme.titleSmall!.copyWith(
                              fontWeight: FontWeight.w400,
                              color: getGreyColor2(context)),
                        )
                      ],
                    ),
                  ],
                ),
                if (user.businessStatus != "PENDING" && !isLoading)
                  Row(
                    children: [
                      CustomButton2(
                          dW: dW, svgIcon: 'scanner_green', onTap: scanQrCode),
                      SizedBox(width: dW * .03),
                      BadgeWidget(
                        value: Provider.of<NotificationProvider>(context)
                            .notificationCount,
                        child: CustomButton2(
                          dW: dW,
                          svgIcon: 'notification',
                          onTap: () => push(
                            NotificationScreen(user: user),
                          ),
                        ),
                      )
                    ],
                  )
              ],
            ),
          ),
          if (user.businessStatus == "REJECTED")
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.023),
                      child: Text(
                        'Your business request has been rejected. Please contact admin.',
                        style: Theme.of(context).textTheme.displaySmall!.copyWith(
                              fontSize: tS * displayMedium,
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => callLaunch(
                          '+91${Provider.of<Auth>(context, listen: false).adminContact}'),
                      child: Container(
                        margin: EdgeInsets.all(dW * 0.04),
                        padding: EdgeInsets.all(dW * 0.02),
                        width: dW * 0.5,
                        color: Colors.transparent,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset('assets/svgIcons/Inquiry.svg'),
                            SizedBox(width: dW * 0.03),
                            Text(
                              'Admin',
                              style: Theme.of(context)
                                  .textTheme
                                  .displaySmall!
                                  .copyWith(
                                    fontSize: tS * displayMedium,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          if (user.businessStatus == "PENDING")
            Expanded(
              child: isLoading
                  ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                  : listOfTurfs.isEmpty
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset("assets/images/home_pending.png",
                                scale: 2.5),
                            SizedBox(height: dW * .08),
                            Padding(
                              padding:
                                  EdgeInsets.symmetric(horizontal: dW * .08),
                              child: Text(
                                'Enhance Your Venue, Boost Your Earnings. List Your Venue and Attract More Bookings Today',
                                style: customTextTheme.displaySmall!.copyWith(
                                  fontSize: 16,
                                  color: Color(0xffA0A0A0),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        )
                      : SingleChildScrollView(
                          padding: EdgeInsets.symmetric(horizontal: dW * .05),
                          physics: BouncingScrollPhysics(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: dW * 0.07),
                              TextWidget(
                                title: 'Venues (${listOfTurfs.length})',
                                fontWeight: FontWeight.w500,
                                color: getGreyColor1(context),
                              ),
                              SizedBox(height: dW * 0.035),
                              ...listOfTurfs.map(
                                (venue) => TurfWidget(
                                  dW: dW,
                                  tS: tS,
                                  turf: venue,
                                  user: user,
                                ),
                              ),
                              SizedBox(height: dW * 0.09),
                            ],
                          ),
                        ),
            ),
          if (user.businessStatus == "APPROVED")
            Expanded(
              child: RefreshIndicator(
                onRefresh: () => refreshBooking(),
                child: NotificationListener<ScrollNotification>(
                  onNotification: _handleScrollNotification,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: dW * 0.07),
                        Column(
                          children: [
                            CustomContainer(
                              hPadding: 0,
                              vPadding: .02,
                              borderColor: Color(0xffF3F4F9),
                              radius: 10,
                              margin: EdgeInsets.only(bottom: dW * 0.05),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  ...['All time', 'Weekly', 'Monthly'].map(
                                    (type) => GestureDetector(
                                      onTap: () => selectDuration(type),
                                      child: Container(
                                        width: dW * 0.25,
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.symmetric(
                                            vertical: dW * 0.02),
                                        decoration: BoxDecoration(
                                          color: selectedDuration == type
                                              ? getThemeColor()
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        child: TextWidget(
                                          title: type,
                                          color: selectedDuration != type
                                              ? Colors.black
                                              : Colors.white,
                                          fontSize: selectedDuration == type
                                              ? tS * 15
                                              : tS * 14,
                                          fontWeight: selectedDuration == type
                                              ? FontWeight.w600
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            if (isFetchingAnalytics)
                              Padding(
                                padding:
                                    EdgeInsets.symmetric(vertical: dW * 0.3),
                                child: CircularLoader(
                                    android: dW * 0.055, iOS: dW * 0.035),
                              ),
                            if (!isFetchingAnalytics) ...[
                              AnalyticsWidget(
                                title: 'Total Booking',
                                data: totalBookingData,
                                barWidth: 0.6,
                                interval: 5,
                                pieData: [],
                              ),
                              // SizedBox(height: dW * 0.05),
                              // AnalyticsWidget(
                              //   title: 'Booked Count',
                              //   data: [],
                              //   barWidth: 0.45,
                              //   interval: 1.0,
                              //   pieData: individualTotalBookingOfSport,
                              // ),
                            ],
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // SizedBox(height: dW * .045),
                            // TextWidget(
                            //   title: language['bookings'],
                            //   fontWeight: FontWeight.w600,
                            //   fontSize: 16,
                            // ),
                            BookingStatusWidget(
                                deviceWidth: dW,
                                getStatusCount: getStatusCount,
                                textScaleFactor: tS,
                                user: user,
                                fromBYSBusiness: true),
                            SizedBox(height: dW * .035),
                            CustomContainer(
                              hPadding: 0,
                              vPadding: .014,
                              borderColor: Color(0xffF3F4F9),
                              radius: 10,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  ...['Upcoming', 'Completed', 'Cancelled'].map(
                                    (tab) => GestureDetector(
                                      onTap: () => selectBookingTab(tab),
                                      child: Container(
                                        width: dW * 0.25,
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.symmetric(
                                            vertical: dW * 0.025),
                                        decoration: BoxDecoration(
                                          color: tab == selectedBookingTab
                                              ? getThemeColor()
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          tab,
                                          style: TextStyle(
                                            color: tab != selectedBookingTab
                                                ? Colors.black
                                                : Colors.white,
                                            fontSize: tab == selectedBookingTab
                                                ? tS * 14
                                                : tS * 13,
                                            fontWeight:
                                                tab == selectedBookingTab
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            isLoading
                                ? CircularLoader(
                                    android: dW * 0.08, iOS: dW * 0.035)
                                : listOfBookings.isEmpty
                                    ? EmptyListWidget(
                                        text:
                                            '$selectedBookingTab booking not found!',
                                        topPadding: 0.2,
                                      )
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (selectedBooking > 0)
                                            SizedBox(height: dW * 0.04),
                                          if (selectedBooking > 0)
                                            GestureDetector(
                                              onTap: cancelConfirmation,
                                              child: Text(
                                                'Cancel Bookings : ${selectedBooking.toString()}',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ),
                                          SizedBox(height: dW * 0.04),
                                          ...listOfBookings.map(
                                            (booking) => BookingWidget(
                                              fromViewAll: true,
                                              // onSelectionChanged:
                                              //     (int count, bool isSelected) {
                                              //   setState(() {
                                              //     selectedBooking += count;
                                              //     if (isSelected) {
                                              //       bookingIds.add(booking.id);
                                              //     } else {
                                              //       bookingIds
                                              //           .remove(booking.id);
                                              //     }
                                              //   }
                                              //   );
                                              // },
                                              booking: booking,
                                              user: user,
                                              deviceWidth: dW,
                                              textScaleFactor: tS,
                                            ),
                                          ),
                                          SizedBox(height: dW * 0.04),
                                          if (lazyLoading) lazyLoader(dW),
                                        ],
                                      ),
                          ],
                        ),
                        SizedBox(height: dW * 0.1),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          if (user.businessStatus == "APPROVED")
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.12,
                radius: 7,
                fontSize: 17,
                buttonText: 'Book Now',
                onPressed: openVenueBottomSheet,
              ),
            ),
        ],
      ),
    );
  }
}
