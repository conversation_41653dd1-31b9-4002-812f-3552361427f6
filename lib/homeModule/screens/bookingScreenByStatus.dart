import 'dart:io';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../fontSizes.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../moreModule.dart/provider/moreProvider.dart';

class BookingScreenByStatus extends StatefulWidget {
  final String status;
  BookingScreenByStatus({Key? key, required this.status}) : super(key: key);

  @override
  BookingScreenByStatusState createState() => BookingScreenByStatusState();
}

class BookingScreenByStatusState extends State<BookingScreenByStatus> {
  bool isLoading = false;
  bool lazyLoading = false;
  List<BookingModel> listOfBookings = [];
  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  // fetchBookingByStatus() async {
  //   try {
  //     setState(() {
  //       isLoading = true;
  //     });
  //     await getBookings();

  //     setState(() {
  //       isLoading = false;
  //     });
  //   } catch (e) {
  //     print(e);
  //     setState(() {
  //       isLoading = false;
  //     });
  //   }
  // }

  fetchBooking() async {
    try {
      if (Provider.of<MoreProvider>(context, listen: false)
          .getBookingByTab(widget.status)
          .isEmpty) {
        List turfId = [];
        if (user.business != null) {
          user.business!.turfs.forEach((turf) {
            turfId.add(turf.id);
          });
        }
        setState(() => isLoading = true);
        await Provider.of<MoreProvider>(context, listen: false)
            .fetchBusinessBookingV2(
          accessToken: user.accessToken,
          //TODO back to default  
          selectedTab:
              widget.status == 'Upcomings' ? 'Upcomings' : widget.status,
          businessId: user.businessId,
          role: user.role,
          refresh: true,
          turfId: turfId,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  lazyLoad() async {
    setState(() => lazyLoading = true);
    List turfId = [];
    if (user.business != null) {
      user.business!.turfs.forEach((turf) {
        turfId.add(turf.id);
      });
    }
    await Provider.of<MoreProvider>(context, listen: false)
        .fetchBusinessBookingV2(
      accessToken: user.accessToken,
      selectedTab: widget.status == 'Upcomings' ? 'Upcoming' : widget.status,
      businessId: user.businessId,
      role: user.role,
      turfId: turfId,
    );
    setState(() => lazyLoading = false);
  }

  // getBookings() async {
  //   List<String> turfId = [];

  //   if (user.business != null) {
  //     user.business!.turfs.forEach((turf) {
  //       turfId.add(turf.id);
  //     });
  //   }

  //   await Provider.of<HomeProvider>(context, listen: false)
  //       .fetchBookingsByStatusNew(
  //     accessToken: user.accessToken,
  //     business: user.businessId,
  //     status: widget.status.toLowerCase(),
  //     role: user.business == null ? 'Business' : 'Employee',
  //     turfs: turfId,
  //   );
  // }

  // lazyLoad() async {
  //   setState(() {
  //     lazyLoading = true;
  //   });
  //   await getBookings();

  //   setState(() {
  //     lazyLoading = false;
  //   });
  // }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        lazyLoad();
      }
    }
    return false;
  }

  @override
  void initState() {
    super.initState();
    Provider.of<HomeProvider>(context, listen: false).emptyStatusBooking();
    user = Provider.of<Auth>(context, listen: false).user;
    // fetchBookingByStatus();
    fetchBooking();
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;
    // listOfBookings = Provider.of<HomeProvider>(context).statusBooking;
    listOfBookings = Provider.of<MoreProvider>(context).getBookingByTab(
        widget.status == 'Upcomings' ? 'Upcomings' : widget.status);

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title: '${widget.status} Bookings',
      deviceWidth: deviceWidth,
      elevation: 1.0,
    );

    return Scaffold(
      appBar: androidAppBar,
      backgroundColor: Colors.white,
      body: SafeArea(
        child: isLoading
            ? Center(
                child: Platform.isAndroid
                    ? MaterialCircularLoader(deviceWidth * 0.07)
                    : CupertinoCircularLoader(15.0),
              )
            : listOfBookings.length == 0
                ? Container(
                    alignment: Alignment.center,
                    child: Text(
                      'Bookings not found!',
                      style: Theme.of(context).textTheme.displayLarge!.copyWith(
                            fontSize: textScaleFactor * displayLarge,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  )
                : Container(
                    height: deviceHeight,
                    child: NotificationListener<ScrollNotification>(
                      onNotification: _handleScrollNotification,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: EdgeInsets.symmetric(
                            horizontal: deviceWidth * 0.05),
                        physics: BouncingScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: deviceWidth * 0.04),
                            ...listOfBookings.map(
                              (booking) => BookingWidget(
                                booking: booking,
                                user: user,
                                deviceWidth: deviceWidth,
                                textScaleFactor: textScaleFactor,
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.04),
                            if (lazyLoading)
                              Center(
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    bottom: deviceWidth * 0.1,
                                  ),
                                  child: Platform.isIOS
                                      ? CupertinoCircularLoader(10.0)
                                      : MaterialCircularLoader(
                                          deviceWidth * 0.06,
                                        ),
                                ),
                              ),
                            SizedBox(height: deviceWidth * 0.12),
                          ],
                        ),
                      ),
                    ),
                  ),
      ),
    );
  }
}
