import 'dart:io' show Platform;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

class QRScannerScreen extends StatefulWidget {
  QRScannerScreen();

  @override
  _QRScannerScreenState createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  Barcode? result;
  late QRViewController controller;

  // In order to get hot reload to work we need to pause the camera if the platform
  // is android, or resume the camera if the platform is iOS.
  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller.pauseCamera();
    } else if (Platform.isIOS) {
      controller.resumeCamera();
    }
  }

  void resuecamara() {
    try {
      if (Platform.isAndroid) {
        controller.pauseCamera();
      }
      controller.resumeCamera();
    } catch (e) {
      print('Camera error: $e');
      showToast('Camera initialization failed. Please try again.');
    }
  }

  showToast(msg) {
    Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.values[5],
      timeInSecForIosWeb: 2,
      backgroundColor: Colors.white,
      textColor: Colors.black,
      fontSize: textScale * 12,
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    try {
      setState(() {
        this.controller = controller;
      });
      
      // Delay camera resume to ensure proper initialization
      Future.delayed(Duration(milliseconds: 500), () {
        try {
          resuecamara();
        } catch (e) {
          print('Camera resume error: $e');
        }
      });
      
      controller.scannedDataStream.listen((scanData) async {
        setState(() {
          result = scanData;
        });
        controller.pauseCamera();
        controller.dispose();
        Navigator.of(context).pop(result!.code);
      });
    } catch (e) {
      print('QR controller error: $e');
      showToast('Camera initialization failed');
    }
  }

  double textScale = 0.0;
  double deviceHeight = 0.0;
  double deviceWidth = 0.0;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    // Future.delayed(Duration(seconds: 2)).then(
    //   (value) => Navigator.pushAndRemoveUntil(
    //       context,
    //       MaterialPageRoute(
    //         builder: (context) => SuccessScreen(
    //           text: 'Paid',
    //         ),
    //       ),
    //       (route) => false),
    // );
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  Future<bool> _willPopCallback() async {
    controller.dispose();

    Navigator.of(context).pop('');

    return true;
  }

  @override
  Widget build(BuildContext context) {
    textScale = MediaQuery.of(context).textScaleFactor;
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;

    return WillPopScope(
      onWillPop: _willPopCallback,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: screenBody(),
      ),
    );
  }

  screenBody() {
    return Container(
      padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
      height: deviceHeight,
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: QRView(
                  overlay: QrScannerOverlayShape(
                    cutOutSize: deviceWidth * 0.65,
                    borderLength: 20,
                    cutOutBottomOffset: 2,
                    borderColor: Colors.green,
                    borderRadius: 5,
                    borderWidth: 7,
                  ),
                  key: qrKey,
                  onQRViewCreated: _onQRViewCreated,
                ),
              ),
            ],
          ),
          Positioned(
            left: deviceWidth * 0.02,
            top: deviceWidth * 0.05,
            child: IconButton(
              color: Colors.transparent,
              onPressed: () {
                controller.dispose();
                Navigator.of(context).pop('');
              },
              icon: Icon(
                Platform.isAndroid
                    ? Icons.arrow_back_sharp
                    : CupertinoIcons.back,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
