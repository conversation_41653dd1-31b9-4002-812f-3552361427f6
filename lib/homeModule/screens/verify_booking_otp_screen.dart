import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';

class VerifyBookingOtpScreen extends StatefulWidget {
  final String bookingId;
  final String accessToken;
  const VerifyBookingOtpScreen({
    Key? key,
    required this.bookingId,
    required this.accessToken,
  }) : super(key: key);

  @override
  State<VerifyBookingOtpScreen> createState() => _VerifyBookingOtpScreenState();
}

class _VerifyBookingOtpScreenState extends State<VerifyBookingOtpScreen> {
  TextEditingController _otpController = new TextEditingController();
  FocusNode otpFocusNode = FocusNode();

  bool isLoading = false;

  verifyBooking() async {
    try {
      if (_otpController.text.trim().isEmpty) {
        return callToastMessage('Please enter otp');
      } else if (_otpController.text.trim().length < 6) {
        return callToastMessage('Please enter valid otp');
      } else {
        setState(() {
          isLoading = true;
        });

        final data = await Provider.of<HomeProvider>(context, listen: false)
            .verifyBookingByOTP(
          bookingId: widget.bookingId,
          accessToken: widget.accessToken,
          otp: _otpController.text.trim(),
        );
        if (data['success']) {
          Navigator.of(context).pop({
            'bookingId': data['result']['_id'],
            'business': data['result']['business'],
          });
        } else {
          if (data['message'] == 'Failed') {
            return callToastMessage('OTP is invalid.');
          } else {
            return callToastMessage('Something went wrong');
          }
        }
      }
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title: 'OTP Verification',
      deviceWidth: deviceWidth,
      elevation: 0.0,
    );

    return Scaffold(
      appBar: androidAppBar,
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Container(
          alignment: Alignment.topLeft,
          margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(
                  top: deviceWidth * 0.4,
                  bottom: deviceWidth * 0.05,
                ),
                child: Text(
                  'Enter correct OTP to verify the booking.',
                  style: Theme.of(context).textTheme.displayMedium!.copyWith(
                        fontSize: textScaleFactor * 15,
                      ),
                ),
              ),
              Container(
                // height: deviceWidth * 0.12,
                width: deviceWidth * 0.87,
                margin: EdgeInsets.only(left: 5),
                child: PinCodeTextField(
                  appContext: context,
                  backgroundColor: Colors.transparent,
                  enablePinAutofill: true,
                  pastedTextStyle: TextStyle(
                    color: Colors.black,
                    fontSize: textScaleFactor * 22,
                    fontWeight: FontWeight.w600,
                  ),
                  length: 6,
                  controller: _otpController,
                  autoDismissKeyboard: true,
                  cursorWidth: 1.5,
                  cursorHeight: deviceWidth * 0.05,
                  cursorColor: Colors.black,
                  focusNode: otpFocusNode,
                  enableActiveFill: true,
                  animationType: AnimationType.fade,
                  keyboardType: TextInputType.number,
                  textStyle: TextStyle(
                    color: Colors.black,
                    fontSize: textScaleFactor * 22,
                    letterSpacing: 0,
                    fontWeight: FontWeight.w600,
                  ),
                  pinTheme: PinTheme(
                    fieldHeight: deviceWidth * 0.12,
                    fieldWidth: deviceWidth * 0.12,
                    shape: PinCodeFieldShape.circle,
                    borderRadius: BorderRadius.circular(10),
                    borderWidth: 0.8,
                    activeColor: Colors.black,
                    activeFillColor: Colors.transparent,
                    selectedColor: Colors.black,
                    selectedFillColor: Colors.transparent,
                    disabledColor: Color(0xffEAEAEA),
                    inactiveColor: Color(0xffEAEAEA),
                    inactiveFillColor: Color(0xffEAEAEA),
                  ),
                  onChanged: (val) {
                    if (val.length > 0) {}
                  },
                  validator: (value) {
                    if (value!.length == 0) {
                      return '';
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
        child: buildRaisedButton(
          deviceWidth * 0.9,
          deviceWidth * 0.12,
          isLoading ? () {} : verifyBooking,
          isLoading
              ? circularForButton(deviceWidth)
              : Text(
                  'Verify',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                        fontSize: textScaleFactor * 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: .50,
                        color: Colors.white,
                      ),
                ),
          TargetPlatform.android,
          Theme.of(context).primaryColor,
          7,
        ),
      ),
    );
  }
}
