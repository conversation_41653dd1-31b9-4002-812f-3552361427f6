// import 'dart:async';

// import 'package:bys_business/colors.dart';
// import 'package:bys_business/commonWidgets/circular_loader.dart';
// import 'package:bys_business/commonWidgets/new_appbar.dart';
// import 'package:bys_business/homeModule/models/bookingModel.dart';
// import 'package:bys_business/venueModule/providers/turfProvider.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

// import '../../authModule/modals/userModel.dart';
// import '../../bulkBookingModule/screens/create_bulk_booking_screen.dart';
// import '../../commonWidgets/custom_button.dart';
// import '../../commonWidgets/text_widget.dart';
// import '../../common_function.dart';
// // import '../../commonWidgets/materialCircularLoader.dart';
// import '../../homeModule/providers/homeProvider.dart';
// import '../../homeModule/screens/bookSummaryScreen.dart';
// import '../../navigators.dart';
// import '../../new_colors.dart';
// import '../../venueModule/models/venue_model.dart';
// import 'package:flutter/material.dart';
// import 'package:date_picker_timeline/date_picker_timeline.dart';
// import 'package:intl/intl.dart';
// import 'package:provider/provider.dart';

// import '../widgets/select_sport_and_size_bottomsheet.dart';
// import '../widgets/time_slot_widget.dart';
// import '../widgets/turf_size_sport_widget.dart';
// import '../widgets/user_detail_bottomsheet.dart';

// class VenueAvailabilityScreen extends StatefulWidget {
//   final UserModal user;
//   Venue? selectedTurf;
//   final String bookingType;
//   final bool updateTime;
//   final BookingModel? booking;
//   // final SportType? selectedSport;
//   // final String selectedTurfSize;
//   VenueAvailabilityScreen({
//     Key? key,
//     required this.user,
//     this.selectedTurf,
//     required this.bookingType,
//     this.updateTime = false,
//     this.booking,
//     // this.selectedSport,
//     // this.selectedTurfSize = '',
//   }) : super(key: key);

//   @override
//   _VenueAvailabilityScreenState createState() =>
//       _VenueAvailabilityScreenState();
// }

// class _VenueAvailabilityScreenState extends State<VenueAvailabilityScreen> {
//   bool isLoading = true;
//   bool isSelecting = false;
//   bool fetchVenue = false;
//   bool updateTime = false;
//   bool setTurfSizeFirstTime = true;
//   // DatePickerController _controller = DatePickerController();
//   DateTime selectedDate = DateTime.now();

//   List timeSlot = [];
//   List bookedSlot = [];
//   List duplicateBookedSlot = [];
//   List sortedTimeList = [];
//   List selectedTime = [];

//   Map pricing = {};

//   double dH = 0;
//   double dW = 0;
//   double tS = 0;

//   List sizeOrSportList = [];
//   String selectedSizeOrSport = '';
//   String selectedCourtType = '';

//   var fetchedBookedTimeSlots;

//   SportType? selectedSport;
//   int totalPlayersAllowed = 0;

//   late TimeOfDay startTime;
//   late TimeOfDay endTime;

//   late Timer timer;

//   String selectedTurfSize = '';

//   List listOfDates = [];
//   final _itemController = ItemScrollController();

//   Future scrollToItem(int index) async {
//     _itemController.scrollTo(
//       index: index,
//       alignment: .2,
//       duration: const Duration(milliseconds: 200),
//     );
//   }

//   Iterable<TimeOfDay> getTimes(
//       TimeOfDay startTime, TimeOfDay endTime, Duration step) sync* {
//     var hour = startTime.hour;
//     var minute = startTime.minute;

//     do {
//       yield TimeOfDay(hour: hour, minute: minute);
//       minute += step.inMinutes;
//       while (minute >= 60) {
//         minute -= 60;
//         hour++;
//       }
//     } while (hour < endTime.hour ||
//         (hour == endTime.hour && minute <= endTime.minute));
//   }

//   fetchBookedTimeSlot({required bool isNet}) async {
//     fetchedBookedTimeSlots =
//         await Provider.of<HomeProvider>(context, listen: false)
//             .fetchTurfBookingSlot(
//       turfId: widget.selectedTurf!.id,
//       startDate: DateTime(
//               selectedDate.year, selectedDate.month, selectedDate.day, 0, 0, 0)
//           .toString(),
//       endDate: DateTime(selectedDate.year, selectedDate.month, selectedDate.day,
//               23, 59, 59)
//           .toString(),
//       sizeOrSport: selectedSizeOrSport,
//       isNet: isNet,
//       updateBooking: widget.booking != null ? true : false,
//       bookingId: widget.booking != null ? widget.booking!.id : '',
//     );

//     // if (isNet) {
//     final blockedSlots = await Provider.of<HomeProvider>(context, listen: false)
//         .fetchBlockedSlot(
//       turfId: widget.selectedTurf!.id,
//       startDate: DateTime(
//               selectedDate.year, selectedDate.month, selectedDate.day, 0, 0, 0)
//           .toString(),
//       endDate: DateTime(selectedDate.year, selectedDate.month, selectedDate.day,
//               23, 59, 59)
//           .toString(),
//       sizeOrSport: selectedSizeOrSport,
//       isNet: isNet,
//       sport: widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
//           ? selectedSport == null
//               ? ''
//               : selectedSport!.sport
//           : selectedSizeOrSport,
//     );

//     blockedSlots.forEach((slot) {
//       duplicateBookedSlot.add(
//           double.parse(slot['startTime'].toString().split('.')[0]) < 10
//               ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//               : slot['startTime'].toStringAsFixed(2).replaceAll('.', ':'));
//       duplicateBookedSlot.add(
//           double.parse(slot['endTime'].toString().split('.')[0]) < 10
//               ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//               : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
//     });
//     // }
//   }

//   getStartEndTime(startTime, endTime) {
//     return getTimes(
//             TimeOfDay(
//                 hour: int.parse(
//                     startTime.toDouble().toStringAsFixed(2).split('.')[0]),
//                 minute: int.parse(
//                     startTime.toDouble().toStringAsFixed(2).split('.')[1])),
//             TimeOfDay(
//                 hour: int.parse(
//                     endTime.toDouble().toStringAsFixed(2).split('.')[0]),
//                 minute: int.parse(
//                     endTime.toDouble().toStringAsFixed(2).split('.')[1])),
//             Duration(minutes: 30))
//         .map((tod) => tod.format(context))
//         .toList();
//   }

//   setTimeList({String sport = '', bool isLoad = true}) async {
//     try {
//       setState(() {
//         isLoading = isLoad;
//       });

//       if (widget.selectedTurf == null) {
//         setState(() {
//           fetchVenue = true;
//         });

//         final result = await Provider.of<TurfProvider>(context, listen: false)
//             .fetchTurfById(
//           accessToken: widget.user.accessToken,
//           turfId: widget.booking!.turfId,
//         );

//         if (result != null) {
//           widget.selectedTurf = result;
//           // generateDateList();
//           selectedDate = widget.booking!.bookingDate!;
//           listOfDates = [
//             {'index': 0, 'date': selectedDate}
//           ];
//           int sportIndex = widget.selectedTurf!.sportsType!
//               .indexWhere((sport) => sport.sport == widget.booking!.sportType);
//           if (sportIndex != -1) {
//             selectedSport = widget.selectedTurf!.sportsType![sportIndex];
//           }
//           setState(() {});
//         } else {
//           showSnackbar('Something went wrong');
//           pop();
//         }
//       }

//       List duplicateTimeSlot = [];
//       duplicateBookedSlot = [];
//       List duplicateSizeOrSportList = [];
//       Map duplicatePricing = {};

//       if (isLoad) {
//         sortedTimeList = [];
//         selectedTime = [];
//       }

//       // String insertingSport =
//       //     widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
//       //         ? selectedSport.sport
//       //         : selectedSizeOrSport;
//       final step = Duration(minutes: widget.selectedTurf!.slotTimeDifference!);

//       bool isNet = widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor'
//           ? false
//           : widget.selectedTurf!.isNet!;

//       List<Slot> listOfSlot = getSportSlots();

//       for (var slot in listOfSlot) {
//         for (var priceQty in slot.priceAndQuantity) {
//           int index = duplicateSizeOrSportList.indexWhere(
//             (data) => data['title'] == priceQty.title,
//           );
//           if (index == -1) {
//             duplicateSizeOrSportList.add({
//               'title': priceQty.title,
//               'label': priceQty.label,
//               'isSelected': false,
//             });
//           }
//         }
//       }

//       sizeOrSportList = List.from(duplicateSizeOrSportList);

//       if (setTurfSizeFirstTime && widget.booking != null) {
//         sport = widget.booking!.sizeOrSport;
//         setTurfSizeFirstTime = false;
//       }

//       if (sport == '') {
//         // duplicateSizeOrSportList[0]['isSelected'] = true;
//         // selectedSizeOrSport = duplicateSizeOrSportList[0]['title'];
//       } else {
//         selectSizeOrSport(sport);
//       }
//       await fetchBookedTimeSlot(isNet: isNet);

//       List<String> extraTime = [];

//       Slot startSlot = listOfSlot.isEmpty
//           ? Slot(
//               id: '',
//               session: 'Morning',
//               startTime: 5,
//               endTime: 12,
//               priceAndQuantity: [],
//             )
//           : listOfSlot[0];
//       Slot endSlot = listOfSlot.isEmpty
//           ? Slot(
//               id: '',
//               session: 'Night',
//               startTime: 12,
//               endTime: 24,
//               priceAndQuantity: [],
//             )
//           : listOfSlot[listOfSlot.length - 1];

//       startTime = TimeOfDay(
//         hour: int.parse(
//           startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
//         ),
//         minute: int.parse(
//           startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
//         ),
//       );

//       if (endSlot.endTime >= 0 && endSlot.endTime <= 5) {
//         endTime = TimeOfDay(
//           hour: 23,
//           minute: 30,
//         );
//         var startTime1 = TimeOfDay(hour: 0, minute: 0);
//         var endTime1 = TimeOfDay(
//           hour: int.parse(
//             endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
//           ),
//           minute: int.parse(
//             endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
//           ),
//         );
//         extraTime = getTimes(
//           startTime1,
//           endTime1,
//           Duration(minutes: 30),
//         ).map((tod) => tod.format(context)).toList();
//       } else {
//         endTime = TimeOfDay(
//           hour: int.parse(
//             endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
//           ),
//           minute: int.parse(
//             endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
//           ),
//         );
//       }
//       var timeDifference = [];
//       timeDifference = getTimes(startTime, endTime, step)
//           .map((tod) => tod.format(context))
//           .toList();
//       int index = timeDifference.indexWhere((time) => time == '24:00');
//       if (index != -1) {
//         timeDifference[index] = '00:00';
//       }
//       // print('TimeDifference: $timeDifference');
//       List difference = [];
//       timeDifference.addAll(extraTime);

//       for (var time in timeDifference) {
//         if (time.contains('AM')) {
//           difference.add(time.toString().replaceAll('AM', '').trim());
//         } else if (time.contains('PM')) {
//           difference.add(time.toString().replaceAll('PM', '').trim());
//         } else {
//           difference.add(time.toString());
//         }
//       }
//       timeDifference = List.from(difference);
//       for (var i = 0; i < timeDifference.length - 1; i++) {
//         duplicateTimeSlot.add([
//           {
//             'time': timeDifference[i],
//             'isSelected': false,
//           },
//           {
//             'time': timeDifference[i + 1],
//             'isSelected': false,
//           },
//         ]);
//       }
//       if (selectedSizeOrSport != '') {
//         fetchedBookedTimeSlots.forEach((slot) async {
//           List timeList = [];
//           if (slot['endTime'] >= 0 && slot['endTime'] <= 5) {
//             if (isNet) {
//               if (slot['quantities'] != null &&
//                   slot['quantities'][selectedSizeOrSport] <= 0) {
//                 timeList.add(double.parse(
//                             slot['startTime'].toString().split('.')[0]) <
//                         10
//                     ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//                     : slot['startTime']
//                         .toStringAsFixed(2)
//                         .replaceAll('.', ':'));
//                 timeList.add(double.parse(
//                             slot['endTime'].toString().split('.')[0]) <
//                         10
//                     ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//                     : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
//               }
//             } else {
//               if (slot['sizeOrSport'] == selectedSizeOrSport &&
//                   slot['remainingCount'] <= 0) {
//                 timeList.add(double.parse(
//                             slot['startTime'].toString().split('.')[0]) <
//                         10
//                     ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//                     : slot['startTime']
//                         .toStringAsFixed(2)
//                         .replaceAll('.', ':'));
//                 timeList.add(double.parse(
//                             slot['endTime'].toString().split('.')[0]) <
//                         10
//                     ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
//                     : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
//               }
//             }
//           } else {
//             if (isNet) {
//               if (slot['quantities'] != null &&
//                   slot['quantities'][selectedSizeOrSport] <= 0) {
//                 timeList = getStartEndTime(
//                   slot['startTime'],
//                   slot['endTime'],
//                 );
//               }
//             } else {
//               if (slot['sizeOrSport'] == selectedSizeOrSport &&
//                   slot['remainingCount'] <= 0) {
//                 timeList = getStartEndTime(
//                   slot['startTime'],
//                   slot['endTime'],
//                 );
//               }
//             }
//           }

//           duplicateBookedSlot.addAll(timeList);
//         });
//       }
//       //BLOCKING TODAYS TIME WHICH HAS PASSED
//       if (DateTime.now().isAfter(selectedDate)) {
//         var blocked = getTimes(
//           TimeOfDay(
//             hour: int.parse(
//               startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
//             ),
//             minute: int.parse(
//               startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
//             ),
//           ),
//           TimeOfDay(
//             hour: DateTime.now().hour,
//             minute: DateTime.now().minute,
//           ),
//           step,
//         ).map((tod) => tod.format(context)).toList();
//         List blocked1 = [];
//         blocked.forEach((time) {
//           if (time.contains('AM')) {
//             blocked1.add(time.toString().replaceAll('AM', '').trim());
//           } else if (time.contains('PM')) {
//             blocked1.add(time.toString().replaceAll('PM', '').trim());
//           } else {
//             blocked1.add(time.toString());
//           }
//         });
//         if (DateTime.now().minute >= 0 && DateTime.now().minute < 30) {
//           blocked1.add("${DateTime.now().hour}:00");
//           blocked1.add("${DateTime.now().hour}:30");
//         } else if (DateTime.now().minute >= 30 && DateTime.now().minute <= 60) {
//           blocked1.add("${DateTime.now().hour}:30");
//           blocked1.add("${DateTime.now().hour + 1}:00");
//         }
//         duplicateBookedSlot.addAll(blocked1);
//       }

//       // if (widget.selectedTurf!.pauseDates!
//       //         .contains(DateFormat('yyyy-MM-dd hh: mm').format(selectedDate)) ||
//       //     !widget.selectedTurf!.days!
//       //         .contains(getWeekDays(selectedDate.weekday))) {
//       //   var blocked = blockWholeDay(step);
//       //   duplicateBookedSlot.addAll(blocked);
//       //   }
//       if (!widget.selectedTurf!.days!
//               .contains(getWeekDays(selectedDate.weekday)) &&
//           !widget.selectedTurf!.weekends!
//               .contains(getWeekDays(selectedDate.weekday))) {
//         var blocked = blockWholeDay(step);
//         duplicateBookedSlot.addAll(blocked);
//       } else {
//         widget.selectedTurf!.pauseDates!.forEach((date) {
//           if (date['start']
//               .contains(DateFormat('yyyy-MM-dd').format(selectedDate))) {
//             var endPauseDate = DateTime.parse(date['end']);
//             var startPauseDate = DateTime.parse(date['start']);
//             var blocked = getTimes(
//               TimeOfDay(
//                 hour: startPauseDate.hour,
//                 minute: startPauseDate.minute,
//               ),
//               TimeOfDay(
//                 hour: endPauseDate.hour == 0 ? 24 : endPauseDate.hour,
//                 minute: endPauseDate.minute,
//               ),
//               step,
//             ).map((tod) => tod.format(context)).toList();
//             duplicateBookedSlot.addAll(blocked);
//           }
//         });
//       }

//       for (var slot in listOfSlot) {
//         for (var priceQty in slot.priceAndQuantity) {
//           if (selectedSizeOrSport == priceQty.title) {
//             if (widget.selectedTurf!.weekends!
//                 .contains(getWeekDays(selectedDate.weekday))) {
//               if (priceQty.weekendPrice == 0) {
//                 if (slot.endTime >= 0 && slot.endTime <= 5) {
//                   var blocked1 = getStartEndTime(slot.startTime, 23.30);
//                   var blocked2 = getStartEndTime(00, slot.endTime);
//                   duplicateBookedSlot.addAll(blocked1);
//                   duplicateBookedSlot.addAll(blocked2);
//                 } else {
//                   var blocked1 = getStartEndTime(slot.startTime,
//                       slot.session == 'Night' ? 24 : slot.endTime);
//                   duplicateBookedSlot.addAll(blocked1);
//                 }
//               }
//             } else {
//               if (priceQty.price == 0) {
//                 if (slot.endTime >= 0 && slot.endTime <= 5) {
//                   var blocked1 = getStartEndTime(slot.startTime, 23.30);
//                   var blocked2 = getStartEndTime(00, slot.endTime);
//                   duplicateBookedSlot.addAll(blocked1);
//                   duplicateBookedSlot.addAll(blocked2);
//                 } else {
//                   var blocked1 = getStartEndTime(slot.startTime,
//                       slot.session == 'Night' ? 24 : slot.endTime);
//                   duplicateBookedSlot.addAll(blocked1);
//                 }
//               }
//             }
//           }
//         }
//       }

//       for (var data in defaultSlots) {
//         int index =
//             listOfSlot.indexWhere((slot) => slot.session == data['title']);
//         if (index == -1) {
//           if (data['endTime'] >= 0 && data['endTime'] <= 5) {
//             var blocked1 = getStartEndTime(data['startTime'], 23.30);
//             var blocked2 = getStartEndTime(00, data['endTime']);
//             duplicateBookedSlot.addAll(blocked1);
//             duplicateBookedSlot.addAll(blocked2);
//           } else {
//             var blocked1 = getStartEndTime(data['startTime'],
//                 data['title'] == 'Night' ? 24 : data['endTime']);
//             duplicateBookedSlot.addAll(blocked1);
//           }
//         }
//       }
//       if (duplicateBookedSlot.contains('24:00')) {
//         duplicateBookedSlot.add('00:00');
//       }

//       //FIND PRICE FOR EACH SLOT
//       for (var slot in listOfSlot) {
//         List list = [];
//         if (slot.endTime > 0 && slot.endTime <= 5) {
//           TimeOfDay start1 = TimeOfDay(
//             hour: int.parse(
//               slot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
//             ),
//             minute: int.parse(
//               slot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
//             ),
//           );
//           TimeOfDay end1 = const TimeOfDay(hour: 23, minute: 30);
//           TimeOfDay start2 = const TimeOfDay(hour: 00, minute: 00);

//           TimeOfDay end2 = TimeOfDay(
//             hour: int.parse(
//               slot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
//             ),
//             minute: int.parse(
//               slot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
//             ),
//           );
//           list.addAll(getTimes(start1, end1, const Duration(minutes: 30))
//               .map((tod) => tod.format(context))
//               .toList());
//           list.addAll(getTimes(start2, end2, const Duration(minutes: 30))
//               .map((tod) => tod.format(context))
//               .toList());
//         } else {
//           TimeOfDay start = TimeOfDay(
//             hour: int.parse(
//               slot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
//             ),
//             minute: int.parse(
//               slot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
//             ),
//           );
//           TimeOfDay end = TimeOfDay(
//             hour: int.parse(
//               slot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
//             ),
//             minute: int.parse(
//               slot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
//             ),
//           );
//           list = getTimes(start, end, const Duration(minutes: 30))
//               .map((tod) => tod.format(context))
//               .toList();
//         }
//         Map data = {};
//         for (var price in slot.priceAndQuantity) {
//           if (!data.containsKey(price.title)) {
//             data[price.title] = {
//               'weekend': price.weekendPrice,
//               'weekday': price.price,
//             };
//           }
//         }
//         for (var t in list) {
//           duplicatePricing[t] = data;
//         }
//       }

//       if (isLoad) {
//         timeSlot = List.from(duplicateTimeSlot);
//       }

//       if (duplicatePricing.containsKey('24:00')) {
//         duplicatePricing['00:00'] = duplicatePricing['24:00'];
//       }

//       bookedSlot = List.from(duplicateBookedSlot);
//       pricing = duplicatePricing;

//       print('Selected1 $selectedTime');
//       List duplicatedSelectedTime = List.from(selectedTime);

//       timeSlot.forEach((data) {
//         if (bookedSlot.contains(data[0]['time']) &&
//             bookedSlot.contains(data[1]['time'])) {
//           data[0]['isSelected'] = false;
//           data[1]['isSelected'] = false;
//           duplicatedSelectedTime
//               .remove('${data[0]['time']}-${data[1]['time']}');
//           sortedTimeList.remove(
//               double.parse((data[0]['time'].toString().replaceAll(':', '.'))));
//           sortedTimeList.remove(
//               double.parse((data[1]['time'].toString().replaceAll(':', '.'))));
//         }
//       });

//       selectedTime = List.from(duplicatedSelectedTime);
//       print('Selected2 $selectedTime');
//       print('Sorted $sortedTimeList');

//       setState(() {
//         isLoading = false;
//         fetchVenue = false;
//       });
//     } catch (e) {
//       print(e);
//       setState(() {
//         isLoading = false;
//       });
//     }
//   }

//   List<Slot> getSportSlots() {
//     Venue venue = widget.selectedTurf!;
//     if (venue.sportCategory!.categoryName == 'Outdoor') {
//       if (venue.cricket != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.cricket!.sport) {
//         return venue.cricket!.slots;
//       } else if (venue.football != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.football!.sport) {
//         return venue.football!.slots;
//       } else {
//         return venue.slots ?? [];
//       }
//     } else if (venue.sportCategory!.categoryName == 'Indoor') {
//       if (venue.badminton != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.badminton!.sport) {
//         return venue.badminton!.slots;
//       } else if (venue.snooker != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.snooker!.sport) {
//         return venue.snooker!.slots;
//       } else if (venue.tableTennis != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.tableTennis!.sport) {
//         return venue.tableTennis!.slots;
//       } else if (venue.tennis != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.tennis!.sport) {
//         return venue.tennis!.slots;
//       } else {
//         return venue.slots ?? [];
//       }
//     } else if (venue.sportCategory!.categoryName == 'Esport') {
//       if (venue.ps3 != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.ps3!.sport) {
//         return venue.ps3!.slots;
//       } else if (venue.ps4 != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.ps4!.sport) {
//         return venue.ps4!.slots;
//       } else if (venue.ps5 != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.ps5!.sport) {
//         return venue.ps5!.slots;
//       } else if (venue.xbox1 != null &&
//           selectedSport != null &&
//           selectedSport!.id == venue.xbox1!.sport) {
//         return venue.xbox1!.slots;
//       } else {
//         return venue.slots ?? [];
//       }
//     } else {
//       return venue.slots ?? [];
//     }
//   }

//   // checkSlotAvailability(time) {
//   //   if (bookedSlot.contains(time)) {
//   //     return true;
//   //   } else {
//   //     return false;
//   //   }
//   // }

//   List<String> generateTimeList(double startTime, double endTime) {
//     List<String> timeList = [];

//     // Add the start time to the list
//     timeList.add(startTime.toStringAsFixed(1));

//     // If start and end times are different, add the intermediate times
//     endTime == 0.0 ? endTime = 24.0 : endTime = endTime;
//     while (startTime < endTime) {
//       if (startTime.toString().split('.')[1] == "0") {
//         double nextTime = startTime + 0.3;
//         timeList.add(nextTime.toStringAsFixed(1));
//         startTime = nextTime;
//       } else {
//         double nextTime = startTime + 0.7;
//         timeList.add(nextTime.toStringAsFixed(1));
//         startTime = nextTime;
//       }
//     }

//     if (timeList.contains("24.0")) {
//       int index = timeList.indexOf("24.0");
//       timeList[index] = "0.0";
//     }
//     return timeList;
//   }

//   checkSlotAvailability(String time) {
//     if (widget.booking != null) {
//       String startTime = widget.booking!.bookingSlotStartTime.toString();
//       String endTime = widget.booking!.bookingSlotEndTime.toString();

//       // Convert the provided time to decimal format
//       String providedTime = convertToDecimal(time);
//       List<String> timeList =
//           generateTimeList(double.parse(startTime), double.parse(endTime));
//       if (timeList.contains(providedTime) || bookedSlot.contains(time)) {
//         return true;
//       } else {
//         return false;
//       }
//     }
//     if (bookedSlot.contains(time)) {
//       return true;
//     } else {
//       return false;
//     }
//   }

//   String convertToDecimal(String time) {
//     // Split the time string into hours and minutes
//     List<String> parts = time.split(':');
//     int hours = int.parse(parts[0]);
//     int minutes = int.parse(parts[1]);

//     // Convert the time to decimal format
//     double decimalTime = hours + (minutes / 100.0);

//     // Return the decimal format as a string
//     return decimalTime.toStringAsFixed(1);
//   }

//   selectSizeOrSport(String title) {
//     sizeOrSportList.forEach((size) {
//       if (size['title'] == title) {
//         size['isSelected'] = true;
//         selectedSizeOrSport = size['title'];
//       } else {
//         size['isSelected'] = false;
//       }
//     });
//     if (widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor') {
//       widget.selectedTurf!.sportsType!.forEach((data) {
//         if (data.sport == title) {
//           selectedSport = data;
//         }
//       });
//     }
//     setState(() {});
//   }

//   blockWholeDay(step) {
//     return getTimes(
//       TimeOfDay(
//         hour: 00,
//         minute: 00,
//       ),
//       TimeOfDay(
//         hour: 24,
//         minute: 00,
//       ),
//       step,
//     ).map((tod) => tod.format(context)).toList();
//   }

//   loopTimeSlot(slot, status) {
//     for (var i = 0; i < timeSlot.length; i++) {
//       if (timeSlot[i][0]['time'] == slot[0]['time']) {
//         setState(() {
//           timeSlot[i][0]['isSelected'] = status;
//           timeSlot[i][1]['isSelected'] = status;
//           if (status) {
//             selectedTime
//                 .add('${timeSlot[i][0]['time']}-${timeSlot[i][1]['time']}');
//             // selectedTime
//             //     .addAll([timeSlot[i][0]['time'], timeSlot[i][1]['time']]);
//           } else {
//             selectedTime
//                 .remove('${timeSlot[i][0]['time']}-${timeSlot[i][1]['time']}');
//             // selectedTime.remove(timeSlot[i][0]['time']);
//             // selectedTime.remove(timeSlot[i][1]['time']);
//           }
//         });
//       }
//     }
//   }

//   getValue(value) {
//     if (value == '00:00' || value == 0.0) {
//       return 24.00;
//     } else if (value == '00:30' || value == 0.3) {
//       return 24.30;
//     } else if (value == '01:00' || value == 1.0) {
//       return 25.00;
//     } else if (value == '01:30' || value == 1.3) {
//       return 25.30;
//     } else if (value == '02:00' || value == 2.0) {
//       return 26.00;
//     } else if (value == '02:30' || value == 2.3) {
//       return 26.30;
//     } else if (value == '03:00' || value == 3.0) {
//       return 27.00;
//     } else if (value == '03:30' || value == 3.3) {
//       return 27.30;
//     } else if (value == '04:00' || value == 4.0) {
//       return 28.00;
//     } else if (value == '04:30' || value == 4.3) {
//       return 28.30;
//     } else if (value == '05:00' || value == 5.0) {
//       return 29.00;
//     } else if (value == "24" || value == "24.0" || value == "24.00") {
//       return 0.0;
//     } else if (value == "24.3" || value == "24.30") {
//       return 0.3;
//     } else if (value == "25" || value == "25.0" || value == "25.00") {
//       return 1.0;
//     } else if (value == "25.3" || value == "25.30") {
//       return 1.3;
//     } else if (value == "26" || value == "26.0" || value == "26.00") {
//       return 2;
//     } else if (value == "26.3" || value == "26.30") {
//       return 2.3;
//     } else if (value == "27" || value == "27.0" || value == "27.00") {
//       return 3;
//     } else if (value == "27.3" || value == "27.30") {
//       return 3.3;
//     } else if (value == "28" || value == "28.0" || value == "28.00") {
//       return 4.0;
//     } else if (value == "28.3" || value == "28.30") {
//       return 4.3;
//     } else if (value == "29" || value == "29.0" || value == "29.00") {
//       return 5.0;
//     } else {
//       return 24.0;
//     }
//   }

//   getText() {
//     if (selectedSport == null) {
//       return 'Please select sport';
//     } else if (selectedSizeOrSport == '') {
//       return widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
//           ? 'Please select turf size'
//           : 'Please select sport';
//     }
//   }

//   selectAndUnselectTimeSlot(slot, add) {
//     if (selectedSport == null || selectedSizeOrSport == '') {
//       return showSnackbar(getText());
//     }

//     if (isSelecting) return;

//     setState(() => isSelecting = true);

//     if (add) {
//       if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//           double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//         sortedTimeList.addAll([
//           getValue(slot[0]['time']),
//           getValue(slot[1]['time'] == '00:00' ? '12:00' : slot[1]['time'])
//         ]);
//       } else {
//         sortedTimeList.addAll([
//           double.parse(slot[0]['time'].replaceAll(":", ".")),
//           double.parse(slot[1]['time'].replaceAll(":", "."))
//         ]);
//       }
//       sortedTimeList.sort();

//       var abc = [];
//       for (var i = 1; i < sortedTimeList.length; i += 2) {
//         abc.add(sortedTimeList[i]);
//       }
//       print(abc);
//       var showAlert = false;
//       for (var i = 0; i < abc.length; i++) {
//         if (i + 1 < abc.length) {
//           if (widget.selectedTurf!.slotTimeDifference == 30 &&
//               double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                       .abs() >
//                   0.7) {
//             print(double.parse(
//                     (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                 .abs());
//             showAlert = true;
//           } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
//               double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                       .abs() >
//                   1) {
//             print(double.parse(
//                     (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                 .abs());
//             showAlert = true;
//           }
//         }
//       }
//       if (!showAlert) {
//         loopTimeSlot(slot, true);
//       } else {
//         if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//             double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//           sortedTimeList.remove(getValue(slot[0]['time']));
//           sortedTimeList.remove(getValue(slot[1]['time']));
//         } else {
//           sortedTimeList
//               .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
//           sortedTimeList
//               .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
//         }

//         // showSnackbar('You can only add consecutive timing.');
//       }
//       // }
//     } else {
//       if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//           double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//         sortedTimeList.remove(getValue(slot[0]['time']));
//         sortedTimeList.remove(getValue(slot[1]['time']));
//       } else {
//         sortedTimeList
//             .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
//         sortedTimeList
//             .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
//       }
//       var abc = [];
//       for (var i = 1; i < sortedTimeList.length; i += 2) {
//         abc.add(sortedTimeList[i]);
//       }
//       var showAlert = false;
//       for (var i = 0; i < abc.length; i++) {
//         if (i + 1 < abc.length) {
//           if (widget.selectedTurf!.slotTimeDifference == 30 &&
//               double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                       .abs() >
//                   0.7) {
//             showAlert = true;
//           } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
//               double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
//                       .abs() >
//                   1) {
//             showAlert = true;
//           }
//         }
//       }
//       if (!showAlert) {
//         loopTimeSlot(slot, false);
//       } else {
//         if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//             double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//           sortedTimeList
//               .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
//         } else {
//           sortedTimeList.addAll([
//             double.parse(slot[0]['time'].replaceAll(":", ".")),
//             double.parse(slot[1]['time'].replaceAll(":", "."))
//           ]);
//         }
//         sortedTimeList.sort();
//         // showSnackbar('You can only add consecutive timing.');
//       }
//     }

//     setState(() => isSelecting = false);
//   }

//   selectDate() {
//     DateTime date = DateTime.now();

//     List days = [];

//     widget.selectedTurf!.days!.forEach((day) {
//       days.add(getWeekNumber(day));
//     });

//     for (var i = 0; i < 7; i++) {
//       if (!days.contains(date.weekday)) {
//         date = date.add(Duration(days: 1));
//       } else {
//         break;
//       }
//     }

//     showDatePicker(
//       context: context,
//       initialDate: date,
//       firstDate: date,
//       lastDate: DateTime(2200),
//       selectableDayPredicate: (DateTime val) =>
//           days.contains(val.weekday) ? true : false,
//     ).then((date) {
//       if (date != null) {
//         if (widget.selectedTurf!.pauseDates!
//             .contains(DateFormat('yyyy-MM-dd').format(date))) {
//           showSnackbar('Booking are not available for this date');
//           return;
//         } else {
//           setState(() {
//             selectedDate = date;
//           });
//         }
//       }
//     });
//   }

//   goToSummaryScreen({
//     required String fullName,
//     required String mobileNo,
//   }) {
//     // if (selectedTime.length < 2) {

//     if (widget.selectedTurf!.slotTimeDifference == 60 && selectedTime.isEmpty) {
//       showSnackbar('Minimum booking should be of atleast 1 hour');
//       return;
//     } else if (widget.selectedTurf!.slotTimeDifference == 30 &&
//         selectedTime.isEmpty) {
//       showSnackbar('Minimum booking should be of atleast 30 mins');
//       return;
//     } else {
//       late SportType sport;

//       if (widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor') {
//         for (var data in widget.selectedTurf!.sportsType!) {
//           if (data.sport == selectedSport!.sport) {
//             sport = data;
//             break;
//           }
//         }
//       } else {
//         sport = selectedSport!;
//       }

//       PricingAndQuantity? priceAndQuantity;

//       List<Slot> listOfSlots = getSportSlots();

//       for (var i = 0; i < listOfSlots.length; i++) {
//         Slot slot = listOfSlots[i];
//         for (var j = 0; j < slot.priceAndQuantity.length; j++) {
//           if (slot.priceAndQuantity[j].title == selectedSizeOrSport) {
//             priceAndQuantity = slot.priceAndQuantity[j];
//             break;
//           }
//         }
//         if (priceAndQuantity != null) {
//           break;
//         }
//       }

//       List time = [];

//       List dummySelectedTime = [];

//       selectedTime.forEach((time) {
//         dummySelectedTime.addAll(time.toString().split('-'));
//       });
//       print('Selected Time Length:${selectedTime.length}');

//       dummySelectedTime.forEach((data) {
//         if (double.parse(data.replaceAll(":", ".")) == 5 &&
//             !selectedTime.contains('04:30-05:00')) {
//           time.add(double.parse(data.replaceAll(":", ".")));
//         } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
//             selectedTime.contains('04:30-05:00')) {
//           time.add(getValue(data));
//         } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
//             double.parse(data.replaceAll(":", ".")) <= 5) {
//           time.add(getValue(data));
//         } else {
//           time.add(double.parse(data.replaceAll(":", ".")));
//         }
//       });

//       time.sort();
//       time.toSet();

//       List dummyTime = [];

//       time.forEach((t) {
//         if (t >= 24 && t <= 29) {
//           dummyTime.add(getValue(t.toString()));
//         } else {
//           dummyTime.add(t);
//         }
//       });
//       time = dummyTime;
//       List duplicateSelectedTime = [];

//       time.forEach((data) {
//         if (data >= 0 && data < 10) {
//           duplicateSelectedTime
//               .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
//         } else {
//           duplicateSelectedTime
//               .add(data.toStringAsFixed(2).replaceAll(".", ":"));
//         }
//       });

//       double amount = 0;

//       for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
//         print(duplicateSelectedTime[i]);
//         if (widget.selectedTurf!.weekends!
//             .contains(getWeekDays(selectedDate.weekday))) {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
//         } else {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
//         }
//       }

//       print('Total Amount: $amount');
//       timer.cancel();
//       if (widget.bookingType == 'Single Booking') {
//         push(
//           BookingSummaryScreen(
//             turf: widget.selectedTurf!,
//             selectedTime: duplicateSelectedTime,
//             bookingDate: DateTime(
//               selectedDate.year,
//               selectedDate.month,
//               selectedDate.day,
//               // DateTime.now().hour,
//               // DateTime.now().minute,
//               // DateTime.now().second,
//               13, 0, 0,
//             ),
//             time:
//                 // '${double.parse(duplicateSelectedTime[0].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')}  - ${double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')}',
//                 '${double.parse(get12HrClockFormat(duplicateSelectedTime[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(duplicateSelectedTime[duplicateSelectedTime.length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].split(':')[0]))}',
//             sport: sport,
//             amount: amount,
//             sizeOrSport: selectedSizeOrSport,
//             name: fullName,
//             phone: mobileNo,
//             quantity: priceAndQuantity!.quantity,
//             label: priceAndQuantity.label,
//             user: widget.user,
//           ),
//         ).then((value) {
//           if (value != null) {
//             Navigator.of(context).pop();
//             setTimeList();
//             timer = Timer.periodic(Duration(seconds: 7), (timer) {
//               setTimeList(isLoad: false, sport: selectedSizeOrSport);
//             });
//           }
//         });
//       } else {
//         push(
//           CreateBulkBookingScreen(
//             user: widget.user,
//             selectedTurf: widget.selectedTurf!,
//             time:
//                 '${double.parse(get12HrClockFormat(duplicateSelectedTime[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(duplicateSelectedTime[duplicateSelectedTime.length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].split(':')[0]))}',
//             hour: '1min',
//             sport: sport,
//             amount: amount,
//             taxes: 100,
//             sizeOrSport: selectedSizeOrSport,
//             selectedTime: duplicateSelectedTime,
//             bookingDate: DateTime(selectedDate.year, selectedDate.month,
//                 selectedDate.day, 13, 0, 0),
//             quantity: priceAndQuantity!.quantity,
//             label: priceAndQuantity.label,
//             size: selectedSizeOrSport,
//             fullName: fullName,
//             mobileNo: mobileNo,
//           ),
//         ).then((value) {
//           if (value) {
//             Navigator.of(context).pop();
//             Navigator.of(context).pop();
//             setTimeList();
//             timer = Timer.periodic(Duration(seconds: 7), (timer) {
//               setTimeList(isLoad: false, sport: selectedSizeOrSport);
//             });
//           }
//         });
//       }
//     }
//   }

//   updateBookingTime() async {
//     try {
//       if (updateTime) return;

//       setState(() => updateTime = true);

//       List time = [];

//       List dummySelectedTime = [];

//       selectedTime.forEach((time) {
//         dummySelectedTime.addAll(time.toString().split('-'));
//       });
//       print('Selected Time Length:${selectedTime.length}');

//       dummySelectedTime.forEach((data) {
//         if (double.parse(data.replaceAll(":", ".")) == 5 &&
//             !selectedTime.contains('04:30-05:00')) {
//           time.add(double.parse(data.replaceAll(":", ".")));
//         } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
//             selectedTime.contains('04:30-05:00')) {
//           time.add(getValue(data));
//         } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
//             double.parse(data.replaceAll(":", ".")) <= 5) {
//           time.add(getValue(data));
//         } else {
//           time.add(double.parse(data.replaceAll(":", ".")));
//         }
//       });

//       time.sort();
//       time.toSet();

//       List dummyTime = [];

//       time.forEach((t) {
//         if (t >= 24 && t <= 29) {
//           dummyTime.add(getValue(t.toString()));
//         } else {
//           dummyTime.add(t);
//         }
//       });
//       time = dummyTime;
//       List duplicateSelectedTime = [];

//       time.forEach((data) {
//         if (data >= 0 && data < 10) {
//           duplicateSelectedTime
//               .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
//         } else {
//           duplicateSelectedTime
//               .add(data.toStringAsFixed(2).replaceAll(".", ":"));
//         }
//       });

//       double amount = 0;

//       for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
//         print(duplicateSelectedTime[i]);
//         if (widget.selectedTurf!.weekends!
//             .contains(getWeekDays(selectedDate.weekday))) {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
//         } else {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
//         }
//       }

//       if (amount < widget.booking!.totalAmount) {
//         return showSnackbar('Amount cannot be less than booking amount');
//       }

//       final result = await Provider.of<HomeProvider>(context, listen: false)
//           .updateBookingTime(
//         accessToken: widget.user.accessToken,
//         bookingId: widget.booking!.id,
//         newStartTime: duplicateSelectedTime[0].toString().replaceAll(':', '.'),
//         newEndTime: duplicateSelectedTime[duplicateSelectedTime.length - 1]
//             .toString()
//             .replaceAll(':', '.'),
//         amount: amount,
//       );

//       if (result != null) {
//         showSnackbar('Booking Time updated successfully', color: greenPrimary);
//         Navigator.of(context).pop(result);
//       } else {
//         showSnackbar('Unable to update booking time');
//       }
//     } catch (e) {
//       print(e);
//       showSnackbar('Something went wrong');
//     } finally {
//       if (mounted) setState(() => updateTime = false);
//     }
//   }

//   getTotalAmount() {
//     if (selectedTime.isEmpty) {
//       return 0;
//     } else {
//       List time = [];
//       List dummySelectedTime = [];

//       selectedTime.forEach((time) {
//         dummySelectedTime.addAll(time.toString().split('-'));
//       });

//       dummySelectedTime.forEach((data) {
//         time.add(double.parse(data.replaceAll(":", ".")));
//       });

//       List allTime = time;
//       allTime.sort();

//       if (allTime[0] >= 0 && allTime[0] <= 5) {
//         var array24 = [];
//         var array0 = [];
//         time.forEach((value) {
//           if (value >= 20 && value <= 24) {
//             array24.add(value);
//           } else {
//             array0.add(value);
//           }
//         });
//         array24.sort();
//         array0.sort();
//         time = [];
//         time.addAll(array24);
//         time.addAll(array0);
//       } else {
//         time.sort();
//       }
//       // time = time.toSet().toList();
//       List duplicateSelectedTime = [];
//       time.forEach((data) {
//         if (data >= 0 && data < 10) {
//           duplicateSelectedTime
//               .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
//         } else {
//           duplicateSelectedTime
//               .add(data.toStringAsFixed(2).replaceAll(".", ":"));
//         }
//       });

//       double amount = 0;

//       for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
//         // print(pricing[duplicateSelectedTime[i]]);
//         if (widget.selectedTurf!.weekends!
//             .contains(getWeekDays(selectedDate.weekday))) {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
//         } else {
//           amount +=
//               pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
//         }
//       }
//       return amount;
//       // return 0;
//     }
//   }

//   getDuration() {
//     if (widget.selectedTurf!.availability == null) {
//       return 365;
//     } else {
//       String duration = widget.selectedTurf!.availability!.duration;
//       int count = widget.selectedTurf!.availability!.count;
//       if (duration == 'Month') {
//         return 31 * count;
//       } else if (duration == 'Year') {
//         return 365 * count;
//       } else if (duration == 'Days') {
//         return count;
//       } else {
//         return 365;
//       }
//     }
//   }

//   setSelectedTime() {
//     //   if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//     //       double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//     //     sortedTimeList
//     //         .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
//     //   } else {
//     //     sortedTimeList.addAll([
//     //       double.parse(slot[0]['time'].replaceAll(":", ".")),
//     //       double.parse(slot[1]['time'].replaceAll(":", "."))
//     //     ]);
//     //   }
//     //   sortedTimeList.sort();

//     //   var abc = [];
//     //   for (var i = 1; i < sortedTimeList.length; i += 2) {
//     //     abc.add(sortedTimeList[i]);
//     //   }
//     //   print(abc);
//     //   var showAlert = false;
//     //   for (var i = 0; i < abc.length; i++) {
//     //     if (i + 1 < abc.length) {
//     //       if (double.parse(
//     //               (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)) >
//     //           0.7) {
//     //         print(double.parse(
//     //             (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)));
//     //         showAlert = true;
//     //       }
//     //     }
//     //   }
//     //   if (!showAlert) {
//     //     loopTimeSlot(slot, true);
//     //   } else {
//     //     if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
//     //         double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
//     //       sortedTimeList.remove(getValue(slot[0]['time']));
//     //       sortedTimeList.remove(getValue(slot[1]['time']));
//     //     } else {
//     //       sortedTimeList
//     //           .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
//     //       sortedTimeList
//     //           .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
//     //     }
//     // }
//   }

//   double getPriceByTime(String time) {
//     double amount = 0;
//     if (widget.selectedTurf!.weekends!
//         .contains(getWeekDays(selectedDate.weekday))) {
//       amount += pricing[time] == null || selectedSizeOrSport == ''
//           ? 0
//           : pricing[time][selectedSizeOrSport]['weekend'] ?? 0;
//     } else {
//       amount += pricing[time] == null || selectedSizeOrSport == ''
//           ? 0
//           : pricing[time][selectedSizeOrSport]['weekday'] ?? 0;
//     }
//     return amount;
//   }

//   getTimeList() {
//     List time = [];

//     List dummySelectedTime = [];

//     for (var time in selectedTime) {
//       dummySelectedTime.addAll(time.toString().split('-'));
//     }
//     print('Selected Time Length:${selectedTime.length}');

//     for (var data in dummySelectedTime) {
//       if (double.parse(data.replaceAll(":", ".")) == 5 &&
//           !selectedTime.contains('04:30-05:00')) {
//         time.add(double.parse(data.replaceAll(":", ".")));
//       } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
//           selectedTime.contains('04:30-05:00')) {
//         time.add(getValue(data));
//       } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
//           double.parse(data.replaceAll(":", ".")) <= 5) {
//         time.add(getValue(data));
//       } else {
//         time.add(double.parse(data.replaceAll(":", ".")));
//       }
//     }

//     // List allTime = time;
//     time.sort();
//     time.toSet();

//     List dummyTime = [];

//     for (var t in time) {
//       if (t >= 24 && t <= 29) {
//         dummyTime.add(getValue(t.toString()));
//       } else {
//         dummyTime.add(t);
//       }
//     }
//     time = dummyTime;
//     List duplicateSelectedTime = [];

//     for (var data in time) {
//       if (data >= 0 && data < 10) {
//         duplicateSelectedTime
//             .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
//       } else {
//         duplicateSelectedTime.add(data.toStringAsFixed(2).replaceAll(".", ":"));
//       }
//     }

//     return duplicateSelectedTime;
//   }

//   submit() {
//     if (selectedSport == null || selectedSizeOrSport == '') {
//       return showSnackbar(getText());
//     } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
//         selectedTime.isEmpty) {
//       showSnackbar('Minimum booking should be of atleast 1 hour');
//       return;
//     } else if (widget.selectedTurf!.slotTimeDifference == 30 &&
//         selectedTime.isEmpty) {
//       showSnackbar('Minimum booking should be of atleast 30 mins');
//       return;
//     } else {
//       if (widget.booking != null) {
//         updateBookingTime();
//       } else {
//         showModalBottomSheet(
//           enableDrag: true,
//           isScrollControlled: true,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(24),
//               topRight: Radius.circular(24),
//             ),
//           ),
//           context: (context),
//           builder: (context) => GestureDetector(
//             child: UserDetailBottomSheet(
//               goToSummaryScreen: goToSummaryScreen,
//             ),
//             onTap: () {},
//             behavior: HitTestBehavior.opaque,
//           ),
//         );
//       }
//     }
//   }

//   generateDateList() {
//     DateTime today = DateTime.now();
//     listOfDates = [];
//     listOfDates.add(
//         {'index': 0, 'date': DateTime(today.year, today.month, today.day)});
//     for (var i = 1; i <= getDuration(); i++) {
//       listOfDates.add({
//         'index': i,
//         'date':
//             DateTime(today.year, today.month, today.day).add(Duration(days: i)),
//       });
//     }
//   }

//   openSportBottomSheet() {
//     // pop();
//     showModalBottomSheet(
//       isDismissible: false,
//       isScrollControlled: true,
//       constraints: BoxConstraints(
//         maxHeight: widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
//             ? dH * 0.5
//             : dH * 0.7,
//       ),
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(20),
//           topRight: Radius.circular(20),
//         ),
//       ),
//       context: context,
//       builder: (context) => SelectSportAndSizeBottomSheet(
//         venue: widget.selectedTurf!,
//         bookingType: widget.bookingType,
//         selectedSport: selectedSport == null
//             ? widget.selectedTurf!.sportsType!.first
//             : selectedSport!,
//         totalPlayersAllowed: totalPlayersAllowed == 0
//             ? widget.selectedTurf!.slots!.first.priceAndQuantity.first
//                 .totalPlayersAllowed
//             : totalPlayersAllowed,
//         selectedTurfSize: selectedSizeOrSport == ''
//             ? widget.selectedTurf!.slots!.first.priceAndQuantity.first.title
//             : selectedSizeOrSport,
//         selectedCourtType: selectedCourtType == '' ? '' : selectedCourtType,
//       ),
//     ).then((value) {
//       if (value != null &&
//           value.containsKey('selectedSport') &&
//           value.containsKey('selectedTurfSize')) {
//         setState(() {
//           isLoading = true;
//         });
//         selectedSport = value['selectedSport'];
//         selectedSizeOrSport = value['selectedTurfSize'];
//         totalPlayersAllowed = value['totalPlayersAllowed'];
//         if (selectedSport!.sport == 'Badminton') {
//           selectedCourtType = value['selectedCourtType'];
//         }
//         setState(() {
//           isLoading = false;
//         });
//       }
//     });
//   }

//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) async {
//       await openSportBottomSheet();
//     });
//     // openSportBottomSheet();
//     if (selectedSport != null) {
//       selectedSport = selectedSport;
//     }
//     if (selectedTurfSize != '') {
//       selectedSizeOrSport = selectedTurfSize;
//     }
//     if (widget.selectedTurf != null) {
//       generateDateList();
//     }
//     Future.delayed(Duration(seconds: 0)).then((_) {
//       setTimeList();
//       if (widget.booking != null) {
//         setSelectedTime();
//       }
//     });

//     timer = Timer.periodic(Duration(seconds: 5), (timer) {
//       setTimeList(isLoad: false, sport: selectedSizeOrSport);
//     });
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     timer.cancel();
//   }

//   @override
//   Widget build(BuildContext context) {
//     dH = MediaQuery.of(context).size.height;
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;

//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
//     );
//   }

//   screenBody() {
//     return fetchVenue || widget.selectedTurf == null
//         ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
//         : Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               SizedBox(height: dW * 0.05),
//               NewAppBar(
//                 dW: dW,
//                 title: widget.selectedTurf == null
//                     ? ''
//                     : widget.selectedTurf!.name,
//               ),
//               SizedBox(height: dW * 0.04),
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // DatePicker(
//                     //   DateTime.now(),
//                     //   height: dW * 0.2,
//                     //   width: dW * 0.15,
//                     //   initialSelectedDate: DateTime.now(),
//                     //   selectionColor: Theme.of(context).primaryColor,
//                     //   selectedTextColor: Colors.white,
//                     //   dateTextStyle: TextStyle(
//                     //     fontSize: tS * 13,
//                     //     fontWeight: FontWeight.w600,
//                     //   ),
//                     //   dayTextStyle: TextStyle(
//                     //     fontSize: tS * 11.5,
//                     //     fontWeight: FontWeight.w600,
//                     //   ),
//                     //   monthTextStyle: TextStyle(
//                     //     fontSize: tS * 11.5,
//                     //     fontWeight: FontWeight.w600,
//                     //   ),
//                     //   controller: _controller,
//                     //   daysCount: getDuration(),
//                     //   onDateChange: (date) {
//                     //     selectedDate = date;
//                     //     setState(() {});
//                     //     setTimeList();
//                     //   },
//                     // ),
//                     SizedBox(
//                         height: dW * 0.2,
//                         child: ScrollablePositionedList.builder(
//                           itemScrollController: _itemController,
//                           physics: const BouncingScrollPhysics(),
//                           itemCount: listOfDates.length,
//                           scrollDirection: Axis.horizontal,
//                           itemBuilder: (context, i) => GestureDetector(
//                             onTap: () {
//                               selectedDate = listOfDates[i]['date'];
//                               if (listOfDates[i]['index'] != 0) {
//                                 scrollToItem(listOfDates[i]['index']);
//                               }
//                               setState(() {});
//                               setTimeList();
//                             },
//                             child: Container(
//                               height: dW * 0.184,
//                               margin: i == listOfDates.length - 1
//                                   ? EdgeInsets.symmetric(horizontal: dW * .06)
//                                   : EdgeInsets.only(
//                                       left: i == 0 ? dW * .06 : dW * 0.025),
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                 color: isSameDay(
//                                         selectedDate, listOfDates[i]['date'])
//                                     ? getThemeColor()
//                                     : Colors.transparent,
//                                 borderRadius: BorderRadius.circular(8),
//                                 border: Border.all(
//                                   color: isSameDay(
//                                           selectedDate, listOfDates[i]['date'])
//                                       ? getThemeColor()
//                                       : getTextBoxBorderColor(context),
//                                   width: 1,
//                                 ),
//                               ),
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: dW * .033, vertical: dW * .02),
//                               child: Column(
//                                 children: [
//                                   TextWidget(
//                                     title: DateFormat('MMM')
//                                         .format(listOfDates[i]['date']),
//                                     fontSize: 12,
//                                     fontWeight: FontWeight.w500,
//                                     color: isSameDay(selectedDate,
//                                             listOfDates[i]['date'])
//                                         ? Colors.white
//                                         : getUnselectedLabelColor(context),
//                                   ),
//                                   TextWidget(
//                                     title: DateFormat('dd')
//                                         .format(listOfDates[i]['date']),
//                                     fontSize: 12,
//                                     fontWeight: FontWeight.bold,
//                                     color: isSameDay(selectedDate,
//                                             listOfDates[i]['date'])
//                                         ? Colors.white
//                                         : getUnselectedLabelColor(context),
//                                   ),
//                                   TextWidget(
//                                     title: DateFormat('EEE')
//                                         .format(listOfDates[i]['date']),
//                                     fontSize: 12,
//                                     fontWeight: FontWeight.w500,
//                                     color: isSameDay(selectedDate,
//                                             listOfDates[i]['date'])
//                                         ? Colors.white
//                                         : getUnselectedLabelColor(context),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         )),
//                     Padding(
//                       padding: EdgeInsets.symmetric(horizontal: dW * 0.06),
//                       child: Divider(
//                           color: getDividerColor(context), thickness: 1),
//                     ),
//                     if (
//                     // widget.selectedTurf!.sportCategory!.categoryName ==
//                     //       'Outdoor' &&
//                     !isLoading)
//                       Padding(
//                         padding: EdgeInsets.symmetric(
//                           horizontal: dW * 0.04,
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             SizedBox(
//                               height: dW * 0.04,
//                             ),
//                             // Container(
//                             //   margin: EdgeInsets.only(
//                             //     left: dW * 0.03,
//                             //     bottom: dW * 0.01,
//                             //   ),
//                             //   child: Text(
//                             //     'Select Sport:',
//                             //     style: Theme.of(context)
//                             //         .textTheme
//                             //         .displayMedium
//                             //         ?.copyWith(
//                             //           fontSize: tS * 13,
//                             //           fontWeight: FontWeight.w600,
//                             //           letterSpacing: .50,
//                             //           color: Colors.black,
//                             //         ),
//                             //   ),
//                             // ),

//                             GestureDetector(
//                               onTap: () {
//                                 openSportBottomSheet();
//                               },
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Column(
//                                     children: [
//                                       Row(
//                                         children: [
//                                           if (selectedSport != null)
//                                             CachedNetworkImage(
//                                               repeat: ImageRepeat.repeat,
//                                               fit: BoxFit.cover,
//                                               height: 18,
//                                               imageUrl: selectedSport!.image,
//                                               placeholder: (_, __) =>
//                                                   Image.asset(
//                                                 'assets/placeholders/placeholder.png',
//                                                 fit: BoxFit.cover,
//                                                 height: 18,
//                                               ),
//                                             ),
//                                           SizedBox(
//                                             width: dW * 0.02,
//                                           ),
//                                           if (selectedSport != null)
//                                             TextWidget(
//                                               title: selectedSport!.sport,
//                                               color: getBlackColor(context),
//                                               fontWeight: FontWeight.w500,
//                                             ),
//                                           Container(
//                                             margin: EdgeInsets.only(
//                                                 left: dW * 0.02,
//                                                 right: dW * 0.02),
//                                             child: const Icon(
//                                               Icons.circle,
//                                               size: 5,
//                                               color: Color(0xffD9D9D9),
//                                             ),
//                                           ),
//                                           TextWidget(
//                                             title: selectedSizeOrSport,
//                                             color: getBlackColor(context),
//                                             fontWeight: FontWeight.w500,
//                                           ),
//                                         ],
//                                       ),
//                                     ],
//                                   ),
//                                   Column(
//                                     children: [
//                                       Row(
//                                         children: [
//                                           TextWidget(
//                                             title: 'Change',
//                                             color: getThemeColor(),
//                                             fontWeight: FontWeight.w500,
//                                             fontSize: 12,
//                                           ),
//                                           SizedBox(
//                                             width: dW * 0.01,
//                                           ),
//                                           Icon(
//                                             Icons.arrow_forward_ios,
//                                             color: getThemeColor(),
//                                             size: 15,
//                                           )
//                                         ],
//                                       )
//                                     ],
//                                   ),
//                                 ],
//                               ),
//                             ),

//                             // SingleChildScrollView(
//                             //   scrollDirection: Axis.horizontal,
//                             //   physics: const BouncingScrollPhysics(),
//                             //   child: Row(
//                             //     children: [
//                             //       ...widget.selectedTurf!.sportsType!
//                             //           .asMap()
//                             //           .map(
//                             //             (index, sport) => MapEntry(
//                             //               index,
//                             //               GestureDetector(
//                             //                 onTap: () {
//                             //                   selectedSport = sport;
//                             //                   setTimeList();
//                             //                 },
//                             //                 child: SelectSportWidget(
//                             //                   dW: dW,
//                             //                   index: index,
//                             //                   sport: sport,
//                             //                   selectedSport: selectedSport,
//                             //                 ),
//                             //               ),
//                             //             ),
//                             //           )
//                             //           .values
//                             //           .toList(),
//                             //     ],
//                             //   ),
//                             // ),

//                             SizedBox(height: dW * 0.01),
//                             Divider(
//                               color: getDividerColor(context),
//                               thickness: 1,
//                             ),
//                           ],
//                         ),
//                       ),
//                     Expanded(
//                       child: isLoading
//                           ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
//                           : SingleChildScrollView(
//                               physics: const BouncingScrollPhysics(),
//                               padding:
//                                   EdgeInsets.symmetric(horizontal: dW * 0.06),
//                               child: Column(
//                                 children: [
//                                   SizedBox(height: dW * 0.02),
//                                   Row(
//                                     children: [
//                                       SlotStatusIndicator(
//                                         borderColor: getThemeColor(),
//                                         fillColor: getThemeColor(),
//                                         dW: dW,
//                                         text: 'Selected',
//                                       ),
//                                       SizedBox(width: dW * .05),
//                                       SlotStatusIndicator(
//                                         borderColor: const Color(0xFFD9D9D9),
//                                         fillColor: getWhiteColor(context),
//                                         text: 'Available',
//                                         dW: dW,
//                                       ),
//                                       SizedBox(width: dW * .05),
//                                       SlotStatusIndicator(
//                                         borderColor: const Color(0xFFD9D9D9),
//                                         fillColor: const Color(0xFFACACB4)
//                                             .withOpacity(.9),
//                                         text: 'Sold Out',
//                                         dW: dW,
//                                       )
//                                     ],
//                                   ),
//                                   SizedBox(height: dW * 0.03),
//                                   ...timeSlot
//                                       .asMap()
//                                       .map(
//                                         (index, time) => MapEntry(
//                                           index,
//                                           GestureDetector(
//                                             onTap: time[0]['isSelected'] &&
//                                                     time[1]['isSelected']
//                                                 ? () =>
//                                                     selectAndUnselectTimeSlot(
//                                                         time, false)
//                                                 : checkSlotAvailability(
//                                                             time[0]['time']) &&
//                                                         checkSlotAvailability(
//                                                             time[1]['time'])
//                                                     ? null
//                                                     : () =>
//                                                         selectAndUnselectTimeSlot(
//                                                             time, true),
//                                             child: TimeSlotWidget(
//                                               dW: dW,
//                                               time: time,
//                                               checkSlotAvailability:
//                                                   checkSlotAvailability,
//                                               getPriceByTime: getPriceByTime,
//                                             ),
//                                           ),
//                                         ),
//                                       )
//                                       .values
//                                       .toList(),
//                                   SizedBox(height: dW * 0.1),
//                                 ],
//                               ),
//                             ),
//                     ),
//                   ],
//                 ),
//               ),
//               Container(
//                 padding: EdgeInsets.symmetric(vertical: dW * 0.03),
//                 width: dW,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // if (sizeOrSportList.isNotEmpty)
//                     //   SingleChildScrollView(
//                     //     scrollDirection: Axis.horizontal,
//                     //     padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
//                     //     physics: const BouncingScrollPhysics(),
//                     //     child: Column(
//                     //       crossAxisAlignment: CrossAxisAlignment.start,
//                     //       children: [
//                     //         Padding(
//                     //           padding:
//                     //               EdgeInsets.symmetric(horizontal: dW * 0.03),
//                     //           child: TextWidget(
//                     //             title: widget.selectedTurf!.sportCategory!
//                     //                         .categoryName ==
//                     //                     'Outdoor'
//                     //                 ? 'Select Turf Size:'
//                     //                 : 'Select Sport:',
//                     //             fontSize: tS * 13,
//                     //             fontWeight: FontWeight.w600,
//                     //             letterSpacing: .50,
//                     //             color: Colors.black,
//                     //           ),
//                     //         ),
//                     //         SizedBox(height: dW * 0.02),
//                     //         Row(
//                     //           children: [
//                     //             ...sizeOrSportList
//                     //                 .asMap()
//                     //                 .map(
//                     //                   (index, size) => MapEntry(
//                     //                     index,
//                     //                     GestureDetector(
//                     //                       onTap: () {
//                     //                         setTimeList(
//                     //                           sport: size['title'],
//                     //                           isLoad: true,
//                     //                         );
//                     //                       },
//                     //                       child: TurfSizeOrSportWidget(
//                     //                         dW: dW,
//                     //                         tS: tS,
//                     //                         size: size,
//                     //                         index: index,
//                     //                         selectedSizeOrSport:
//                     //                             selectedSizeOrSport,
//                     //                       ),
//                     //                     ),
//                     //                   ),
//                     //                 )
//                     //                 .values
//                     //                 .toList(),
//                     //           ],
//                     //         ),
//                     //       ],
//                     //     ),
//                     //   ),
//                     Padding(
//                       padding: EdgeInsets.only(
//                           top: dW * 0.04, bottom: dW * 0.04, left: dW * 0.06),
//                       child: TextWidget(
//                         title:
//                             '*Max $totalPlayersAllowed members allowed per session ',
//                         color: Color(0xffD84848),
//                       ),
//                     ),
//                     Padding(
//                       padding: EdgeInsets.symmetric(horizontal: dW * 0.06),
//                       child: Column(
//                         children: [
//                           SizedBox(height: dW * 0.01),
//                           Divider(
//                             color: getDividerColor(context),
//                             thickness: 1,
//                           ),
//                           SizedBox(height: dW * 0.01),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               IntrinsicHeight(
//                                 child: Row(
//                                   children: [
//                                     TextWidget(
//                                       title: '\u20b9${getTotalAmount()}',
//                                       fontWeight: FontWeight.w500,
//                                       color: getThemeColor(),
//                                     ),
//                                     VerticalDivider(
//                                       color: getGreyColor6(context),
//                                       indent: dW * .008,
//                                       endIndent: dW * .01,
//                                       thickness: 1,
//                                     ),
//                                     TextWidget(
//                                       title: getTotalAmount() == 0
//                                           ? 'hh:mm - hh:mm'
//                                           : '${double.parse(get12HrClockFormat(getTimeList()[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(getTimeList()[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(getTimeList()[getTimeList().length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(getTimeList()[getTimeList().length - 1].split(':')[0]))}',
//                                       fontSize: getTotalAmount() == 0 ? 12 : 13,
//                                       fontWeight: FontWeight.w500,
//                                       color: const Color(0xFF124E00),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                               CustomButton(
//                                 width: dW * .25,
//                                 height: dW * .1,
//                                 radius: 8,
//                                 buttonText: 'Book',
//                                 fontSize: 14,
//                                 onPressed: updateTime ? () {} : submit,
//                               )
//                             ],
//                           )
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           );
//   }
// }

import 'dart:async';
import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/homeModule/models/bookingModel.dart';
import 'package:bys_business/venueModule/providers/turfProvider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../authModule/modals/userModel.dart';
import '../../bulkBookingModule/screens/create_bulk_booking_screen.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
// import '../../commonWidgets/materialCircularLoader.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/screens/bookSummaryScreen.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';
import 'package:date_picker_timeline/date_picker_timeline.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../widgets/select_sport_and_size_bottomsheet.dart';
import '../widgets/time_slot_widget.dart';
import '../widgets/turf_size_sport_widget.dart';
import '../widgets/user_detail_bottomsheet.dart';

class VenueAvailabilityScreen extends StatefulWidget {
  final UserModal user;
  Venue? selectedTurf;
  final String bookingType;
  final bool updateTime;
  final BookingModel? booking;
  // final SportType? selectedSport;
  // final String selectedTurfSize;
  VenueAvailabilityScreen({
    Key? key,
    required this.user,
    this.selectedTurf,
    required this.bookingType,
    this.updateTime = false,
    this.booking,
    // this.selectedSport,
    // this.selectedTurfSize = '',
  }) : super(key: key);

  @override
  _VenueAvailabilityScreenState createState() =>
      _VenueAvailabilityScreenState();
}

class _VenueAvailabilityScreenState extends State<VenueAvailabilityScreen> {
  bool isLoading = true;
  bool isSelecting = false;
  bool fetchVenue = false;
  bool updateTime = false;
  bool setTurfSizeFirstTime = true;
  // DatePickerController _controller = DatePickerController();
  DateTime selectedDate = DateTime.now();

  List timeSlot = [];
  List bookedSlot = [];
  List duplicateBookedSlot = [];
  List sortedTimeList = [];
  List selectedTime = [];

  Map pricing = {};

  double dH = 0;
  double dW = 0;
  double tS = 0;

  List sizeOrSportList = [];
  String selectedSizeOrSport = '';
  String selectedCourtType = '';

  var fetchedBookedTimeSlots;

  SportType? selectedSport;
  int totalPlayersAllowed = 0;

  late TimeOfDay startTime;
  late TimeOfDay endTime;

  late Timer timer;

  String selectedTurfSize = '';

  List listOfDates = [];
  final _itemController = ItemScrollController();

  Future scrollToItem(int index) async {
    _itemController.scrollTo(
      index: index,
      alignment: .2,
      duration: const Duration(milliseconds: 200),
    );
  }

  Iterable<TimeOfDay> getTimes(
      TimeOfDay startTime, TimeOfDay endTime, Duration step) sync* {
    var hour = startTime.hour;
    var minute = startTime.minute;

    do {
      yield TimeOfDay(hour: hour, minute: minute);
      minute += step.inMinutes;
      while (minute >= 60) {
        minute -= 60;
        hour++;
      }
    } while (hour < endTime.hour ||
        (hour == endTime.hour && minute <= endTime.minute));
  }

  fetchBookedTimeSlot({required bool isNet}) async {
    fetchedBookedTimeSlots =
        await Provider.of<HomeProvider>(context, listen: false)
            .fetchTurfBookingSlot(
      turfId: widget.selectedTurf!.id,
      startDate: DateTime(
              selectedDate.year, selectedDate.month, selectedDate.day, 0, 0, 0)
          .toString(),
      endDate: DateTime(selectedDate.year, selectedDate.month, selectedDate.day,
              23, 59, 59)
          .toString(),
      sizeOrSport: selectedSizeOrSport,
      isNet: isNet,
      updateBooking: widget.booking != null ? true : false,
      bookingId: widget.booking != null ? widget.booking!.id : '',
    );

    // if (isNet) {
    final blockedSlots = await Provider.of<HomeProvider>(context, listen: false)
        .fetchBlockedSlot(
      turfId: widget.selectedTurf!.id,
      startDate: DateTime(
              selectedDate.year, selectedDate.month, selectedDate.day, 0, 0, 0)
          .toString(),
      endDate: DateTime(selectedDate.year, selectedDate.month, selectedDate.day,
              23, 59, 59)
          .toString(),
      sizeOrSport: selectedSizeOrSport,
      isNet: isNet,
      sport: widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
          ? selectedSport == null
              ? ''
              : selectedSport!.sport
          : selectedSizeOrSport,
    );

    blockedSlots.forEach((slot) {
      duplicateBookedSlot.add(
          double.parse(slot['startTime'].toString().split('.')[0]) < 10
              ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
              : slot['startTime'].toStringAsFixed(2).replaceAll('.', ':'));
      duplicateBookedSlot.add(
          double.parse(slot['endTime'].toString().split('.')[0]) < 10
              ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
              : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
    });
    // }
  }

  getStartEndTime(startTime, endTime) {
    return getTimes(
            TimeOfDay(
                hour: int.parse(
                    startTime.toDouble().toStringAsFixed(2).split('.')[0]),
                minute: int.parse(
                    startTime.toDouble().toStringAsFixed(2).split('.')[1])),
            TimeOfDay(
                hour: int.parse(
                    endTime.toDouble().toStringAsFixed(2).split('.')[0]),
                minute: int.parse(
                    endTime.toDouble().toStringAsFixed(2).split('.')[1])),
            Duration(minutes: 30))
        .map((tod) => tod.format(context))
        .toList();
  }

  setTimeList({String sport = '', bool isLoad = true}) async {
    try {
      setState(() {
        isLoading = isLoad;
      });

      if (widget.selectedTurf == null) {
        setState(() {
          fetchVenue = true;
        });

        final result = await Provider.of<TurfProvider>(context, listen: false)
            .fetchTurfById(
          accessToken: widget.user.accessToken,
          turfId: widget.booking!.turfId,
        );

        if (result != null) {
          widget.selectedTurf = result;
          // generateDateList();
          selectedDate = widget.booking!.bookingDate!;
          listOfDates = [
            {'index': 0, 'date': selectedDate}
          ];
          int sportIndex = widget.selectedTurf!.sportsType!
              .indexWhere((sport) => sport.sport == widget.booking!.sportType);
          if (sportIndex != -1) {
            selectedSport = widget.selectedTurf!.sportsType![sportIndex];
          }
          setState(() {});
        } else {
          showSnackbar('Something went wrong');
          pop();
        }
      }

      List duplicateTimeSlot = [];
      duplicateBookedSlot = [];
      List duplicateSizeOrSportList = [];
      Map duplicatePricing = {};

      if (isLoad) {
        sortedTimeList = [];
        selectedTime = [];
      }

      // String insertingSport =
      //     widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
      //         ? selectedSport.sport
      //         : selectedSizeOrSport;
      final step = Duration(minutes: widget.selectedTurf!.slotTimeDifference!);

      bool isNet = widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor'
          ? false
          : widget.selectedTurf!.isNet!;

      List<Slot> listOfSlot = getSportSlots();

      for (var slot in listOfSlot) {
        for (var priceQty in slot.priceAndQuantity) {
          int index = duplicateSizeOrSportList.indexWhere(
            (data) => data['title'] == priceQty.title,
          );
          if (index == -1) {
            duplicateSizeOrSportList.add({
              'title': priceQty.title,
              'label': priceQty.label,
              'isSelected': false,
            });
          }
        }
      }

      sizeOrSportList = List.from(duplicateSizeOrSportList);

      if (setTurfSizeFirstTime && widget.booking != null) {
        sport = widget.booking!.sizeOrSport;
        setTurfSizeFirstTime = false;
      }

      if (sport == '') {
        // duplicateSizeOrSportList[0]['isSelected'] = true;
        // selectedSizeOrSport = duplicateSizeOrSportList[0]['title'];
      } else {
        selectSizeOrSport(sport);
      }
      await fetchBookedTimeSlot(isNet: isNet);

      List<String> extraTime = [];

      Slot startSlot = listOfSlot.isEmpty
          ? Slot(
              id: '',
              session: 'Morning',
              startTime: 5,
              endTime: 12,
              priceAndQuantity: [],
            )
          : listOfSlot[0];
      Slot endSlot = listOfSlot.isEmpty
          ? Slot(
              id: '',
              session: 'Night',
              startTime: 12,
              endTime: 24,
              priceAndQuantity: [],
            )
          : listOfSlot[listOfSlot.length - 1];

      startTime = TimeOfDay(
        hour: int.parse(
          startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
        ),
        minute: int.parse(
          startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
        ),
      );

      if (endSlot.endTime >= 0 && endSlot.endTime <= 5) {
        endTime = TimeOfDay(
          hour: 23,
          minute: 30,
        );
        var startTime1 = TimeOfDay(hour: 0, minute: 0);
        var endTime1 = TimeOfDay(
          hour: int.parse(
            endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
          ),
          minute: int.parse(
            endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
          ),
        );
        extraTime = getTimes(
          startTime1,
          endTime1,
          Duration(minutes: 30),
        ).map((tod) => tod.format(context)).toList();
      } else {
        endTime = TimeOfDay(
          hour: int.parse(
            endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
          ),
          minute: int.parse(
            endSlot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
          ),
        );
      }
      var timeDifference = [];
      timeDifference = getTimes(startTime, endTime, step)
          .map((tod) => tod.format(context))
          .toList();
      int index = timeDifference.indexWhere((time) => time == '24:00');
      if (index != -1) {
        timeDifference[index] = '00:00';
      }
      // print('TimeDifference: $timeDifference');
      List difference = [];
      timeDifference.addAll(extraTime);

      for (var time in timeDifference) {
        if (time.contains('AM')) {
          difference.add(time.toString().replaceAll('AM', '').trim());
        } else if (time.contains('PM')) {
          difference.add(time.toString().replaceAll('PM', '').trim());
        } else {
          difference.add(time.toString());
        }
      }
      timeDifference = List.from(difference);
      for (var i = 0; i < timeDifference.length - 1; i++) {
        duplicateTimeSlot.add([
          {
            'time': timeDifference[i],
            'isSelected': false,
          },
          {
            'time': timeDifference[i + 1],
            'isSelected': false,
          },
        ]);
      }
      if (selectedSizeOrSport != '') {
        fetchedBookedTimeSlots.forEach((slot) async {
          List timeList = [];
          if (slot['endTime'] >= 0 && slot['endTime'] <= 5) {
            if (isNet) {
              if (slot['quantities'] != null &&
                  slot['quantities'][selectedSizeOrSport] <= 0) {
                timeList.add(double.parse(
                            slot['startTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['startTime']
                        .toStringAsFixed(2)
                        .replaceAll('.', ':'));
                timeList.add(double.parse(
                            slot['endTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
              }
            } else {
              if (slot['sizeOrSport'] == selectedSizeOrSport &&
                  slot['remainingCount'] <= 0) {
                timeList.add(double.parse(
                            slot['startTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['startTime']
                        .toStringAsFixed(2)
                        .replaceAll('.', ':'));
                timeList.add(double.parse(
                            slot['endTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
              }
            }
          } else {
            if (isNet) {
              if (slot['quantities'] != null &&
                  slot['quantities'][selectedSizeOrSport] <= 0) {
                timeList = getStartEndTime(
                  slot['startTime'],
                  slot['endTime'],
                );
              }
            } else {
              if (slot['sizeOrSport'] == selectedSizeOrSport &&
                  slot['remainingCount'] <= 0) {
                timeList = getStartEndTime(
                  slot['startTime'],
                  slot['endTime'],
                );
              }
            }
          }

          duplicateBookedSlot.addAll(timeList);
        });
      }
      //BLOCKING TODAYS TIME WHICH HAS PASSED
      if (DateTime.now().isAfter(selectedDate)) {
        var blocked = getTimes(
          TimeOfDay(
            hour: int.parse(
              startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
            ),
            minute: int.parse(
              startSlot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
            ),
          ),
          TimeOfDay(
            hour: DateTime.now().hour,
            minute: DateTime.now().minute,
          ),
          step,
        ).map((tod) => tod.format(context)).toList();
        List blocked1 = [];
        blocked.forEach((time) {
          if (time.contains('AM')) {
            blocked1.add(time.toString().replaceAll('AM', '').trim());
          } else if (time.contains('PM')) {
            blocked1.add(time.toString().replaceAll('PM', '').trim());
          } else {
            blocked1.add(time.toString());
          }
        });
        if (DateTime.now().minute >= 0 && DateTime.now().minute < 30) {
          blocked1.add("${DateTime.now().hour}:00");
          blocked1.add("${DateTime.now().hour}:30");
        } else if (DateTime.now().minute >= 30 && DateTime.now().minute <= 60) {
          blocked1.add("${DateTime.now().hour}:30");
          blocked1.add("${DateTime.now().hour + 1}:00");
        }
        duplicateBookedSlot.addAll(blocked1);
      }

      // if (widget.selectedTurf!.pauseDates!
      //         .contains(DateFormat('yyyy-MM-dd hh: mm').format(selectedDate)) ||
      //     !widget.selectedTurf!.days!
      //         .contains(getWeekDays(selectedDate.weekday))) {
      //   var blocked = blockWholeDay(step);
      //   duplicateBookedSlot.addAll(blocked);
      //   }
      if (!widget.selectedTurf!.days!
              .contains(getWeekDays(selectedDate.weekday)) &&
          !widget.selectedTurf!.weekends!
              .contains(getWeekDays(selectedDate.weekday))) {
        var blocked = blockWholeDay(step);
        duplicateBookedSlot.addAll(blocked);
      } else {
        widget.selectedTurf!.pauseDates!.forEach((date) {
          if (date['start']
              .contains(DateFormat('yyyy-MM-dd').format(selectedDate))) {
            var endPauseDate = DateTime.parse(date['end']);
            var startPauseDate = DateTime.parse(date['start']);
            var blocked = getTimes(
              TimeOfDay(
                hour: startPauseDate.hour,
                minute: startPauseDate.minute,
              ),
              TimeOfDay(
                hour: endPauseDate.hour == 0 ? 24 : endPauseDate.hour,
                minute: endPauseDate.minute,
              ),
              step,
            ).map((tod) => tod.format(context)).toList();
            duplicateBookedSlot.addAll(blocked);
          }
        });
      }

      for (var slot in listOfSlot) {
        for (var priceQty in slot.priceAndQuantity) {
          if (selectedSizeOrSport == priceQty.title) {
            if (widget.selectedTurf!.weekends!
                .contains(getWeekDays(selectedDate.weekday))) {
              if (priceQty.weekendPrice == 0) {
                if (slot.endTime >= 0 && slot.endTime <= 5) {
                  var blocked1 = getStartEndTime(slot.startTime, 23.30);
                  var blocked2 = getStartEndTime(00, slot.endTime);
                  duplicateBookedSlot.addAll(blocked1);
                  duplicateBookedSlot.addAll(blocked2);
                } else {
                  var blocked1 = getStartEndTime(slot.startTime,
                      slot.session == 'Night' ? 24 : slot.endTime);
                  duplicateBookedSlot.addAll(blocked1);
                }
              }
            } else {
              if (priceQty.price == 0) {
                if (slot.endTime >= 0 && slot.endTime <= 5) {
                  var blocked1 = getStartEndTime(slot.startTime, 23.30);
                  var blocked2 = getStartEndTime(00, slot.endTime);
                  duplicateBookedSlot.addAll(blocked1);
                  duplicateBookedSlot.addAll(blocked2);
                } else {
                  var blocked1 = getStartEndTime(slot.startTime,
                      slot.session == 'Night' ? 24 : slot.endTime);
                  duplicateBookedSlot.addAll(blocked1);
                }
              }
            }
          }
        }
      }

      for (var data in defaultSlots) {
        int index =
            listOfSlot.indexWhere((slot) => slot.session == data['title']);
        if (index == -1) {
          if (data['endTime'] >= 0 && data['endTime'] <= 5) {
            var blocked1 = getStartEndTime(data['startTime'], 23.30);
            var blocked2 = getStartEndTime(00, data['endTime']);
            duplicateBookedSlot.addAll(blocked1);
            duplicateBookedSlot.addAll(blocked2);
          } else {
            var blocked1 = getStartEndTime(data['startTime'],
                data['title'] == 'Night' ? 24 : data['endTime']);
            duplicateBookedSlot.addAll(blocked1);
          }
        }
      }
      if (duplicateBookedSlot.contains('24:00')) {
        duplicateBookedSlot.add('00:00');
      }

      //FIND PRICE FOR EACH SLOT
      for (var slot in listOfSlot) {
        List list = [];
        if (slot.endTime > 0 && slot.endTime <= 5) {
          TimeOfDay start1 = TimeOfDay(
            hour: int.parse(
              slot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
            ),
            minute: int.parse(
              slot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
            ),
          );
          TimeOfDay end1 = const TimeOfDay(hour: 23, minute: 30);
          TimeOfDay start2 = const TimeOfDay(hour: 00, minute: 00);

          TimeOfDay end2 = TimeOfDay(
            hour: int.parse(
              slot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
            ),
            minute: int.parse(
              slot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
            ),
          );
          list.addAll(getTimes(start1, end1, const Duration(minutes: 30))
              .map((tod) => tod.format(context))
              .toList());
          list.addAll(getTimes(start2, end2, const Duration(minutes: 30))
              .map((tod) => tod.format(context))
              .toList());
        } else {
          TimeOfDay start = TimeOfDay(
            hour: int.parse(
              slot.startTime.toDouble().toStringAsFixed(2).split('.')[0],
            ),
            minute: int.parse(
              slot.startTime.toDouble().toStringAsFixed(2).split('.')[1],
            ),
          );
          TimeOfDay end = TimeOfDay(
            hour: int.parse(
              slot.endTime.toDouble().toStringAsFixed(2).split('.')[0],
            ),
            minute: int.parse(
              slot.endTime.toDouble().toStringAsFixed(2).split('.')[1],
            ),
          );
          list = getTimes(start, end, const Duration(minutes: 30))
              .map((tod) => tod.format(context))
              .toList();
        }
        Map data = {};
        for (var price in slot.priceAndQuantity) {
          if (!data.containsKey(price.title)) {
            data[price.title] = {
              'weekend': price.weekendPrice,
              'weekday': price.price,
            };
          }
        }
        for (var t in list) {
          duplicatePricing[t] = data;
        }
      }

      if (isLoad) {
        timeSlot = List.from(duplicateTimeSlot);
      }

      if (duplicatePricing.containsKey('24:00')) {
        duplicatePricing['00:00'] = duplicatePricing['24:00'];
      }

      bookedSlot = List.from(duplicateBookedSlot);
      pricing = duplicatePricing;

      print('Selected1 $selectedTime');
      List duplicatedSelectedTime = List.from(selectedTime);

      timeSlot.forEach((data) {
        if (bookedSlot.contains(data[0]['time']) &&
            bookedSlot.contains(data[1]['time'])) {
          data[0]['isSelected'] = false;
          data[1]['isSelected'] = false;
          duplicatedSelectedTime
              .remove('${data[0]['time']}-${data[1]['time']}');
          sortedTimeList.remove(
              double.parse((data[0]['time'].toString().replaceAll(':', '.'))));
          sortedTimeList.remove(
              double.parse((data[1]['time'].toString().replaceAll(':', '.'))));
        }
      });

      selectedTime = List.from(duplicatedSelectedTime);
      print('Selected2 $selectedTime');
      print('Sorted $sortedTimeList');

      setState(() {
        isLoading = false;
        fetchVenue = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  List<Slot> getSportSlots() {
    Venue venue = widget.selectedTurf!;
    if (venue.sportCategory!.categoryName == 'Outdoor') {
      if (venue.cricket != null &&
          selectedSport != null &&
          selectedSport!.id == venue.cricket!.sport) {
        return venue.cricket!.slots;
      } else if (venue.football != null &&
          selectedSport != null &&
          selectedSport!.id == venue.football!.sport) {
        return venue.football!.slots;
      } else {
        return venue.slots ?? [];
      }
    } else if (venue.sportCategory!.categoryName == 'Indoor') {
      if (venue.badminton != null &&
          selectedSport != null &&
          selectedSport!.id == venue.badminton!.sport) {
        return venue.badminton!.slots;
      } else if (venue.snooker != null &&
          selectedSport != null &&
          selectedSport!.id == venue.snooker!.sport) {
        return venue.snooker!.slots;
      } else if (venue.pool != null &&
          selectedSport != null &&
          selectedSport!.id == venue.pool!.sport) {
        return venue.pool!.slots;
      } else if (venue.pickleball != null &&
          selectedSport != null &&
          selectedSport!.id == venue.pickleball!.sport) {
        return venue.pickleball!.slots;
      } else if (venue.tableTennis != null &&
          selectedSport != null &&
          selectedSport!.id == venue.tableTennis!.sport) {
        return venue.tableTennis!.slots;
      } else if (venue.tennis != null &&
          selectedSport != null &&
          selectedSport!.id == venue.tennis!.sport) {
        return venue.tennis!.slots;
      } else {
        return venue.slots ?? [];
      }
    } else if (venue.sportCategory!.categoryName == 'Esport') {
      if (venue.ps3 != null &&
          selectedSport != null &&
          selectedSport!.id == venue.ps3!.sport) {
        return venue.ps3!.slots;
      } else if (venue.ps4 != null &&
          selectedSport != null &&
          selectedSport!.id == venue.ps4!.sport) {
        return venue.ps4!.slots;
      } else if (venue.ps5 != null &&
          selectedSport != null &&
          selectedSport!.id == venue.ps5!.sport) {
        return venue.ps5!.slots;
      } else if (venue.xbox1 != null &&
          selectedSport != null &&
          selectedSport!.id == venue.xbox1!.sport) {
        return venue.xbox1!.slots;
      } else {
        return venue.slots ?? [];
      }
    } else {
      return venue.slots ?? [];
    }
  }

  // checkSlotAvailability(time) {
  //   if (bookedSlot.contains(time)) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // }

  List<String> generateTimeList(double startTime, double endTime) {
    List<String> timeList = [];

    // Add the start time to the list
    timeList.add(startTime.toStringAsFixed(1));

    // If start and end times are different, add the intermediate times
    endTime == 0.0 ? endTime = 24.0 : endTime = endTime;
    while (startTime < endTime) {
      if (startTime.toString().split('.')[1] == "0") {
        double nextTime = startTime + 0.3;
        timeList.add(nextTime.toStringAsFixed(1));
        startTime = nextTime;
      } else {
        double nextTime = startTime + 0.7;
        timeList.add(nextTime.toStringAsFixed(1));
        startTime = nextTime;
      }
    }

    if (timeList.contains("24.0")) {
      int index = timeList.indexOf("24.0");
      timeList[index] = "0.0";
    }
    return timeList;
  }

  checkSlotAvailability(String time) {
    if (widget.booking != null) {
      String startTime = widget.booking!.bookingSlotStartTime.toString();
      String endTime = widget.booking!.bookingSlotEndTime.toString();

      // Convert the provided time to decimal format
      String providedTime = convertToDecimal(time);
      List<String> timeList =
          generateTimeList(double.parse(startTime), double.parse(endTime));
      if (timeList.contains(providedTime) || bookedSlot.contains(time)) {
        return true;
      } else {
        return false;
      }
    }
    if (bookedSlot.contains(time)) {
      return true;
    } else {
      return false;
    }
  }

  String convertToDecimal(String time) {
    // Split the time string into hours and minutes
    List<String> parts = time.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);

    // Convert the time to decimal format
    double decimalTime = hours + (minutes / 100.0);

    // Return the decimal format as a string
    return decimalTime.toStringAsFixed(1);
  }

  selectSizeOrSport(String title) {
    sizeOrSportList.forEach((size) {
      if (size['title'] == title) {
        size['isSelected'] = true;
        selectedSizeOrSport = size['title'];
      } else {
        size['isSelected'] = false;
      }
    });
    if (widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor') {
      widget.selectedTurf!.sportsType!.forEach((data) {
        if (data.sport == title) {
          selectedSport = data;
        }
      });
    }
    setState(() {});
  }

  blockWholeDay(step) {
    return getTimes(
      TimeOfDay(
        hour: 00,
        minute: 00,
      ),
      TimeOfDay(
        hour: 24,
        minute: 00,
      ),
      step,
    ).map((tod) => tod.format(context)).toList();
  }

  loopTimeSlot(slot, status) {
    for (var i = 0; i < timeSlot.length; i++) {
      if (timeSlot[i][0]['time'] == slot[0]['time']) {
        setState(() {
          timeSlot[i][0]['isSelected'] = status;
          timeSlot[i][1]['isSelected'] = status;
          if (status) {
            selectedTime
                .add('${timeSlot[i][0]['time']}-${timeSlot[i][1]['time']}');
            // selectedTime
            //     .addAll([timeSlot[i][0]['time'], timeSlot[i][1]['time']]);
          } else {
            selectedTime
                .remove('${timeSlot[i][0]['time']}-${timeSlot[i][1]['time']}');
            // selectedTime.remove(timeSlot[i][0]['time']);
            // selectedTime.remove(timeSlot[i][1]['time']);
          }
        });
      }
    }
  }

  getValue(value) {
    if (value == '00:00' || value == 0.0) {
      return 24.00;
    } else if (value == '00:30' || value == 0.3) {
      return 24.30;
    } else if (value == '01:00' || value == 1.0) {
      return 25.00;
    } else if (value == '01:30' || value == 1.3) {
      return 25.30;
    } else if (value == '02:00' || value == 2.0) {
      return 26.00;
    } else if (value == '02:30' || value == 2.3) {
      return 26.30;
    } else if (value == '03:00' || value == 3.0) {
      return 27.00;
    } else if (value == '03:30' || value == 3.3) {
      return 27.30;
    } else if (value == '04:00' || value == 4.0) {
      return 28.00;
    } else if (value == '04:30' || value == 4.3) {
      return 28.30;
    } else if (value == '05:00' || value == 5.0) {
      return 29.00;
    } else if (value == "24" || value == "24.0" || value == "24.00") {
      return 0.0;
    } else if (value == "24.3" || value == "24.30") {
      return 0.3;
    } else if (value == "25" || value == "25.0" || value == "25.00") {
      return 1.0;
    } else if (value == "25.3" || value == "25.30") {
      return 1.3;
    } else if (value == "26" || value == "26.0" || value == "26.00") {
      return 2;
    } else if (value == "26.3" || value == "26.30") {
      return 2.3;
    } else if (value == "27" || value == "27.0" || value == "27.00") {
      return 3;
    } else if (value == "27.3" || value == "27.30") {
      return 3.3;
    } else if (value == "28" || value == "28.0" || value == "28.00") {
      return 4.0;
    } else if (value == "28.3" || value == "28.30") {
      return 4.3;
    } else if (value == "29" || value == "29.0" || value == "29.00") {
      return 5.0;
    } else {
      return 24.0;
    }
  }

  getText() {
    if (selectedSport == null) {
      return 'Please select sport';
    } else if (selectedSizeOrSport == '') {
      return widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
          ? 'Please select turf size'
          : 'Please select sport';
    }
  }

  selectAndUnselectTimeSlot(slot, add) {
    if (selectedSport == null || selectedSizeOrSport == '') {
      return showSnackbar(getText());
    }

    if (isSelecting) return;

    setState(() => isSelecting = true);

    if (add) {
      if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
          double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
        sortedTimeList.addAll([
          getValue(slot[0]['time']),
          getValue(slot[1]['time'] == '00:00' ? '12:00' : slot[1]['time'])
        ]);
      } else {
        sortedTimeList.addAll([
          double.parse(slot[0]['time'].replaceAll(":", ".")),
          double.parse(slot[1]['time'].replaceAll(":", "."))
        ]);
      }
      sortedTimeList.sort();

      var abc = [];
      for (var i = 1; i < sortedTimeList.length; i += 2) {
        abc.add(sortedTimeList[i]);
      }
      print(abc);
      var showAlert = false;
      for (var i = 0; i < abc.length; i++) {
        if (i + 1 < abc.length) {
          if (widget.selectedTurf!.slotTimeDifference == 30 &&
              double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                      .abs() >
                  0.7) {
            print(double.parse(
                    (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                .abs());
            showAlert = true;
          } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
              double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                      .abs() >
                  1) {
            print(double.parse(
                    (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                .abs());
            showAlert = true;
          }
        }
      }
      if (!showAlert) {
        loopTimeSlot(slot, true);
      } else {
        if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
            double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
          sortedTimeList.remove(getValue(slot[0]['time']));
          sortedTimeList.remove(getValue(slot[1]['time']));
        } else {
          sortedTimeList
              .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
          sortedTimeList
              .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
        }

        // showSnackbar('You can only add consecutive timing.');
      }
      // }
    } else {
      if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
          double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
        sortedTimeList.remove(getValue(slot[0]['time']));
        sortedTimeList.remove(getValue(slot[1]['time']));
      } else {
        sortedTimeList
            .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
        sortedTimeList
            .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
      }
      var abc = [];
      for (var i = 1; i < sortedTimeList.length; i += 2) {
        abc.add(sortedTimeList[i]);
      }
      var showAlert = false;
      for (var i = 0; i < abc.length; i++) {
        if (i + 1 < abc.length) {
          if (widget.selectedTurf!.slotTimeDifference == 30 &&
              double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                      .abs() >
                  0.7) {
            showAlert = true;
          } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
              double.parse((abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2))
                      .abs() >
                  1) {
            showAlert = true;
          }
        }
      }
      if (!showAlert) {
        loopTimeSlot(slot, false);
      } else {
        if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
            double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
          sortedTimeList
              .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
        } else {
          sortedTimeList.addAll([
            double.parse(slot[0]['time'].replaceAll(":", ".")),
            double.parse(slot[1]['time'].replaceAll(":", "."))
          ]);
        }
        sortedTimeList.sort();
        // showSnackbar('You can only add consecutive timing.');
      }
    }

    setState(() => isSelecting = false);
  }

  selectDate() {
    DateTime date = DateTime.now();

    List days = [];

    widget.selectedTurf!.days!.forEach((day) {
      days.add(getWeekNumber(day));
    });

    for (var i = 0; i < 7; i++) {
      if (!days.contains(date.weekday)) {
        date = date.add(Duration(days: 1));
      } else {
        break;
      }
    }

    showDatePicker(
      context: context,
      initialDate: date,
      firstDate: date,
      lastDate: DateTime(2200),
      selectableDayPredicate: (DateTime val) =>
          days.contains(val.weekday) ? true : false,
    ).then((date) {
      if (date != null) {
        if (widget.selectedTurf!.pauseDates!
            .contains(DateFormat('yyyy-MM-dd').format(date))) {
          showSnackbar('Booking are not available for this date');
          return;
        } else {
          setState(() {
            selectedDate = date;
          });
        }
      }
    });
  }

  goToSummaryScreen({
    required String fullName,
    required String mobileNo,
  }) {
    // if (selectedTime.length < 2) {

    if (widget.selectedTurf!.slotTimeDifference == 60 && selectedTime.isEmpty) {
      showSnackbar('Minimum booking should be of atleast 1 hour');
      return;
    } else if (widget.selectedTurf!.slotTimeDifference == 30 &&
        selectedTime.isEmpty) {
      showSnackbar('Minimum booking should be of atleast 30 mins');
      return;
    } else {
      late SportType sport;

      if (widget.selectedTurf!.sportCategory!.categoryName != 'Outdoor') {
        for (var data in widget.selectedTurf!.sportsType!) {
          if (data.sport == selectedSport!.sport) {
            sport = data;
            break;
          }
        }
      } else {
        sport = selectedSport!;
      }

      PricingAndQuantity? priceAndQuantity;

      List<Slot> listOfSlots = getSportSlots();

      for (var i = 0; i < listOfSlots.length; i++) {
        Slot slot = listOfSlots[i];
        for (var j = 0; j < slot.priceAndQuantity.length; j++) {
          if (slot.priceAndQuantity[j].title == selectedSizeOrSport) {
            priceAndQuantity = slot.priceAndQuantity[j];
            break;
          }
        }
        if (priceAndQuantity != null) {
          break;
        }
      }

      List time = [];

      List dummySelectedTime = [];

      selectedTime.forEach((time) {
        dummySelectedTime.addAll(time.toString().split('-'));
      });
      print('Selected Time Length:${selectedTime.length}');

      dummySelectedTime.forEach((data) {
        if (double.parse(data.replaceAll(":", ".")) == 5 &&
            !selectedTime.contains('04:30-05:00')) {
          time.add(double.parse(data.replaceAll(":", ".")));
        } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
            selectedTime.contains('04:30-05:00')) {
          time.add(getValue(data));
        } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
            double.parse(data.replaceAll(":", ".")) <= 5) {
          time.add(getValue(data));
        } else {
          time.add(double.parse(data.replaceAll(":", ".")));
        }
      });

      time.sort();
      time.toSet();

      List dummyTime = [];

      time.forEach((t) {
        if (t >= 24 && t <= 29) {
          dummyTime.add(getValue(t.toString()));
        } else {
          dummyTime.add(t);
        }
      });
      time = dummyTime;
      List duplicateSelectedTime = [];

      time.forEach((data) {
        if (data >= 0 && data < 10) {
          duplicateSelectedTime
              .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
        } else {
          duplicateSelectedTime
              .add(data.toStringAsFixed(2).replaceAll(".", ":"));
        }
      });

      double amount = 0;

      for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
        print(duplicateSelectedTime[i]);
        if (widget.selectedTurf!.weekends!
            .contains(getWeekDays(selectedDate.weekday))) {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
        } else {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
        }
      }

      print('Total Amount: $amount');
      timer.cancel();
      if (widget.bookingType == 'Single Booking') {
        push(
          BookingSummaryScreen(
            turf: widget.selectedTurf!,
            selectedTime: duplicateSelectedTime,
            bookingDate: DateTime(
              selectedDate.year,
              selectedDate.month,
              selectedDate.day,
              // DateTime.now().hour,
              // DateTime.now().minute,
              // DateTime.now().second,
              13, 0, 0,
            ),
            time:
                // '${double.parse(duplicateSelectedTime[0].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')}  - ${double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')}',
                '${double.parse(get12HrClockFormat(duplicateSelectedTime[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(duplicateSelectedTime[duplicateSelectedTime.length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].split(':')[0]))}',
            sport: sport,
            amount: amount,
            sizeOrSport: selectedSizeOrSport,
            name: fullName,
            phone: mobileNo,
            quantity: priceAndQuantity!.quantity,
            label: priceAndQuantity.label,
            user: widget.user,
          ),
        ).then((value) {
          if (value != null) {
            Navigator.of(context).pop();
            setTimeList();
            timer = Timer.periodic(Duration(seconds: 7), (timer) {
              setTimeList(isLoad: false, sport: selectedSizeOrSport);
            });
          }
        });
      } else {
        push(
          CreateBulkBookingScreen(
            user: widget.user,
            selectedTurf: widget.selectedTurf!,
            time:
                '${double.parse(get12HrClockFormat(duplicateSelectedTime[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(duplicateSelectedTime[duplicateSelectedTime.length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(duplicateSelectedTime[duplicateSelectedTime.length - 1].split(':')[0]))}',
            hour: '1min',
            sport: sport,
            amount: amount,
            taxes: 100,
            sizeOrSport: selectedSizeOrSport,
            selectedTime: duplicateSelectedTime,
            bookingDate: DateTime(selectedDate.year, selectedDate.month,
                selectedDate.day, 13, 0, 0),
            quantity: priceAndQuantity!.quantity,
            label: priceAndQuantity.label,
            size: selectedSizeOrSport,
            fullName: fullName,
            mobileNo: mobileNo,
          ),
        ).then((value) {
          if (value) {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
            setTimeList();
            timer = Timer.periodic(Duration(seconds: 7), (timer) {
              setTimeList(isLoad: false, sport: selectedSizeOrSport);
            });
          }
        });
      }
    }
  }

  updateBookingTime() async {
    try {
      if (updateTime) return;

      setState(() => updateTime = true);

      List time = [];

      List dummySelectedTime = [];

      selectedTime.forEach((time) {
        dummySelectedTime.addAll(time.toString().split('-'));
      });
      print('Selected Time Length:${selectedTime.length}');

      dummySelectedTime.forEach((data) {
        if (double.parse(data.replaceAll(":", ".")) == 5 &&
            !selectedTime.contains('04:30-05:00')) {
          time.add(double.parse(data.replaceAll(":", ".")));
        } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
            selectedTime.contains('04:30-05:00')) {
          time.add(getValue(data));
        } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
            double.parse(data.replaceAll(":", ".")) <= 5) {
          time.add(getValue(data));
        } else {
          time.add(double.parse(data.replaceAll(":", ".")));
        }
      });

      time.sort();
      time.toSet();

      List dummyTime = [];

      time.forEach((t) {
        if (t >= 24 && t <= 29) {
          dummyTime.add(getValue(t.toString()));
        } else {
          dummyTime.add(t);
        }
      });
      time = dummyTime;
      List duplicateSelectedTime = [];

      time.forEach((data) {
        if (data >= 0 && data < 10) {
          duplicateSelectedTime
              .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
        } else {
          duplicateSelectedTime
              .add(data.toStringAsFixed(2).replaceAll(".", ":"));
        }
      });

      double amount = 0;

      for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
        print(duplicateSelectedTime[i]);
        if (widget.selectedTurf!.weekends!
            .contains(getWeekDays(selectedDate.weekday))) {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
        } else {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
        }
      }

      if (amount < widget.booking!.totalAmount) {
        return showSnackbar('Amount cannot be less than booking amount');
      }

      final result = await Provider.of<HomeProvider>(context, listen: false)
          .updateBookingTime(
        accessToken: widget.user.accessToken,
        bookingId: widget.booking!.id,
        newStartTime: duplicateSelectedTime[0].toString().replaceAll(':', '.'),
        newEndTime: duplicateSelectedTime[duplicateSelectedTime.length - 1]
            .toString()
            .replaceAll(':', '.'),
        amount: amount,
      );

      if (result != null) {
        showSnackbar('Booking Time updated successfully', color: greenPrimary);
        Navigator.of(context).pop(result);
      } else {
        showSnackbar('Unable to update booking time');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => updateTime = false);
    }
  }

  getTotalAmount() {
    if (selectedTime.isEmpty) {
      return 0;
    } else {
      List time = [];
      List dummySelectedTime = [];

      selectedTime.forEach((time) {
        dummySelectedTime.addAll(time.toString().split('-'));
      });

      dummySelectedTime.forEach((data) {
        time.add(double.parse(data.replaceAll(":", ".")));
      });

      List allTime = time;
      allTime.sort();

      if (allTime[0] >= 0 && allTime[0] <= 5) {
        var array24 = [];
        var array0 = [];
        time.forEach((value) {
          if (value >= 20 && value <= 24) {
            array24.add(value);
          } else {
            array0.add(value);
          }
        });
        array24.sort();
        array0.sort();
        time = [];
        time.addAll(array24);
        time.addAll(array0);
      } else {
        time.sort();
      }
      // time = time.toSet().toList();
      List duplicateSelectedTime = [];
      time.forEach((data) {
        if (data >= 0 && data < 10) {
          duplicateSelectedTime
              .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
        } else {
          duplicateSelectedTime
              .add(data.toStringAsFixed(2).replaceAll(".", ":"));
        }
      });

      double amount = 0;

      for (var i = 0; i < duplicateSelectedTime.length; i = i + 2) {
        // print(pricing[duplicateSelectedTime[i]]);
        if (widget.selectedTurf!.weekends!
            .contains(getWeekDays(selectedDate.weekday))) {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekend'];
        } else {
          amount +=
              pricing[duplicateSelectedTime[i]][selectedSizeOrSport]['weekday'];
        }
      }
      return amount;
      // return 0;
    }
  }

  getDuration() {
    if (widget.selectedTurf!.availability == null) {
      return 365;
    } else {
      String duration = widget.selectedTurf!.availability!.duration;
      int count = widget.selectedTurf!.availability!.count;
      if (duration == 'Month') {
        return 31 * count;
      } else if (duration == 'Year') {
        return 365 * count;
      } else if (duration == 'Days') {
        return count;
      } else {
        return 365;
      }
    }
  }

  setSelectedTime() {
    //   if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
    //       double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
    //     sortedTimeList
    //         .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
    //   } else {
    //     sortedTimeList.addAll([
    //       double.parse(slot[0]['time'].replaceAll(":", ".")),
    //       double.parse(slot[1]['time'].replaceAll(":", "."))
    //     ]);
    //   }
    //   sortedTimeList.sort();

    //   var abc = [];
    //   for (var i = 1; i < sortedTimeList.length; i += 2) {
    //     abc.add(sortedTimeList[i]);
    //   }
    //   print(abc);
    //   var showAlert = false;
    //   for (var i = 0; i < abc.length; i++) {
    //     if (i + 1 < abc.length) {
    //       if (double.parse(
    //               (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)) >
    //           0.7) {
    //         print(double.parse(
    //             (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)));
    //         showAlert = true;
    //       }
    //     }
    //   }
    //   if (!showAlert) {
    //     loopTimeSlot(slot, true);
    //   } else {
    //     if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
    //         double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0) {
    //       sortedTimeList.remove(getValue(slot[0]['time']));
    //       sortedTimeList.remove(getValue(slot[1]['time']));
    //     } else {
    //       sortedTimeList
    //           .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
    //       sortedTimeList
    //           .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
    //     }
    // }
  }

  double getPriceByTime(String time) {
    double amount = 0;
    if (widget.selectedTurf!.weekends!
        .contains(getWeekDays(selectedDate.weekday))) {
      amount += pricing[time] == null || selectedSizeOrSport == ''
          ? 0
          : pricing[time][selectedSizeOrSport]['weekend'] ?? 0;
    } else {
      amount += pricing[time] == null || selectedSizeOrSport == ''
          ? 0
          : pricing[time][selectedSizeOrSport]['weekday'] ?? 0;
    }
    return amount;
  }

  getTimeList() {
    List time = [];

    List dummySelectedTime = [];

    for (var time in selectedTime) {
      dummySelectedTime.addAll(time.toString().split('-'));
    }
    print('Selected Time Length:${selectedTime.length}');

    for (var data in dummySelectedTime) {
      if (double.parse(data.replaceAll(":", ".")) == 5 &&
          !selectedTime.contains('04:30-05:00')) {
        time.add(double.parse(data.replaceAll(":", ".")));
      } else if (double.parse(data.replaceAll(":", ".")) == 5 &&
          selectedTime.contains('04:30-05:00')) {
        time.add(getValue(data));
      } else if (double.parse(data.replaceAll(":", ".")) >= 0 &&
          double.parse(data.replaceAll(":", ".")) <= 5) {
        time.add(getValue(data));
      } else {
        time.add(double.parse(data.replaceAll(":", ".")));
      }
    }

    // List allTime = time;
    time.sort();
    time.toSet();

    List dummyTime = [];

    for (var t in time) {
      if (t >= 24 && t <= 29) {
        dummyTime.add(getValue(t.toString()));
      } else {
        dummyTime.add(t);
      }
    }
    time = dummyTime;
    List duplicateSelectedTime = [];

    for (var data in time) {
      if (data >= 0 && data < 10) {
        duplicateSelectedTime
            .add('0' + data.toStringAsFixed(2).replaceAll(".", ":"));
      } else {
        duplicateSelectedTime.add(data.toStringAsFixed(2).replaceAll(".", ":"));
      }
    }

    return duplicateSelectedTime;
  }

  submit() {
    if (selectedSport == null || selectedSizeOrSport == '') {
      return showSnackbar(getText());
    } else if (widget.selectedTurf!.slotTimeDifference == 60 &&
        selectedTime.isEmpty) {
      showSnackbar('Minimum booking should be of atleast 1 hour');
      return;
    } else if (widget.selectedTurf!.slotTimeDifference == 30 &&
        selectedTime.isEmpty) {
      showSnackbar('Minimum booking should be of atleast 30 mins');
      return;
    } else {
      if (widget.booking != null) {
        updateBookingTime();
      } else {
        showModalBottomSheet(
          enableDrag: true,
          isScrollControlled: true,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          context: (context),
          builder: (context) => GestureDetector(
            child: UserDetailBottomSheet(
              goToSummaryScreen: goToSummaryScreen,
            ),
            onTap: () {},
            behavior: HitTestBehavior.opaque,
          ),
        );
      }
    }
  }

  generateDateList() {
    DateTime today = DateTime.now();
    listOfDates = [];
    listOfDates.add(
        {'index': 0, 'date': DateTime(today.year, today.month, today.day)});
    for (var i = 1; i <= getDuration(); i++) {
      listOfDates.add({
        'index': i,
        'date':
            DateTime(today.year, today.month, today.day).add(Duration(days: i)),
      });
    }
  }

  openSportBottomSheet() {
    // pop();
    showModalBottomSheet(
      isDismissible: false,
      isScrollControlled: true,
      constraints: BoxConstraints(
        maxHeight: widget.selectedTurf!.sportCategory!.categoryName == 'Outdoor'
            ? dH * 0.5
            : dH * 0.7,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) => SelectSportAndSizeBottomSheet(
        venue: widget.selectedTurf!,
        bookingType: widget.bookingType,
        selectedSport: selectedSport == null
            ? widget.selectedTurf!.sportsType!.first
            : selectedSport!,
        totalPlayersAllowed: totalPlayersAllowed == 0
            ? widget.selectedTurf!.slots!.first.priceAndQuantity.first
                .totalPlayersAllowed
            : totalPlayersAllowed,
        selectedTurfSize: selectedSizeOrSport == ''
            ? widget.selectedTurf!.slots!.first.priceAndQuantity.first.title
            : selectedSizeOrSport,
        selectedCourtType: selectedCourtType == '' ? '' : selectedCourtType,
      ),
    ).then((value) {
      if (value != null &&
          value.containsKey('selectedSport') &&
          value.containsKey('selectedTurfSize')) {
        setState(() {
          isLoading = true;
        });
        selectedSport = value['selectedSport'];
        selectedSizeOrSport = value['selectedTurfSize'];
        totalPlayersAllowed = value['totalPlayersAllowed'];
        if (selectedSport!.sport == 'Badminton') {
          selectedCourtType = value['selectedCourtType'];
        }
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await openSportBottomSheet();
    });
    // openSportBottomSheet();
    if (selectedSport != null) {
      selectedSport = selectedSport;
    }
    if (selectedTurfSize != '') {
      selectedSizeOrSport = selectedTurfSize;
    }
    if (widget.selectedTurf != null) {
      generateDateList();
    }
    Future.delayed(Duration(seconds: 0)).then((_) {
      setTimeList();
      if (widget.booking != null) {
        setSelectedTime();
      }
    });

    timer = Timer.periodic(Duration(seconds: 5), (timer) {
      setTimeList(isLoad: false, sport: selectedSizeOrSport);
    });
  }

  @override
  void dispose() {
    super.dispose();
    timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return fetchVenue || widget.selectedTurf == null
        ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
        : Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SizedBox(height: dW * 0.05),
                NewAppBar(
                  dW: dW,
                  title: widget.selectedTurf == null
                      ? ''
                      : widget.selectedTurf!.name,
                ),
                SizedBox(height: dW * 0.04),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // DatePicker(
                      //   DateTime.now(),
                      //   height: dW * 0.2,
                      //   width: dW * 0.15,
                      //   initialSelectedDate: DateTime.now(),
                      //   selectionColor: Theme.of(context).primaryColor,
                      //   selectedTextColor: Colors.white,
                      //   dateTextStyle: TextStyle(
                      //     fontSize: tS * 13,
                      //     fontWeight: FontWeight.w600,
                      //   ),
                      //   dayTextStyle: TextStyle(
                      //     fontSize: tS * 11.5,
                      //     fontWeight: FontWeight.w600,
                      //   ),
                      //   monthTextStyle: TextStyle(
                      //     fontSize: tS * 11.5,
                      //     fontWeight: FontWeight.w600,
                      //   ),
                      //   controller: _controller,
                      //   daysCount: getDuration(),
                      //   onDateChange: (date) {
                      //     selectedDate = date;
                      //     setState(() {});
                      //     setTimeList();
                      //   },
                      // ),
                      SizedBox(
                          height: dW * 0.2,
                          child: ScrollablePositionedList.builder(
                            itemScrollController: _itemController,
                            physics: const BouncingScrollPhysics(),
                            itemCount: listOfDates.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, i) => GestureDetector(
                              onTap: () {
                                selectedDate = listOfDates[i]['date'];
                                if (listOfDates[i]['index'] != 0) {
                                  scrollToItem(listOfDates[i]['index']);
                                }
                                setState(() {});
                                setTimeList();
                              },
                              child: Container(
                                height: dW * 0.184,
                                margin: i == listOfDates.length - 1
                                    ? EdgeInsets.symmetric(horizontal: dW * .06)
                                    : EdgeInsets.only(
                                        left: i == 0 ? dW * .06 : dW * 0.025),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: isSameDay(
                                          selectedDate, listOfDates[i]['date'])
                                      ? getThemeColor()
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: isSameDay(
                                            selectedDate, listOfDates[i]['date'])
                                        ? getThemeColor()
                                        : getTextBoxBorderColor(context),
                                    width: 1,
                                  ),
                                ),
                                padding: EdgeInsets.symmetric(
                                    horizontal: dW * .033, vertical: dW * .02),
                                child: Column(
                                  children: [
                                    TextWidget(
                                      title: DateFormat('MMM')
                                          .format(listOfDates[i]['date']),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: isSameDay(selectedDate,
                                              listOfDates[i]['date'])
                                          ? Colors.white
                                          : getUnselectedLabelColor(context),
                                    ),
                                    TextWidget(
                                      title: DateFormat('dd')
                                          .format(listOfDates[i]['date']),
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: isSameDay(selectedDate,
                                              listOfDates[i]['date'])
                                          ? Colors.white
                                          : getUnselectedLabelColor(context),
                                    ),
                                    TextWidget(
                                      title: DateFormat('EEE')
                                          .format(listOfDates[i]['date']),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: isSameDay(selectedDate,
                                              listOfDates[i]['date'])
                                          ? Colors.white
                                          : getUnselectedLabelColor(context),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.06),
                        child: Divider(
                            color: getDividerColor(context), thickness: 1),
                      ),
                      if (
                      // widget.selectedTurf!.sportCategory!.categoryName ==
                      //       'Outdoor' &&
                      !isLoading)
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: dW * 0.04,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: dW * 0.04,
                              ),
                              // Container(
                              //   margin: EdgeInsets.only(
                              //     left: dW * 0.03,
                              //     bottom: dW * 0.01,
                              //   ),
                              //   child: Text(
                              //     'Select Sport:',
                              //     style: Theme.of(context)
                              //         .textTheme
                              //         .displayMedium
                              //         ?.copyWith(
                              //           fontSize: tS * 13,
                              //           fontWeight: FontWeight.w600,
                              //           letterSpacing: .50,
                              //           color: Colors.black,
                              //         ),
                              //   ),
                              // ),
          
                              GestureDetector(
                                onTap: () {
                                  openSportBottomSheet();
                                },
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      children: [
                                        Row(
                                          children: [
                                            if (selectedSport != null)
                                              CachedNetworkImage(
                                                repeat: ImageRepeat.repeat,
                                                fit: BoxFit.cover,
                                                height: 18,
                                                imageUrl: selectedSport!.image,
                                                placeholder: (_, __) =>
                                                    Image.asset(
                                                  'assets/placeholders/placeholder.png',
                                                  fit: BoxFit.cover,
                                                  height: 18,
                                                ),
                                              ),
                                            SizedBox(
                                              width: dW * 0.02,
                                            ),
                                            if (selectedSport != null)
                                              TextWidget(
                                                title: selectedSport!.sport,
                                                color: getBlackColor(context),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                  left: dW * 0.02,
                                                  right: dW * 0.02),
                                              child: const Icon(
                                                Icons.circle,
                                                size: 5,
                                                color: Color(0xffD9D9D9),
                                              ),
                                            ),
                                            // TextWidget(
                                            //   title: selectedSizeOrSport,
                                            //   color: getBlackColor(context),
                                            //   fontWeight: FontWeight.w500,
                                            // ),
                                            TextWidget(
                                              title: widget
                                                          .selectedTurf!
                                                          .sportCategory!
                                                          .categoryName ==
                                                      'Outdoor'
                                                  ? selectedSizeOrSport
                                                  : selectedSizeOrSport
                                                      .split('(')[0]
                                                      .trim(),
                                              color: getBlackColor(context),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Column(
                                      children: [
                                        Row(
                                          children: [
                                            TextWidget(
                                              title: 'Change',
                                              color: getThemeColor(),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 12,
                                            ),
                                            SizedBox(
                                              width: dW * 0.01,
                                            ),
                                            Icon(
                                              Icons.arrow_forward_ios,
                                              color: getThemeColor(),
                                              size: 15,
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),
          
                              // SingleChildScrollView(
                              //   scrollDirection: Axis.horizontal,
                              //   physics: const BouncingScrollPhysics(),
                              //   child: Row(
                              //     children: [
                              //       ...widget.selectedTurf!.sportsType!
                              //           .asMap()
                              //           .map(
                              //             (index, sport) => MapEntry(
                              //               index,
                              //               GestureDetector(
                              //                 onTap: () {
                              //                   selectedSport = sport;
                              //                   setTimeList();
                              //                 },
                              //                 child: SelectSportWidget(
                              //                   dW: dW,
                              //                   index: index,
                              //                   sport: sport,
                              //                   selectedSport: selectedSport,
                              //                 ),
                              //               ),
                              //             ),
                              //           )
                              //           .values
                              //           .toList(),
                              //     ],
                              //   ),
                              // ),
          
                              SizedBox(height: dW * 0.01),
                              Divider(
                                color: getDividerColor(context),
                                thickness: 1,
                              ),
                            ],
                          ),
                        ),
                      Expanded(
                        child: isLoading
                            ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                            : SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                padding:
                                    EdgeInsets.symmetric(horizontal: dW * 0.06),
                                child: Column(
                                  children: [
                                    SizedBox(height: dW * 0.02),
                                    Row(
                                      children: [
                                        SlotStatusIndicator(
                                          borderColor: getThemeColor(),
                                          fillColor: getThemeColor(),
                                          dW: dW,
                                          text: 'Selected',
                                        ),
                                        SizedBox(width: dW * .05),
                                        SlotStatusIndicator(
                                          borderColor: const Color(0xFFD9D9D9),
                                          fillColor: getWhiteColor(context),
                                          text: 'Available',
                                          dW: dW,
                                        ),
                                        SizedBox(width: dW * .05),
                                        SlotStatusIndicator(
                                          borderColor: const Color(0xFFD9D9D9),
                                          fillColor: const Color(0xFFACACB4)
                                              .withOpacity(.9),
                                          text: 'Sold Out',
                                          dW: dW,
                                        )
                                      ],
                                    ),
                                    SizedBox(height: dW * 0.03),
                                    ...timeSlot
                                        .asMap()
                                        .map(
                                          (index, time) => MapEntry(
                                            index,
                                            GestureDetector(
                                              onTap: time[0]['isSelected'] &&
                                                      time[1]['isSelected']
                                                  ? () =>
                                                      selectAndUnselectTimeSlot(
                                                          time, false)
                                                  : checkSlotAvailability(
                                                              time[0]['time']) &&
                                                          checkSlotAvailability(
                                                              time[1]['time'])
                                                      ? null
                                                      : () =>
                                                          selectAndUnselectTimeSlot(
                                                              time, true),
                                              child: TimeSlotWidget(
                                                dW: dW,
                                                time: time,
                                                checkSlotAvailability:
                                                    checkSlotAvailability,
                                                getPriceByTime: getPriceByTime,
                                              ),
                                            ),
                                          ),
                                        )
                                        .values
                                        .toList(),
                                    SizedBox(height: dW * 0.1),
                                  ],
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: dW * 0.03),
                  width: dW,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // if (sizeOrSportList.isNotEmpty)
                      //   SingleChildScrollView(
                      //     scrollDirection: Axis.horizontal,
                      //     padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
                      //     physics: const BouncingScrollPhysics(),
                      //     child: Column(
                      //       crossAxisAlignment: CrossAxisAlignment.start,
                      //       children: [
                      //         Padding(
                      //           padding:
                      //               EdgeInsets.symmetric(horizontal: dW * 0.03),
                      //           child: TextWidget(
                      //             title: widget.selectedTurf!.sportCategory!
                      //                         .categoryName ==
                      //                     'Outdoor'
                      //                 ? 'Select Turf Size:'
                      //                 : 'Select Sport:',
                      //             fontSize: tS * 13,
                      //             fontWeight: FontWeight.w600,
                      //             letterSpacing: .50,
                      //             color: Colors.black,
                      //           ),
                      //         ),
                      //         SizedBox(height: dW * 0.02),
                      //         Row(
                      //           children: [
                      //             ...sizeOrSportList
                      //                 .asMap()
                      //                 .map(
                      //                   (index, size) => MapEntry(
                      //                     index,
                      //                     GestureDetector(
                      //                       onTap: () {
                      //                         setTimeList(
                      //                           sport: size['title'],
                      //                           isLoad: true,
                      //                         );
                      //                       },
                      //                       child: TurfSizeOrSportWidget(
                      //                         dW: dW,
                      //                         tS: tS,
                      //                         size: size,
                      //                         index: index,
                      //                         selectedSizeOrSport:
                      //                             selectedSizeOrSport,
                      //                       ),
                      //                     ),
                      //                   ),
                      //                 )
                      //                 .values
                      //                 .toList(),
                      //           ],
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: dW * 0.04, bottom: dW * 0.04, left: dW * 0.06),
                        child: TextWidget(
                          title:
                              '*Max $totalPlayersAllowed members allowed per session ',
                          color: Color(0xffD84848),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.06),
                        child: Column(
                          children: [
                            SizedBox(height: dW * 0.01),
                            Divider(
                              color: getDividerColor(context),
                              thickness: 1,
                            ),
                            SizedBox(height: dW * 0.01),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                IntrinsicHeight(
                                  child: Row(
                                    children: [
                                      TextWidget(
                                        title: '\u20b9${getTotalAmount()}',
                                        fontWeight: FontWeight.w500,
                                        color: getThemeColor(),
                                      ),
                                      VerticalDivider(
                                        color: getGreyColor6(context),
                                        indent: dW * .008,
                                        endIndent: dW * .01,
                                        thickness: 1,
                                      ),
                                      TextWidget(
                                        title: getTotalAmount() == 0
                                            ? 'hh:mm - hh:mm'
                                            : '${double.parse(get12HrClockFormat(getTimeList()[0]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(getTimeList()[0].split(':')[0]))} - ${double.parse(get12HrClockFormat(getTimeList()[getTimeList().length - 1]).replaceAll(":", ".")).toStringAsFixed(2).replaceAll('.', ':')} ${getTimePeriod(double.parse(getTimeList()[getTimeList().length - 1].split(':')[0]))}',
                                        fontSize: getTotalAmount() == 0 ? 12 : 13,
                                        fontWeight: FontWeight.w500,
                                        color: const Color(0xFF124E00),
                                      ),
                                    ],
                                  ),
                                ),
                                CustomButton(
                                  width: dW * .25,
                                  height: dW * .1,
                                  radius: 8,
                                  buttonText: 'Book',
                                  fontSize: 14,
                                  onPressed: updateTime ? () {} : submit,
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
        );
  }
}
