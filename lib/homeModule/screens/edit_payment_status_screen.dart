import 'dart:io';

import 'package:bys_business/api.dart';
import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../navigators.dart';

class EditPaymentStatusScreen extends StatefulWidget {
  final BookingModel booking;

  EditPaymentStatusScreen({Key? key, required this.booking}) : super(key: key);

  @override
  State<EditPaymentStatusScreen> createState() =>
      _EditPaymentStatusScreenState();
}

class _EditPaymentStatusScreenState extends State<EditPaymentStatusScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  String selectedStatus = 'Fully Paid';
  late UserModal user;

  TextEditingController advanceController = TextEditingController();

  setPaymentStatus() {
    if (widget.booking.amountPaid == 0) {
      selectedStatus = 'Unpaid/Cash';
    } else if (widget.booking.totalAmount == widget.booking.amountPaid) {
      selectedStatus = 'Fully Paid';
    } else if (widget.booking.amountPaid > 0 &&
        widget.booking.totalAmount != widget.booking.amountPaid) {
      selectedStatus = 'Partially Paid/Advance';
      advanceController.text = widget.booking.amountPaid.toString();
    }
  }

  selectStatus(String status) {
    if (isLoading) return;
    setState(() => selectedStatus = status);
  }

  isButtonEnable() {
    if (selectedStatus == '') {
      return false;
    } else if (selectedStatus == 'Partially Paid/Advance') {
      if (advanceController.text.isEmpty || advanceController.text == '0') {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  update() {
    if (isLoading) return;
    if (selectedStatus == 'Partially Paid/Advance') {
      hideKeyBoard(context);
      double amount = double.parse(advanceController.text);
      if (amount > widget.booking.totalAmount) {
        return showSnackbar(
            'Advance amount cannot be greater than total amount');
      }
    }

    showDialog(
        context: context,
        builder: ((context) => CustomDialog(
            title:
                'Are you sure you want to update the payment status of this booking?',
            noText: 'Cancel',
            yesText: 'Yes, Update',
            noFunction: () {
              pop();
            },
            yesFunction: () {
              pop();
              updatePaymentStatus();
            })));
  }

  updatePaymentStatus() async {
    try {
      setState(() => isLoading = true);

      final result = await Provider.of<HomeProvider>(context, listen: false)
          .updatePaymentStatus(
        accessToken: user.accessToken,
        bookingId: widget.booking.id,
        amount: advanceController.text.isEmpty ? '0' : advanceController.text,
        status: selectedStatus,
      );

      if (result != null) {
        showSnackbar(
          'Payment status updated successfully',
          color: greenPrimary,
        );
        Navigator.of(context).pop(result);
      } else {
        showSnackbar('Unable to update the payment status');
      }
    } catch (e) {
      showSnackbar('Something went wrong');
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    setPaymentStatus();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      appBar: CustomAppBar(dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    bool isOnline = widget.booking.bookingMode == 'Online';
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: dW * 0.05),
                    TextWidget(
                      title:
                          '*If booking is online then Unpaid/Cash option will be disabled.',
                      color: Color(0xffACACB4),
                      fontSize: 12,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: dW * 0.035),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextWidget(title: 'Booking Amount:', fontSize: 15),
                        Container(
                          constraints: BoxConstraints(maxWidth: dW * 0.5),
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: TextWidget(
                              title:
                                  '\u20b9 ${widget.booking.totalAmount.toString()}',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: dW * 0.035),
                    TextWidget(title: 'Payment Status:', fontSize: 16),
                    SizedBox(height: dW * 0.05),
                    ...['Fully Paid', 'Partially Paid/Advance', 'Unpaid/Cash']
                        .map(
                      (status) => GestureDetector(
                        onTap: isOnline && status == 'Unpaid/Cash'
                            ? null
                            : () => selectStatus(status),
                        child: Container(
                          margin: EdgeInsets.only(bottom: dW * 0.05),
                          color: Colors.transparent,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: dW * 0.04, top: dW * 0.005),
                                    height: dW * 0.05,
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: selectedStatus == status
                                            ? Theme.of(context).primaryColor
                                            : Color(0xffB6B7BA),
                                      ),
                                    ),
                                    child: CircleAvatar(
                                      radius: 8,
                                      backgroundColor: selectedStatus == status
                                          ? Theme.of(context).primaryColor
                                          : Colors.white,
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      TextWidget(
                                        title: status,
                                        fontSize: 16,
                                        color:
                                            isOnline && status == 'Unpaid/Cash'
                                                ? Colors.grey
                                                : Colors.black,
                                      ),
                                      if (selectedStatus ==
                                              'Partially Paid/Advance' &&
                                          status == 'Partially Paid/Advance')
                                        Container(
                                          width: dW * 0.8,
                                          margin:
                                              EdgeInsets.only(top: dW * 0.035),
                                          child: CustomTextFieldWithLabel(
                                            label: '',
                                            controller: advanceController,
                                            hintText: 'Enter advance amount',
                                            inputFormatter: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly
                                            ],
                                            inputType: Platform.isIOS
                                                ? TextInputType
                                                    .numberWithOptions(
                                                        signed: true,
                                                        decimal: false)
                                                : TextInputType.number,
                                            maxLength: 10,
                                            prefixIcon:
                                                advanceController.text.isEmpty
                                                    ? null
                                                    : Icon(
                                                        Icons
                                                            .currency_rupee_rounded,
                                                        color: Colors.black,
                                                        size: 22,
                                                      ),
                                            onChanged: (value) =>
                                                setState(() {}),
                                          ),
                                        ),
                                    ],
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: dW * 0.1),
                  ],
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Update',
                isLoading: isLoading,
                onPressed: isButtonEnable() ? update : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
