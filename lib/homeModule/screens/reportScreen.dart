import 'dart:io' show Platform;

import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../common_function.dart';
import '../../homeModule/models/reportModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/generateReportBottomSheet.dart';
import '../../homeModule/widgets/reportWidget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ReportScreen extends StatefulWidget {
  final UserModal user;
  const ReportScreen({Key? key, required this.user}) : super(key: key);

  @override
  _ReportScreenState createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  List<ReportModel> listOfReports = [];

  fetchData() async {
    try {
      setState(() {
        isLoading = true;
      });
      await Provider.of<HomeProvider>(context, listen: false)
          .fetchBusinessReport(
        // businessId: widget.business == null
        //     ? widget.user.businessId
        //     : widget.business!.owner.businessId,
        businessId: widget.user.businessId,
        accessToken: widget.user.accessToken,
      );

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  generateReportBottomSheet() {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: GenerateReportBottomSheet(
          dW: dW,
          dH: dH,
          tS: tS,
          user: widget.user,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {});
  }

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfReports = Provider.of<HomeProvider>(context).reports;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return isLoading
        ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
        : Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
              children: [
                NewAppBar(dW: dW, title: 'Business Reports'),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () => fetchData(),
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      physics: BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: dW * 0.05),
                          if (listOfReports.isEmpty)
                            EmptyListWidget(
                              text: 'Generate Reports!',
                              topPadding: .3,
                              image: 'no_report',
                              imageSize: dW * 0.6,
                            ),
                          if (listOfReports.isNotEmpty)
                            ...listOfReports.map(
                              (report) => ReportWidget(
                                deviceWidth: dW,
                                deviceHeight: dH,
                                textScaleFactor: tS,
                                report: report,
                                accessToken: widget.user.accessToken,
                              ),
                            ),
                          SizedBox(height: dW * 0.18),
                        ],
                      ),
                    ),
                  ),
                ),
                BottomAlignedWidget(
                  dW: dW,
                  dH: dW * 0.13,
                  child: CustomButton(
                    width: dW,
                    height: dW * 0.13,
                    radius: 8,
                    buttonText: 'Generate Report',
                    onPressed: generateReportBottomSheet,
                    fontSize: 16,
                  ),
                )
              ],
            ),
        );
  }
}
