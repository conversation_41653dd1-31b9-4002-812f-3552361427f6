import 'dart:async';
import 'dart:io';

import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../common_function.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SearchBooking extends StatefulWidget {
  SearchBooking({Key? key}) : super(key: key);

  @override
  _SearchBookingState createState() => _SearchBookingState();
}

class _SearchBookingState extends State<SearchBooking> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isLoading = false;
  bool lazyLoading = false;
  List<BookingModel> listOfBookings = [];

  String message = 'Search bookings by venue, address, size/sport or user.';

  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  FocusNode _searchFocusNode = FocusNode();
  Timer? _debounce;

  TextEditingController searchController = TextEditingController();

  onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 800), () async {
      await searchBookings();
    });
  }

  searchBookings() async {
    try {
      if (searchController.text.trim() == '') {
        setState(() {
          listOfBookings = [];
          message = 'Search bookings by venue, address, size/sport or user.';
        });
        return;
      }
      setState(() {
        isLoading = true;
      });
      List<String> turfId = [];

      if (user.business != null) {
        user.business!.turfs.forEach((turf) {
          turfId.add(turf.id);
        });
      }
      final data = await Provider.of<HomeProvider>(context, listen: false)
          .searchBookingNew(
        accessToken: user.accessToken,
        business: user.businessId,
        searchedString: searchController.text.trim(),
        turfs: turfId,
        role: user.business == null ? 'Business' : 'Employee',
      );
      if (data.length == 0) {
        setState(() {
          listOfBookings = [];
          message = 'Bookings not found';
        });
      } else {
        setState(() {
          listOfBookings = data;
        });
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: deviceHeight,
        width: deviceWidth,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              
              NewAppBar(dW: deviceWidth, title: 'Search Bookings'),
              SizedBox(height: deviceWidth * 0.07),
              Container(
                height: deviceWidth * 0.12,
                alignment: Alignment.center,
                margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 2,
                      spreadRadius: .2,
                      color: Colors.grey.withOpacity(0.3),
                    )
                  ],
                ),
                child: TextFormField(
                  controller: searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    hintText: 'Search here...',
                    hintStyle: TextStyle(
                      color: Colors.black,
                      fontSize: textScaleFactor * 12,
                    ),
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: SvgPicture.asset(
                        'assets/svgIcons/search.svg',
                        height: 0,
                        width: 0,
                        color: Colors.black,
                      ),
                    ),
                    suffixIcon: _searchFocusNode.hasFocus
                        ? GestureDetector(
                            onTap: () {
                              searchController.text = '';
                              _searchFocusNode.unfocus();
                              setState(() {
                                message =
                                    'Search bookings by venue, address, size/sport or user.';
                                listOfBookings = [];
                              });
                            },
                            child: Icon(
                              Icons.clear_rounded,
                              size: 20,
                              color: Colors.black,
                            ),
                          )
                        : SizedBox.shrink(),
                    border: InputBorder.none,
                  ),
                  onChanged: onSearchChanged,
                ),
              ),
              Expanded(
                child: isLoading
                    ? CircularLoader(
                        android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
                    : listOfBookings.isEmpty
                        ? Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: deviceWidth * 0.13,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              message,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: textScaleFactor * 13,
                                letterSpacing: .5,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          )
                        : Container(
                            padding: EdgeInsets.only(top: deviceHeight * 0.01),
                            child: SingleChildScrollView(
                              padding: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.05),
                              physics: BouncingScrollPhysics(),
                              controller: _scrollController,
                              child: Column(
                                children: [
                                  SizedBox(height: deviceWidth * 0.03),
                                  ...listOfBookings.map(
                                    (booking) {
                                      return BookingWidget(
                                        fromViewAll: true,
                                        booking: booking,
                                        user: user,
                                        deviceWidth: deviceWidth,
                                        textScaleFactor: textScaleFactor,
                                      );
                                    },
                                  ),
                                  SizedBox(height: deviceWidth * 0.05),
                                ],
                              ),
                            ),
                          ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
