// ignore_for_file: unrelated_type_equality_checks

import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/homeModule/widgets/add_extra.dart';
import 'package:bys_business/homeModule/widgets/select_venue_sheet.dart';

import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/radio_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../employeeModule/models/business_model.dart';
import 'package:flutter/services.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../homeModule/screens/successScreen.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../../skeletonLoader/bookingTurfSummarySkeletonLoader.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../homeModule/widgets/bookingSummaryCard.dart';
import '../../fontSizes.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../homeModule/widgets/rentaltemWidget.dart';
import '../../venueModule/models/venue_model.dart';
import '../../homeModule/models/bookingModel.dart';
import '../models/extra_item_model.dart';
import '../widgets/user_detail_bottomsheet.dart';

class BookingSummaryScreen extends StatefulWidget {
  final DateTime bookingDate;
  final List selectedTime;
  final Venue turf;
  final String sizeOrSport;
  final SportType sport;
  final String time;
  final double amount;
  final String phone;
  final String name;
  final int quantity;
  final UserModal user;
  // final Slot selectedSlot;
  String label;

  BookingSummaryScreen({
    Key? key,
    required this.turf,
    required this.phone,
    required this.name,
    required this.selectedTime,
    required this.bookingDate,
    required this.time,
    required this.sport,
    required this.amount,
    required this.label,
    // required this.selectedSlot,
    this.quantity = 0,
    required this.user,
    required this.sizeOrSport,
  }) : super(key: key);

  @override
  _BookingSummaryScreenState createState() => _BookingSummaryScreenState();
}

class _BookingSummaryScreenState extends State<BookingSummaryScreen>
    with WidgetsBindingObserver {
  bool isLoading = false;
  bool booking = false;
  bool loadConvenienceCharges = false;
  double tS = 0.0;
  double dH = 0.0;
  double dW = 0.0;
  String totalHour = '';
  late UserModal user;
  Timer? _timer;
  int _start = 0;
  var blockedSlot;
  int bookingTime = 0;
  double total = 0.0;
  double discountedTotal = 0.0;
  List<RentalItem> rentals = [];
  double discountPercentage = 0.0;
  double advancePayment = 0.0;
  List<RentalItem> listOfRentalItems = [];
  int currentIndex = 1;

  TextEditingController amountController = TextEditingController();
  TextEditingController discountController = TextEditingController();
  TextEditingController noteController = TextEditingController();
  TextEditingController gstController = TextEditingController();
  FocusNode amountNode = FocusNode();

  String invoiceType = '';

  List paymentStatus = [
    {'index': 1, 'title': 'Fully Paid', 'isSelected': true},
    {'index': 2, 'title': 'Partially Paid/Advance', 'isSelected': false},
    {'index': 3, 'title': 'Unpaid/Cash', 'isSelected': false},
  ];

  String timesUp = '';
  void startTimer() {
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(oneSec, (Timer timer) {
      if (_start == 0) {
        if (this.mounted) {
          setState(() {
            timer.cancel();
          });
        }
      } else {
        if (this.mounted) {
          if (_start != 0) {
            setState(() {
              _start--;
            });
          }
        }
      }
    });
  }

  showPop() async {
    updateQuantity();

    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          title: Text(
            "Time's up",
            style: Theme.of(context).textTheme.displayLarge!.copyWith(
              fontSize: tS * displayMedium,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            '$timesUp',
            style: Theme.of(context).textTheme.displayLarge!.copyWith(
              fontSize: tS * displayLarge,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'OK',
                style: Theme.of(context).textTheme.displayLarge!.copyWith(
                  fontSize: tS * displayLarge,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onPressed: () {
                pop({'value': true, 'goBack': false});
                pop({'value': true, 'goBack': false});
              },
            ),
          ],
        );
      },
    );
  }

  double totalMinutes() {
    // var totalTime = widget.selectedTime.length / 2;
    // if (currentIndex == 1 || currentIndex == 3) {
    //   return totalTime * widget.turf.convenienceFeeForFullPay;
    // } else {
    //   return totalTime * widget.turf.convenienceFeeForAdvancePay;
    // }
    return 0;
  }

  double totalConvenienceFee() {
    return (totalMinutes()) + ((totalMinutes()) * 0.18);
  }

  createBooking() async {
    if (booking) return;
    double dsAmount =
        discountController.text.isEmpty
            ? 0
            : double.parse(discountController.text);

    if ((currentIndex == 2) && amountController.text.trim().length == 0) {
      return showSnackbar('Enter paid amount');
    }
    if (currentIndex == 2) {
      if (double.parse(amountController.text.trim()) == 0) {
        return showSnackbar('Please enter amount greater than zero');
      } else if (double.parse(amountController.text.trim()) >
          (total - dsAmount)) {
        return showSnackbar('Please enter amount less than total amount');
      }
    }

    if (discountController.text.isNotEmpty &&
        double.parse(discountController.text.trim()) > total) {
      return showSnackbar('Discount cannot be greater than total amount');
    }

    try {
      setState(() {
        booking = true;
      });
      var rental = [];
      List<ExtraItem> extras = [];

      bool net =
          widget.turf.sportCategory!.categoryName != 'Outdoor'
              ? false
              : widget.turf.isNet!;
      List turfSizes = [];

      if (net) {
        widget.turf.slots!.forEach((slot) {
          slot.priceAndQuantity.forEach((price) {
            if (!turfSizes.contains(price.title)) {
              turfSizes.add(price.title);
            }
          });
        });
      }

      listOfRentalItems.forEach((rent) {
        if (rent.quantity > 0) {
          rental.add({
            'productId': rent.id,
            'productName': rent.productName,
            'productImage': rent.productImage,
            'quantity': rent.quantity,
            'duration': rent.duration,
            'price': rent.price,
          });
        }
      });

      Provider.of<HomeProvider>(context, listen: false).extraItems.forEach((
        extra,
      ) {
        if (extra.quantity > 0) {
          extras.add(
            ExtraItem(
              name: extra.name,
              quantity: extra.quantity,
              price: extra.price,
            ),
          );
        }
      });

      var sport;
      if (widget.turf.sportCategory!.categoryName != 'Outdoor') {
        sport = Provider.of<Auth>(
          context,
          listen: false,
        ).getSportByTitle(widget.sport.sport);
      }

      String startTime = widget.selectedTime[0].toString().replaceAll(':', '.');
      String endTime = widget.selectedTime[widget.selectedTime.length - 1]
          .toString()
          .replaceAll(':', '.');

      // Check if start time is '00:00' and replace it with '12:00'
      //       if (startTime == '00.00') {
      //         startTime = '12.00';
      //       }

      // // Check if end time is '00:00' and replace it with '12:00'
      //       if (endTime == '00.00') {
      //         endTime = '12.00';
      //       }

      var data = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).createBooking(
        paymentType: '',
        turfId: widget.turf.id,
        sportCategory: widget.turf.sportCategory!.id,
        fcmToken: user.fcmToken!,
        businessId: user.businessId,
        sizeOrSport: widget.sizeOrSport,
        sport:
            widget.turf.sportCategory!.categoryName != 'Outdoor'
                ? sport['id']
                : widget.sport.id,
        bookingDate: widget.bookingDate.toString(),
        paymentStatus: getPaymentStatus(),
        bookingStatus: getBookingStatus(),
        totalAmount:
            invoiceType == '' || invoiceType == 'Inclusive'
                ? total + totalConvenienceFee()
                : total + totalConvenienceFee() + getTotalGST(),
        discountedAmount: dsAmount,
        convenienceFee: (totalMinutes()).toDouble(),
        gstOnConvenienceFee: (totalMinutes()) * 0.18,
        amountPaid: getAmountPaid(dsAmount),
        startTime: startTime,
        // widget.selectedTime[0].toString().replaceAll(':', '.'),
        endTime: endTime,
        //  widget.selectedTime[widget.selectedTime.length - 1]
        //     .toString()
        //     .replaceAll(':', '.'),
        name: widget.name,
        phone: widget.phone,
        accessToken: user.accessToken,
        rental: rental,
        extras: extras,
        label: widget.label,
        option: widget.turf.option!,
        note: noteController.text.trim(),
        employeeId:
            widget.user.business == null
                ? null
                : widget.user.business!.employee.id,
        bookedBy: widget.user.role,
        employeeMappingId:
            widget.user.business == null
                ? null
                : widget.user.business!.mappingId,
        invoiceType: invoiceType,
        tax: getTotalGST(),
        customerGSTNo: gstController.text.trim(),
      );

      if (data['success']) {
        Provider.of<HomeProvider>(context, listen: false).clearExtraItems();
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => SuccessScreen(text: 'Booked', user: user),
          ),
          (route) => false,
        );
      } else {
        if (data['message'] == 'TURF_INACTIVE') {
          showSnackbar(
            'Turf is deactivated by admin. Contact admin for further information.',
          );
        } else {
          showSnackbar('Something went wrong');
        }
      }

      setState(() {
        booking = false;
      });
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
      setState(() {
        booking = false;
      });
    }
  }

  // checkIsNine(List turfSizes) {
  //   if (turfSizes.length == 1 && turfSizes.contains('6:6')) {
  //     return false;
  //   } else if ((turfSizes.contains('9:9') ||
  //           turfSizes.contains('7:7') ||
  //           turfSizes.contains('5:5') ||
  //           (turfSizes.contains('6:6'))) &&
  //       !turfSizes.contains('8:8')) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // }
  checkIsNine(List turfSizes) {
    if (turfSizes.length == 1 && turfSizes.contains('6:6')) {
      return false;
    } else if (turfSizes.length == 1 && turfSizes.contains('5:5')) {
      return false;
    } else if (turfSizes.contains('5:5') &&
        turfSizes.contains('6:6') &&
        !turfSizes.contains('9:9') &&
        !turfSizes.contains('7:7')) {
      return false;
    } else if ((turfSizes.contains('9:9') || turfSizes.contains('7:7')) &&
        !turfSizes.contains('8:8')) {
      return true;
    } else {
      return false;
    }
  }

  getPaymentStatus() {
    if (currentIndex == 1) {
      return 'SUCCESS';
    } else if (currentIndex == 2 || currentIndex == 3) {
      return 'OFFLINE';
    }
  }

  getBookingStatus() {
    if (currentIndex == 1) {
      return 'PENDING';
    } else if (currentIndex == 2) {
      return 'PARTIALLYPAID';
    } else if (currentIndex == 3) {
      return 'UNPAID';
    }
  }

  getAmountPaid(double dsAmount) {
    if (currentIndex == 1) {
      return invoiceType == '' || invoiceType == 'Inclusive'
          ? (total + totalConvenienceFee() - dsAmount)
          : (total + totalConvenienceFee() - dsAmount) + getTotalGST();
    } else if (currentIndex == 2) {
      return double.parse(amountController.text.trim());
    } else if (currentIndex == 3) {
      return 0.0;
    }
  }

  getDuration(int minutes) {
    if (minutes != 0) {
      int remainder = 0;
      int quotient = 0;
      String min = '';
      String sec = '';
      quotient = minutes ~/ 60;
      remainder = minutes % 60;
      min = '$quotient';
      sec = "$remainder";

      if (quotient < 9) {
        min = '0$quotient';
      } else {
        min = '$quotient';
      }

      if (remainder < 9) {
        sec = '0$remainder';
      } else {
        sec = '$remainder';
      }
      return '$min:$sec';
    } else {
      return '00:00';
    }
  }

  myInit() async {
    try {
      setState(() {
        isLoading = true;
      });
      Map turfQuanity = {};
      widget.turf.slots!.forEach((slot) {
        slot.priceAndQuantity.forEach((size) {
          if (!turfQuanity.containsKey(size.title)) {
            turfQuanity[size.title] = size.quantity;
          }
        });
      });
      blockedSlot = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).insertBookingSlot(
        turfId: widget.turf.id,
        startTime: widget.selectedTime[0].toString().replaceAll(':', '.'),
        endTime: widget.selectedTime[widget.selectedTime.length - 1]
            .toString()
            .replaceAll(':', '.'),
        bookingDate: widget.bookingDate.toString(),
        startDate:
            DateTime(
              widget.bookingDate.year,
              widget.bookingDate.month,
              widget.bookingDate.day,
              0,
              0,
              0,
            ).toString(),
        endDate:
            DateTime(
              widget.bookingDate.year,
              widget.bookingDate.month,
              widget.bookingDate.day,
              23,
              59,
              59,
            ).toString(),
        sizeOrSport: widget.sizeOrSport,
        turfQuantity:
            widget.turf.sportCategory!.categoryName != 'Outdoor'
                ? widget.quantity
                : widget.turf.isNet!
                ? turfQuanity
                : widget.quantity,
        isNet:
            widget.turf.sportCategory!.categoryName != 'Outdoor'
                ? false
                : widget.turf.isNet!,
        sport: widget.sport.sport,
      );
      final result = await Provider.of<Auth>(
        context,
        listen: false,
      ).fetchCommonAppConfig("Booking");
      result.forEach((data) {
        if (data['type'] == 'Razorpay') {
        } else if (data['type'] == 'DiscountPercentage') {
          discountPercentage = double.parse(data['value']);
        } else if (data['type'] == 'BookingTime') {
          bookingTime = int.parse(data['value']);
        } else if (data['type'] == 'Payme/Advancent') {
          advancePayment = double.parse(data['value']);
        } else if (data['type'] == 'TimeUp') {
          timesUp = data['value'];
        }
      });
      // total = widget.amount * (widget.selectedTime.length / 2);
      total = widget.amount;
      var sport;
      if (widget.turf.sportCategory!.categoryName != 'Outdoor') {
        sport = Provider.of<Auth>(
          context,
          listen: false,
        ).getSportByTitle(widget.sport.sport);
      }

      listOfRentalItems = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).fetchRentalItemBySport(
        turf: widget.turf.id,
        sport:
            widget.turf.sportCategory!.categoryName != 'Outdoor'
                ? sport['id']
                : widget.sport.id,
      );
      _start = bookingTime * 60;
      startTimer();

      setState(() {
        isLoading = false;
      });
      // Future.delayed(Duration(minutes: bookingTime)).then((value) {
      //   return showPop();
      // });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
      Navigator.of(context).pop({'value': true, 'goBack': false});
    }
  }

  getExtraItemTotalPrice() {
    double extra = 0.0;
    extra =
        Provider.of<HomeProvider>(
          context,
          listen: false,
        ).getTotalExtraItemsPrice();

    total += extra;
    setState(() {});
    return extra;
  }

  getSelectedRentalItemsPrice() {
    // total = widget.amount * (widget.selectedTime.length / 2);
    total = widget.amount;
    double totalRent = 0.0;
    rentals = listOfRentalItems.where((rent) => rent.quantity > 0).toList();
    print(rentals);
    rentals.forEach((rent) {
      totalRent += rent.price * rent.quantity;
    });
    setState(() {});

    total += totalRent;
    return totalRent;
  }

  updateQuantity() async {
    await Provider.of<HomeProvider>(context, listen: false).updateTurfQuantity(
      turfId: widget.turf.id,
      startTime: widget.selectedTime[0].toString().replaceAll(':', '.'),
      endTime: widget.selectedTime[widget.selectedTime.length - 1]
          .toString()
          .replaceAll(':', '.'),
      startDate:
          DateTime(
            widget.bookingDate.year,
            widget.bookingDate.month,
            widget.bookingDate.day,
            0,
            0,
            0,
          ).toString(),
      endDate:
          DateTime(
            widget.bookingDate.year,
            widget.bookingDate.month,
            widget.bookingDate.day,
            23,
            59,
            59,
          ).toString(),
      sizeOrSport: widget.sizeOrSport,
      isNet:
          widget.turf.sportCategory!.categoryName != 'Outdoor'
              ? false
              : widget.turf.isNet!,
      updateQuantity: false,
      sport: widget.sport.sport,
      option: widget.turf.option!,
    );
  }

  setPaymentStatus(int index) {
    paymentStatus.forEach((status) {
      if (status['index'] == index) {
        setState(() {
          status['isSelected'] = true;
          currentIndex = index;
        });
      } else {
        setState(() {
          status['isSelected'] = false;
        });
      }
    });
    amountController.clear();
    if (currentIndex == 3) {
      amountController.text = widget.amount.toString();
      setState(() {});
    }
  }

  showExitDialogBox() {
    return showDialog(
      context: context,
      builder:
          ((context) => CustomDialog(
            title: 'Are you sure you want to discard this booking?',
            noText: 'Yes, Cancel',
            yesText: 'No',
            noFunction: () {
              updateQuantity();
              pop({'value': true, 'goBack': false});
              pop({'value': true, 'goBack': false});
            },
            yesFunction: () {
              Provider.of<HomeProvider>(
                context,
                listen: false,
              ).clearExtraItems();
              pop();
            },
          )),
    );
  }

  num getTotalGST() {
    num totalGst = 0;

    double totalAmount =
        total +
        getExtraItemTotalPrice() +
        totalConvenienceFee() -
        (discountController.text.isEmpty
            ? 0
            : double.parse(discountController.text));

    totalGst = (totalAmount * 18) / 100;
    return totalGst;
  }

  Widget getBookingSummaryText(
    String title,
    String value, {
    num fontSize = 14,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: dW * 0.025),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: TextWidget(
              title: title,
              fontWeight: fontWeight ?? FontWeight.w400,
              color: color ?? getGreyColor2(context),
              fontSize: tS * fontSize,
              textOverflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: dW * 0.02),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: dW * 0.5),
            child: TextWidget(
              title: value,
              fontWeight: fontWeight ?? FontWeight.w600,
              fontSize: tS * fontSize,
              color: color ?? getGreyColor2(context),
              maxLines: 2,
              textOverflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    user = Provider.of<Auth>(context, listen: false).user;
    myInit();
    print(widget.sport);
    double time1 = double.parse(
      widget.selectedTime[0].toString().replaceAll(':', '.'),
    );
    double time2 = double.parse(
      widget.selectedTime[widget.selectedTime.length - 1].toString().replaceAll(
        ':',
        '.',
      ),
    );

    DateTime hr1 = DateTime.now().add(
      Duration(
        hours: int.parse(time1.toStringAsFixed(2).split('.')[0]),
        minutes: int.parse(time1.toStringAsFixed(2).split('.')[1]),
      ),
    );

    DateTime hr2 = DateTime.now().add(
      Duration(
        hours: int.parse(time2.toStringAsFixed(2).split('.')[0]),
        minutes: int.parse(time2.toStringAsFixed(2).split('.')[1]),
      ),
    );

    totalHour = getDuration(hr2.difference(hr1).inMinutes);
  }

  @override
  void dispose() {
  
    super.dispose();
    _timer!.cancel();

    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    print('State: $state');
    if (state == AppLifecycleState.inactive) {
      updateQuantity();
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return WillPopScope(
      onWillPop: () async {
        Provider.of<HomeProvider>(context, listen: false).clearExtraItems();
        showExitDialogBox();
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return isLoading
        ? BookTurfOfflineSummarySkeletonLoader(deviceWidth: dW)
        : GestureDetector(
          onTap: () => hideKeyBoard(context),
          child: Container(
            height: dH,
            width: dW,
            padding:
                Platform.isIOS
                    ? EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top,
                      bottom: dH * 0.02,
                    )
                    : EdgeInsets.only(top: dH * 0.02, bottom: dH * 0.02),
            child: Column(
              children: [
                SizedBox(height: dW * 0.025),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NewAppBar(
                      dW: dW,
                      title: 'Booking Summary',
                      onTap: showExitDialogBox,
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: dW * 0.15,
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.02),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            margin: EdgeInsets.symmetric(vertical: dW * 0.01),
                            width: dW * 0.14,
                            alignment: Alignment.center,
                            child: SvgPicture.asset(
                              'assets/svgIcons/Clock.svg',
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          Container(
                            width: dW * 0.135,
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                              horizontal: dW * 0.02,
                            ),
                            child: FittedBox(
                              child: TextWidget(
                                title: getDuration(_start).toString(),
                                color: getThemeColor(),
                                fontSize: displayLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.01),
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: dW * 0.03),

                        Container(
                          width: dW,
                          padding: EdgeInsets.only(bottom: dW * 0.045),
                          margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              AssetSvgIcon(
                                iconName: 'Clock',
                                color: getThemeColor(),
                              ),
                              SizedBox(width: dW * 0.02),
                              TextWidget(
                                title:
                                    "You have $bookingTime mins to book the slot",
                                fontSize: 13,
                                color: getThemeColor(),
                                fontWeight: FontWeight.w600,
                              ),
                            ],
                          ),
                        ),

                        // GST Options
                        CustomContainer(
                          margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                title: 'Type of GST Invoice you want to sent?',
                              ),
                              SizedBox(height: dW * 0.025),
                              Row(
                                children: [
                                  ...['Inclusive', 'Exclusive'].map(
                                    (value) => GestureDetector(
                                      onTap: () {
                                        if (value == invoiceType) {
                                          invoiceType = '';
                                        } else {
                                          invoiceType = value;
                                        }
                                        setState(() {});
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(
                                          right: dW * 0.05,
                                        ),
                                        child: RadioWidget(
                                          active: invoiceType == value,
                                          activeColor:
                                              Theme.of(context).primaryColor,
                                          inActiveBorderColor:
                                              Theme.of(context).primaryColor,
                                          title: value,
                                          radius: 6,
                                          inactiveBorderRadius: 6,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: dW * 0.055),
                              CustomTextFieldWithLabel(
                                label: 'Customer GST No.',
                                controller: gstController,
                                optional: true,
                                hintText: 'Enter Gst  No. Here...',
                                inputAction: TextInputAction.done,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),

                        if (listOfRentalItems.isNotEmpty)
                          CustomContainer(
                            margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  title: 'You can rent',
                                  fontWeight: FontWeight.w600,
                                ),
                                SizedBox(height: dW * 0.02),
                                ...listOfRentalItems.map(
                                  (e) => RentalItemsWidget(
                                    deviceWidth: dW,
                                    textScaleFactor: tS,
                                    rentalItem: e,
                                    function: getSelectedRentalItemsPrice,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        SizedBox(height: dW * 0.05),
                        CustomContainer(
                          margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                title: 'Booking Summary',
                                fontWeight: FontWeight.w600,
                                color: getGreyColor2(context),
                              ),
                              SizedBox(height: dW * .04),
                              getBookingSummaryText(
                                'Date',
                                DateFormat(
                                  'dd-MM-yyyy',
                                ).format(widget.bookingDate),
                              ),
                              // if (widget.turf.sportCategory!.categoryName ==
                              //     'Outdoor')
                              if (widget.turf.sportCategory!.categoryName ==
                                  'Indoor')
                                getBookingSummaryText(
                                  'Sport',
                                  widget.sport.sport,
                                ),
                              if (widget.turf.sportCategory!.categoryName ==
                                  'Indoor')
                                getBookingSummaryText(
                                  'Game Type',
                                  widget.label == []
                                      ? widget.sizeOrSport.split('(')[0].trim()
                                      : '${widget.sizeOrSport.split('(')[0].trim()}',
                                ),
                              if (widget.turf.sportCategory!.categoryName ==
                                  'Outdoor')
                                getBookingSummaryText(
                                  'Turf Size',
                                  widget.label == []
                                      ? widget.sizeOrSport
                                      : '${widget.sizeOrSport}',
                                ),
                              // '${widget.sizeOrSport} (${widget.label})'),

                              // getBookingSummaryText(
                              //   widget.turf.sportCategory!.categoryName ==
                              //           'Outdoor'
                              //       ? "Turf Size"
                              //       : 'Type',
                              //   widget.label == ''
                              //       ? widget.sizeOrSport
                              //       : '${widget.sizeOrSport} (${widget.label})',
                              // ),
                              getBookingSummaryText('Time Slot', widget.time),
                              getBookingSummaryText('Booked By', widget.name),
                              DividerWidget(top: 0),
                              getBookingSummaryText(
                                'Basic Amount',
                                '\u20b9 ${widget.amount}',
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),

                              Consumer<HomeProvider>(
                                builder: (context, homeprovider, child) {
                                  return homeprovider.extraItems.isNotEmpty
                                      ? Column(
                                        children:
                                            homeprovider.extraItems
                                                .map(
                                                  (item) => Row(
                                                    children: [
                                                      Expanded(
                                                        child: getBookingSummaryText(
                                                          item.name,
                                                          '+ \u20b9 ${item.price * item.quantity}',
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                                .toList(),
                                      )
                                      : SizedBox.shrink();
                                },
                              ),
                              if (discountController.text.isNotEmpty) ...[
                                getBookingSummaryText(
                                  'Discount',
                                  '- \u20b9 ${discountController.text}',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                ),
                              ],
                              if (totalMinutes() != 0)
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        loadConvenienceCharges =
                                            !loadConvenienceCharges;
                                        setState(() {});
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            alignment: Alignment.topLeft,
                                            width: dW * 0.3,
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: TextWidget(
                                                title: 'Convenience Fee',
                                                fontSize: displayLarge,
                                                color: Colors.black,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: dW * 0.02),
                                          SvgPicture.asset(
                                            "assets/svgIcons/${loadConvenienceCharges ? 'chevron-up' : 'down-arrow'}.svg",
                                            width: dW * 0.04,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      alignment: Alignment.topRight,
                                      width: dW * 0.4,
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: TextWidget(
                                          title:
                                              '\u20b9 ${totalConvenienceFee()}',
                                          fontWeight: FontWeight.w600,
                                          color: getGreyColor2(context),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              if (loadConvenienceCharges) ...[
                                SizedBox(height: dW * .025),
                                getBookingSummaryText(
                                  '     Base Amount',
                                  '\u20b9 ${(totalMinutes()).toStringAsFixed(2)}',
                                  fontSize: 12,
                                ),
                                getBookingSummaryText(
                                  '     GST (18%)',
                                  '${((totalMinutes()) * 0.18).toStringAsFixed(2)}',
                                  fontSize: 12,
                                ),
                              ],

                              if (getSelectedRentalItemsPrice() != 0.0)
                                getBookingSummaryText(
                                  'Rental Items',
                                  "\u20b9 ${getSelectedRentalItemsPrice()}",
                                  fontWeight: FontWeight.w600,
                                ),
                              DividerWidget(top: 0),
                              if (invoiceType != '') ...[
                                getBookingSummaryText(
                                  'Sub Total',
                                  invoiceType == 'Inclusive'
                                      ? '\u20b9 ${(total + getExtraItemTotalPrice() + totalConvenienceFee() - (discountController.text.isEmpty ? 0 : double.parse(discountController.text)) - getTotalGST()).toStringAsFixed(2)}'
                                      : '\u20b9 ${(total + getExtraItemTotalPrice() + totalConvenienceFee() - (discountController.text.isEmpty ? 0 : double.parse(discountController.text))).toStringAsFixed(2)}',
                                ),
                                getBookingSummaryText(
                                  'GST(18%)',
                                  '+ \u20b9 ${getTotalGST().toStringAsFixed(2)}',
                                ),
                                DividerWidget(top: 0),
                                getBookingSummaryText(
                                  'Total',
                                  invoiceType == 'Inclusive'
                                      ? '\u20b9 ${(total + getExtraItemTotalPrice() + totalConvenienceFee() - (discountController.text.isEmpty ? 0 : double.parse(discountController.text))).toStringAsFixed(2)}'
                                      : '\u20b9 ${(total + totalConvenienceFee() + getExtraItemTotalPrice() - (discountController.text.isEmpty ? 0 : double.parse(discountController.text)) + getTotalGST()).toStringAsFixed(2)}',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                ),
                              ],
                              if (invoiceType == '')
                                getBookingSummaryText(
                                  'Total',
                                  '\u20b9 ${(total + getExtraItemTotalPrice() + totalConvenienceFee() - (discountController.text.isEmpty ? 0 : double.parse(discountController.text))).toStringAsFixed(2)}',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                ),
                            ],
                          ),
                        ),

                        // CustomContainer(
                        //   vPadding: 0.02,

                        //   width: double.infinity,
                        //   margin: EdgeInsets.symmetric(
                        //     horizontal: dW * 0.05,
                        //     vertical: dW * 0.05,
                        //   ),
                        //   child: Column(
                        //     crossAxisAlignment: CrossAxisAlignment.start,

                        //     children: [
                        //       Row(
                        //         mainAxisSize: MainAxisSize.max,
                        //         children: [
                        //           Expanded(
                        //             child: TextWidget(
                        //               title: 'Extras',
                        //               fontWeight: FontWeight.w600,
                        //             ),
                        //           ),

                        //           InkWell(
                        //             enableFeedback: true,
                        //             onTap: () {
                        //               openVenueBottomSheet();
                        //             },
                        //             child: CustomContainer(
                        //               hPadding: 0.01,
                        //               vPadding: 0.01,
                        //               child: Icon(
                        //                 Icons.add,
                        //                 color: getThemeColor(),
                        //               ),
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //       Consumer<HomeProvider>(
                        //         builder: (context, homeprovider, child) {
                        //           return homeprovider.extraItems.isNotEmpty
                        //               ? Column(
                        //                 children:
                        //                     homeprovider.extraItems
                        //                         .map(
                        //                           (item) => Container(
                        //                             margin: EdgeInsets.only(
                        //                               bottom: dW * 0.02,
                        //                               top: dW * 0.02,
                        //                             ),
                        //                             child: Row(
                        //                               children: [
                        //                                 InkWell(
                        //                                   onTap: () {
                        //                                     homeprovider
                        //                                         .removeExtraItem(
                        //                                           homeprovider
                        //                                               .extraItems
                        //                                               .indexOf(
                        //                                                 item,
                        //                                               ),
                        //                                         );
                        //                                     setState(() {});
                        //                                   },
                        //                                   child: Icon(
                        //                                     Icons
                        //                                         .delete_outline,
                        //                                     color: Colors.red,
                        //                                   ),
                        //                                 ),
                        //                                 SizedBox(
                        //                                   width: dW * 0.02,
                        //                                 ),
                        //                                 Expanded(
                        //                                   flex: 5,
                        //                                   child: TextWidget(
                        //                                     textOverflow:
                        //                                         TextOverflow
                        //                                             .ellipsis,
                        //                                     title: item.name,
                        //                                     fontWeight:
                        //                                         FontWeight.w600,
                        //                                   ),
                        //                                 ),
                        //                                 Row(
                        //                                   mainAxisSize:
                        //                                       MainAxisSize.min,
                        //                                   children: [
                        //                                     IconButton(
                        //                                       onPressed: () {
                        //                                         homeprovider.updateExtraItemQuantity(
                        //                                           homeprovider
                        //                                               .extraItems
                        //                                               .indexOf(
                        //                                                 item,
                        //                                               ),
                        //                                           item.quantity -
                        //                                               1,
                        //                                         );
                        //                                         setState(() {});
                        //                                       },
                        //                                       icon: Icon(
                        //                                         Icons
                        //                                             .remove_circle,
                        //                                         color:
                        //                                             getThemeColor(),
                        //                                       ),
                        //                                     ),
                        //                                     TextWidget(
                        //                                       title:
                        //                                           '${item.quantity}',
                        //                                       fontWeight:
                        //                                           FontWeight
                        //                                               .w600,
                        //                                     ),
                        //                                     IconButton(
                        //                                       onPressed: () {
                        //                                         homeprovider.updateExtraItemQuantity(
                        //                                           homeprovider
                        //                                               .extraItems
                        //                                               .indexOf(
                        //                                                 item,
                        //                                               ),
                        //                                           item.quantity +
                        //                                               1,
                        //                                         );
                        //                                         setState(() {});
                        //                                       },
                        //                                       icon: Icon(
                        //                                         Icons
                        //                                             .add_circle,
                        //                                         color:
                        //                                             getThemeColor(),
                        //                                       ),
                        //                                     ),
                        //                                   ],
                        //                                 ),
                        //                               ],
                        //                             ),
                        //                           ),
                        //                         )
                        //                         .toList(),
                        //               )
                        //               : SizedBox.shrink();
                        //         },
                        //       ),
                        //     ],
                        //   ),
                        // ),

                        CustomContainer(
                          margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          vPadding: 0.02,
                          child: CustomTextFieldWithLabel(
                            label: 'Note',
                            controller: noteController,
                            optional: true,
                            hintText: 'Write your note here.....',
                            maxLines: 3,
                            maxLength: 250,
                            textFS: 14,
                            labelFW: FontWeight.w600,
                            counterText:
                                '${noteController.text.trim().length}/250',
                            onChanged: (value) => setState(() {}),
                          ),
                        ),

                        SizedBox(height: dW * 0.05),

                        CustomContainer(
                          margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          hPadding: 0,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: EdgeInsets.symmetric(
                                  horizontal: dW * 0.05,
                                ),
                                child: TextWidget(
                                  title: 'Payment Status:',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: dW * 0.03),
                              ...paymentStatus.map(
                                (payment) => GestureDetector(
                                  onTap:
                                      () => setPaymentStatus(payment['index']),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.05,
                                      vertical: dW * 0.03,
                                    ),
                                    width: double.infinity,
                                    color:
                                        payment['isSelected']
                                            ? Theme.of(
                                              context,
                                            ).primaryColor.withOpacity(0.15)
                                            : Colors.transparent,
                                    margin: EdgeInsets.only(bottom: dW * 0.02),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              color: Colors.transparent,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  TextWidget(
                                                    title:
                                                        '${payment['title']}',
                                                    color: Color(0xff242530),
                                                    fontWeight:
                                                        payment['isSelected']
                                                            ? FontWeight.w600
                                                            : FontWeight.w400,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              height: dW * 0.05,
                                              padding: EdgeInsets.all(2),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color:
                                                      payment['isSelected']
                                                          ? Theme.of(
                                                            context,
                                                          ).primaryColor
                                                          : Color(0xffB6B7BA),
                                                ),
                                              ),
                                              child: CircleAvatar(
                                                radius: 8,
                                                backgroundColor:
                                                    payment['isSelected']
                                                        ? Theme.of(
                                                          context,
                                                        ).primaryColor
                                                        : Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        if ((payment['index'] == 2) &&
                                            payment['isSelected'] == true)
                                          Container(
                                            width: dW * 0.7,
                                            padding: EdgeInsets.only(
                                              top: dW * 0.025,
                                            ),
                                            child: CustomTextFieldWithLabel(
                                              label: '',
                                              borderRadius: 5,
                                              controller: amountController,
                                              hintText: 'Enter amount',
                                              hintFS: 13,
                                              textFS: 14,
                                              inputFormatter: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly,
                                              ],
                                              inputType:
                                                  Platform.isIOS
                                                      ? TextInputType.numberWithOptions(
                                                        signed: true,
                                                        decimal: false,
                                                      )
                                                      : TextInputType.number,
                                              maxLength: 10,
                                              focusNode: amountNode,
                                              onTap: () {
                                                amountNode.requestFocus();
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),

                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: dW * 0.05,
                                ),
                                child: DividerWidget(),
                              ),

                              // Discount
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: dW * 0.05,
                                ),
                                child: CustomTextFieldWithLabel(
                                  label: 'Discount Amount',
                                  optional: true,
                                  labelFW: FontWeight.w600,
                                  controller: discountController,
                                  hintText: 'Enter discount amount',
                                  inputFormatter: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  inputType:
                                      Platform.isIOS
                                          ? TextInputType.numberWithOptions(
                                            signed: true,
                                            decimal: false,
                                          )
                                          : TextInputType.number,
                                  maxLength: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.15),
                      ],
                    ),
                  ),
                ),
                BottomAlignedWidget(
                  dW: dW,
                  dH: dH,
                  child: CustomButton(
                    width: dW,
                    height: dW * 0.12,
                    buttonText: 'Book',
                    onPressed: booking ? () {} : createBooking,
                    radius: 7,
                    isLoading: booking,
                  ),
                ),
              ],
            ),
          ),
        );
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder:
          (context) => GestureDetector(
            onTap: () {},
            behavior: HitTestBehavior.opaque,
            child: const AddExtra(),
          ),
    );
  }
}
