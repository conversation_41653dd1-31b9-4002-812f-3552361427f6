import 'dart:convert';
import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/homeModule/models/extra_item_model.dart';
import 'package:bys_business/homeModule/screens/edit_payment_status_screen.dart';
import 'package:bys_business/homeModule/screens/qrScannerScreen.dart';
import 'package:bys_business/homeModule/screens/venue_availability_screen.dart';
import 'package:bys_business/homeModule/widgets/edit_note_bottomsheet.dart';
import 'package:bys_business/moreModule.dart/provider/moreProvider.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/widgets/unPaidDropdown.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../fontSizes.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/dialogBox.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../common_function.dart';
import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../new_colors.dart';
import '../widgets/add_extra.dart';
import '../widgets/payment_summary_widget.dart';
import 'verify_booking_otp_screen.dart';

class BookingDescriptionScreen extends StatefulWidget {
  BookingModel? booking;

  final UserModal user;
  final String bookingId;
  final String comingFrom;
  BookingDescriptionScreen({
    Key? key,
    this.booking,
    required this.user,
    this.bookingId = '',
    this.comingFrom = '',
  }) : super(key: key);

  @override
  _BookingDescriptionScreenState createState() =>
      _BookingDescriptionScreenState();
}

class _BookingDescriptionScreenState extends State<BookingDescriptionScreen> {
  bool isLoading = false;
  bool complete = false;
  bool isValid = true;
  bool loadConvenienceCharges = false;

  double deviceWidth = 0;
  double deviceHeight = 0;
  double textScaleFactor = 0;

  getDuration(int minutes) {
    int remainder = 0;
    int quotient = 0;
    String hr = '';
    String min = '';
    quotient = minutes ~/ 60;
    remainder = minutes % 60;
    hr = '$quotient';

    if (remainder < 9) {
      min = '0$remainder';
    } else {
      min = '$remainder';
    }
    return hr + ':' + min + ' hour';
  }

  cancelBooking() async {
    try {
      setState(() {
        isLoading = true;
      });

      final data = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).cancelAndRescheduleBooking(
        accessToken: widget.user.accessToken,
        bookingId: widget.booking!.id,
        cancel: true,
        startDate:
            DateTime(
              widget.booking!.bookingDate!.year,
              widget.booking!.bookingDate!.month,
              widget.booking!.bookingDate!.day,
              0,
              0,
              0,
            ).toString(),
        endDate:
            DateTime(
              widget.booking!.bookingDate!.year,
              widget.booking!.bookingDate!.month,
              widget.booking!.bookingDate!.day,
              23,
              59,
              59,
            ).toString(),
      );
      if (data != null) {
        showSnackbar('Booked cancelled successfully ', color: greenPrimary);
        Provider.of<MoreProvider>(
          context,
          listen: false,
        ).updateCancelStatus(widget.booking!.id, data);
        pop();
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  markComplete(String bookingId) async {
    try {
      setState(() {
        complete = true;
      });
      final data = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).markBookingCompleted(
        accessToken: widget.user.accessToken,
        bookingId: widget.booking!.id,
        businessId: widget.user.businessId,
        amountToAdd: widget.booking!.totalAmount - widget.booking!.amountPaid,
        paymentType: '',
      );
      if (data) {
        showSnackbar(
          'Booking Status updated successfully',
          color: greenPrimary,
        );
        // Navigator.of(context).pop();
        setState(() {
          widget.booking!.bookingStatus = 'COMPLETED';
        });
      }
      setState(() {
        complete = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        complete = false;
      });
    }
  }

  fetchBookingById() async {
    if (widget.booking == null) {
      setState(() {
        isLoading = true;
      });

      final booking = await Provider.of<HomeProvider>(
        context,
        listen: false,
      ).fetchBookingByIdNew(bookingId: widget.bookingId);
      widget.booking = booking;
    }

    var date = widget.booking!.ogBookingDate;
    var time = widget.booking!.bookingSlotStartTime
        .toStringAsFixed(2)
        .split('.');

    var bookingDate = DateTime(
      date!.year,
      date.month,
      date.day,
      int.parse(time[0]),
      int.parse(time[1]),
    );

    if (DateTime.now().isAfter(bookingDate)) {
      isValid = false;
    } else {
      isValid = true;
    }

    //check for if he is coming after scanning qr
    if (widget.comingFrom == 'qrScanner') {
      if (widget.booking!.totalAmount == widget.booking!.amountPaid) {
        await markComplete(widget.bookingId);
      } else {
        bottomSheetForUnpaidAmount(widget.bookingId);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  bottomSheetForUnpaidAmount(String bookingId) {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder:
          (context) => GestureDetector(
            onTap: () {},
            behavior: HitTestBehavior.opaque,
            child: UnPaidDropdown(
              bookingId: bookingId,
              user: widget.user,
              booking: widget.booking!,
              paymentType: '',
            ),
          ),
    ).then((value) {
      if (value != null && value) {
        setState(() {
          widget.booking!.bookingStatus = 'COMPLETED';
        });
      }
    });
  }

  bottomSheetForEditNote() {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder:
          (context) => GestureDetector(
            child: EditNoteBottomSheet(
              bookingId: widget.booking!.id,
              user: widget.user,
              note: widget.booking!.note,
            ),
            onTap: () {},
            behavior: HitTestBehavior.opaque,
          ),
    ).then((value) {
      if (value != null) {
        setState(() {
          widget.booking!.note = value;
        });
      }
    });
  }

  cancelConfirmation() {
    return showDialog(
      context: context,
      builder:
          ((context) => CustomDialog(
            title: 'Are you sure you want to cancel this booking?',
            noText: 'Yes, Cancel',
            yesText: 'No',
            noFunction: () {
              pop();
              cancelBooking();
            },
            yesFunction: () => pop(),
          )),
    );
  }

  handleOnTap(value) {
    if (value == 1) {
      if (!isValid) return;
      return cancelConfirmation();
    } else if (value == 2) {
      push(
        VenueAvailabilityScreen(
          user: widget.user,
          bookingType: 'Single Booking',
          booking: widget.booking,
          updateTime: true,
        ),
      ).then((value) {
        if (value != null) {
          widget.booking!.bookingSlotStartTime =
              value['bookingSlotStartTime'].toDouble();
          widget.booking!.bookingSlotEndTime =
              value['bookingSlotEndTime'].toDouble();
          widget.booking!.totalAmount = value['totalAmount'].toDouble();
          setState(() {});
        }
      });
    } else if (value == 3) {
      push(EditPaymentStatusScreen(booking: widget.booking!)).then((value) {
        if (value != null) {
          widget.booking!.paymentStatus = value['paymentStatus'];
          widget.booking!.bookingStatus = value['bookingStatus'];
          widget.booking!
            ..amountPaid =
                value['amountPaid'] == null
                    ? 0
                    : value['amountPaid'].toDouble();
          setState(() {});
        }
      });
    }
  }

  scanQrCode() {
    if (complete) return;
    push(QRScannerScreen()).then((value) async {
      if (value != null && value != '') {
        value = json.decode(value);
        if (value['business'] != widget.user.businessId) {
          showSnackbar('This booking does not belong to your venue');
          return;
        } else if (widget.booking!.id != value['bookingId']) {
          showSnackbar('Booking ID does not matched');
          return;
        } else {
          if (widget.booking!.totalAmount ==
              widget.booking!.amountPaid + widget.booking!.discountedAmount) {
            await markComplete(value['bookingId']);
          } else {
            bottomSheetForUnpaidAmount(value['bookingId']);
          }
        }
      }
    });
  }

  goToOTPScreen() {
    if (complete) return;
    push(
      VerifyBookingOtpScreen(
        bookingId: widget.booking!.id,
        accessToken: widget.user.accessToken,
      ),
    ).then((value) async {
      if (value != null && value != '') {
        if (value['business'] != widget.user.businessId) {
          showSnackbar('This booking does not belong to your venue');
          return;
        } else if (widget.booking!.id != value['bookingId']) {
          showSnackbar('Booking ID does not matched');
          return;
        } else {
          if (widget.booking!.totalAmount ==
              Provider.of<HomeProvider>(context, listen: false)
                  .getbookingBalanceAmount(widget.booking!)) {
            await markComplete(value['bookingId']);
          } else {
            bottomSheetForUnpaidAmount(value['bookingId']);
          }
        }
      }
    });
  }

  Widget buildColumnWidget({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: title,
          fontWeight: FontWeight.w500,
          color: getLightGreyColor1(context),
        ),
        SizedBox(height: deviceWidth * .015),
        TextWidget(title: value, fontWeight: FontWeight.w600),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    fetchBookingById();
    setExtraItems();
  }

  setExtraItems() async {
    if (widget.booking != null) {
      Provider.of<HomeProvider>(
        context,
        listen: false,
      ).getAllitemsService(widget.booking!.id, widget.user.accessToken);
    }
  }

  @override
  Widget build(BuildContext context) {
    deviceWidth = MediaQuery.of(context).size.width;
    deviceHeight = MediaQuery.of(context).size.height;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body:
          iOSCondition(deviceHeight)
              ? screenBody()
              : SafeArea(child: screenBody()),
    );
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder:
          (context) => GestureDetector(
            onTap: () {},
            behavior: HitTestBehavior.opaque,
            child: AddExtra(
              onItemAdded: (item) async {
                await Provider.of<HomeProvider>(
                  context,
                  listen: false,
                ).additemtoBooking(
                  widget.booking!.id,
                  item,
                  widget.user.accessToken,
                );
                // Navigator.pop(context);
              },
            ),
          ),
    );
  }

  screenBody() {
    return isLoading || widget.booking == null
        ? Center(child: MaterialCircularLoader(deviceWidth * 0.07))
        : Padding(
          padding:
              Platform.isIOS
                  ? EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top,
                    bottom: deviceHeight * 0.02,
                  )
                  : EdgeInsets.only(
                    top: deviceHeight * 0.02,
                    bottom: deviceHeight * 0.02,
                  ),
          child: Column(
            children: [
              // SizedBox(height: deviceWidth * 0.05),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NewAppBar(dW: deviceWidth, title: 'Booking Details'),
                  if (widget.booking!.bookingStatus != 'COMPLETED' &&
                      widget.booking!.bookingStatus != 'CANCELLED'
                  //  &&
                  // isValid
                  )
                    Align(
                      alignment: Alignment.bottomRight,
                      child: PopupMenuButton(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(7),
                        ),
                        icon: Icon(
                          Icons.more_vert,
                          color: Colors.black,
                          size: 22,
                        ),
                        itemBuilder:
                            (BuildContext bc) => [
                              // popupMenuItem(
                              //   position: 2,
                              //   title: "Edit Timing",
                              //   dW: deviceWidth,
                              // ),
                              if (widget.booking!.totalAmount !=
                                  widget.booking!.amountPaid)
                                popupMenuItem(
                                  position: 3,
                                  title: "Edit Payment Status",
                                  dW: deviceWidth,
                                ),
                              if (widget.user.business != null &&
                                      widget.user.business!.roles.contains(
                                        'Cancel Booking',
                                      ) ||
                                  widget.user.role == 'Business')
                                popupMenuItem(
                                  position: 1,
                                  title: "Cancel Booking",
                                  dW: deviceWidth,
                                  // isActive: isValid,
                                ),
                            ],
                        onSelected: (value) => handleOnTap(value),
                      ),
                    ),
                ],
              ),
              SizedBox(height: deviceWidth * 0.05),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      if (widget.booking!.bookingStatus == 'COMPLETED' ||
                          widget.booking!.bookingStatus == 'CANCELLED') ...[
                        Container(
                          padding: EdgeInsets.symmetric(
                            vertical: deviceWidth * .02,
                          ),
                          color:
                              widget.booking!.bookingStatus == 'COMPLETED'
                                  ? getLightGreenColor1(context)
                                  : widget.booking!.bookingStatus == 'CANCELLED'
                                  ? getLightRedColor1(context)
                                  : getLightYellowColor1(context),
                          width: deviceWidth,
                          alignment: Alignment.center,
                          child: TextWidget(
                            title: 'BOOKING ${widget.booking!.bookingStatus}',
                            fontWeight: FontWeight.w600,
                            color:
                                widget.booking!.bookingStatus == 'COMPLETED'
                                    ? getThemeColor()
                                    : widget.booking!.bookingStatus ==
                                        'CANCELLED'
                                    ? getRedColor2(context)
                                    : getYellowColor1(context),
                          ),
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                      ],
                      if (widget.booking != null &&
                          widget.booking!.bookingStatus != 'CANCELLED') ...[
                        Container(
                          padding: EdgeInsets.symmetric(
                            vertical: deviceWidth * .02,
                          ),
                          color: getLightGreenColor1(context),
                          width: deviceWidth,
                          alignment: Alignment.center,
                          child: TextWidget(
                            title: 'BOOKING CONFIRMED',
                            fontWeight: FontWeight.w600,
                            color: getThemeColor(),
                          ),
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                      ],
                      CustomContainer(
                        margin: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.04,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomContainer(
                              hPadding: 0.03,
                              vPadding: 0.027,
                              boxShadow: [],
                              bgColor: Color(0xffE3FEDB),
                              borderColor: Color(0xffE3FEDB),
                              radius: 8,
                              margin: EdgeInsets.only(
                                bottom: deviceWidth * 0.035,
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      TextWidget(
                                        title: 'Player name:',
                                        fontSize: 12,
                                      ),
                                      SizedBox(height: deviceWidth * 0.01),
                                      ConstrainedBox(
                                        constraints: BoxConstraints(
                                          maxWidth: deviceWidth * 0.6,
                                        ),
                                        child: TextWidget(
                                          title: widget.booking!.user!.name,
                                          fontWeight: FontWeight.w600,
                                          color: getThemeColor(),
                                        ),
                                      ),
                                      SizedBox(height: deviceWidth * 0.01),
                                    ],
                                  ),
                                  GestureDetector(
                                    onTap:
                                        () => customLaunch(
                                          'tel:${widget.booking!.user!.mobileNo}',
                                        ),
                                    child: Container(
                                      padding: EdgeInsets.all(
                                        deviceWidth * 0.017,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const AssetSvgIcon(
                                        iconName: 'phone2',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // TextWidget(
                            //   title:
                            //       '${widget.booking!.bookingMode} Booking ID: #${widget.booking!.id.substring(8, widget.booking!.id.length - 7).toUpperCase()}',
                            //   fontWeight: FontWeight.w600,
                            // ),
                            // SizedBox(height: deviceWidth * .04),
                            buildColumnWidget(
                              title: "Venue Name",
                              value: widget.booking!.turfName,
                            ),
                            if (widget.booking!.bookedBy == 'Employee') ...[
                              SizedBox(height: deviceWidth * .04),
                              buildColumnWidget(
                                title: "Booked By Employee",
                                value:
                                    widget.booking!.mappedEmployee == null
                                        ? ''
                                        : widget.booking!.mappedEmployee!.name,
                              ),
                            ],
                            if (widget.booking!.rentedItem.isNotEmpty) ...[
                              SizedBox(height: deviceWidth * .04),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    title: "Rental Items",
                                    fontWeight: FontWeight.w500,
                                    color: getLightGreyColor1(context),
                                  ),
                                  SizedBox(height: deviceWidth * .015),
                                  ...widget.booking!.rentedItem
                                      .asMap()
                                      .map(
                                        (i, item) => MapEntry(
                                          i,
                                          Container(
                                            margin: EdgeInsets.only(
                                              bottom: deviceWidth * 0.01,
                                            ),
                                            child: TextWidget(
                                              title:
                                                  item.quantity < 9
                                                      ? '${i + 1}. ${item.productName} x (0${item.quantity})'
                                                      : '${i + 1}. ${item.productName} x (${item.quantity})',
                                              textAlign: TextAlign.center,
                                              fontSize: textScaleFactor * 14.5,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      )
                                      .values
                                      .toList(),
                                ],
                              ),
                            ],
                            SizedBox(height: deviceWidth * .04),
                            TextWidget(
                              title: 'Date & Time',
                              fontWeight: FontWeight.w500,
                              color: getLightGreyColor1(context),
                            ),
                            SizedBox(height: deviceWidth * .03),
                            Row(
                              children: [
                                AssetSvgIcon(
                                  iconName: 'calendar1',
                                  height: deviceWidth * .05,
                                ),
                                SizedBox(width: deviceWidth * .02),
                                TextWidget(
                                  title: DateFormat(
                                    'MMM dd, yyyy',
                                  ).format(widget.booking!.ogBookingDate!),
                                  fontWeight: FontWeight.w600,
                                  color: getGreyColor2(context),
                                ),
                              ],
                            ),
                            SizedBox(height: deviceWidth * .025),
                            Row(
                              children: [
                                AssetSvgIcon(
                                  iconName: 'clock2',
                                  height: deviceWidth * .05,
                                ),
                                SizedBox(width: deviceWidth * .02),
                                TextWidget(
                                  title:
                                      '${double.parse(get12HrFormat(widget.booking!.bookingSlotStartTime.toDouble()).toString()).toStringAsFixed(2)} ${getTimePeriod(widget.booking!.bookingSlotStartTime.toDouble())} - ${double.parse(get12HrFormat((widget.booking!.bookingSlotEndTime == 0.0 ? 12.0 : widget.booking!.bookingSlotEndTime).toDouble()).toString()).toStringAsFixed(2)} ${getTimePeriod(widget.booking!.bookingSlotEndTime.toDouble())}',
                                  fontWeight: FontWeight.w600,
                                  color: getGreyColor2(context),
                                ),
                              ],
                            ),
                            SizedBox(height: deviceWidth * .04),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  title:
                                      widget
                                                  .booking!
                                                  .sportCategory
                                                  .categoryName ==
                                              'Outdoor'
                                          ? "Sports & Turf Size"
                                          : "Sports & Game type",
                                  fontWeight: FontWeight.w500,
                                  color: getLightGreyColor1(context),
                                ),
                                SizedBox(height: deviceWidth * .02),
                                IntrinsicHeight(
                                  child: Row(
                                    children: [
                                      Image.network(
                                        widget.booking!.sportImage,
                                        height: 18,
                                      ),
                                      SizedBox(width: deviceWidth * .02),
                                      widget.booking!.sportType == 'Snooker'
                                          ? TextWidget(
                                            title: widget.booking!.sportType,
                                            color: getGreyColor2(context),
                                            fontWeight: FontWeight.w500,
                                          )
                                          : TextWidget(
                                            title: widget.booking!.sportType,
                                            color: getGreyColor2(context),
                                            fontWeight: FontWeight.w500,
                                          ),
                                      if (widget
                                              .booking!
                                              .sportCategory
                                              .categoryName ==
                                          'Outdoor') ...[
                                        SizedBox(width: deviceWidth * .02),
                                        VerticalDivider(
                                          width: 1,
                                          color: Color(0xff363636),
                                        ),
                                        SizedBox(width: deviceWidth * .02),
                                        TextWidget(
                                          title:
                                              '${widget.booking!.sizeOrSport.replaceAll(':', 'x')}${widget.booking!.label != '' ? ' ${widget.booking!.label}' : ''}',
                                          color: getGreyColor2(context),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ],
                                      if (widget
                                              .booking!
                                              .sportCategory
                                              .categoryName ==
                                          'Indoor') ...[
                                        SizedBox(width: deviceWidth * .02),
                                        VerticalDivider(
                                          width: 1,
                                          color: Color(0xff363636),
                                        ),
                                        SizedBox(width: deviceWidth * .02),
                                        TextWidget(
                                          title:
                                              widget.booking!.sizeOrSport
                                                  .split('(')[0]
                                                  .trim(),
                                          color: getGreyColor2(context),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                                SizedBox(height: deviceWidth * .04),
                                buildColumnWidget(
                                  title: "Booking Created Date Time",
                                  value: DateFormat(
                                    'dd MMM yyyy, hh:mm a',
                                  ).format(widget.booking!.createdAt),
                                ),
                              ],
                            ),
                            if (widget.booking!.cancelledOn != null) ...[
                              SizedBox(height: deviceWidth * .05),
                              buildColumnWidget(
                                title: "Cancelled Date Time",
                                value: DateFormat(
                                  'dd MMM yyyy, hh:mm a',
                                ).format(widget.booking!.cancelledOn!),
                              ),
                            ],
                            if (widget.booking!.onlinePaid != 0) ...[
                              SizedBox(height: deviceWidth * .05),
                              buildColumnWidget(
                                title: "Online Paid Amount",
                                value:
                                    '\u20b9${convertAmountString(widget.booking!.onlinePaid.toDouble())}',
                              ),
                            ],
                          ],
                        ),
                      ),
                      // if (widget.booking!.note != '') ...[
                      SizedBox(height: deviceWidth * .07),
                      CustomContainer(
                        margin: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.04,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                buildHeading(
                                  deviceWidth,
                                  context,
                                  textScaleFactor,
                                  'Note:',
                                ),
                                if (widget.booking!.bookingStatus !=
                                    'CANCELLED')
                                  TextButton.icon(
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.all(0),
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    onPressed: bottomSheetForEditNote,
                                    icon: Icon(
                                      Icons.edit,
                                      size: 15,
                                      color: getThemeColor(),
                                    ),
                                    label: Text(
                                      widget.booking!.note == ''
                                          ? 'Add'
                                          : 'Edit',
                                      style: TextStyle(
                                        color: getThemeColor(),
                                        fontSize: textScaleFactor * 13,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            Container(
                              constraints: BoxConstraints(
                                maxWidth: deviceWidth * 0.9,
                              ),
                              child: TextWidget(
                                title:
                                    widget.booking!.note == ''
                                        ? 'Add Note..'
                                        : widget.booking!.note,
                                textAlign: TextAlign.left,
                                fontSize: textScaleFactor * 14,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // ],
                      SizedBox(height: deviceWidth * .07),
                      PaymentSummaryWidget(booking: widget.booking!),

                      if (widget.booking!.tab == 'Upcoming') ...{
                        CustomContainer(
                          vPadding: 0.02,

                          width: double.infinity,
                          margin: EdgeInsets.symmetric(
                            horizontal: deviceWidth * 0.05,
                            vertical: deviceWidth * 0.05,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,

                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Expanded(
                                    child: TextWidget(
                                      title: 'Extras',
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),

                                  InkWell(
                                    enableFeedback: true,
                                    onTap: () {
                                      openVenueBottomSheet();
                                    },
                                    child: CustomContainer(
                                      hPadding: 0.01,
                                      vPadding: 0.01,
                                      child: Icon(
                                        Icons.add,
                                        color: getThemeColor(),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Consumer<HomeProvider>(
                                builder: (context, homeprovider, child) {
                                  List<ExtraItem> extraItems = homeprovider
                                      .extraItemsByBooking(widget.booking!.id);
                                  return extraItems.isNotEmpty
                                      ? Column(
                                        children:
                                            extraItems
                                                .map(
                                                  (item) => Container(
                                                    margin: EdgeInsets.only(
                                                      bottom:
                                                          deviceWidth * 0.02,
                                                      top: deviceWidth * 0.02,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        InkWell(
                                                          onTap: () {
                                                            homeprovider
                                                                .removeExtraItemFromBooking(
                                                                  widget
                                                                      .booking!
                                                                      .id,
                                                                  extraItems
                                                                      .indexOf(
                                                                        item,
                                                                      ),
                                                                  widget
                                                                      .user
                                                                      .accessToken,
                                                                );
                                                            setState(() {});
                                                          },
                                                          child: Icon(
                                                            Icons
                                                                .delete_outline,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width:
                                                              deviceWidth *
                                                              0.02,
                                                        ),
                                                        Expanded(
                                                          flex: 5,
                                                          child: TextWidget(
                                                            textOverflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            title: item.name,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                        Row(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            IconButton(
                                                              onPressed: () {
                                                                homeprovider.updateBookingExtraItemQuantity(
                                                                  widget
                                                                      .booking!
                                                                      .id,
                                                                  extraItems
                                                                      .indexOf(
                                                                        item,
                                                                      ),
                                                                  item.quantity -
                                                                      1,
                                                                  widget
                                                                      .user
                                                                      .accessToken,
                                                                );
                                                                setState(() {});
                                                              },
                                                              icon: Icon(
                                                                Icons
                                                                    .remove_circle,
                                                                color:
                                                                    getThemeColor(),
                                                              ),
                                                            ),
                                                            TextWidget(
                                                              title:
                                                                  '${item.quantity}',
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                            ),
                                                            IconButton(
                                                              onPressed: () {
                                                                homeprovider.updateBookingExtraItemQuantity(
                                                                  widget
                                                                      .booking!
                                                                      .id,
                                                                  extraItems
                                                                      .indexOf(
                                                                        item,
                                                                      ),
                                                                  item.quantity +
                                                                      1,
                                                                  widget
                                                                      .user
                                                                      .accessToken,
                                                                );
                                                                setState(() {});
                                                              },
                                                              icon: Icon(
                                                                Icons
                                                                    .add_circle,
                                                                color:
                                                                    getThemeColor(),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                )
                                                .toList(),
                                      )
                                      : SizedBox.shrink();
                                },
                              ),
                            ],
                          ),
                        ),
                      },
                    ],
                  ),
                ),
              ),
              if (widget.booking!.bookingStatus == 'PENDING' ||
                  widget.booking!.bookingStatus == 'PARTIALLYPAID' ||
                  widget.booking!.bookingStatus == 'RESCHEDULED' ||
                  widget.booking!.bookingStatus == 'UNPAID')
                BottomAlignedWidget(
                  dW: deviceWidth,
                  dH: deviceWidth * 0.14,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomButton(
                        width: deviceWidth * 0.44,
                        height: deviceWidth * 0.12,
                        radius: 8,
                        buttonText: 'Enter OTP',
                        fontSize: 15,
                        buttonColor: Colors.white,
                        elevation: 0,
                        textColor: getThemeColor(),
                        borderColor: getThemeColor(),
                        onPressed: goToOTPScreen,
                      ),
                      CustomButton(
                        width: deviceWidth * 0.44,
                        height: deviceWidth * 0.12,
                        radius: 8,
                        buttonText: 'Scan QR Code',
                        onPressed: scanQrCode,
                        isLoading: complete,
                        fontSize: 15,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
  }
}

Widget buildColumn(
  double deviceWidth,
  BuildContext context,
  double textScaleFactor,
  String heading,
  String value, {
  Color color = Colors.black,
}) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
          child: Text(
            heading,
            textAlign: TextAlign.left,
            style: Theme.of(context).textTheme.displayLarge!.copyWith(
              fontSize: textScaleFactor * displayLarge,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
        ),
        Container(
          constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
          child: Text(
            value,
            textAlign: TextAlign.left,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
              fontSize: textScaleFactor * 14.5,
              fontWeight: FontWeight.w600,
              color: color,
            ),
            softWrap: true,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    ),
  );
}

Widget buildHeading(
  double deviceWidth,
  BuildContext context,
  double textScaleFactor,
  String heading,
) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.022),
    child: Text(
      heading,
      textAlign: TextAlign.left,
      style: Theme.of(context).textTheme.displayLarge!.copyWith(
        fontSize: textScaleFactor * 14,
        fontWeight: FontWeight.w500,
        color: Colors.black,
      ),
    ),
  );
}

Widget buildListTile(
  double deviceWidth,
  BuildContext context,
  double textScaleFactor,
  String value,
  IconData icon, {
  double fontSize = displayMedium,
}) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
    child: Row(
      children: [
        CircleAvatar(
          radius: 15,
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Icon(icon, size: 15, color: Colors.black),
        ),
        SizedBox(width: deviceWidth * 0.02),
        Container(
          child: Text(
            value,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
              fontSize: textScaleFactor * fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    ),
  );
}

Widget buildRow(
  double deviceWidth,
  BuildContext context,
  double textScaleFactor,
  String title,
  String value, {
  bool roboto = false,
  bool addorSubtract = false,
  String operationIcon = '',
  bool convenienceCharges = false,
}) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.017),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          constraints: BoxConstraints(maxWidth: deviceWidth * 0.5),
          child: TextWidget(
            title: '$title',
            fontSize: 13.5,
            fontWeight: FontWeight.normal,
            color: convenienceCharges ? Colors.grey : Colors.black,
          ),
        ),
        roboto
            ? RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: addorSubtract ? '$operationIcon \u20b9' : '\u20b9',
                    style: TextStyle(
                      fontSize: textScaleFactor * 14.5,
                      fontWeight: FontWeight.w600,
                      color: convenienceCharges ? Colors.grey : Colors.black,
                    ),
                  ),
                  TextSpan(
                    text: '$value',
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      fontSize: textScaleFactor * 14.5,
                      fontWeight: FontWeight.w600,
                      color: convenienceCharges ? Colors.grey : Colors.black,
                    ),
                  ),
                ],
              ),
            )
            : Container(
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.5),
              child: TextWidget(
                title: '$value',
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
      ],
    ),
  );
}
