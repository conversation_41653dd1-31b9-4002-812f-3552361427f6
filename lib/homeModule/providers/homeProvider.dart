import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

import '../../homeModule/models/reportModel.dart';
import '../../http_helper.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';
import '../../homeModule/models/bookingModel.dart';
import '../models/extra_item_model.dart';

class HomeProvider with ChangeNotifier {
  int _limit = 10;
  int _contentToSkip = 0;
  int completedCount2 = 0;
  int cancelledCount = 0;
  int completedCount = 0;
  int newCount = 0;
  Map<String, List<ExtraItem>> _extraItemsByBookings = {};

  List<ExtraItem> extraItemsByBooking(String bookingId) {
    return _extraItemsByBookings[bookingId] ?? [];
  }

  void setExtraItemsByBooking(String bookingId, List<ExtraItem> extraItems) {
    _extraItemsByBookings[bookingId] = extraItems; //cehck if item exists
  }

  void updatbookingmodel(String bookingId, List<ExtraItem> extraItems) {
    int index = _bookings.indexWhere((b) => b.id == bookingId);
    if (index != -1) {
      _bookings[index] = _bookings[index].copyWith(extraItems: extraItems);
      notifyListeners();
    }
  }

  List<ExtraItem> _extraItems = [];
  List<ExtraItem> get extraItems => _extraItems;

  List<BookingModel> _bookings = [];

  List<BookingModel> get bookings {
    return [..._bookings];
  }

  List<BookingModel> _statusBooking = [];

  List<BookingModel> get statusBooking {
    return [..._statusBooking];
  }

  List<BookingModel> _bulkBooking = [];

  List<BookingModel> get bulkBooking {
    return [..._bulkBooking];
  }

  void addExtraItem(ExtraItem item) {
    _extraItems.add(item);
    notifyListeners();
  }

  Future<void> additemtoBooking(
    String bookingId,
    ExtraItem extraItem,
    String accessToken,
  ) async {
    var response = await addOrUpdatExtraItemService(
      bookingId,
      extraItem,
      accessToken,
    );
    if (response == null) {
      showSnackbar('Failed to add/update item. Please try again.');
      return;
    } else {
      setExtraItemsByBooking(bookingId, [...response[bookingId] ?? []]);
      // _extraItemsByBookings[bookingId] = [...response[bookingId] ?? []];
    }
    notifyListeners();
    //api call to add extra item to booking
  }

  Future<Map<String, List<ExtraItem>>?> addOrUpdatExtraItemService(
    String bookingId,
    ExtraItem extraItem,
    String accessToken,
  ) async {
    try {
      final response = await http.put(
        Uri.parse('${webApi['domain']}${endPoint['addOrUpdateExtraItems']}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode({'bookingId': bookingId, 'item': extraItem.toJson()}),
      );
      log('Response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        var responseData = json.decode(response.body);
        List<ExtraItem> extraItems =
            responseData['result']['extraItems']
                .map<ExtraItem>((item) => ExtraItem.fromJson(item))
                .toList();
        // setExtraItemsByBooking(bookingId, extraItems);
        return {
          bookingId: [...extraItems],
        };
      } else {
        throw Exception('Failed to add or update extra item');
      }
    } catch (e) {
      return null;
    }
  }

  void updateBookingExtraItemQuantity(
    String bookingId,
    int index,
    int quantity,
    String accessToken,
  ) async {
    if (_extraItemsByBookings.containsKey(bookingId)) {
      if (quantity < 1) return;
      List<ExtraItem> existingItems = _extraItemsByBookings[bookingId]!;
      if (index != -1) {
        existingItems[index].quantity = quantity;
        _extraItemsByBookings[bookingId] = existingItems;
        var res = await addOrUpdatExtraItemService(
          bookingId,
          existingItems[index],
          accessToken,
        );
        if (res != null) {
        } else {
          showSnackbar('Failed to update item quantity. Please try again.');
        }
      }
    }
    notifyListeners();
  }

  void removeExtraItemFromBooking(
    String bookingId,
    int index,
    String accessToken,
  ) async {
    if (_extraItemsByBookings.containsKey(bookingId)) {
      List<ExtraItem> existingItems = _extraItemsByBookings[bookingId]!;
      if (index >= 0 && index < existingItems.length) {
        await _removeExtraItemService(
          bookingId,
          existingItems[index].id ?? "",
          accessToken,
        );
        existingItems.removeAt(index);
        _extraItemsByBookings[bookingId] = existingItems;
      }
    }
    notifyListeners();
  }

  Future<void> getAllitemsService(String bookingId, String accessToken) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${webApi['domain']}${endPoint['fetchAllBookingItems']}/$bookingId',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
      );
      if (response.statusCode == 200) {
        var responseData = json.decode(response.body);
        List<ExtraItem> extraItems = [];
        for (var item in responseData['extraItems']) {
          extraItems = [...extraItems, ExtraItem.fromJson(item)];
        }
        setExtraItemsByBooking(bookingId, extraItems);
      } else {
        throw Exception('Failed to fetch items');
      }
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> _removeExtraItemService(
    String bookingID,
    String itemID,
    String accessToken,
  ) async {
    try {
      final response = await http.delete(
        Uri.parse(
          '${webApi['domain']}${endPoint['removeExtraItemFromBooking']}/$bookingID/$itemID',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
      );
      if (response.statusCode == 200) {
        // Item removed successfully
      } else {
        throw Exception('Failed to remove extra item');
      }
    } catch (e) {
      print(e);
    }
  }

  void removeExtraItem(int index) {
    _extraItems.removeAt(index);
    notifyListeners();
  }

  void updateExtraItemQuantity(int index, int quantity) {
    if (index < 0 || index >= _extraItems.length) return;
    if (quantity > 0) {
      _extraItems[index].quantity = quantity;
      notifyListeners();
    }

    notifyListeners();
  }

  double getTotalExtraItemsPrice() {
    return _extraItems.fold(
      0.0,
      (sum, item) => sum + (item.price * item.quantity),
    );
  }

  void clearExtraItems() {
    _extraItems = [];
    notifyListeners();
  }

  resetBooking() {
    _bookings = [];
    _statusBooking = [];
    _bulkBooking = [];
    cancelledCount = 0;
    completedCount = 0;
    newCount = 0;
    _contentToSkip = 0;
  }

  resetBulkBooking() {
    _bulkBooking = [];
    bulkSkip = 0;
    bulkLimit = 15;
  }

  int _statusSkip = 0;
  int _statusLimit = 12;

  int bulkLimit = 15;
  int bulkSkip = 0;

  List<ReportModel> _reports = [];

  List<ReportModel> get reports {
    return [..._reports];
  }

  cancelBooking(String id) {
    _bookings.forEach((booking) {
      if (booking.id == id) {
        booking.bookingStatus = 'CANCELLED';
      }
    });
    notifyListeners();
  }

  getOfflineBooking() {
    return _bookings
        .where((booking) => booking.bookingMode == 'Offline')
        .toList();
  }

  emptyBooking() {
    _contentToSkip = 0;
    _bookings = [];
  }

  emptyStatusBooking() {
    _statusBooking = [];
    _statusLimit = 12;
    _statusSkip = 0;
  }

  fetchBusinessBookingNew({
    required String accessToken,
    required String businessId,
    required String startDate,
    required String endDate,
    required String status,
    required String role,
    required bool refresh,
    required List<String> turfs,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBusinessBookingNew']}';

    if (_contentToSkip == 0) _bookings = [];
    if (refresh) {
      emptyBooking();
    }

    var str = json.encode({
      "businessId": businessId,
      "startDate": startDate,
      "endDate": endDate,
      "status": status,
      'limit': _limit,
      'skip': _contentToSkip,
      "role": role,
      "turfs": turfs,
    });

    try {
      List<BookingModel> loadedBooking = [];
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        loadedBooking = loadData(responseData['result']);
        if (_contentToSkip == 0) {
          _bookings = List.from(loadedBooking);
        } else {
          _bookings.addAll(loadedBooking);
        }
        if (loadedBooking.isNotEmpty) {
          _contentToSkip += _limit;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchBookingsByStatusNew({
    required String accessToken,
    required String business,
    required String status,
    required String role,
    required List<String> turfs,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBookingsByStatusNew']}';

    if (_statusSkip == 0) _statusBooking = [];

    var str = json.encode({
      "business": business,
      "status": status,
      'limit': _statusLimit,
      'skip': _statusSkip,
      "role": role,
      "turfs": turfs,
    });

    try {
      List<BookingModel> loadedBooking = [];
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        loadedBooking = loadData(responseData['result']);
        if (_statusSkip == 0) {
          _statusBooking = List.from(loadedBooking);
        } else {
          _statusBooking.addAll(loadedBooking);
        }
        if (loadedBooking.isNotEmpty) {
          _statusSkip += loadedBooking.length;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchBookingByBulkIdNew({
    required String accessToken,
    required String bulkId,
    required String business,
    bool refresh = false,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBookingByBulkIdNew']}';

    if (refresh) _statusBooking = [];

    var str = json.encode({
      "bulkId": bulkId,
      "business": business,
      'limit': bulkLimit,
      'skip': bulkSkip,
    });

    try {
      List<BookingModel> loadedBooking = [];
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        loadedBooking = loadData(responseData['result']);
        if (bulkSkip == 0) {
          _bulkBooking = List.from(loadedBooking);
        } else {
          _bulkBooking.addAll(loadedBooking);
        }
        if (loadedBooking.isNotEmpty) {
          bulkSkip += loadedBooking.length;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  searchBookingNew({
    required String accessToken,
    required String business,
    required String searchedString,
    required String role,
    required List<String> turfs,
  }) async {
    final url = '${webApi['domain']}${endPoint['searchBookingNew']}';

    var str = json.encode({
      "business": business,
      "searchedString": searchedString,
      "role": role,
      "turfs": turfs,
    });

    try {
      List<BookingModel> loadedBooking = [];
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        loadedBooking = loadData(responseData['result']);
      }
      return loadedBooking;
    } catch (error) {
      print(error);
      return [];
    }
  }

  loadData(responseData) {
    List<BookingModel> loadedBooking = [];
    responseData.forEach(
      (booking) => loadedBooking.add(BookingModel.jsonToBooking(booking)),
    );
    return loadedBooking;
  }

  fetchBookingByDates({
    required String accessToken,
    required String businessId,
    required String startDate,
    required String endDate,
    required String status,
    required String role,
    required String venueId,
    required List<String> turfs,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBusinessBookingNew']}';

    if (_contentToSkip == 0) _bookings = [];

    var str = json.encode({
      "businessId": businessId,
      "startDate": startDate,
      "endDate": endDate,
      "status": status,
      "role": role,
      "venueId": venueId,
      "turfs": turfs,
    });

    try {
      List<BookingModel> loadedBooking = [];
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        loadedBooking = loadData(responseData['result']);
        return loadedBooking;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchBookingByTurfId(String turfId) {
    return _bookings.where((booking) => booking.turfId == turfId).toList();
  }

  fetchBookingByIdNew({required String bookingId}) async {
    final url = '${webApi['domain']}${endPoint['fetchBookingByIdNew']}';
    var str = json.encode({"bookingId": bookingId});
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        BookingModel loadedBooking = BookingModel.jsonToBooking(
          responseData['result'],
        );

        notifyListeners();
        return loadedBooking;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  cancelAndRescheduleBooking({
    required String accessToken,
    required String bookingId,
    required bool cancel,
    required String startDate,
    required String endDate,
  }) async {
    final url = '${webApi['domain']}${endPoint['cancelAndRescheduleBooking']}';
    var str = json.encode({
      "bookingId": bookingId,
      "cancel": cancel,
      "startDate": startDate,
      "endDate": endDate,
    });
    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        int bookingIndex = _bookings.indexWhere(
          (booking) => booking.id == bookingId,
        );
        if (bookingIndex != -1) {
          _bookings[bookingIndex].bookingStatus = 'CANCELLED';
          _bookings[bookingIndex].paymentStatus =
              responseData['result']['paymentStatus'];
          _bookings[bookingIndex].refundedAmount =
              responseData['result']['refundedAmount'] == null
                  ? 0
                  : responseData['result']['refundedAmount'].toDouble();
          cancelledCount += 1;
          if (newCount != 0) newCount -= 1;
        }

        int bulkBookingIndex = _bulkBooking.indexWhere(
          (booking) => booking.id == bookingId,
        );
        if (bulkBookingIndex != -1) {
          _bulkBooking[bulkBookingIndex].bookingStatus = 'CANCELLED';
          _bulkBooking[bulkBookingIndex].paymentStatus =
              responseData['result']['paymentStatus'];
          _bulkBooking[bulkBookingIndex].refundedAmount =
              responseData['result']['refundedAmount'] == null
                  ? 0
                  : responseData['result']['refundedAmount'].toDouble();
        }
        notifyListeners();
        return responseData['result'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  cancelBulkBooking({required Map body, required String accessToken}) async {
    try {
      final url = '${webApi['domain']}${endPoint['cancelBulkBooking']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );
      if (response['success']) {}
      notifyListeners();
      return response;
    } catch (e) {
      return {'success': false};
    }
  }

  fetchTurfBookingSlot({
    required String turfId,
    required String startDate,
    required String endDate,
    required String sizeOrSport,
    bool? isNet,
    required bool updateBooking,
    String bookingId = '',
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchTurfBookingSlot']}';
    var str = json.encode({
      "turfId": turfId,
      "startDate": startDate,
      "endDate": endDate,
      "sizeOrSport": sizeOrSport,
      "isNet": isNet,
      "updateBooking": updateBooking,
      "bookingId": bookingId,
    });
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notifyListeners();
        return responseData['result'];
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  updateBlockStatus({required String slotId, required bool blocked}) async {
    final url = '${webApi['domain']}${endPoint['updateBlockStatus']}';
    var str = json.encode({"slotId": slotId, "blocked": blocked});
    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notifyListeners();
        return responseData['result'];
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchBlockedSlot({
    required String turfId,
    required String startDate,
    required String endDate,
    required String sizeOrSport,
    required String sport,
    required bool isNet,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBlockedSlot']}';
    var str = json.encode({
      "turfId": turfId,
      "startDate": startDate,
      "endDate": endDate,
      "sizeOrSport": sizeOrSport,
      "sport": sport,
      "isNet": isNet,
    });
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notifyListeners();
        return responseData['result'];
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  insertBookingSlot({
    required String turfId,
    required String startTime,
    required String endTime,
    required String bookingDate,
    required String startDate,
    required String endDate,
    required String sizeOrSport,
    required String sport,
    required turfQuantity,
    required bool isNet,
  }) async {
    final url = '${webApi['domain']}${endPoint['insertBookingSlot']}';
    var str = json.encode({
      "turfId": turfId,
      "startTime": startTime,
      "endTime": endTime,
      "bookingDate": bookingDate,
      "startDate": startDate,
      "endDate": endDate,
      "sizeOrSport": sizeOrSport,
      "sport": sport,
      "turfQuantity": turfQuantity,
      "isNet": isNet,
    });
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      return responseData['result'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  markBookingCompleted({
    required String accessToken,
    required String bookingId,
    required String businessId,
    required double amountToAdd,
    required String paymentType,
  }) async {
    final url = '${webApi['domain']}${endPoint['markBookingCompleted']}';
    var str = json.encode({
      "bookingId": bookingId,
      "business": businessId,
      "amountToAdd": amountToAdd,
      "paymentType": paymentType,
    });
    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        _bookings.forEach((booking) {
          if (booking.id == bookingId) {
            booking.bookingStatus = "COMPLETED";
            booking.paymentType = paymentType;
            booking.amountPaid = booking.amountPaid + amountToAdd;
            booking.remainingBalance = 0;
            completedCount += 1;
            if (newCount != 0) newCount -= 1;
          }
        });
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  updateTurfQuantity({
    required String turfId,
    required String startTime,
    required String endTime,
    required String startDate,
    required String endDate,
    required String sizeOrSport,
    required String sport,
    required String option,
    required bool isNet,
    bool updateQuantity = true,
    bool isNine = false,
  }) async {
    final url = '${webApi['domain']}${endPoint['updateTurfQuantity']}';
    var str = json.encode({
      "turfId": turfId,
      "startTime": startTime,
      "endTime": endTime,
      "startDate": startDate,
      "endDate": endDate,
      "sizeOrSport": sizeOrSport,
      "sport": sport,
      "isNet": isNet,
      "isNine": isNine,
      "option": option,
      "updateQuantity": updateQuantity,
    });
    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  createBooking({
    required String turfId,
    required String fcmToken,
    required String businessId,
    required String sizeOrSport,
    required String sport,
    required String bookingDate,
    required double totalAmount,
    required String paymentStatus,
    required String bookingStatus,
    required double amountPaid,
    required String startTime,
    required String endTime,
    required String phone,
    required String name,
    required double discountedAmount,
    required String accessToken,
    required String sportCategory,
    required List rental,
    required List<ExtraItem> extras,
    required String label,
    required String note,
    required String option,
    required employeeId,
    required String bookedBy,
    required double convenienceFee,
    required double gstOnConvenienceFee,
    required employeeMappingId,
    required String invoiceType,
    required String customerGSTNo,
    required num tax,
    required String paymentType,
  }) async {
    String firstName = '';
    String lastName = '';
    if (name.split(' ').length > 1) {
      firstName = name.split(' ')[0];
      lastName = name.split(' ')[1];
    } else {
      firstName = name;
      lastName = '';
    }
    final url = '${webApi['domain']}${endPoint['createBooking']}';
    Map body = {
      "phone": phone,
      "firstName": firstName,
      "lastName": lastName,
      "turfId": turfId,
      "business": businessId,
      "fcmToken": fcmToken,
      "bookingMode": "Offline",
      "bookingStatus": bookingStatus,
      "totalAmount": totalAmount,
      "discountedAmount": discountedAmount,
      "amountPaid": amountPaid,
      "convenienceFee": convenienceFee,
      "gstOnConvenienceFee": gstOnConvenienceFee,
      "sizeOrSport": sizeOrSport,
      "sport": sport,
      "option": option,
      "bookingDate": bookingDate,
      "rentedItem": [],
      "extraItems": extras.map((e) => e.toJson()).toList(),
      "bookingSlotStartTime": startTime,
      "bookingSlotEndTime": endTime,
      "rental": rental,
      "paymentStatus": paymentStatus,
      "sportCategory": sportCategory,
      "label": label,
      "employeeId": employeeId,
      "bookedBy": bookedBy,
      "employeeMappingId": employeeMappingId,
      "note": note,
      'os': Platform.operatingSystem,
      'tax': tax,
      'paymentType': paymentType,
    };
    if (invoiceType != '') {
      body['invoiceType'] = invoiceType;
    }
    if (customerGSTNo != '') {
      body['customerGSTNo'] = customerGSTNo;
    }
    var str = json.encode(body);
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<RentalItem> loadedRented = [];

        responseData['result']['rentedItem'].forEach((data) {
          loadedRented.add(
            RentalItem(
              id: data['_id'],
              price: data['price'].toDouble(),
              duration: data['duration'],
              // productId: data['productId'],
              productImage: data['productImage'],
              productName: data['productName'],
              quantity: data['quantity'],
            ),
          );
        });

        newCount += 1;
        notifyListeners();
      }
      return responseData;
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchRentalItemBySport({required String sport, required String turf}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchRentalItemBySport']}';
      var str = json.encode({"sport": sport, "turf": turf});

      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<RentalItem> loadedRented = [];
        responseData['result'].forEach((rental) {
          loadedRented.add(RentalItem.jsonToRentalItem(rental));
        });
        return loadedRented;
      } else {
        return [];
      }
    } catch (error) {
      print(error);
      return [];
    }
  }

  List<RentalItem> _rentalItems = [];

  List<RentalItem> get rentalItems => [..._rentalItems];

  fetchRentalItemBySelectedSport({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchRentalItemBySelectedSport']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        accessToken: accessToken,
        body: body,
      );

      if (response['success']) {
        List<RentalItem> fetchedRentalItems = [];

        response['result'].forEach((rentalItems) {
          fetchedRentalItems.add(RentalItem.jsonToRentalItem(rentalItems));
        });
        _rentalItems = fetchedRentalItems;
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {'success': false, 'message': 'Failed to get rental items'};
    }
  }

  fetchBusinessReport({
    required String businessId,
    required String accessToken,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBusinessReport']}';
    var str = json.encode({"businessId": businessId});
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<ReportModel> loadedReport = [];
        responseData['result'].forEach((data) {
          loadedReport.add(
            ReportModel(
              id: data['_id'],
              createdAt: DateTime.parse(data['createdAt']).toLocal(),
              type: data['type'],
              fileName: data['fileName'],
              generatedBy: data['generatedBy'],
              fileLink: data['fileLink'],
              startedAt: DateTime.parse(data['startedAt']).toLocal(),
              businessId: data['businessId'],
            ),
          );
        });
        _reports = List.from(loadedReport);
        notifyListeners();
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchBookingsCount({
    required String businessId,
    required String accessToken,
    required String role,
    required List<String> turfs,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBookingsCount']}';
    var str = json.encode({
      "businessId": businessId,
      "role": role,
      "turfs": turfs,
    });
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        cancelledCount = responseData['cancelledCount'].toInt();
        completedCount = responseData['completedCount'].toInt();
        newCount = responseData['newCount'].toInt();
        notifyListeners();
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }
double getbookingBalanceAmount(BookingModel booking){
  double totalAmount = booking.totalAmount;
_extraItemsByBookings[booking.id]?.forEach((item) {
    totalAmount += item.price * item.quantity;
  });
  return (totalAmount - booking.amountPaid - booking.discountedAmount).abs();

  
}

  generateBookingReport({
    required String businessId,
    required String accessToken,
    required String startDate,
    required String endDate,
    required String turfId,
    required String generatedBy,
  }) async {
    final url = '${webApi['domain']}${endPoint['generateBookingReport']}';
    var str = json.encode({
      "businessId": businessId,
      "startDate": startDate,
      "endDate": endDate,
      "generatedBy": generatedBy,
      "turfId": turfId,
    });
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        _reports.add(
          ReportModel(
            id: responseData['result']['_id'],
            createdAt:
                DateTime.parse(responseData['result']['createdAt']).toLocal(),
            type: responseData['result']['type'],
            fileName: responseData['result']['fileName'] ?? '',
            generatedBy: responseData['result']['generatedBy'],
            fileLink: responseData['result']['fileLink'] ?? '',
            startedAt:
                DateTime.parse(responseData['result']['startedAt']).toLocal(),
            businessId: responseData['result']['businessId'],
          ),
        );
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  editNoteForBooking({
    required String bookingId,
    required String accessToken,
    required String note,
  }) async {
    final url = '${webApi['domain']}${endPoint['editNoteForBooking']}';
    var str = json.encode({"bookingId": bookingId, "note": note});
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        int index = _bookings.indexWhere((booking) => booking.id == bookingId);
        if (index != -1) {
          _bookings[index].note = note;
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  deleteReport({required String reportId, required String accessToken}) async {
    final url = '${webApi['domain']}${endPoint['deleteReport']}';
    var str = json.encode({"reportId": reportId});
    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        int index = _reports.indexWhere((report) => report.id == reportId);
        if (index != -1) {
          _reports.removeAt(index);
          notifyListeners();
        }
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  verifyBookingByOTP({
    required String bookingId,
    required String accessToken,
    required String otp,
  }) async {
    final url = '${webApi['domain']}${endPoint['verifyBookingByOTP']}';
    var str = json.encode({"bookingId": bookingId, "otp": otp});
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      return responseData;
    } catch (error) {
      print(error);
      return false;
    }
  }

  fetchTurfAdmin({required String accessToken}) async {
    final url = '${webApi['domain']}${endPoint['fetchTurfAdmin']}';

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          'Authorization': "Bearer $accessToken",
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        return responseData['result'];
      } else {
        return [];
      }
    } catch (error) {
      print(error);
      return [];
    }
  }

  Future updatePaymentStatus({
    required String accessToken,
    required String bookingId,
    required String amount,
    required String status,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updatePaymentStatus']}';

      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        body: {'bookingId': bookingId, 'amount': amount, 'status': status},
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        int index = _bookings.indexWhere((booking) => booking.id == bookingId);
        if (index != -1) {
          _bookings[index].paymentStatus = response['result']['paymentStatus'];
          _bookings[index].bookingStatus = response['result']['bookingStatus'];
          _bookings[index].amountPaid =
              response['result']['amountPaid'] == null
                  ? 0
                  : response['result']['amountPaid'].toDouble();
          notifyListeners();
        }
        return response['result'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  Future updateBookingTime({
    required String accessToken,
    required String bookingId,
    required String newStartTime,
    required String newEndTime,
    required double amount,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateBookingTime']}';

      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        body: {
          'bookingId': bookingId,
          'newStartTime': newStartTime,
          'newEndTime': newEndTime,
          'amount': amount,
        },
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        int index = _bookings.indexWhere((booking) => booking.id == bookingId);
        if (index != -1) {
          _bookings[index].bookingSlotStartTime =
              response['result']['bookingSlotStartTime'].toDouble();
          _bookings[index].bookingSlotEndTime =
              response['result']['bookingSlotEndTime'].toDouble();
          _bookings[index].totalAmount =
              response['result']['totalAmount'].toDouble();
          notifyListeners();
        }
        return response['result'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  BookingModel? findBookingById(String id) {
    int index = _bookings.indexWhere((booking) => booking.id == id);
    return index == -1 ? null : _bookings[index];
  }

  getSkipCount(String selectedTab) {
    return _bookings.where((booking) => booking.tab == selectedTab).length;
  }

  List<BookingModel> getBookingByTab(String selectedTab) {
    return _bookings.where((booking) => booking.tab == selectedTab).toList();
  }

  fetchBusinessBookingV2({
    required String accessToken,
    required String selectedTab,
    required String businessId,
    required String role,
    bool refresh = false,
  }) async {
    try {
      if (refresh) {
        _bookings.removeWhere((booking) => booking.tab == selectedTab);
      }

      final url = '${webApi['domain']}${endPoint['fetchBusinessBookingV2']}';

      var body = {
        "businessId": businessId,
        "role": role,
        "status": selectedTab,
        "limit": 20,
        "skip": getSkipCount(selectedTab),
        "turfs": [],
      };

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<BookingModel> loadedBooking = [];

        response['result'].forEach(
          (booking) => loadedBooking.add(
            BookingModel.jsonToBooking(booking, selectedTab: selectedTab),
          ),
        );

        if (loadedBooking.isNotEmpty) _bookings = List.from(loadedBooking);

        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }
}
