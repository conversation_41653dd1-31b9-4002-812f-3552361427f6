import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../new_colors.dart';
import '../models/bookingModel.dart';
import '../models/extra_item_model.dart';

class PaymentSummaryWidget extends StatefulWidget {
  final BookingModel booking;
  final ExtraItem? extraItem;
  const PaymentSummaryWidget({Key? key, required this.booking, this.extraItem})
    : super(key: key);
  @override
  State<PaymentSummaryWidget> createState() => _PaymentSummaryWidgetState();
}

class _PaymentSummaryWidgetState extends State<PaymentSummaryWidget> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme customTextTheme = const TextTheme();
  Map language = {};
  bool feeHide = false;
  bool loadConvenienceCharges = false;

  showConvenience() {
    loadConvenienceCharges = !loadConvenienceCharges;
    setState(() {});
  }

  Widget buildRow(
    String title,
    String value, {
    num fontSize = 14,
    FontWeight? fontWeight = FontWeight.w500,
    Color? color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: dW * 0.02),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: customTextTheme.titleLarge!.copyWith(
              fontWeight: fontWeight ?? FontWeight.w400,
              color: color ?? getGreyColor5(context),
              fontSize: tS * fontSize,
            ),
          ),
          SizedBox(width: dW * 0.02),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: dW * 0.5),
            child: TextWidget(
              title: value,
              fontWeight: fontWeight ?? FontWeight.w500,
              fontSize: tS * fontSize,
              color: color ?? getGreyColor5(context),
              maxLines: 2,
              textOverflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String getPaymentStatus() {
    if (widget.booking.amountPaid == 0) {
      return 'Unpaid';
    } else if (widget.booking.amountPaid <
        widget.booking.totalAmount - widget.booking.discountedAmount) {
      return "Partially Paid";
    } else {
      return "Paid";
    }
  }

  double gettotalAmountafterExtraitempprice() {
    double total = widget.booking.totalAmount;
    Provider.of<HomeProvider>(
      context,
      listen: true,
    ).extraItemsByBooking(widget.booking.id).forEach((item) {
      total += item.price * item.quantity;
    });
    return total;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    customTextTheme = Theme.of(context).textTheme;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: dW * 0.04),
      decoration: BoxDecoration(
        color: getWhiteColor(context),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(.15),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(vertical: dW * .02),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color:
                  getPaymentStatus() == 'Paid'
                      ? getGreenColor1(context)
                      : getLightYellowColor1(context),
            ),
            child: TextWidget(
              title: getPaymentStatus(),
              fontSize: 16,
              textAlign: TextAlign.center,
              color:
                  getPaymentStatus() == 'Paid'
                      ? getThemeColor()
                      : getYellowColor1(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          Padding(
            padding: EdgeInsets.all(dW * .04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  title: 'Payment Summary',
                  fontSize: 16,
                  color: Color(0xff3E3E3E),
                ),
                SizedBox(height: dW * 0.04),
                if (widget.booking.invoiceType != '') ...[
                  buildRow(
                    'Sub Total',
                    '\u20b9${(widget.booking.totalAmount - widget.booking.tax).toStringAsFixed(2)}',
                  ),
                  SizedBox(height: dW * 0.02),
                  buildRow(
                    'GST(18%)',
                    '\u20b9${convertAmountString(widget.booking.tax.toDouble())}',
                  ),
                  const Divider(color: Colors.black45, thickness: 0.2),
                ],
                if (widget.booking.convenienceFee != 0) ...[
                  buildRow(
                    'Sub Total',
                    widget.booking.basicAmount == 0
                        ? '\u20b9${convertAmountString((widget.booking.totalAmount - widget.booking.convenienceFee - widget.booking.gstOnConvenienceFee).toDouble())}'
                        : '\u20b9${convertAmountString(widget.booking.basicAmount.toDouble())}',
                  ),
                  SizedBox(height: dW * 0.017),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () => showConvenience(),
                        child: Row(
                          children: [
                            Container(
                              alignment: Alignment.topLeft,
                              width: dW * 0.35,
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: 'Convenience Fee',
                                  color: getGreyColor5(context),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Icon(
                              loadConvenienceCharges
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              color: getGreyColor5(context),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        alignment: Alignment.topRight,
                        width: dW * 0.3,
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: TextWidget(
                            title:
                                '+ \u20b9 ${widget.booking.convenienceFee + widget.booking.gstOnConvenienceFee}',
                            fontSize: 14,
                            color: getGreyColor5(context),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (loadConvenienceCharges) ...[
                    SizedBox(height: dW * 0.02),
                    Padding(
                      padding: EdgeInsets.only(left: dW * 0.1),
                      child: buildRow(
                        'Base Amouny',
                        '\u20b9 ${convertAmountString(widget.booking.convenienceFee.toDouble())}',
                        fontSize: 12,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: dW * 0.1),
                      child: buildRow(
                        'GST (18%)',
                        '\u20b9 ${convertAmountString(widget.booking.gstOnConvenienceFee.toDouble())}',
                        fontSize: 12,
                      ),
                    ),
                  ],
                  const Divider(color: Colors.black45, thickness: 0.2),
                  SizedBox(height: dW * 0.01),
                ],
                buildRow(
                  'Total',
                  '\u20b9${convertAmountString(widget.booking.totalAmount.toDouble())}',
                  color: getThemeColor(),
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
                const Divider(color: Colors.black45, thickness: 0.2),
                SizedBox(height: dW * 0.01),
                buildRow(
                  'Discount',
                  '- \u20b9${convertAmountString(widget.booking.discountedAmount.toDouble())}',
                ),
                SizedBox(height: dW * 0.017),
                buildRow(
                  'Paid',
                  '- \u20b9${convertAmountString(widget.booking.amountPaid.toDouble())}',
                ),
                const Divider(color: Colors.black45),
                if (widget.booking.paymentStatus == "REFUND")
                  SizedBox(height: dW * 0.017),
                if (widget.booking.bookingStatus == "CANCELLED")
                  buildRow(
                    'Refunded Amount',
                    '\u20b9${convertAmountString(widget.booking.refundedAmount.toDouble())}',
                    color: getThemeColor(),
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                Consumer<HomeProvider>(
                  builder: (context, homeprovider, _) {
                    List<ExtraItem> extraItems = [
                      ...homeprovider.extraItemsByBooking(widget.booking.id),
                    ];
                    return extraItems.isNotEmpty
                        ? Column(
                          children:
                              extraItems.map((item) {
                                return buildRow(
                                  item.name,
                                  '+ \u20b9${convertAmountString((item.price * item.quantity).toDouble())}',
                                  // color: getThemeColor(),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                );
                              }).toList(),
                        )
                        : const SizedBox.shrink();
                  },
                ),
                if (widget.booking.bookingStatus != "CANCELLED") ...{
                  buildRow(
                    'Balance Amount',
                    '\u20b9${convertAmountString(Provider.of<HomeProvider>(context,listen: true).getbookingBalanceAmount(widget.booking))}',
                    color: getThemeColor(),
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                },
              ],
            ),
          ),
        ],
      ),
    );
  }
}
