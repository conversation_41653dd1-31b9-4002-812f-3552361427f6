import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:open_file_safe_plus/open_file_safe_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../homeModule/models/reportModel.dart';

class ReportWidget extends StatefulWidget {
  final double deviceWidth;
  final double deviceHeight;
  final double textScaleFactor;
  final String accessToken;
  final ReportModel report;
  const ReportWidget({
    Key? key,
    required this.deviceWidth,
    required this.deviceHeight,
    required this.textScaleFactor,
    required this.report,
    required this.accessToken,
  }) : super(key: key);

  @override
  State<ReportWidget> createState() => _ReportWidgetState();
}

class _ReportWidgetState extends State<ReportWidget> {
  Dio dio = new Dio();

  bool isLoading = false;

  getPermission() async {
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      await Permission.storage.request();
    }

    // var manage = await Permission.manageExternalStorage.status;
    // if (!manage.isGranted) {
    //   await Permission.manageExternalStorage.request();
    // }
  }

  Future openFile({required String url, required String fileName}) async {
    final file = await downloadFileIIOS(url, fileName);

    if (file == null) {
      callToastMessage('Unable to view');
      return;
    }

    print('Path: ${file.path}');
    try {
      final a = await OpenFileSafePlus.open(file.path);
      print(a.message);
    } catch (e) {
      print(e);
      callToastMessage('Unable to view');
      return;
    }
  }

  Future<File?> downloadFileIIOS(String url, String? fileName) async {
    final appStorage = await getApplicationDocumentsDirectory();
    final file = File('${appStorage.path}/$fileName');
    //
    try {
      final response = await Dio().get(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: false,
          receiveTimeout: Duration(seconds: 0),
        ),
      );
      //
      final raf = file.openSync(mode: FileMode.write);
      raf.writeFromSync(response.data);
      await raf.close();
      //
      return file;
    } catch (e) {
      return null;
    }
  }

  downloadFileAndroid() async {
    await getPermission();
    setState(() {
      widget.report.download = true;
    });

    Directory directory;
    try {
      directory = (await getExternalStorageDirectory())!;
      // String newPath = "";
      print(directory);
      // List<String> paths = directory.path.split("/");
      // for (int x = 1; x < paths.length; x++) {
      //   String folder = paths[x];
      //   if (folder != "Android") {
      //     newPath += "/" + folder;
      //   } else {
      //     break;
      //   }
      // }
      // newPath = newPath + "/BYS_Report";
      // directory = Directory(newPath);
      // if (!await directory.exists()) {
      //   await directory.create(recursive: true);
      // }
      directory = Directory('/storage/emulated/0/Download');

      File saveFile = File(directory.path + "/${widget.report.fileName}");

      await dio.download(widget.report.fileLink, saveFile.path);

      setState(() {
        widget.report.download = false;
      });

      callToastMessage('Successfully Downloaded in Download Folder');

      OpenFileSafePlus.open(saveFile.path);
    } catch (error) {
      print(error);
      setState(() {
        widget.report.download = false;
      });
    }
  }

  deleteReport() async {
    try {
      setState(() {
        isLoading = true;
      });
      final data =
          await Provider.of<HomeProvider>(context, listen: false).deleteReport(
        reportId: widget.report.id,
        accessToken: widget.accessToken,
      );
      if (data) {
        callToastMessage('Report Deleted Successfully');
      } else {
        callToastMessage('Something went wrong');
      }
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      margin: EdgeInsets.only(bottom: widget.deviceWidth * 0.05),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: widget.deviceWidth * 0.03,
                  vertical: widget.deviceWidth * 0.03,
                ),
                decoration: BoxDecoration(
                    color: getThemeColor(),
                    borderRadius: BorderRadius.circular(6)),
                child: AssetSvgIcon(iconName: 'report3'),
              ),
              SizedBox(width: widget.deviceWidth * 0.032),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ConstrainedBox(
                    constraints:
                        BoxConstraints(maxWidth: widget.deviceWidth * 0.45),
                    child: TextWidget(
                      title: widget.report.fileName,
                      maxLines: 2,
                      fontWeight: FontWeight.w500,
                      textAlign: TextAlign.left,
                      textOverflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: widget.deviceWidth * 0.01),
                  ConstrainedBox(
                    constraints:
                        BoxConstraints(maxWidth: widget.deviceWidth * 0.45),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: TextWidget(
                        title:
                            'Created: ${DateFormat('dd MMM yyyy').format(widget.report.createdAt)}',
                        fontSize: 11,
                        color: greyTextColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (widget.report.fileLink == '')
            Positioned(
              right: 0,
              bottom: 0,
              child: TextWidget(
                title: 'INPROGRESS',
                fontSize: 10,
                color: Colors.orange,
                fontWeight: FontWeight.w600,
              ),
            ),
          if (widget.report.fileLink != '')
            Positioned(
              right: 0,
              top: 5,
              child: widget.report.download || isLoading
                  ? circularForButton(widget.deviceWidth,
                      color: getThemeColor())
                  : Theme(
                      data: Theme.of(context).copyWith(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                      ),
                      child: PopupMenuButton(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.zero,
                        itemBuilder: (BuildContext bc) => [
                          popupMenuItem(
                            position: 1,
                            title: 'Download',
                            dW: widget.deviceWidth,
                            icon: 'download',
                            removeColor: true,
                          ),
                          popupMenuItem(
                            position: 2,
                            title: 'Delete',
                            dW: widget.deviceWidth,
                            icon: 'delete1',
                          ),
                        ],
                        onSelected: (value) {
                          if (value == 1) {
                            Platform.isAndroid
                                ? downloadFileAndroid()
                                : openFile(
                                    url: widget.report.fileLink,
                                    fileName: widget.report.fileName,
                                  );
                          } else if (value == 2) {
                            deleteReport();
                          }
                        },
                        child: const Icon(
                          Icons.more_vert,
                          color: Colors.black,
                          size: 20,
                        ),
                      ),
                    ),
            ),
        ],
      ),
    );
  }
}
