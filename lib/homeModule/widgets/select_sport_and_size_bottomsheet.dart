// import 'package:bys_business/colors.dart';
// import 'package:bys_business/homeModule/screens/venue_availability_screen.dart';
// import 'package:bys_business/new_colors.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';

// import '../../authModule/providers/auth.dart';
// import '../../commonWidgets/custom_button.dart';
// import '../../commonWidgets/divider_widget.dart';
// import '../../commonWidgets/text_widget.dart';
// import '../../common_function.dart';
// import '../../navigators.dart';
// import '../../venueModule/models/venue_model.dart';

// class SelectSportAndSizeBottomSheet extends StatefulWidget {
//   final Venue venue;
//   final String bookingType;

//   final SportType? selectedSport;
//   final String? selectedTurfSize;
//   final String? selectedCourtType;
//   final int? totalPlayersAllowed;

//   const SelectSportAndSizeBottomSheet({
//     required this.venue,
//     required this.bookingType,
//     this.selectedSport,
//     this.selectedTurfSize,
//     this.totalPlayersAllowed,
//     this.selectedCourtType,
//   });

//   @override
//   State<SelectSportAndSizeBottomSheet> createState() =>
//       _SelectSportAndSizeBottomSheetState();
// }

// class _SelectSportAndSizeBottomSheetState
//     extends State<SelectSportAndSizeBottomSheet> {
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;

//   TextTheme customTextTheme = const TextTheme();

//   bool isLoading = false;

//   SportType? selectedSport;
//   String selectedTurfSize = '';

//   int totalPlayersAllowed = 0;

//   String selectedCourtType = '';

//   List turfSizes = [];

//   setTurfSize(List<Slot> slots) {
//     for (var slot in slots) {
//       for (var size in slot.priceAndQuantity) {
//         if (!turfSizes.contains(size.title)) {
//           turfSizes.add(size.title);
//         }
//       }
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     if (widget.venue.sportCategory!.categoryName == 'Outdoor') {
//       if (widget.venue.cricket != null) {
//         setTurfSize(widget.venue.cricket!.slots);
//       } else if (widget.venue.football != null) {
//         setTurfSize(widget.venue.football!.slots);
//       }
//     } else if (widget.venue.sportCategory!.categoryName == 'Indoor') {
//       if (widget.venue.badminton != null) {
//         setTurfSize(widget.venue.badminton!.slots);
//       } else if (widget.venue.tableTennis != null) {
//         setTurfSize(widget.venue.tableTennis!.slots);
//       } else if (widget.venue.snooker != null) {
//         setTurfSize(widget.venue.snooker!.slots);
//       }
//     }

//     selectedSport = widget.selectedSport;
//     selectedTurfSize = widget.selectedTurfSize!;
//     if (widget.selectedSport!.sport == 'Badminton' &&
//         widget.selectedCourtType != '') {
//       selectedCourtType = widget.selectedCourtType!;
//     }
//     totalPlayersAllowed = widget.totalPlayersAllowed!;
//   }

//   @override
//   Widget build(BuildContext context) {
//     dH = MediaQuery.of(context).size.height;
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     customTextTheme = Theme.of(context).textTheme;

//     return SizedBox(
//       height: dH,
//       width: dW,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             margin: EdgeInsets.only(top: dW * 0.05),
//             padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
//             child: TextWidget(
//               title: widget.venue.sportCategory!.categoryName == 'Outdoor'
//                   ? 'Select Sport and Turf Size'
//                   : 'Select Sport',
//               fontWeight: FontWeight.w500,
//               fontSize: 15,
//               color: getGreyColor2(context),
//             ),
//           ),
//           DividerWidget(color: getGreyColor1(context)),
//           Expanded(
//             child: SingleChildScrollView(
//               padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
//               physics: const BouncingScrollPhysics(),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   TextWidget(
//                     title: 'Select Sport',
//                     color: getBlackColor(context),
//                     fontWeight: FontWeight.w600,
//                   ),
//                   SizedBox(height: dW * 0.03),
//                   Wrap(
//                     children: [
//                       ...widget.venue.sportsType!.map(
//                         (sport) => GestureDetector(
//                           onTap: () {
//                             selectedSport = sport;
//                             if (widget.venue.sportCategory!.categoryName !=
//                                 'Outdoor') {
//                               selectedSport = sport;
//                               totalPlayersAllowed = 0;
//                               selectedCourtType = '';
//                               selectedTurfSize = '';
//                             }
//                             setState(() {});
//                           },
//                           child: IntrinsicWidth(
//                             child: Container(
//                               // width: sport.sport == 'Badminton' ||
//                               //         sport.sport == 'Tennis' ||
//                               //         sport.sport == 'Snooker'
//                               //     ? dW * 0.4
//                               //     : dW * 0.3,
//                               margin: EdgeInsets.only(
//                                   right: dW * .035, bottom: dW * 0.03),
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                   color: selectedSport != null &&
//                                           selectedSport!.id == sport.id
//                                       ? getThemeColor()
//                                       : Colors.transparent,
//                                   borderRadius: BorderRadius.circular(8),
//                                   border: Border.all(
//                                       color: getThemeColor(), width: 1)),
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: dW * .03, vertical: dW * .02),
//                               child: Row(
//                                 crossAxisAlignment: CrossAxisAlignment.center,
//                                 children: [
//                                   // Image.network(sport.image, height: 20),
//                                   CachedNetworkImage(
//                                     imageUrl: sport.image,
//                                     height: 20,
//                                     placeholder: (_, __) {
//                                       return Image.asset(
//                                         'assets/placeholders/placeholder.png',
//                                         fit: BoxFit.fill,
//                                         height: 20,
//                                       );
//                                     },
//                                     fit: BoxFit.fill,
//                                   ),
//                                   SizedBox(width: dW * .02),
//                                   Text(
//                                     sport.sport,
//                                     style: customTextTheme.titleLarge!.copyWith(
//                                       fontWeight: FontWeight.w600,
//                                       color: selectedSport != null &&
//                                               selectedSport!.id == sport.id
//                                           ? whiteColor
//                                           : getThemeColor(),
//                                     ),
//                                   )
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: dW * 0.035),
//                   //if (widget.venue.sportCategory.categoryName == 'Outdoor')
//                   ...[
//                     if (selectedSport != null)
//                       TextWidget(
//                         title: widget.venue.sportCategory!.categoryName ==
//                                 'Outdoor'
//                             ? 'Select Turf Size'
//                             : (selectedSport != null &&
//                                     selectedSport!.sport == 'Snooker')
//                                 ? 'Select Table'
//                                 : 'Select Game Type',
//                         color: getBlackColor(context),
//                         fontWeight: FontWeight.w600,
//                       ),
//                     SizedBox(height: dW * 0.03),
//                     Wrap(
//                       children: [
//                         if (selectedSport != null)
//                           ...widget.venue.slots!.first.priceAndQuantity
//                               .map((e) {
//                             return Column(
//                               children: [
//                                 e.sport == selectedSport!.sport
//                                     ? GestureDetector(
//                                         onTap: () {
//                                           selectedTurfSize = e.title;
//                                           totalPlayersAllowed =
//                                               e.totalPlayersAllowed;
//                                           setState(() {});
//                                         },
//                                         child: IntrinsicWidth(
//                                           child: Container(
//                                             // width: dW * 0.15,
//                                             margin: EdgeInsets.only(
//                                                 right: dW * .035,
//                                                 bottom: dW * 0.03),
//                                             alignment: Alignment.center,
//                                             decoration: BoxDecoration(
//                                                 color:
//                                                     selectedTurfSize == e.title
//                                                         ? getThemeColor()
//                                                         : Colors.transparent,
//                                                 borderRadius:
//                                                     BorderRadius.circular(8),
//                                                 border: Border.all(
//                                                     color: getThemeColor(),
//                                                     width: 1)),
//                                             padding: EdgeInsets.symmetric(
//                                                 horizontal: dW * .03,
//                                                 vertical: dW * .02),
//                                             child: IntrinsicWidth(
//                                               child: Row(
//                                                 children: [
//                                                   // Image.asset(
//                                                   //   'assets/images/${e.title}.png',
//                                                   //   width: 15,
//                                                   //   height: 15,
//                                                   // ),
//                                                   SizedBox(
//                                                     width: dW * 0.02,
//                                                   ),
//                                                   Text(
//                                                     e.title,
//                                                     style: customTextTheme
//                                                         .titleLarge!
//                                                         .copyWith(
//                                                       fontWeight:
//                                                           FontWeight.w600,
//                                                       color: selectedTurfSize ==
//                                                               e.title
//                                                           ? whiteColor
//                                                           : getThemeColor(),
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                           ),
//                                         ),
//                                       )
//                                     : SizedBox(),
//                               ],
//                             );
//                           }),
//                         if (widget.venue.sportCategory!.categoryName ==
//                             'Outdoor')
//                           ...turfSizes.map(
//                             (size) => GestureDetector(
//                               onTap: () {
//                                 selectedTurfSize = size;
//                                 setState(() {});
//                               },
//                               child: IntrinsicWidth(
//                                 child: Container(
//                                   // width: dW * 0.15,
//                                   margin: EdgeInsets.only(
//                                       right: dW * .035, bottom: dW * 0.03),
//                                   alignment: Alignment.center,
//                                   decoration: BoxDecoration(
//                                       color: selectedTurfSize == size
//                                           ? getThemeColor()
//                                           : Colors.transparent,
//                                       borderRadius: BorderRadius.circular(8),
//                                       border: Border.all(
//                                           color: getThemeColor(), width: 1)),
//                                   padding: EdgeInsets.symmetric(
//                                       horizontal: dW * .03, vertical: dW * .02),
//                                   child: Text(
//                                     size,
//                                     style: customTextTheme.titleLarge!.copyWith(
//                                       fontWeight: FontWeight.w600,
//                                       color: selectedTurfSize == size
//                                           ? whiteColor
//                                           : getThemeColor(),
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           )
//                       ],
//                     ),
//                     if (selectedSport != null &&
//                         selectedSport!.sport == 'Badminton')
//                       Container(
//                         margin: EdgeInsets.only(bottom: dW * 0.03),
//                         child: TextWidget(
//                           title: 'Select Court type',
//                           color: getBlackColor(context),
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     if (selectedSport != null &&
//                         selectedSport!.sport == 'Badminton')
//                       ...widget.venue.slots!.first.priceAndQuantity.map((e) {
//                         return Column(
//                           children: [
//                             e.sport == selectedSport!.sport
//                                 ? GestureDetector(
//                                     onTap: () {
//                                       selectedCourtType = e.courtOrTable;
//                                       setState(() {});
//                                     },
//                                     child: IntrinsicWidth(
//                                       child: e.title == 'Singles'
//                                           ? Container(
//                                               // width: dW * 0.15,
//                                               margin: EdgeInsets.only(
//                                                   right: dW * .035,
//                                                   bottom: dW * 0.03),
//                                               alignment: Alignment.center,
//                                               decoration: BoxDecoration(
//                                                   color: selectedCourtType ==
//                                                           e.courtOrTable
//                                                       ? getThemeColor()
//                                                       : Colors.transparent,
//                                                   borderRadius:
//                                                       BorderRadius.circular(8),
//                                                   border: Border.all(
//                                                       color: getThemeColor(),
//                                                       width: 1)),
//                                               padding: EdgeInsets.symmetric(
//                                                   horizontal: dW * .03,
//                                                   vertical: dW * .02),
//                                               child: Row(
//                                                 children: [
//                                                   // Image.asset(
//                                                   //   'assets/images/${e.courtOrTable}.png',
//                                                   //   width: 15,
//                                                   //   height: 15,
//                                                   // ),
//                                                   SizedBox(
//                                                     width: dW * 0.02,
//                                                   ),
//                                                   Text(
//                                                     e.courtOrTable ==
//                                                             'Wooden Court'
//                                                         ? 'Default Court'
//                                                         : e.courtOrTable,
//                                                     style: customTextTheme
//                                                         .titleLarge!
//                                                         .copyWith(
//                                                       fontWeight:
//                                                           FontWeight.w600,
//                                                       color:
//                                                           selectedCourtType ==
//                                                                   e.courtOrTable
//                                                               ? whiteColor
//                                                               : getThemeColor(),
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                             )
//                                           : SizedBox.shrink(),
//                                     ),
//                                   )
//                                 : SizedBox(),
//                           ],
//                         );
//                       }),
//                   ]
// //                   ...[
// //   if (selectedSport != null)
// //     TextWidget(
// //       title: widget.venue.sportCategory!.categoryName == 'Outdoor'
// //           ? 'Select Turf Size'
// //           : (selectedSport != null &&
// //                   (selectedSport!.sport == 'Snooker' ||
// //                       selectedSport!.sport == 'Badminton'))
// //               ? 'Select Game Type'
// //               : 'Select Game Type',
// //       color: getBlackColor(context),
// //       fontWeight: FontWeight.w600,
// //     ),
// //   SizedBox(height: dW * 0.03),
// //   Wrap(
// //     children: [
// //       if (selectedSport != null)
// //         ...widget.venue.slots!.first.priceAndQuantity.map((e) {
// //           return Column(
// //             children: [
// //               e.sport == selectedSport!.sport
// //                   ? GestureDetector(
// //                       onTap: () {
// //                         selectedTurfSize = e.title;
// //                         totalPlayersAllowed = e.totalPlayersAllowed;
// //                         setState(() {});
// //                       },
// //                       child: IntrinsicWidth(
// //                         child: Container(
// //                           margin: EdgeInsets.only(
// //                               right: dW * .035, bottom: dW * 0.03),
// //                           alignment: Alignment.center,
// //                           decoration: BoxDecoration(
// //                               color: selectedTurfSize == e.title
// //                                   ? getThemeColor()
// //                                   : Colors.transparent,
// //                               borderRadius: BorderRadius.circular(8),
// //                               border: Border.all(
// //                                   color: getThemeColor(), width: 1)),
// //                           padding: EdgeInsets.symmetric(
// //                               horizontal: dW * .03, vertical: dW * .02),
// //                           child: Row(
// //                             children: [
// //                               SizedBox(
// //                                 width: dW * 0.02,
// //                               ),
// //                               Text(
// //                                 e.title,
// //                                 style: customTextTheme.titleLarge!.copyWith(
// //                                   fontWeight: FontWeight.w600,
// //                                   color: selectedTurfSize == e.title
// //                                       ? whiteColor
// //                                       : getThemeColor(),
// //                                 ),
// //                               ),
// //                             ],
// //                           ),
// //                         ),
// //                       ),
// //                     )
// //                   : SizedBox(),
// //             ],
// //           );
// //         }),
// //     ],
// //   ),
// //   if (selectedSport != null && selectedSport!.sport == 'Badminton')
// //     Container(
// //       margin: EdgeInsets.only(bottom: dW * 0.03),
// //       child: TextWidget(
// //         title: 'Select Court type',
// //         color: getBlackColor(context),
// //         fontWeight: FontWeight.w600,
// //       ),
// //     ),
// //   if (selectedSport != null && selectedSport!.sport == 'Badminton')
// //     ...widget.venue.slots!.first.priceAndQuantity.map((e) {
// //       return Column(
// //         children: [
// //           e.sport == selectedSport!.sport
// //               ? GestureDetector(
// //                   onTap: () {
// //                     selectedCourtType = e.courtOrTable;
// //                     setState(() {});
// //                   },
// //                   child: IntrinsicWidth(
// //                     child: e.title == 'Singles'
// //                         ? Container(
// //                             margin: EdgeInsets.only(
// //                                 right: dW * .035, bottom: dW * 0.03),
// //                             alignment: Alignment.center,
// //                             decoration: BoxDecoration(
// //                                 color: selectedCourtType == e.courtOrTable
// //                                     ? getThemeColor()
// //                                     : Colors.transparent,
// //                                 borderRadius: BorderRadius.circular(8),
// //                                 border: Border.all(
// //                                     color: getThemeColor(), width: 1)),
// //                             padding: EdgeInsets.symmetric(
// //                                 horizontal: dW * .03, vertical: dW * .02),
// //                             child: Row(
// //                               children: [
// //                                 SizedBox(
// //                                   width: dW * 0.02,
// //                                 ),
// //                                 Text(
// //                                   e.courtOrTable == 'Wooden Court'
// //                                       ? 'Default Court'
// //                                       : e.courtOrTable,
// //                                   style: customTextTheme.titleLarge!.copyWith(
// //                                     fontWeight: FontWeight.w600,
// //                                     color: selectedCourtType == e.courtOrTable
// //                                         ? whiteColor
// //                                         : getThemeColor(),
// //                                   ),
// //                                 ),
// //                               ],
// //                             ),
// //                           )
// //                         : SizedBox.shrink(),
// //                   ),
// //                 )
// //               : SizedBox(),
// //         ],
// //       );
// //     }),
// // ]
//                 ],
//               ),
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.symmetric(
//                 horizontal: dW * 0.05, vertical: dW * 0.04),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: CustomButton(
//                     buttonColor: getWhiteColor(context),
//                     textColor: getThemeColor(),
//                     borderColor: getThemeColor(),
//                     width: dW,
//                     height: dW * .13,
//                     buttonText: 'Cancel',
//                     onPressed: () => pop(),
//                   ),
//                 ),
//                 SizedBox(
//                   width: dW * 0.04,
//                 ),
//                 Expanded(
//                   child: CustomButton(
//                     width: dW,
//                     height: dW * .13,
//                     buttonText: 'Apply',
//                     onPressed: selectedSport != null &&
//                             selectedTurfSize != '' &&
//                             (selectedSport!.sport == 'Badminton'
//                                 ? selectedCourtType != ''
//                                 : selectedTurfSize != '')
//                         ? () {
//                             // pop(true );
//                             pop({
//                               'selectedSport': selectedSport,
//                               'selectedTurfSize': selectedTurfSize,
//                               'selectedCourtType': selectedCourtType,
//                               'totalPlayersAllowed': totalPlayersAllowed,
//                             });
//                             // Navigator.pop(context, {
//                             //   'selectedSport': selectedSport!,
//                             //   'selectedTurfSize': selectedTurfSize,
//                             // });
//                             // push(
//                             //   NamedRoute.bookingScreen,
//                             //   arguments: CreateBookingScreenArguments(
//                             //     venue: widget.venue,
//                             //     bookingType: widget.bookingType,
//                             //     selectedSport: selectedSport!,
//                             //     selectedTurfSize: selectedTurfSize,
//                             //   ),
//                             // );
//                           }
//                         : null,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// // SizedBox(
// //       height: dH,
// //       width: dW,
// //       child: Column(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           Container(
// //             margin: EdgeInsets.only(top: dW * 0.05),
// //             padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
// //             child: TextWidget(
// //               title: widget.venue.sportCategory!.categoryName == 'Outdoor'
// //                   ? 'Select Sport and Turf Size'
// //                   : 'Select Sport',
// //               fontWeight: FontWeight.w500,
// //               fontSize: 15,
// //               color: getGreyColor2(context),
// //             ),
// //           ),
// //           DividerWidget(color: getGreyColor1(context)),
// //           Expanded(
// //             child: SingleChildScrollView(
// //               padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
// //               physics: const BouncingScrollPhysics(),
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.start,
// //                 children: [
// //                   TextWidget(
// //                     title: 'Select Sport',
// //                     color: getBlackColor(context),
// //                     fontWeight: FontWeight.w600,
// //                   ),
// //                   SizedBox(height: dW * 0.03),
// //                   Wrap(
// //                     children: [
// //                       ...widget.venue.sportsType!.map(
// //                         (sport) => GestureDetector(
// //                           onTap: () {
// //                             selectedSport = sport;
// //                             if (widget.venue.sportCategory!.categoryName !=
// //                                 'Outdoor') {
// //                               selectedTurfSize = selectedSport!.sport;
// //                             }
// //                             setState(() {});
// //                           },
// //                           child: IntrinsicWidth(
// //                             child: Container(
// //                               // width: sport.sport == 'Badminton' ||
// //                               //         sport.sport == 'Table Tennis'
// //                               //     ? dW * 0.4
// //                               //     : dW * 0.3,
// //                               margin: EdgeInsets.only(
// //                                   right: dW * .035, bottom: dW * 0.03),
// //                               alignment: Alignment.center,
// //                               decoration: BoxDecoration(
// //                                   color: selectedSport != null &&
// //                                           selectedSport!.id == sport.id
// //                                       ? getThemeColor()
// //                                       : Colors.transparent,
// //                                   borderRadius: BorderRadius.circular(8),
// //                                   border: Border.all(
// //                                       color: getThemeColor(), width: 1)),
// //                               padding: EdgeInsets.symmetric(
// //                                   horizontal: dW * .03, vertical: dW * .02),
// //                               child: Row(
// //                                 crossAxisAlignment: CrossAxisAlignment.center,
// //                                 children: [
// //                                   Image.network(sport.image, height: 20),
// //                                   SizedBox(width: dW * .02),
// //                                   Text(
// //                                     sport.sport,
// //                                     style: customTextTheme.titleLarge!.copyWith(
// //                                       fontWeight: FontWeight.w600,
// //                                       color: selectedSport != null &&
// //                                               selectedSport!.id == sport.id
// //                                           ? Colors.white
// //                                           : getThemeColor(),
// //                                     ),
// //                                   )
// //                                 ],
// //                               ),
// //                             ),
// //                           ),
// //                         ),
// //                       ),
// //                     ],
// //                   ),
// //                   SizedBox(height: dW * 0.035),
// //                   // if (widget.venue.sportCategory!.categoryName ==
// //                   //     'Outdoor')
// //                   ...[
// //                     TextWidget(
// //                       title:
// //                           widget.venue.sportCategory!.categoryName == 'Outdoor'
// //                               ? 'Select Turf Size'
// //                               : 'Select Type',
// //                       color: getBlackColor(context),
// //                       fontWeight: FontWeight.w600,
// //                     ),
// //                     SizedBox(height: dW * 0.03),
// //                     Wrap(
// //                       children: [
// //                         ...turfSizes.map(
// //                           (size) => GestureDetector(
// //                             onTap: () {
// //                               selectedTurfSize = size;
// //                               setState(() {});
// //                             },
// //                             child: IntrinsicWidth(
// //                               child: Container(
// //                                 // width: dW * 0.15,
// //                                 margin: EdgeInsets.only(
// //                                     right: dW * .035, bottom: dW * 0.03),
// //                                 alignment: Alignment.center,
// //                                 decoration: BoxDecoration(
// //                                     color: selectedTurfSize == size
// //                                         ? getThemeColor()
// //                                         : Colors.transparent,
// //                                     borderRadius: BorderRadius.circular(8),
// //                                     border: Border.all(
// //                                         color: getThemeColor(), width: 1)),
// //                                 padding: EdgeInsets.symmetric(
// //                                     horizontal: dW * .03, vertical: dW * .02),
// //                                 child: Text(
// //                                   size,
// //                                   style: customTextTheme.titleLarge!.copyWith(
// //                                     fontWeight: FontWeight.w600,
// //                                     color: selectedTurfSize == size
// //                                         ? Colors.white
// //                                         : getThemeColor(),
// //                                   ),
// //                                 ),
// //                               ),
// //                             ),
// //                           ),
// //                         )
// //                       ],
// //                     ),
// //                   ]
// //                 ],
// //               ),
// //             ),
// //           ),
// //           Padding(
// //             padding: EdgeInsets.symmetric(
// //                 horizontal: dW * 0.05, vertical: dW * 0.04),
// //             child: Row(
// //               children: [
// //                 Expanded(
// //                   child: CustomButton(
// //                     buttonColor: getWhiteColor(context),
// //                     textColor: getThemeColor(),
// //                     borderColor: getThemeColor(),
// //                     width: dW,
// //                     height: dW * .13,
// //                     buttonText: 'Cancel',
// //                     onPressed: pop,
// //                   ),
// //                 ),
// //                 SizedBox(
// //                   width: dW * 0.04,
// //                 ),
// //                 Expanded(
// //                   child: CustomButton(
// //                     width: dW,
// //                     height: dW * .13,
// //                     buttonText: 'Continue',
// //                     onPressed: selectedSport != null && selectedTurfSize != ''
// //                         ? () {
// //                             // pop();
// //                             pop({
// //                               'selectedSport': selectedSport,
// //                               'selectedTurfSize': selectedTurfSize,
// //                             });
// //                             // push(
// //                             //   VenueAvailabilityScreen(
// //                             //     user: Provider.of<Auth>(context, listen: false)
// //                             //         .user,
// //                             //     selectedTurf: widget.venue,
// //                             //     bookingType: widget.bookingType,
// //                             //     // selectedSport: selectedSport!,
// //                             //     // selectedTurfSize: selectedTurfSize,
// //                             //   ),
// //                             // );
// //                           }
// //                         : null,
// //                   ),
// //                 ),
// //               ],
// //             ),
// //           ),
// //         ],
// //       ),
// //     );

import 'package:bys_business/colors.dart';
import 'package:bys_business/homeModule/screens/venue_availability_screen.dart';
import 'package:bys_business/new_colors.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../navigators.dart';
import '../../venueModule/models/venue_model.dart';

class SelectSportAndSizeBottomSheet extends StatefulWidget {
  final Venue venue;
  final String bookingType;

  final SportType? selectedSport;
  final String? selectedTurfSize;
  final String? selectedCourtType;
  final int? totalPlayersAllowed;

  const SelectSportAndSizeBottomSheet({
    required this.venue,
    required this.bookingType,
    this.selectedSport,
    this.selectedTurfSize,
    this.totalPlayersAllowed,
    this.selectedCourtType,
  });

  @override
  State<SelectSportAndSizeBottomSheet> createState() =>
      _SelectSportAndSizeBottomSheetState();
}

class _SelectSportAndSizeBottomSheetState
    extends State<SelectSportAndSizeBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  TextTheme customTextTheme = const TextTheme();

  bool isLoading = false;

  SportType? selectedSport;
  String selectedTurfSize = '';

  int totalPlayersAllowed = 0;

  String selectedCourtType = '';

  List turfSizes = [];

  setTurfSize(List<Slot> slots) {
    for (var slot in slots) {
      for (var size in slot.priceAndQuantity) {
        if (!turfSizes.contains(size.title)) {
          turfSizes.add(size.title);
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.venue.sportCategory!.categoryName == 'Outdoor') {
      if (widget.venue.cricket != null) {
        setTurfSize(widget.venue.cricket!.slots);
      } else if (widget.venue.football != null) {
        setTurfSize(widget.venue.football!.slots);
      }
    } else if (widget.venue.sportCategory!.categoryName == 'Indoor') {
      if (widget.venue.badminton != null) {
        setTurfSize(widget.venue.badminton!.slots);
      } else if (widget.venue.tableTennis != null) {
        setTurfSize(widget.venue.tableTennis!.slots);
      } else if (widget.venue.snooker != null) {
        setTurfSize(widget.venue.snooker!.slots);
      } else if (widget.venue.pool != null) {
        setTurfSize(widget.venue.pool!.slots);
      } else if (widget.venue.pickleball != null) {
        setTurfSize(widget.venue.pickleball!.slots);
      }
    }

    selectedSport = widget.selectedSport;
    selectedTurfSize = widget.selectedTurfSize!;
    if (widget.selectedSport!.sport == 'Badminton' &&
        widget.selectedCourtType != '') {
      selectedCourtType = widget.selectedCourtType!;
    }
    totalPlayersAllowed = widget.totalPlayersAllowed!;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    customTextTheme = Theme.of(context).textTheme;

    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: dW * 0.05),
            padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: TextWidget(
              title: widget.venue.sportCategory!.categoryName == 'Outdoor'
                  ? 'Select Sport and Turf Size'
                  : 'Select Sport',
              fontWeight: FontWeight.w500,
              fontSize: 15,
              color: getGreyColor2(context),
            ),
          ),
          DividerWidget(color: getGreyColor1(context)),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    title: 'Select Sport',
                    color: getBlackColor(context),
                    fontWeight: FontWeight.w600,
                  ),
                  SizedBox(height: dW * 0.03),
                  Wrap(
                    children: [
                      ...widget.venue.sportsType!.map(
                        (sport) => GestureDetector(
                          onTap: () {
                            selectedSport = sport;
                            if (widget.venue.sportCategory!.categoryName !=
                                'Outdoor') {
                              selectedSport = sport;
                              totalPlayersAllowed = 0;
                              selectedCourtType = '';
                              selectedTurfSize = '';
                            }
                            setState(() {});
                          },
                          child: IntrinsicWidth(
                            child: Container(
                              // width: sport.sport == 'Badminton' ||
                              //         sport.sport == 'Tennis' ||
                              //         sport.sport == 'Snooker'
                              //     ? dW * 0.4
                              //     : dW * 0.3,
                              margin: EdgeInsets.only(
                                  right: dW * .035, bottom: dW * 0.03),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: selectedSport != null &&
                                          selectedSport!.id == sport.id
                                      ? getThemeColor()
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                      color: getThemeColor(), width: 1)),
                              padding: EdgeInsets.symmetric(
                                  horizontal: dW * .03, vertical: dW * .02),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // Image.network(sport.image, height: 20),
                                  CachedNetworkImage(
                                    imageUrl: sport.image,
                                    height: 20,
                                    placeholder: (_, __) {
                                      return Image.asset(
                                        'assets/placeholders/placeholder.png',
                                        fit: BoxFit.fill,
                                        height: 20,
                                      );
                                    },
                                    fit: BoxFit.fill,
                                  ),
                                  SizedBox(width: dW * .02),
                                  Text(
                                    sport.sport,
                                    style: customTextTheme.titleLarge!.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: selectedSport != null &&
                                              selectedSport!.id == sport.id
                                          ? whiteColor
                                          : getThemeColor(),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: dW * 0.035),
                  //if (widget.venue.sportCategory.categoryName == 'Outdoor')
                  ...[
                    if (selectedSport != null)
                      TextWidget(
                        title: widget.venue.sportCategory!.categoryName ==
                                'Outdoor'
                            ? 'Select Turf Size'
                            : (selectedSport != null &&
                                    (selectedSport!.sport == 'Snooker' ||
                                        selectedSport!.sport == 'Pool'))
                                ? 'Select Table'
                                : 'Select Game Type',
                        color: getBlackColor(context),
                        fontWeight: FontWeight.w600,
                      ),
                    SizedBox(height: dW * 0.03),
                    Wrap(
                      children: [
                        if (selectedSport != null)
                          ...widget.venue.slots!.first.priceAndQuantity
                              .map((e) {
                            return Column(
                              children: [
                                e.sport == selectedSport!.sport
                                    ? GestureDetector(
                                        onTap: () {
                                          selectedTurfSize = e.title;
                                          totalPlayersAllowed =
                                              e.totalPlayersAllowed;
                                          setState(() {});
                                        },
                                        child: IntrinsicWidth(
                                          child: Container(
                                            // width: dW * 0.15,
                                            margin: EdgeInsets.only(
                                                right: dW * .035,
                                                bottom: dW * 0.03),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color:
                                                    selectedTurfSize == e.title
                                                        ? getThemeColor()
                                                        : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                    color: getThemeColor(),
                                                    width: 1)),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: dW * .03,
                                                vertical: dW * .02),
                                            child: IntrinsicWidth(
                                              child: Row(
                                                children: [
                                                  // Image.asset(
                                                  //   'assets/images/${e.title}.png',
                                                  //   width: 15,
                                                  //   height: 15,
                                                  // ),
                                                  SizedBox(
                                                    width: dW * 0.02,
                                                  ),
                                                  // Text(
                                                  //   e.title,
                                                  //   style: customTextTheme
                                                  //       .titleLarge!
                                                  //       .copyWith(
                                                  //     fontWeight:
                                                  //         FontWeight.w600,
                                                  //     color: selectedTurfSize ==
                                                  //             e.title
                                                  //         ? whiteColor
                                                  //         : getThemeColor(),
                                                  //   ),
                                                  // ),

                                                  Text(
                                                    widget.venue.sportCategory!
                                                                .categoryName ==
                                                            'Outdoor'
                                                        ? e.title
                                                        : e.title
                                                            .split('(')[0]
                                                            .trim(),
                                                    style: customTextTheme
                                                        .titleLarge!
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: selectedTurfSize ==
                                                              e.title
                                                          ? whiteColor
                                                          : getThemeColor(),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    : SizedBox(),
                              ],
                            );
                          }),
                        if (widget.venue.sportCategory!.categoryName ==
                            'Outdoor')
                          ...turfSizes.map(
                            (size) => GestureDetector(
                              onTap: () {
                                selectedTurfSize = size;
                                setState(() {});
                              },
                              child: IntrinsicWidth(
                                child: Container(
                                  // width: dW * 0.15,
                                  margin: EdgeInsets.only(
                                      right: dW * .035, bottom: dW * 0.03),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      color: selectedTurfSize == size
                                          ? getThemeColor()
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: getThemeColor(), width: 1)),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: dW * .03, vertical: dW * .02),
                                  child: Text(
                                    size,
                                    style: customTextTheme.titleLarge!.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: selectedTurfSize == size
                                          ? whiteColor
                                          : getThemeColor(),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          )
                      ],
                    ),
                    // if (selectedSport != null &&
                    //     selectedSport!.sport == 'Badminton')
                    //   Container(
                    //     margin: EdgeInsets.only(bottom: dW * 0.03),
                    //     child: TextWidget(
                    //       title: 'Select Court type',
                    //       color: getBlackColor(context),
                    //       fontWeight: FontWeight.w600,
                    //     ),
                    //   ),
                    if (selectedSport != null &&
                        selectedSport!.sport == 'Badminton')
                      ...widget.venue.slots!.first.priceAndQuantity.map((e) {
                        return Column(
                          children: [
                            e.sport == selectedSport!.sport
                                ? GestureDetector(
                                    onTap: () {
                                      selectedCourtType = e.courtOrTable;
                                      setState(() {});
                                    },
                                    child: IntrinsicWidth(
                                      child: e.title == 'Singles'
                                          ? Container(
                                              // width: dW * 0.15,
                                              margin: EdgeInsets.only(
                                                  right: dW * .035,
                                                  bottom: dW * 0.03),
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                  color: selectedCourtType ==
                                                          e.courtOrTable
                                                      ? getThemeColor()
                                                      : Colors.transparent,
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  border: Border.all(
                                                      color: getThemeColor(),
                                                      width: 1)),
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: dW * .03,
                                                  vertical: dW * .02),
                                              child: Row(
                                                children: [
                                                  // Image.asset(
                                                  //   'assets/images/${e.courtOrTable}.png',
                                                  //   width: 15,
                                                  //   height: 15,
                                                  // ),
                                                  SizedBox(
                                                    width: dW * 0.02,
                                                  ),
                                                  Text(
                                                    e.courtOrTable ==
                                                            'Wooden Court'
                                                        ? 'Default Court'
                                                        : e.courtOrTable,
                                                    style: customTextTheme
                                                        .titleLarge!
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color:
                                                          selectedCourtType ==
                                                                  e.courtOrTable
                                                              ? whiteColor
                                                              : getThemeColor(),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : SizedBox.shrink(),
                                    ),
                                  )
                                : SizedBox(),
                          ],
                        );
                      }),
                  ]
//                   ...[
//   if (selectedSport != null)
//     TextWidget(
//       title: widget.venue.sportCategory!.categoryName == 'Outdoor'
//           ? 'Select Turf Size'
//           : (selectedSport != null &&
//                   (selectedSport!.sport == 'Snooker' ||
//                       selectedSport!.sport == 'Badminton'))
//               ? 'Select Game Type'
//               : 'Select Game Type',
//       color: getBlackColor(context),
//       fontWeight: FontWeight.w600,
//     ),
//   SizedBox(height: dW * 0.03),
//   Wrap(
//     children: [
//       if (selectedSport != null)
//         ...widget.venue.slots!.first.priceAndQuantity.map((e) {
//           return Column(
//             children: [
//               e.sport == selectedSport!.sport
//                   ? GestureDetector(
//                       onTap: () {
//                         selectedTurfSize = e.title;
//                         totalPlayersAllowed = e.totalPlayersAllowed;
//                         setState(() {});
//                       },
//                       child: IntrinsicWidth(
//                         child: Container(
//                           margin: EdgeInsets.only(
//                               right: dW * .035, bottom: dW * 0.03),
//                           alignment: Alignment.center,
//                           decoration: BoxDecoration(
//                               color: selectedTurfSize == e.title
//                                   ? getThemeColor()
//                                   : Colors.transparent,
//                               borderRadius: BorderRadius.circular(8),
//                               border: Border.all(
//                                   color: getThemeColor(), width: 1)),
//                           padding: EdgeInsets.symmetric(
//                               horizontal: dW * .03, vertical: dW * .02),
//                           child: Row(
//                             children: [
//                               SizedBox(
//                                 width: dW * 0.02,
//                               ),
//                               Text(
//                                 e.title,
//                                 style: customTextTheme.titleLarge!.copyWith(
//                                   fontWeight: FontWeight.w600,
//                                   color: selectedTurfSize == e.title
//                                       ? whiteColor
//                                       : getThemeColor(),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     )
//                   : SizedBox(),
//             ],
//           );
//         }),
//     ],
//   ),
//   if (selectedSport != null && selectedSport!.sport == 'Badminton')
//     Container(
//       margin: EdgeInsets.only(bottom: dW * 0.03),
//       child: TextWidget(
//         title: 'Select Court type',
//         color: getBlackColor(context),
//         fontWeight: FontWeight.w600,
//       ),
//     ),
//   if (selectedSport != null && selectedSport!.sport == 'Badminton')
//     ...widget.venue.slots!.first.priceAndQuantity.map((e) {
//       return Column(
//         children: [
//           e.sport == selectedSport!.sport
//               ? GestureDetector(
//                   onTap: () {
//                     selectedCourtType = e.courtOrTable;
//                     setState(() {});
//                   },
//                   child: IntrinsicWidth(
//                     child: e.title == 'Singles'
//                         ? Container(
//                             margin: EdgeInsets.only(
//                                 right: dW * .035, bottom: dW * 0.03),
//                             alignment: Alignment.center,
//                             decoration: BoxDecoration(
//                                 color: selectedCourtType == e.courtOrTable
//                                     ? getThemeColor()
//                                     : Colors.transparent,
//                                 borderRadius: BorderRadius.circular(8),
//                                 border: Border.all(
//                                     color: getThemeColor(), width: 1)),
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: dW * .03, vertical: dW * .02),
//                             child: Row(
//                               children: [
//                                 SizedBox(
//                                   width: dW * 0.02,
//                                 ),
//                                 Text(
//                                   e.courtOrTable == 'Wooden Court'
//                                       ? 'Default Court'
//                                       : e.courtOrTable,
//                                   style: customTextTheme.titleLarge!.copyWith(
//                                     fontWeight: FontWeight.w600,
//                                     color: selectedCourtType == e.courtOrTable
//                                         ? whiteColor
//                                         : getThemeColor(),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           )
//                         : SizedBox.shrink(),
//                   ),
//                 )
//               : SizedBox(),
//         ],
//       );
//     }),
// ]
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: dW * 0.05, vertical: dW * 0.04),
            child: Row(
              children: [
                Expanded(
                  child: CustomButton(
                    buttonColor: getWhiteColor(context),
                    textColor: getThemeColor(),
                    borderColor: getThemeColor(),
                    width: dW,
                    height: dW * .13,
                    buttonText: 'Cancel',
                    onPressed: () => pop(),
                  ),
                ),
                SizedBox(
                  width: dW * 0.04,
                ),
                Expanded(
                  child: CustomButton(
                    width: dW,
                    height: dW * .13,
                    buttonText: 'Apply',
                    onPressed: selectedSport != null && selectedTurfSize != ''
                        // &&
                        // (selectedSport!.sport == 'Badminton'
                        //     ? selectedCourtType != ''
                        //     : selectedTurfSize != '')
                        ? () {
                            // pop(true );
                            pop({
                              'selectedSport': selectedSport,
                              'selectedTurfSize': selectedTurfSize,
                              'selectedCourtType': selectedCourtType,
                              'totalPlayersAllowed': totalPlayersAllowed,
                            });
                            // Navigator.pop(context, {
                            //   'selectedSport': selectedSport!,
                            //   'selectedTurfSize': selectedTurfSize,
                            // });
                            // push(
                            //   NamedRoute.bookingScreen,
                            //   arguments: CreateBookingScreenArguments(
                            //     venue: widget.venue,
                            //     bookingType: widget.bookingType,
                            //     selectedSport: selectedSport!,
                            //     selectedTurfSize: selectedTurfSize,
                            //   ),
                            // );
                          }
                        : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


// SizedBox(
//       height: dH,
//       width: dW,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             margin: EdgeInsets.only(top: dW * 0.05),
//             padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
//             child: TextWidget(
//               title: widget.venue.sportCategory!.categoryName == 'Outdoor'
//                   ? 'Select Sport and Turf Size'
//                   : 'Select Sport',
//               fontWeight: FontWeight.w500,
//               fontSize: 15,
//               color: getGreyColor2(context),
//             ),
//           ),
//           DividerWidget(color: getGreyColor1(context)),
//           Expanded(
//             child: SingleChildScrollView(
//               padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
//               physics: const BouncingScrollPhysics(),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   TextWidget(
//                     title: 'Select Sport',
//                     color: getBlackColor(context),
//                     fontWeight: FontWeight.w600,
//                   ),
//                   SizedBox(height: dW * 0.03),
//                   Wrap(
//                     children: [
//                       ...widget.venue.sportsType!.map(
//                         (sport) => GestureDetector(
//                           onTap: () {
//                             selectedSport = sport;
//                             if (widget.venue.sportCategory!.categoryName !=
//                                 'Outdoor') {
//                               selectedTurfSize = selectedSport!.sport;
//                             }
//                             setState(() {});
//                           },
//                           child: IntrinsicWidth(
//                             child: Container(
//                               // width: sport.sport == 'Badminton' ||
//                               //         sport.sport == 'Table Tennis'
//                               //     ? dW * 0.4
//                               //     : dW * 0.3,
//                               margin: EdgeInsets.only(
//                                   right: dW * .035, bottom: dW * 0.03),
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                   color: selectedSport != null &&
//                                           selectedSport!.id == sport.id
//                                       ? getThemeColor()
//                                       : Colors.transparent,
//                                   borderRadius: BorderRadius.circular(8),
//                                   border: Border.all(
//                                       color: getThemeColor(), width: 1)),
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: dW * .03, vertical: dW * .02),
//                               child: Row(
//                                 crossAxisAlignment: CrossAxisAlignment.center,
//                                 children: [
//                                   Image.network(sport.image, height: 20),
//                                   SizedBox(width: dW * .02),
//                                   Text(
//                                     sport.sport,
//                                     style: customTextTheme.titleLarge!.copyWith(
//                                       fontWeight: FontWeight.w600,
//                                       color: selectedSport != null &&
//                                               selectedSport!.id == sport.id
//                                           ? Colors.white
//                                           : getThemeColor(),
//                                     ),
//                                   )
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: dW * 0.035),
//                   // if (widget.venue.sportCategory!.categoryName ==
//                   //     'Outdoor')
//                   ...[
//                     TextWidget(
//                       title:
//                           widget.venue.sportCategory!.categoryName == 'Outdoor'
//                               ? 'Select Turf Size'
//                               : 'Select Type',
//                       color: getBlackColor(context),
//                       fontWeight: FontWeight.w600,
//                     ),
//                     SizedBox(height: dW * 0.03),
//                     Wrap(
//                       children: [
//                         ...turfSizes.map(
//                           (size) => GestureDetector(
//                             onTap: () {
//                               selectedTurfSize = size;
//                               setState(() {});
//                             },
//                             child: IntrinsicWidth(
//                               child: Container(
//                                 // width: dW * 0.15,
//                                 margin: EdgeInsets.only(
//                                     right: dW * .035, bottom: dW * 0.03),
//                                 alignment: Alignment.center,
//                                 decoration: BoxDecoration(
//                                     color: selectedTurfSize == size
//                                         ? getThemeColor()
//                                         : Colors.transparent,
//                                     borderRadius: BorderRadius.circular(8),
//                                     border: Border.all(
//                                         color: getThemeColor(), width: 1)),
//                                 padding: EdgeInsets.symmetric(
//                                     horizontal: dW * .03, vertical: dW * .02),
//                                 child: Text(
//                                   size,
//                                   style: customTextTheme.titleLarge!.copyWith(
//                                     fontWeight: FontWeight.w600,
//                                     color: selectedTurfSize == size
//                                         ? Colors.white
//                                         : getThemeColor(),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                   ]
//                 ],
//               ),
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.symmetric(
//                 horizontal: dW * 0.05, vertical: dW * 0.04),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: CustomButton(
//                     buttonColor: getWhiteColor(context),
//                     textColor: getThemeColor(),
//                     borderColor: getThemeColor(),
//                     width: dW,
//                     height: dW * .13,
//                     buttonText: 'Cancel',
//                     onPressed: pop,
//                   ),
//                 ),
//                 SizedBox(
//                   width: dW * 0.04,
//                 ),
//                 Expanded(
//                   child: CustomButton(
//                     width: dW,
//                     height: dW * .13,
//                     buttonText: 'Continue',
//                     onPressed: selectedSport != null && selectedTurfSize != ''
//                         ? () {
//                             // pop();
//                             pop({
//                               'selectedSport': selectedSport,
//                               'selectedTurfSize': selectedTurfSize,
//                             });
//                             // push(
//                             //   VenueAvailabilityScreen(
//                             //     user: Provider.of<Auth>(context, listen: false)
//                             //         .user,
//                             //     selectedTurf: widget.venue,
//                             //     bookingType: widget.bookingType,
//                             //     // selectedSport: selectedSport!,
//                             //     // selectedTurfSize: selectedTurfSize,
//                             //   ),
//                             // );
//                           }
//                         : null,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
  
