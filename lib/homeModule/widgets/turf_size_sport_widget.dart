import 'package:flutter/material.dart';

class TurfSizeOrSportWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final Map size;
  final int index;
  final String selectedSizeOrSport;
  const TurfSizeOrSportWidget({
    required this.dW,
    required this.tS,
    required this.size,
    required this.index,
    required this.selectedSizeOrSport,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
        color: selectedSizeOrSport == size['title']
            ? Theme.of(context).primaryColor
            : null,
        border: Border.all(
          color: selectedSizeOrSport == size['title']
              ? Theme.of(context).primaryColor
              : Colors.grey.shade400,
        ),
      ),
      constraints: BoxConstraints(minWidth: dW * 0.14),
      padding: EdgeInsets.symmetric(
        horizontal: dW * 0.023,
        vertical: dW * 0.013,
      ),
      margin: EdgeInsets.only(
        right: dW * 0.022,
        left: index == 0 ? dW * 0.03 : 0,
      ),
      child: Column(
        children: [
          Text(
            '${size['title'].toString().replaceAll(':', ' x ')} ',
            style: Theme.of(context).textTheme.displayLarge!.copyWith(
                  fontSize: tS * 12.5,
                  color: selectedSizeOrSport == size['title']
                      ? Colors.white
                      : Colors.black,
                  fontWeight: selectedSizeOrSport == size['title']
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
          ),
          SizedBox(height: dW * 0.005),
          if (size['label'] != '')
            Container(
              constraints: BoxConstraints(
                maxWidth: dW * 0.32,
              ),
              child: Text(
                '(${size['label'] ?? ''})',
                style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      fontSize: tS * 11,
                      color: selectedSizeOrSport == size['title']
                          ? Colors.white
                          : Colors.black,
                      fontWeight: selectedSizeOrSport == size['title']
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
        ],
      ),
    );
  }
}

class SlotStatusIndicator extends StatelessWidget {
  final Color borderColor;
  final Color fillColor;
  final double dW;
  final String text;

  const SlotStatusIndicator({
    required this.borderColor,
    required this.fillColor,
    required this.dW,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: dW * .03,
          width: dW * .03,
          decoration: BoxDecoration(
            color: fillColor,
            border: Border.all(color: borderColor),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        SizedBox(width: dW * .02),
        Text(
          text,
          style: Theme.of(context).textTheme.titleSmall!.copyWith(
                fontWeight: FontWeight.w400,
                color: const Color(0xFF434343),
              ),
        )
      ],
    );
  }
}
