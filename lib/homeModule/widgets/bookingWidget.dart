import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/new_colors.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

import '../../authModule/modals/userModel.dart';
import 'package:flutter/material.dart';

import '../../commonWidgets/circle_avatar_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../common_function.dart';

class BookingWidget extends StatefulWidget {
  final BookingModel booking;
  final UserModal user;
  final double deviceWidth;
  final double textScaleFactor;
  final bool fromCustomer;
  final Function(int count, bool isSelected)? onSelectionChanged;
  bool fromViewAll;
  BookingWidget({
    Key? key,
    required this.booking,
    required this.user,
    required this.deviceWidth,
    required this.textScaleFactor,
    this.fromCustomer = false,
    this.onSelectionChanged,
    this.fromViewAll = false,
  }) : super(key: key);

  @override
  State<BookingWidget> createState() => _BookingWidgetState();
}

class _BookingWidgetState extends State<BookingWidget> {
  bool isSelected = false;

  goToDescriptionScreen(BuildContext context) {
    push(BookingDescriptionScreen(booking: widget.booking, user: widget.user));
  }

  String getPaymentStatus() {
    // if (booking.amountPaid == 0) {
    //   return 'Unpaid';
    // } else if (booking.amountPaid <
    //     booking.totalAmount - booking.discountedAmount) {
    //   return "Partially Paid";
    // } else {
    //   return "Paid";
    // }
    return widget.booking.bookingStatus == 'CANCELLED'
        ? 'Cancelled'
        : widget.booking.amountPaid == 0
            ? 'Unpaid'
            : widget.booking.amountPaid.ceil() <
                    widget.booking.totalAmount.ceil() -
                        widget.booking.discountedAmount.ceil()
                ? "Partially Paid"
                : "Paid";
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        goToDescriptionScreen(context);
      },
      onLongPress: () {},
      child: CustomContainer(
          margin: EdgeInsets.only(bottom: widget.deviceWidth * 0.05),
          vPadding: 0,
          hPadding: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // if (fromCustomer)
              //   Container(
              //     padding: EdgeInsets.symmetric(vertical: deviceWidth * .02),
              //     decoration: BoxDecoration(
              //       borderRadius: BorderRadius.only(
              //           topLeft: Radius.circular(8),
              //           topRight: Radius.circular(8)),
              //       color: booking.bookingStatus == 'COMPLETED'
              //           ? getLightGreenColor1(context)
              //           : booking.bookingStatus == 'CANCELLED'
              //               ? getLightRedColor1(context)
              //               : getLightYellowColor1(context),
              //     ),
              //     width: deviceWidth,
              //     alignment: Alignment.center,
              //     child: TextWidget(
              //       title: booking.bookingStatus,
              //       fontWeight: FontWeight.w600,
              //       color: booking.bookingStatus == 'COMPLETED'
              //           ? getThemeColor()
              //           : booking.bookingStatus == 'CANCELLED'
              //               ? getRedColor2(context)
              //               : getYellowColor1(context),
              //     ),
              //   ),
              Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: widget.deviceWidth * 0.045,
                      right: widget.deviceWidth * 0.045,
                      top: widget.deviceWidth * 0.025,
                      bottom: widget.deviceWidth * 0.04,
                    ),
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.booking.user != null)
                          CircleAvatarWidget(
                            avatar: widget.booking.user!.avatar,
                            userName: '${widget.booking.user!.name}',
                            height: 0.14,
                            parentRadius: 26,
                            fontSize: 17,
                          ),
                        SizedBox(width: widget.deviceWidth * 0.04),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (widget.booking.user != null)
                              ConstrainedBox(
                                constraints: BoxConstraints(
                                    maxWidth: widget.deviceWidth * 0.56),
                                child: TextWidget(
                                  title: '${widget.booking.user!.name}',
                                  fontWeight: FontWeight.w600,
                                  maxLines: 1,
                                  textOverflow: TextOverflow.ellipsis,
                                ),
                              ),
                            SizedBox(
                              height: widget.deviceWidth * 0.01,
                            ),
                            Row(
                              children: [
                                AssetSvgIcon(iconName: 'venue'),
                                SizedBox(
                                  width: widget.deviceWidth * 0.01,
                                ),
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                      maxWidth: widget.deviceWidth * 0.5),
                                  child: TextWidget(
                                    title: widget.booking.turfName,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              ],
                            ),
                            SizedBox(height: widget.deviceWidth * 0.01),
                            Row(
                              children: [
                                AssetSvgIcon(iconName: 'calendar1'),
                                SizedBox(width: widget.deviceWidth * 0.01),
                                if (widget.booking.ogBookingDate != null)
                                  ConstrainedBox(
                                    constraints: BoxConstraints(
                                        maxWidth: widget.deviceWidth * 0.5),
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      child: TextWidget(
                                        title:
                                            '${DateFormat('dd MMM yyyy').format(widget.booking.ogBookingDate!)}, ${get12HrFormat(widget.booking.bookingSlotStartTime).toStringAsFixed(2)} ${getTimePeriod(widget.booking.bookingSlotStartTime)} - ${get12HrFormat(widget.booking.bookingSlotEndTime == 0.0 ? 12.0 : widget.booking.bookingSlotEndTime).toStringAsFixed(2)} ${getTimePeriod(widget.booking.bookingSlotEndTime)}',
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(height: widget.deviceWidth * 0.02),
                            Row(
                              children: [
                                Image.network(widget.booking.sportImage,
                                    height: 15),
                                SizedBox(width: widget.deviceWidth * 0.02),
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                      maxWidth: widget.deviceWidth * 0.2),
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: TextWidget(
                                      title: widget.booking.sportCategory
                                                  .categoryName ==
                                              'Indoor'
                                          ? '${widget.booking.sizeOrSport.split('(')[0].trim()}'
                                          : '${widget.booking.sizeOrSport}',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                SizedBox(width: widget.deviceWidth * 0.03),
                                TextWidget(
                                  title: '|',
                                  fontSize: 12,
                                  color: Color(0xffB9B9B9),
                                ),
                                SizedBox(width: widget.deviceWidth * 0.03),
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                      maxWidth: widget.deviceWidth * 0.2),
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: TextWidget(
                                      title:
                                          '\u20b9${widget.booking.totalAmount.toStringAsFixed(2)}',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    right: 0,
                    child: Container(
                      alignment: Alignment.center,
                      constraints: BoxConstraints(
                          maxWidth: widget.deviceWidth * 0.25,
                          minWidth: widget.deviceWidth * 0.2),
                      padding: EdgeInsets.symmetric(
                          vertical: widget.deviceWidth * 0.01),
                      decoration: BoxDecoration(
                        borderRadius: widget.fromCustomer
                            ? BorderRadius.only(bottomLeft: Radius.circular(30))
                            : BorderRadius.only(
                                bottomLeft: Radius.circular(30),
                                topRight: Radius.circular(8),
                              ),
                        color: getPaymentStatus() == 'Cancelled'
                            ? getRedColor1(context)
                            : getPaymentStatus() == 'Paid' ||
                                    getPaymentStatus() == 'Completed'
                                ? getThemeColor()
                                : getYellowColor1(context),
                      ),
                      child: TextWidget(
                        title: getPaymentStatus(),
                        fontSize: 11,
                        textAlign: TextAlign.center,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  if (widget.user.business != null &&
                      widget.fromViewAll == false &&
                      widget.booking.bookingStatus != 'CANCELLED' &&
                      widget.user.business!.roles
                          .contains('Cancel Bulk Booking'))
                    Positioned(
                      top: widget.deviceWidth * 0.1,
                      right: widget.deviceWidth * 0.01,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            isSelected = !isSelected;
                          });
                        },
                        child: Checkbox(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          activeColor: getThemeColor(),
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              isSelected = value ?? false;
                              if (widget.onSelectionChanged != null) {
                                widget.onSelectionChanged
                                    ?.call(isSelected ? 1 : -1, isSelected);
                              }
                            });
                          },
                        ),
                      ),
                    ),
                  if (widget.fromViewAll == false &&
                      widget.booking.bookingStatus != 'CANCELLED')
                    Positioned(
                      top: widget.deviceWidth * 0.1,
                      right: widget.deviceWidth * 0.01,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            isSelected = !isSelected;
                          });
                        },
                        child: Checkbox(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          activeColor: getThemeColor(),
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              isSelected = value ?? false;
                              if (widget.onSelectionChanged != null) {
                                widget.onSelectionChanged
                                    ?.call(isSelected ? 1 : -1, isSelected);
                              }
                            });
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ],
          )),
    );
  }
}
