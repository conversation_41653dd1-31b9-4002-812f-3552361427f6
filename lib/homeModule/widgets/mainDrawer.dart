import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/couponModule/screens/coupon_screen.dart';
import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:bys_business/moreModule.dart/provider/moreProvider.dart';
import 'package:bys_business/withdrawalModule/screens/withdrawal_screen.dart';

import '../../bulkBookingModule/screens/bulk_booking_screen.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/text_widget.dart';
import '../../customerModule/screens/customer_screen.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../employeeManagement/screens/all_employee_screen.dart';
import '../../employeeModule/screens/employee_profile_screen.dart';
import '../../homeModule/screens/reportScreen.dart';
import '../../moreModule.dart/screens/business_profile_screen.dart';
import '../../moreModule.dart/screens/new_booking_screen.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import '../../venueModule/providers/turfProvider.dart';

import '../../authModule/screens/onBoardScreen.dart';
import '../../commonWidgets/raisedButton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../homeModule/screens/homeScreen.dart';
import '../../venueModule/screens/turfScreen.dart';
import '../../moreModule.dart/screens/analyticsScreen.dart';
import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../moreModule.dart/screens/accountScreen.dart';

class MainDrawer extends StatefulWidget {
  MainDrawer();
  @override
  _MainDrawerState createState() => _MainDrawerState();
}

class _MainDrawerState extends State<MainDrawer> {
  final LocalStorage storage = new LocalStorage('bysBusiness');

  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  late UserModal user;

  List options = [
    {
      'title': 'Bookings',
      'icon': 'turf',
      'type': 'Customers&Bookings',
    },
    {
      'title': 'Bulk\nBookings',
      'icon': 'report2',
      'type': 'Customers&Bookings',
    },
    {
      'title': 'Customers',
      'icon': 'customer',
      'type': 'Customers&Bookings',
    },
    {
      'title': 'Business\nProfile',
      'icon': 'business_profile',
      'type': 'BusinessManagement',
    },
    {
      'title': 'Venues',
      'icon': 'venues',
      'type': 'BusinessManagement',
    },
    {
      'title': 'Coupons',
      'icon': 'coupon2',
      'type': 'BusinessManagement',
    },
    {
      'title': 'Employees',
      'icon': 'employee',
      'type': 'BusinessManagement',
    },
    {
      'title': 'Money\nWithdrawal',
      'icon': 'withdrawal',
      'type': 'More',
    },
    {
      'title': 'Business\nReport',
      'icon': 'report1',
      'type': 'More',
    },
    {
      'title': 'Logout',
      'icon': 'Logout',
      'type': 'More',
    },
  ];

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;

    if (user.businessStatus == 'REJECTED' || user.businessStatus == 'PENDING') {
      options = [
        {
          'title': 'Business\nProfile',
          'icon': 'business_profile',
          'type': 'BusinessManagement',
        },
        {
          'title': 'Logout',
          'icon': 'Logout',
          'type': 'More',
        },
      ];
    }

    if (user.role == 'Employee') {
      options.removeWhere((option) =>
          option['title'] == 'Money\nWithdrawal' ||
          option['title'] == 'Business\nProfile' ||
          option['title'] == 'Customers' ||
          option['title'] == 'Employees');
      if (user.business != null) {
        if (!user.business!.roles.contains('Venue')) {
          options.removeWhere((option) => option['title'] == 'Venues');
        }
        if (!user.business!.roles.contains('Booking')) {
          options.removeWhere((option) => option['title'] == 'Bookings');
        }
        if (!user.business!.roles.contains('Bulk Booking')) {
          options.removeWhere((option) => option['title'] == 'Bulk\nBookings');
        }
        if (!user.business!.roles.contains('Coupon')) {
          options.removeWhere((option) => option['title'] == 'Coupons');
        }
        if (!user.business!.roles.contains('Report')) {
          options
              .removeWhere((option) => option['title'] == 'Business\nReport');
        }
      }
    }

    if (Provider.of<Auth>(context, listen: false).deleteAccount) {
      options.insert(
        0,
        {
          'title': "Delete\nAccount",
          'icon': 'delete',
          'type': 'More',
        },
      );
    }
  }

  goToAccountScreen() {
    if (user.role == 'Employee') {
      push(EmployeeProfileScreen(business: user.business!))
          .then((value) => Navigator.of(context).pop());
    } else {
      push(AccountScreen(user: user))
          .then((value) => Navigator.of(context).pop());
    }
  }

  helpFunction() async {
    final result =
        await customLaunch(Provider.of<Auth>(context, listen: false).helpLink);
    if (!result) {
      callToastMessage('WhatsApp application is not found');
    }
  }

  handleCardClick(cardName, tS) {
    switch (cardName) {
      case 'Home':
        push(HomeScreen());
        break;
      case 'Venues':
        push(TurfScreen());
        // .then((value) => pop());
        break;
      case 'Bulk\nBookings':
        push(BulkBookingScreen());
        // .then((value) => pop());
        break;
      case 'Analytics':
        push(AnalyticsScreen(user: user));
        // .then((value) => pop());
        break;
      case 'Bookings':
        push(NewBookingScreen());
        // .then((value) => pop());
        break;
      case 'Employees':
        push(AllEmployeeScreen(user: user));
        // .then((value) => pop());
        break;
      case 'Money\nWithdrawal':
        push(WithdrawalScreen());
        // .then((value) => pop());
        break;
      case 'Account':
        goToAccountScreen();
        break;
      case 'Business\nProfile':
        push(BusinessProfileScreen());
        // .then((value) => pop());
        break;

      case 'Business\nReport':
        push(ReportScreen(user: user));
        // .then((value) => pop());
        break;

      case 'Coupons':
        push(CouponScreen(user: user));
        // .then((value) => pop());
        break;

      case 'Customers':
        push(CustomerScreen(user: user));
        // .then((value) => pop());
        break;

      case 'Logout':
        showLogoutDialog(tS);
        break;

      case 'Delete\nAccount':
        showDeleteDialogBox(tS);
        break;
      default:
    }
  }

  logOut() async {
    Provider.of<Auth>(context, listen: false).deleteFCMToken();
    Provider.of<TurfProvider>(context, listen: false).emptyTurf();
    Provider.of<HomeProvider>(context, listen: false).resetBooking();
    Provider.of<MoreProvider>(context, listen: false).emptyBooking();
    Provider.of<HomeProvider>(context, listen: false).resetBulkBooking();
    Provider.of<NotificationProvider>(context, listen: false)
        .notificationCount = 0;
    Provider.of<EmployeeManagementProvider>(context, listen: false)
        .emptyEmployee();
    storage.clear();
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => OnBoardScreen('', ''),
        ),
        (route) => false);
    print("logOut");
  }

  deleteAcount() async {
    try {
      final data = await Provider.of<Auth>(context, listen: false)
          .deleteBusiness(user.businessId);
      if (data) {
        logOut();
        callToastMessage('Acoount deleted successfully.');
      } else {
        callToastMessage('Something went wrong');
      }
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {}
  }

  showDeleteDialogBox(tS) {
    return showDialog(
        context: context,
        builder: ((context) => CustomDialog(
            title:
                'Are you sure you want delete your account. All Information related to your account will be deleted?',
            noText: 'Yes, Delete',
            yesText: 'No',
            noFunction: () {
              pop();
              deleteAcount();
            },
            yesFunction: () {
              pop();
            })));
  }

  showLogoutDialog(tS) {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title: 'Are you sure you want to logout?',
            noText: 'Yes, Logout',
            yesText: 'No',
            noFunction: () {
              pop();
              logOut();
            },
            yesFunction: pop,
          )),
    );
  }

  List getOptionsByType(String type) {
    return options.where((option) => option['type'] == type).toList();
  }

  String getInitials(String inputString) => inputString.isNotEmpty
      ? inputString.trim().split(' ').map((l) => l[0]).take(2).join()
      : '';

  Widget getCardWidget(option) {
    return GestureDetector(
      onTap: () => handleCardClick(option['title'], tS),
      child: CustomContainer(
        boxShadow: [],
        bgColor: getLightGreenColor2(context),
        width: dW * 0.28,
        height: dW * 0.29,
        borderColor: getLightGreenColor2(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            AssetSvgIcon(
              iconName: option['icon'],
              color: Colors.black,
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: TextWidget(
                title: option['title'],
                textAlign: TextAlign.center,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Drawer(
      child: isLoading
          ? Center()
          : Scaffold(
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: dW * 0.14),
                        user.avatar == '' || user.avatar == null
                            ? Container(
                                child: SvgPicture.asset(
                                  'assets/svgIcons/user.svg',
                                  height: dW * 0.17,
                                  width: dW * 0.17,
                                ),
                              )
                            : Container(
                                height: dW * 0.19,
                                width: dW * 0.19,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(100),
                                  child: FadeInImage(
                                    placeholder:
                                        AssetImage('assets/images/user.png'),
                                    image: NetworkImage(
                                      user.avatar!,
                                    ),
                                    fit: BoxFit.fill,
                                  ),
                                ),
                              ),
                        ListTile(
                          onTap: goToAccountScreen,
                          contentPadding: EdgeInsets.all(0),
                          minLeadingWidth: 0,
                          title: TextWidget(
                            title: '${user.firstName} ${user.lastName}',
                            fontWeight: FontWeight.w600,
                          ),
                          subtitle: TextWidget(
                            title: '${user.email}',
                            fontSize: tS * headline9,
                            color: Colors.black,
                          ),
                          trailing: IconButton(
                            onPressed: goToAccountScreen,
                            icon: Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.black,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (getOptionsByType('Customers&Bookings')
                              .isNotEmpty) ...[
                            SizedBox(height: dW * 0.05),
                            TextWidget(
                              title: 'Bookings & Customers',
                              fontWeight: FontWeight.w500,
                              color: Color(0xff3E3E3E),
                            ),
                            SizedBox(height: dW * 0.03),
                            Wrap(
                              runSpacing: 10,
                              spacing: 15,
                              children: [
                                ...getOptionsByType('Customers&Bookings')
                                    .map((option) => getCardWidget(option))
                              ],
                            ),
                          ],
                          if (getOptionsByType('BusinessManagement')
                              .isNotEmpty) ...[
                            SizedBox(height: dW * 0.05),
                            TextWidget(
                              title: 'Business Management',
                              fontWeight: FontWeight.w500,
                              color: Color(0xff3E3E3E),
                            ),
                            SizedBox(height: dW * 0.03),
                            Wrap(
                              runSpacing: 10,
                              spacing: 15,
                              children: [
                                ...getOptionsByType('BusinessManagement')
                                    .map((option) => getCardWidget(option))
                              ],
                            ),
                          ],
                          if (getOptionsByType('More').isNotEmpty) ...[
                            SizedBox(height: dW * 0.05),
                            TextWidget(
                              title: 'More',
                              fontWeight: FontWeight.w500,
                              color: Color(0xff3E3E3E),
                            ),
                            SizedBox(height: dW * 0.03),
                            Wrap(
                              runSpacing: 10,
                              spacing: 15,
                              children: [
                                ...getOptionsByType('More')
                                    .map((option) => getCardWidget(option))
                              ],
                            ),
                          ],
                          SizedBox(height: dW * 0.08),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
