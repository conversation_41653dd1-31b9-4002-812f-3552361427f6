import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';

import '../../commonWidgets/text_widget.dart';

class UserDetailBottomSheet extends StatefulWidget {
  final Function goToSummaryScreen;
  UserDetailBottomSheet({
    required this.goToSummaryScreen,
  });

  @override
  UserDetailBottomSheetState createState() => UserDetailBottomSheetState();
}

class UserDetailBottomSheetState extends State<UserDetailBottomSheet> {
  FlutterNativeContactPicker contactPicker = new FlutterNativeContactPicker();

  TextEditingController fullNameController = TextEditingController();
  TextEditingController mobileNoController = TextEditingController();

  FocusNode nameNode = FocusNode();
  FocusNode phoneNode = FocusNode();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  submit() {
    final isValid = _formKey.currentState!.validate();
    if (isValid) {
      widget.goToSummaryScreen(
        fullName: fullNameController.text.trim(),
        mobileNo: mobileNoController.text.trim(),
      );
    }
  }

  selectContact() async {
    Contact? contact = await contactPicker.selectContact();
    if (contact != null) {
      print(contact);
      fullNameController.text =
          contact.fullName == null ? '' : contact.fullName!.trim();
      mobileNoController.text = contact.phoneNumbers!.length > 0
          ? contact.phoneNumbers![0]
              .replaceAll('(', '')
              .replaceAll(')', '')
              .replaceAll('-', '')
              .replaceAll(' ', '')
              .replaceAll('+91', '')
          : '';
    } else {
      print('Error');
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;

    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {});
    }
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: Container(
        height: nameNode.hasFocus || phoneNode.hasFocus
            ? deviceWidth * 1.75
            : deviceWidth * 0.99,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.053),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  TextWidget(
                    title: 'User Details',
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop(null);
                    },
                    child: CircleAvatar(
                      radius: 13,
                      backgroundColor: Colors.grey.shade300,
                      child: Icon(
                        Icons.clear,
                        color: Colors.black54,
                        size: 18,
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomContainer(
                          child: Column(
                            children: [
                              CustomTextFieldWithLabel(
                                label: 'Player Name',
                                controller: fullNameController,
                                hintText: 'Enter player name',
                                widget: GestureDetector(
                                  onTap: selectContact,
                                  child: Container(
                                    padding:
                                        EdgeInsets.all(deviceWidth * 0.013),
                                    color: Colors.transparent,
                                    child: TextWidget(
                                      title: 'Select from contacts',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).primaryColor,
                                      textDecoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                                maxLines: null,
                                inputAction: TextInputAction.next,
                                inputType: TextInputType.text,
                                focusNode: nameNode,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Please enter full name';
                                  }
                                },
                              ),
                              SizedBox(height: deviceWidth * 0.05),
                              CustomTextFieldWithLabel(
                                label: 'Phone Number ',
                                controller: mobileNoController,
                                hintText: 'Enter phone number',
                                inputFormatter: [
                                  FilteringTextInputFormatter.deny(' ',
                                      replacementString: ''),
                                  FilteringTextInputFormatter.deny('+91',
                                      replacementString: ''),
                                  FilteringTextInputFormatter.deny('-',
                                      replacementString: '')
                                ],
                                inputType: TextInputType.number,
                                inputAction: TextInputAction.done,
                                maxLength: 10,
                                focusNode: phoneNode,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Please enter a phone number';
                                  } else if (value.length < 10) {
                                    return 'Please enter a valid phone number';
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                        CustomButton(
                          width: deviceWidth,
                          height: deviceWidth * 0.12,
                          buttonText: 'Next',
                          onPressed: submit,
                          radius: 7,
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
