import 'dart:async';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../new_colors.dart';
import '../../venueModule/models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../screens/venue_availability_screen.dart';
import 'select_sport_and_size_bottomsheet.dart';

class SelectVenueSheet extends StatefulWidget {
  final UserModal user;
  final String bookingType;
  SelectVenueSheet({
    required this.user,
    required this.bookingType,
  });

  @override
  SelectVenueSheetState createState() => SelectVenueSheetState();
}

class SelectVenueSheetState extends State<SelectVenueSheet> {
  double dW = 0;
  double dH = 0;
  double tS = 0;
  bool isLoading = false;
  bool isSearchLoading = false;
  bool searchMode = false;

  List<Venue> listOfVenues = [];
  List<Venue> listOfSearchedVenues = [];

  TextEditingController searchVenueController = TextEditingController();
  FocusNode searchVenueFocus = FocusNode();
  Venue? selectedVenue;
  String selectedBookingType = '';
  Timer? _debounce;

  Future<void> fetchTurf() async {
    try {
      setState(() => isLoading = true);
      await Provider.of<TurfProvider>(context, listen: false)
          .fetchTurfsByBusinessId(
        widget.user.accessToken,
        widget.user.businessId,
      );
      listOfVenues =
          Provider.of<TurfProvider>(context, listen: false).getVerifiedTurf(1);

      if (widget.user.business != null) {
        List<String> turfIds = [];
        widget.user.business!.turfs.forEach((turf) => turfIds.add(turf.id));
        listOfVenues.removeWhere((turf) => !turfIds.contains(turf.id));
      }
      if (listOfVenues.length == 1) selectedVenue = listOfVenues.first;
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  searchTurf() async {
    listOfSearchedVenues =
        await Provider.of<TurfProvider>(context, listen: false).searchTurfs2(
            searchText: searchVenueController.text.trim(), index: 1);
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
  }

  selectVenue(Venue venue) {
    selectedVenue != null && selectedVenue!.id == venue.id
        ? selectedVenue = null
        : selectedVenue = venue;
    setState(() {});
  }

  selectBookingType(String type) {
    selectedBookingType == type
        ? selectedBookingType = ''
        : selectedBookingType = type;
    setState(() {});
  }

  openSportBottomSheet() {
    pop();
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: selectedVenue!.sportCategory!.categoryName == 'Outdoor'
            ? dH * 0.5
            : dH * 0.4,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) => SelectSportAndSizeBottomSheet(
        venue: selectedVenue!,
        bookingType: selectedBookingType,
      ),
    );
  }

  search(_) async {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    if (searchVenueController.text.trim().length != 0) {
      setState(() {
        searchMode = true;
      });
    } else {
      setState(() {
        searchMode = false;
      });
    }
    if (searchVenueController.text.trim().length > 2) {
      _debounce = Timer(const Duration(seconds: 1), () async {
        setState(() => isSearchLoading = true);
        await searchTurf();
        setState(() => isSearchLoading = false);

        if (_debounce?.isActive ?? false) _debounce!.cancel();
      });
    }
  }

  goToNextScreen() {
    openSportBottomSheet();
  }

  @override
  void initState() {
    super.initState();
    selectedBookingType = widget.bookingType;
    fetchTurf();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    final searchedturf =
        Provider.of<TurfProvider>(context, listen: false).searchedTurfs;
    return Container(
      decoration: BoxDecoration(
          color: getWhiteColor(context),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24), topRight: Radius.circular(24))),
      height: dH * 0.75,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          Container(
            margin: EdgeInsets.symmetric(horizontal: dW * 0.053),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                GestureDetector(
                  onTap: () => pop(null),
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(Icons.clear, color: Colors.black),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(
                horizontal: dW * 0.053, vertical: dW * 0.04),
            child: TextFormField(
              controller: searchVenueController,
              focusNode: searchVenueFocus,
              // autofocus: true,
              onChanged: search,
              decoration: InputDecoration(
                filled: true,
                fillColor: getTextFieldGreyBackgroundColor(context),
                hintText: 'Search...',
                border: InputBorder.none,
                prefixIcon: Image.asset(
                  'assets/images/search.png',
                  scale: 1.8,
                ),
                suffixIcon: IconButton(
                  focusColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onPressed: () {
                    setState(() => searchVenueController.clear());
                    hideKeyBoard(context);
                    fetchTurf();
                  },
                  icon: searchVenueController.text.isEmpty
                      ? const SizedBox.shrink()
                      : const Icon(
                          Icons.clear,
                          color: Colors.black87,
                        ),
                ),
              ),
            ),
          ),
          // GestureDetector(
          //   onTap: () {},
          //   child: Container(
          //     decoration: BoxDecoration(
          //         color: getTextFieldGreyBackgroundColor(context),
          //         borderRadius: BorderRadius.circular(8)),
          //     padding: EdgeInsets.symmetric(
          //         vertical: dW * 0.04, horizontal: dW * 0.04),
          //     margin: EdgeInsets.symmetric(
          //         horizontal: dW * 0.053, vertical: dW * 0.04),
          //     child: Row(
          //       children: [
          //         AssetSvgIcon(iconName: 'search1'),
          //         SizedBox(
          //           width: dW * 0.04,
          //         ),
          //         TextWidget(
          //           title: 'Search venue...',
          //           color: getLightGreyColor11(context),
          //           fontSize: 10,
          //         )
          //       ],
          //     ),
          //   ),
          // ),

          Container(
            margin: EdgeInsets.symmetric(horizontal: dW * 0.053),
            child: TextWidget(
              title: 'Select Venue',
              fontSize: 15,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: dW * 0.03),
          Expanded(
            child: isSearchLoading || isLoading
                ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                // : listOfVenues.isEmpty
                //     ? EmptyListWidget(
                //         text: searchVenueController.text.trim().isEmpty
                //             ? 'Search venues'
                //             : 'no venues',
                //         topPadding: 0)
                : searchMode && searchVenueController.text.isNotEmpty
                    ? listOfSearchedVenues.isNotEmpty
                        ? SingleChildScrollView(
                            scrollDirection: Axis.vertical,
                            physics: BouncingScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ...listOfSearchedVenues.map(
                                  (venue) => GestureDetector(
                                    onTap: () {
                                      selectVenue(venue);
                                      hideKeyBoard(context);
                                    },
                                    child: CustomContainer(
                                      width: dW,
                                      // width: listOfVenues.length == 1
                                      //     ? dW * 0.9
                                      //     : dW * 0.8,
                                      radius: 10,
                                      margin: EdgeInsets.only(
                                          // right: listOfVenues.length == 1
                                          //     ? 0
                                          //     : dW * 0.03,
                                          // left: dW * 0.08,
                                          left: dW * 0.053,
                                          right: dW * 0.053,
                                          bottom: dW * 0.04),
                                      borderColor: selectedVenue != null &&
                                              selectedVenue!.id == venue.id
                                          ? getThemeColor()
                                          : null,
                                      borderWidth: selectedVenue != null &&
                                              selectedVenue!.id == venue.id
                                          ? 1.5
                                          : 1,
                                      // boxShadow: [],
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          TextWidget(
                                            title: venue.name,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          SizedBox(height: dW * 0.015),
                                          TextWidget(
                                            title:
                                                '${venue.address.streetName}, ${venue.address.landmark}, ${venue.address.city}, ${venue.address.state}, ${venue.address.pincode}',
                                            fontSize: 12,
                                            color: Colors.black,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : EmptyListWidget(text: 'no venue found', topPadding: 0)
                    : listOfVenues.isEmpty
                        ? Container(
                            padding:
                                EdgeInsets.symmetric(horizontal: dW * 0.05),
                            alignment: Alignment.center,
                            child: TextWidget(
                              title: 'Venues not found!',
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        : SingleChildScrollView(
                            physics: BouncingScrollPhysics(),
                            child: Container(
                              alignment: Alignment.center,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(height: dW * 0.03),
                                  if (listOfVenues.isNotEmpty)
                                    SingleChildScrollView(
                                      scrollDirection: Axis.vertical,
                                      physics: BouncingScrollPhysics(),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          ...listOfVenues.map(
                                            (venue) => GestureDetector(
                                              onTap: () {
                                                selectVenue(venue);
                                                hideKeyBoard(context);
                                              },
                                              child: CustomContainer(
                                                width: dW,
                                                // width: listOfVenues.length == 1
                                                //     ? dW * 0.9
                                                //     : dW * 0.8,
                                                radius: 10,
                                                margin: EdgeInsets.only(
                                                    // right: listOfVenues.length == 1
                                                    //     ? 0
                                                    //     : dW * 0.03,
                                                    // left: dW * 0.08,
                                                    left: dW * 0.053,
                                                    right: dW * 0.053,
                                                    bottom: dW * 0.04),
                                                borderColor:
                                                    selectedVenue != null &&
                                                            selectedVenue!.id ==
                                                                venue.id
                                                        ? getThemeColor()
                                                        : null,
                                                borderWidth:
                                                    selectedVenue != null &&
                                                            selectedVenue!.id ==
                                                                venue.id
                                                        ? 1.5
                                                        : 1,
                                                // boxShadow: [],
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    TextWidget(
                                                      title: venue.name,
                                                      color: Colors.black,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                    SizedBox(
                                                        height: dW * 0.015),
                                                    // TextWidget(
                                                    //   title:
                                                    //       '${venue.address.streetName}, ${venue.address.landmark}, ${venue.address.city}, ${venue.address.state}, ${venue.address.pincode}',
                                                    //   fontSize: 12,
                                                    //   color: Colors.black,
                                                    // ),
                                                    TextWidget(
                                                      title:
                                                          '${venue.address.landmark}, ${venue.address.city}, ${venue.address.state}, ${venue.address.pincode}',
                                                      fontSize: 12,
                                                      color: Colors.black,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  // Padding(
                                  //   padding:
                                  //       EdgeInsets.symmetric(horizontal: dW * 0.05),
                                  //   child: Column(
                                  //     crossAxisAlignment: CrossAxisAlignment.start,
                                  //     children: [
                                  //       SizedBox(height: dW * 0.05),
                                  //       TextWidget(
                                  //         title: 'Select Booking Type',
                                  //         fontSize: 15,
                                  //         fontWeight: FontWeight.w600,
                                  //       ),
                                  //       SizedBox(height: dW * 0.03),
                                  //       CustomContainer(
                                  //         vPadding: 0,
                                  //         hPadding: 0,
                                  //         child: Column(
                                  //           crossAxisAlignment:
                                  //               CrossAxisAlignment.center,
                                  //           children: [
                                  //             SizedBox(height: dW * 0.03),
                                  //             ...['Single Booking', 'Bulk Booking']
                                  //                 .map(
                                  //               (type) => GestureDetector(
                                  //                 onTap: () =>
                                  //                     selectBookingType(type),
                                  //                 child: Container(
                                  //                   margin: EdgeInsets.only(
                                  //                     bottom: dW * 0.03,
                                  //                   ),
                                  //                   padding: EdgeInsets.symmetric(
                                  //                       horizontal: dW * 0.04,
                                  //                       vertical: dW * 0.03),
                                  //                   decoration: BoxDecoration(
                                  //                     color: selectedBookingType ==
                                  //                             type
                                  //                         ? getThemeColor()
                                  //                             .withOpacity(.22)
                                  //                         : Colors.transparent,
                                  //                   ),
                                  //                   child: Row(
                                  //                     mainAxisAlignment:
                                  //                         MainAxisAlignment
                                  //                             .spaceBetween,
                                  //                     children: [
                                  //                       TextWidget(
                                  //                         title: type,
                                  //                         fontWeight:
                                  //                             FontWeight.w500,
                                  //                       ),
                                  //                       Container(
                                  //                         padding: EdgeInsets.all(
                                  //                             selectedBookingType ==
                                  //                                     type
                                  //                                 ? 3
                                  //                                 : 10),
                                  //                         decoration: BoxDecoration(
                                  //                           border: Border.all(
                                  //                             color: selectedBookingType ==
                                  //                                     type
                                  //                                 ? getThemeColor()
                                  //                                 : Colors.black54,
                                  //                             width: 1.4,
                                  //                           ),
                                  //                           shape: BoxShape.circle,
                                  //                         ),
                                  //                         child: selectedBookingType !=
                                  //                                 type
                                  //                             ? null
                                  //                             : CircleAvatar(
                                  //                                 radius: 7,
                                  //                                 backgroundColor:
                                  //                                     getThemeColor()),
                                  //                       )
                                  //                     ],
                                  //                   ),
                                  //                 ),
                                  //               ),
                                  //             )
                                  //           ],
                                  //         ),
                                  //       ),
                                  //       SizedBox(height: dW * 0.05),
                                  //     ],
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          ),
          ),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                bottom: dW * 0.005,
                top: dW * 0.045),
            child: Column(
              children: [
                ...['Single Booking', 'Bulk Booking'].map(
                  (type) => CustomButton(
                      width: dW * 0.9,
                      height: dW * 0.12,
                      bottomMargin:
                          type.length == type.lastIndexOf(type) ? 0 : dW * 0.03,
                      buttonText: type,
                      buttonTextSyle: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                        color: selectedVenue != null && type == 'Bulk Booking'
                            ? blackColor1
                            : whiteColor,
                      ),
                      buttonColor: type == 'Single Booking'
                          ? getThemeColor()
                          : whiteColor,
                      radius: 8,
                      onPressed: selectedVenue != null
                          ? () {
                              setState(() {
                                selectedBookingType = type;
                              });
                              // goToNextScreen();
                              pop();
                              push(
                                VenueAvailabilityScreen(
                                  user:
                                      Provider.of<Auth>(context, listen: false)
                                          .user,
                                  selectedTurf: selectedVenue!,
                                  bookingType: selectedBookingType,
                                ),
                              );
                            }
                          : null),
                )
              ],
            ),
            //  CustomButton(
            //   width: dW * 0.9,
            //   height: dW * 0.12,
            //   buttonText: 'Next',
            //   radius: 8,
            //   onPressed: selectedVenue != null && selectedBookingType != ''
            //       ? goToNextScreen
            //       : null,
            // ),
          ),
        ],
      ),
    );
  }
}
