import 'package:flutter/material.dart';

import '../../fontSizes.dart';
import '../../venueModule/models/venue_model.dart';

class SelectTurfSizeDropdown extends StatefulWidget {
  final Slot selectedSlot;
  final String categoryName;
  final DateTime selectedDate;
  final String selectedSport;
  final String option;
  SelectTurfSizeDropdown({
    required this.selectedSlot,
    required this.categoryName,
    required this.selectedDate,
    required this.selectedSport,
    required this.option,
  });

  @override
  SselectTurfSizeDropdownState createState() => SselectTurfSizeDropdownState();
}

class SselectTurfSizeDropdownState extends State<SelectTurfSizeDropdown> {
  bool isLoading = false;

  checkForSportAvailability(PricingAndQuantity price) {
    bool available = false;
    if (widget.selectedDate.weekday == 6 || widget.selectedDate.weekday == 7) {
      if (price.weekendPrice != 0) {
        available = true;
      }
    } else {
      if (price.price != 0) {
        available = true;
      }
    }
    return available;
  }

  checkSport(String size) {
    if (widget.selectedSport == 'Badminton') {
      if (size == '2:2') {
        return true;
      } else {
        return false;
      }
    } else {
      if (size == '2:2') {
        return false;
      } else {
        return true;
      }
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: width * 0.65,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  widget.categoryName != 'Outdoor'
                      ? 'Select Game'
                      : 'Select Size',
                  style: Theme.of(context)
                      .textTheme
                      .displaySmall!
                      .copyWith(fontSize: textScaleFactor * headlineMedium),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: width * 0.05),
            Wrap(
              children: [
                ...widget.selectedSlot.priceAndQuantity.map(
                  (size) => !checkForSportAvailability(size)
                      ? SizedBox.shrink()
                      :
                      // !checkSport(size.title)
                      //     ? SizedBox.shrink()
                      //     :
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop(size);
                          },
                          child: Container(
                            margin: EdgeInsets.only(
                              right: width * 0.03,
                              bottom: width * 0.03,
                            ),
                            padding: EdgeInsets.symmetric(
                              vertical: width * 0.032,
                              horizontal: width * 0.04,
                            ),
                            decoration: BoxDecoration(
                              color: Color(0xffF3F3F4),
                              borderRadius: BorderRadius.circular(100),
                            ),
                            child: Text(
                              '${size.title} ${size.label == '' ? '' : '(${size.label})'}',
                              style: Theme.of(context)
                                  .textTheme
                                  .displaySmall!
                                  .copyWith(
                                    fontSize: textScaleFactor * displayMedium,
                                    color: Color(0xff6E7271),
                                  ),
                            ),
                          ),
                        ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
