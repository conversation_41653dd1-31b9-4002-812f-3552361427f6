// import 'package:bys_business/commonWidgets/custom_container.dart';
// import 'package:flutter/material.dart';
// import 'package:syncfusion_flutter_charts/charts.dart';

// import '../../commonWidgets/text_widget.dart';
// import '../../common_function.dart';
// import '../../moreModule.dart/model/barChartModel.dart';

// class AnalyticsWidget extends StatelessWidget {
//   final String title;
//   final List<BarChartModel> data;
//   final List<PieData> pieData;
//   final double barWidth;
//   final double interval;

//   const AnalyticsWidget({
//     Key? key,
//     required this.title,
//     required this.data,
//     required this.barWidth,
//     required this.interval,
//     required this.pieData,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     final dW = MediaQuery.of(context).size.width;
//     final tS = MediaQuery.of(context).textScaleFactor;
//     return CustomContainer(
//       radius: 15,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: <Widget>[
//           Padding(
//             padding: EdgeInsets.only(
//                 left: dW * 0.02, bottom: pieData.isNotEmpty ? 0 : dW * 0.05),
//             child: TextWidget(title: title, fontWeight: FontWeight.w600),
//           ),
//           if (pieData.isNotEmpty)
//             SfCircularChart(
//               legend: Legend(isVisible: true),
//               series: <PieSeries<PieData, String>>[
//                 PieSeries<PieData, String>(
//                   dataSource: pieData,
//                   xValueMapper: (PieData data, _) => '${data.yData} Bookings',
//                   yValueMapper: (PieData data, _) => data.yData,
//                   dataLabelMapper: (PieData data, _) => '${data.xData}',
//                   dataLabelSettings: DataLabelSettings(
//                     isVisible: true,
//                     textStyle: TextStyle(
//                       fontWeight: FontWeight.w600,
//                       fontSize: tS * 12,
//                       letterSpacing: .4,
//                     ),
//                   ),
//                   radius: '90',
//                 ),
//               ],
//             ),
//           if (data.isNotEmpty)
//             SfCartesianChart(
//               selectionGesture: ActivationMode.singleTap,
//               primaryXAxis: CategoryAxis(
//                 labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
//                       color: Colors.black,
//                       fontSize: tS * 14,
//                       fontWeight: FontWeight.w500,
//                     ),
//                 isVisible: true,
//                 maximumLabelWidth: dW * 0.16,
//                 majorGridLines:
//                     MajorGridLines(width: 0.5, color: Colors.transparent),
//                 axisLine: AxisLine(width: 0.5, color: Colors.transparent),
//               ),
//               primaryYAxis: NumericAxis(
//                 majorGridLines: MajorGridLines(width: 0.5),
//                 axisLine: AxisLine(width: 0.5),
//                 labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
//                       color: Colors.black,
//                       fontSize: tS * 14,
//                       fontWeight: FontWeight.w500,
//                     ),
//                 interval: interval,
//               ),
//               series: <ChartSeries>[
//                 ColumnSeries<BarChartModel, String>(
//                   dataSource: data,
//                   xValueMapper: (BarChartModel views, _) =>
//                       views.title.toString(),
//                   yValueMapper: (BarChartModel views, _) => views.value.toInt(),
//                   pointColorMapper: (BarChartModel views, _) => views.color,
//                   borderRadius: BorderRadius.only(
//                     topLeft: Radius.circular(2),
//                     topRight: Radius.circular(2),
//                   ),
//                   width: barWidth,
//                   selectionBehavior:
//                       SelectionBehavior(selectedColor: getThemeColor()),
//                 )
//               ],
//             ),
//         ],
//       ),
//     );
//   }
// }

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../moreModule.dart/model/barChartModel.dart';

class AnalyticsWidget extends StatelessWidget {
  final String title;
  final List<BarChartModel> data;
  final List<PieData> pieData;
  final double barWidth;
  final double interval;

  const AnalyticsWidget({
    Key? key,
    required this.title,
    required this.data,
    required this.barWidth,
    required this.interval,
    required this.pieData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dW = MediaQuery.of(context).size.width;
    final tS = MediaQuery.of(context).textScaleFactor;

    // Check if data is empty
    final bool isDataEmpty = data.isEmpty;

    return CustomContainer(
      radius: 15,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.02, bottom: pieData.isNotEmpty ? 0 : dW * 0.05),
            child: TextWidget(title: title, fontWeight: FontWeight.w600),
          ),
          if (pieData.isNotEmpty)
            SfCircularChart(
              legend: Legend(isVisible: true),
              series: <PieSeries<PieData, String>>[
                PieSeries<PieData, String>(
                  dataSource: pieData,
                  xValueMapper: (PieData data, _) => '${data.yData} Bookings',
                  yValueMapper: (PieData data, _) => data.yData,
                  dataLabelMapper: (PieData data, _) => '${data.xData}',
                  dataLabelSettings: DataLabelSettings(
                    isVisible: true,
                    textStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: tS * 12,
                      letterSpacing: .4,
                    ),
                  ),
                  radius: '90',
                ),
              ],
            ),
          if (!isDataEmpty)
            SfCartesianChart(
              selectionGesture: ActivationMode.singleTap,
              primaryXAxis: CategoryAxis(
                labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.black,
                      fontSize: tS * 14,
                      fontWeight: FontWeight.w500,
                    ),
                isVisible: true,
                maximumLabelWidth: dW * 0.16,
                majorGridLines:
                    MajorGridLines(width: 0.5, color: Colors.transparent),
                axisLine: AxisLine(width: 0.5, color: Colors.transparent),
              ),
              primaryYAxis: NumericAxis(
                majorGridLines: MajorGridLines(width: 0.5),
                axisLine: AxisLine(width: 0.5),
                labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.black,
                      fontSize: tS * 14,
                      fontWeight: FontWeight.w500,
                    ),
                interval: interval,
              ),
              series: <CartesianSeries>[
                ColumnSeries<BarChartModel, String>(
                  dataSource: data,
                  xValueMapper: (BarChartModel views, _) =>
                      views.title.toString(),
                  yValueMapper: (BarChartModel views, _) => views.value.toInt(),
                  pointColorMapper: (BarChartModel views, _) => views.color,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(2),
                    topRight: Radius.circular(2),
                  ),
                  width: barWidth,
                  selectionBehavior:
                      SelectionBehavior(selectedColor: getThemeColor()),
                )
              ],
            ),
          if (isDataEmpty)
            Padding(
              padding: EdgeInsets.symmetric(vertical: dW * 0.02),
              child: TextWidget(
                title: 'No data available for the chart.',
                fontSize: tS * 14,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }
}
