import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class UnPaidDropdown extends StatefulWidget {
  final UserModal user;
  final BookingModel booking;
  final String bookingId;
  final String paymentType;
  UnPaidDropdown({
    required this.user,
    required this.booking,
    required this.bookingId,
    required this.paymentType,
  });

  @override
  _UnPaidDropdownState createState() => _UnPaidDropdownState();
}

class _UnPaidDropdownState extends State<UnPaidDropdown> {
  bool isLoading = false;

  String selectedPaymentType = '';
  List<String> paymentTypes = ['Cash', 'UPI (GPay, PayTM, PhonePe)'];

  TextEditingController amountController = TextEditingController();
  FocusNode amountNode = FocusNode();

  updateStatus() async {
    try {
      // if (double.parse(amountController.text) >
      //     (widget.booking.totalAmount - widget.booking.amountPaid)) {
      //   callToastMessage(
      //     'Entered amount cannot be greater than balance amount',
      //   );
      //   return;
      // }
      setState(() {
        isLoading = true;
      });
      final data = await Provider.of<HomeProvider>(context, listen: false)
          .markBookingCompleted(
        accessToken: widget.user.accessToken,
        bookingId: widget.bookingId,
        businessId: widget.user.businessId,
        amountToAdd: double.parse(amountController.text),
        paymentType: selectedPaymentType,
      );
      if (data) {
        callToastMessage('Booking Status updated successfully');
        Navigator.of(context).pop(true);
        // Navigator.of(context).pop();
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    amountController.text = 

    Provider.of<HomeProvider>(context, listen: false)
        .getbookingBalanceAmount(widget.booking)
        .toStringAsFixed(2);

        // (widget.booking.totalAmount -
        //     widget.booking.amountPaid -
        //     widget.booking.discountedAmount)
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {});
    }

    return Container(
      // height: amountNode.hasFocus ? width * 1.4 : width * 0.7,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Balance Amount',
                  style: Theme.of(context)
                      .textTheme
                      .displaySmall!
                      .copyWith(fontSize: textScaleFactor * 16),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: width * 0.05),
            Text(
              'Amount Pending: \u20b9${Provider.of<HomeProvider>(context,listen: true).getbookingBalanceAmount(widget.booking).toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: textScaleFactor * 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: width * 0.04),
            Container(
              // width: width * 0.7,
              child: TextFormField(
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                controller: amountController,
                style: TextStyle(
                  fontSize: textScaleFactor * 16,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter balance amount',
                  hintStyle: TextStyle(
                    fontSize: textScaleFactor * 12,
                    fontWeight: FontWeight.w500,
                  ),
                  counterText: '',
                  contentPadding: EdgeInsets.only(left: width * 0.03),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(7),
                    borderSide: BorderSide(
                      color: Colors.black38,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(7),
                    borderSide: BorderSide(
                      color: Colors.black38,
                    ),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(7),
                    borderSide: BorderSide(
                      color: Colors.black38,
                    ),
                  ),
                ),
                keyboardType: TextInputType.number,
                maxLength: 10,
                focusNode: amountNode,
                onTap: () {
                  setState(() {
                    amountNode.requestFocus();
                  });
                },
              ),
            ),
            ...paymentTypes
                .map(
                  (option) => RadioListTile(
                    activeColor: Theme.of(context).primaryColor,
                    contentPadding: EdgeInsets.all(0),
                    title: Text(
                      option,
                      style: TextStyle(
                        fontSize: textScaleFactor * 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    value: option,
                    groupValue: selectedPaymentType,
                    onChanged: (value) {
                      setState(() {
                        selectedPaymentType = value.toString();
                      });
                    },
                  ),
                )
                .toList(),
            SizedBox(height: width * 0.07),
            buildRaisedButton(
              width,
              width * 0.12,
              isLoading
                  ? () {}
                  : () {
                      if (selectedPaymentType != '') {
                        updateStatus();
                      } else {
                        callToastMessage("Plaese select payment type");
                      }
                    },
              isLoading
                  ? circularForButton(width)
                  : Text(
                      'Update',
                      style: Theme.of(context).textTheme.displayMedium?.copyWith(
                            fontSize: textScaleFactor * 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: .50,
                            color: Colors.white,
                          ),
                    ),
              TargetPlatform.android,
              Theme.of(context).primaryColor,
              7,
            ),
          ],
        ),
      ),
    );
  }
}
