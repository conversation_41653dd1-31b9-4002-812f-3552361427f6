import 'dart:io';

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../fontSizes.dart';

class FilterDropdown extends StatefulWidget {
  final UserModal user;

  FilterDropdown({required this.user});

  @override
  FilterDropdownState createState() => FilterDropdownState();
}

class FilterDropdownState extends State<FilterDropdown> {
  bool isLoading = false;
  bool filtered = false;
  bool fetchVenue = false;

  List<BookingModel> listOfBookings = [];
  List venues = [];
  DateTime? startDate;
  DateTime? endDate;
  String turf = '0';

  Widget? dialog;

  initialDateRange() {
    if (startDate == null && endDate == null) {
      return null;
    } else {
      return DateTimeRange(start: startDate!, end: endDate!);
    }
  }

  selectStartDate() {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2021),
      lastDate: DateTime(2200),
    ).then((date) {
      if (date != null) {
        setState(() {
          startDate = date;
          endDate = null;
        });
      }
    });
  }

  selectEndDate() {
    showDatePicker(
      context: context,
      initialDate: startDate!,
      firstDate: startDate!,
      lastDate: DateTime(2200),
    ).then((date) async {
      if (date != null) {
        setState(() {
          endDate = date;
        });
        await fetchBookingDates();
      }
    });
  }

  fetchBookingDates() async {
    try {
      setState(() {
        isLoading = true;
        filtered = false;
      });

      List<String> turfId = [];

      if (widget.user.business != null) {
        widget.user.business!.turfs.forEach((turf) {
          turfId.add(turf.id);
        });
      }

      listOfBookings = await Provider.of<HomeProvider>(context, listen: false)
          .fetchBookingByDates(
        accessToken: widget.user.accessToken,
        businessId: widget.user.businessId,
        startDate: startDate.toString(),
        endDate: DateTime(
          endDate!.year,
          endDate!.month,
          endDate!.day,
          23,
          59,
          59,
        ).toString(),
        status: 'Date',
        turfs: turfId,
        role: widget.user.business == null ? 'Business' : 'Employee',
        venueId: turf,
      );
      setState(() {
        isLoading = false;
        filtered = true;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print(e);
    }
  }

  fetchVenues() async {
    try {
      setState(() {
        fetchVenue = false;
      });
      final data = await Provider.of<HomeProvider>(context, listen: false)
          .fetchTurfAdmin(
        accessToken: widget.user.accessToken,
      );
      venues = List.from(data);
      venues.insert(0, {'_id': '0', 'name': 'All'});
      print(venues);
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      setState(() {
        fetchVenue = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.user.business != null) {
      venues = [];
      venues.insert(0, {'_id': '0', 'name': 'All'});
      widget.user.business!.turfs.forEach((data) {
        venues.add({'_id': data.id, 'name': data.name});
      });
    } else {
      fetchVenues();
    }

    dialog = DateRangePickerDialog(
      firstDate: DateTime(2018),
      lastDate: DateTime(2200),
      currentDate: DateTime.now(),
      initialDateRange: initialDateRange(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final deviceHeight = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: deviceHeight * 0.93,
      child: fetchVenue
          ? Center(
              child: Platform.isAndroid
                  ? MaterialCircularLoader(deviceWidth * 0.07)
                  : CupertinoCircularLoader(15.0),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * 0.05),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.053),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      TextWidget(
                        title: 'Filter Bookings',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop(null);
                        },
                        child: CircleAvatar(
                          radius: 15,
                          backgroundColor: Colors.grey.shade300,
                          child: Icon(
                            Icons.clear,
                            color: Colors.black54,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(height: deviceWidth * 0.06),
                CustomContainer(
                  margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        title: 'Select Venue:',
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(height: deviceWidth * 0.03),
                      Container(
                        height: deviceWidth * 0.12,
                        padding: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.03,
                          vertical: deviceHeight * 0.005,
                        ),
                        margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
                        decoration: BoxDecoration(
                          border: Border.all(color: getThemeColor()),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton(
                            value: turf,
                            isExpanded: true,
                            iconSize: 30,
                            icon: Icon(
                              Icons.keyboard_arrow_down,
                              color: Theme.of(context).primaryColor,
                            ),
                            borderRadius: BorderRadius.circular(10),
                            iconEnabledColor: Colors.black,
                            hint: const Text(
                              'Select Venue:',
                              style: TextStyle(fontSize: 16),
                            ),
                            items: venues.map(
                              (item) {
                                return DropdownMenuItem(
                                  value: item['_id'],
                                  child: TextWidget(
                                    title: item['name'],
                                    letterSpacing: 0.3,
                                    fontWeight: FontWeight.w500,
                                  ),
                                );
                              },
                            ).toList(),
                            onChanged: (value) {
                              turf = value.toString();
                              setState(() {});
                              if (startDate != null && endDate != null) {
                                fetchBookingDates();
                              }
                            },
                          ),
                        ),
                      ),
                      TextWidget(
                        title: 'Select Date Range:',
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(height: deviceWidth * 0.03),
                      GestureDetector(
                        onTap: () {
                          showDialog<DateTimeRange>(
                            context: context,
                            useSafeArea: false,
                            builder: (BuildContext context) {
                              return Theme(
                                data: ThemeData.light().copyWith(
                                  colorScheme: ColorScheme.fromSwatch(
                                    primarySwatch: Colors.green,
                                    // primaryColorDark:
                                    //     Theme.of(context).primaryColor,
                                    accentColor: Theme.of(context).primaryColor,
                                  ),
                                  dialogBackgroundColor: Colors.white,
                                ),
                                child: dialog!,
                              );
                            },
                          ).then((value) {
                            if (value != null) {
                              startDate = value.start;
                              endDate = value.end;
                              fetchBookingDates();
                            }
                          });
                        },
                        child: CustomContainer(
                          boxShadow: [],
                          vPadding: .035,
                          radius: 8,
                          borderColor: getThemeColor(),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextWidget(
                                title: startDate == null
                                    ? 'Select date range'
                                    : '${DateFormat('dd MMM yyyy').format(startDate!)} - ${DateFormat('dd MMM yyyy').format(endDate!)}',
                                letterSpacing: 0.3,
                                fontWeight: FontWeight.w500,
                              ),
                              AssetSvgIcon(
                                  iconName: 'Calendarr', color: getThemeColor())
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: isLoading
                      ? CircularLoader(
                          android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
                      : SingleChildScrollView(
                          physics: BouncingScrollPhysics(),
                          child: Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: deviceWidth * 0.05),
                            child: Column(
                              children: [
                                SizedBox(height: deviceWidth * 0.08),
                                if (listOfBookings.length == 0 && filtered)
                                  EmptyListWidget(
                                      text: 'Bookings not found!',
                                      topPadding: .5),
                                if (!filtered)
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: deviceWidth * 0.05,
                                    ),
                                    margin:
                                        EdgeInsets.only(top: deviceWidth * 0.4),
                                    alignment: Alignment.center,
                                    child: Text(
                                      'Select booking date range',
                                      style: Theme.of(context)
                                          .textTheme
                                          .displaySmall!
                                          .copyWith(
                                            fontSize:
                                                textScaleFactor * displayMedium,
                                            color: Colors.black,
                                          ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                if (listOfBookings.isNotEmpty)
                                  Container(
                                    alignment: Alignment.topLeft,
                                    margin: EdgeInsets.only(
                                        bottom: deviceWidth * 0.02),
                                    child: TextWidget(
                                      title:
                                          'Bookings (${listOfBookings.length}):',
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ...listOfBookings.map(
                                  (booking) => BookingWidget(
                                    fromViewAll: true,
                                    booking: booking,
                                    user: widget.user,
                                    deviceWidth: deviceWidth,
                                    textScaleFactor: textScaleFactor,
                                  ),
                                ),
                                SizedBox(height: deviceWidth * 0.05),
                              ],
                            ),
                          ),
                        ),
                ),
              ],
            ),
    );
  }
}
