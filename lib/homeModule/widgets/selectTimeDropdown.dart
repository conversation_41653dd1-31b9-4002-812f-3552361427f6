import '../../homeModule/providers/homeProvider.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:intl/intl.dart';

import '../../common_function.dart';
import '../../commonWidgets/raisedButton.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../fontSizes.dart';

class SelectTimeDropdown extends StatefulWidget {
  final Venue turf;
  final double amount;
  final DateTime selectedDate;
  final Slot selectedSlot;
  final String sizeOrSport;
  final String sport;
  List selectedTime;
  SelectTimeDropdown({
    required this.turf,
    required this.amount,
    required this.selectedSlot,
    required this.selectedTime,
    required this.sizeOrSport,
    required this.selectedDate,
    required this.sport,
  });

  @override
  SSelectTimeDropdownState createState() => SSelectTimeDropdownState();
}

class SSelectTimeDropdownState extends State<SelectTimeDropdown> {
  bool isLoading = true;
  DateTime time = DateTime.now();
  late TimeOfDay startTime;
  late TimeOfDay endTime;
  List timeSlot = [];
  int lastIndexAdded = 0;
  List sortedTimeList = [];
  List bookedSlot = [];
  @override
  void initState() {
    super.initState();
    fetchBookedTimeSlot();
    widget.selectedTime = [];
  }

  fetchBookedTimeSlot() async {
    try {
      setState(() {
        isLoading = true;
      });
      List<String> extraTime = [];
      final slots = await Provider.of<HomeProvider>(context, listen: false)
          .fetchTurfBookingSlot(
        turfId: widget.turf.id,
        startDate: DateTime(widget.selectedDate.year, widget.selectedDate.month,
                widget.selectedDate.day, 0, 0, 0)
            .toString(),
        endDate: DateTime(widget.selectedDate.year, widget.selectedDate.month,
                widget.selectedDate.day, 23, 59, 59)
            .toString(),
        sizeOrSport: widget.sizeOrSport,
        isNet: widget.turf.sportCategory!.categoryName != 'Outdoor'
            ? false
            : widget.turf.isNet,
        updateBooking: false,
      );

      //TIME
      startTime = TimeOfDay(
        hour: int.parse(
          widget.selectedSlot.startTime
              .toDouble()
              .toStringAsFixed(2)
              .split('.')[0],
        ),
        minute: int.parse(
          widget.selectedSlot.startTime
              .toDouble()
              .toStringAsFixed(2)
              .split('.')[1],
        ),
      );
      if (widget.selectedSlot.session == 'Night') {
        if (widget.selectedSlot.endTime >= 0 &&
            widget.selectedSlot.endTime <= 5) {
          endTime = TimeOfDay(
            hour: 23,
            minute: 30,
          );
          var startTime1 = TimeOfDay(hour: 0, minute: 0);
          var endTime1 = TimeOfDay(
            hour: int.parse(
              widget.selectedSlot.endTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[0],
            ),
            minute: int.parse(
              widget.selectedSlot.endTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[1],
            ),
          );
          extraTime = getTimes(
            startTime1,
            endTime1,
            Duration(minutes: 30),
          ).map((tod) => tod.format(context)).toList();
          print('Extra Time: $extraTime');
        } else {
          endTime = TimeOfDay(
            hour: int.parse(
              widget.selectedSlot.endTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[0],
            ),
            minute: int.parse(
              widget.selectedSlot.endTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[1],
            ),
          );
        }
      } else {
        endTime = TimeOfDay(
          hour: int.parse(
            widget.selectedSlot.endTime
                .toDouble()
                .toStringAsFixed(2)
                .split('.')[0],
          ),
          minute: int.parse(
            widget.selectedSlot.endTime
                .toDouble()
                .toStringAsFixed(2)
                .split('.')[1],
          ),
        );
      }
      final step = Duration(minutes: widget.turf.slotTimeDifference!);
      var timeDifference = [];
      timeDifference = getTimes(startTime, endTime, step)
          .map((tod) => tod.format(context))
          .toList();
      print('TimeDifference: $timeDifference');
      List difference = [];
      timeDifference.addAll(extraTime);

      timeDifference.forEach((time) {
        if (time.contains('AM')) {
          difference.add(time.toString().replaceAll('AM', '').trim());
        } else if (time.contains('PM')) {
          difference.add(time.toString().replaceAll('PM', '').trim());
        } else {
          difference.add(time.toString());
        }
      });
      timeDifference = List.from(difference);
      for (var i = 0; i < timeDifference.length - 1; i++) {
        timeSlot.add([
          {
            'time': timeDifference[i],
            'isSelected': false,
            'alreadyBooked': false,
          },
          {
            'time': timeDifference[i + 1],
            'isSelected': false,
            'alreadyBooked': false,
          },
        ]);
      }

      bool? net = widget.turf.sportCategory!.categoryName != 'Outdoor'
          ? false
          : widget.turf.isNet;

      slots.forEach((slot) async {
        List timeList = [];
        if (widget.selectedSlot.session == 'Night') {
          if (slot['endTime'] >= 0 && slot['endTime'] <= 5) {
            if (net != null && net) {
              if (slot['quantities'] != null &&
                  slot['quantities'][widget.sizeOrSport] <= 0) {
                timeList.add(double.parse(
                            slot['startTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['startTime']
                        .toStringAsFixed(2)
                        .replaceAll('.', ':'));
                timeList.add(double.parse(
                            slot['endTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
              }
            } else {
              if (slot['remainingCount'] == 0 &&
                  slot['sizeOrSport'] == widget.sizeOrSport) {
                timeList.add(double.parse(
                            slot['startTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['startTime']
                        .toStringAsFixed(2)
                        .replaceAll('.', ':'));
                timeList.add(double.parse(
                            slot['endTime'].toString().split('.')[0]) <
                        10
                    ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                    : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
              }
            }
          } else {
            if (net != null && net) {
              if (slot['quantities'] != null &&
                  slot['quantities'][widget.sizeOrSport] <= 0) {
                timeList = getStartEndTime(
                  slot['startTime'],
                  slot['endTime'],
                );
              }
            } else {
              if (slot['remainingCount'] == 0 &&
                  slot['sizeOrSport'] == widget.sizeOrSport) {
                timeList = getStartEndTime(
                  slot['startTime'],
                  slot['endTime'],
                );
              }
            }
          }
        } else {
          if (net != null && net) {
            if (slot['quantities'] != null &&
                slot['quantities'][widget.sizeOrSport] <= 0) {
              timeList = getStartEndTime(
                slot['startTime'],
                slot['endTime'],
              );
            }
          } else {
            if (slot['remainingCount'] == 0 &&
                slot['sizeOrSport'] == widget.sizeOrSport) {
              timeList = getStartEndTime(
                slot['startTime'],
                slot['endTime'],
              );
            }
          }
        }
        bookedSlot.addAll(timeList);
      });

      // if (net) {
      final blockedSlots =
          await Provider.of<HomeProvider>(context, listen: false)
              .fetchBlockedSlot(
        turfId: widget.turf.id,
        startDate: widget.selectedDate
            .add(Duration(hours: 0, minutes: 0, seconds: 0))
            .toString(),
        endDate: widget.selectedDate
            .add(Duration(hours: 23, minutes: 59, seconds: 59))
            .toString(),
        sizeOrSport: widget.sizeOrSport,
        sport: widget.sport,
        isNet: widget.turf.isNet!,
      );

      blockedSlots.forEach((slot) {
        bookedSlot.add(double.parse(
                    slot['startTime'].toString().split('.')[0]) <
                10
            ? '0${slot['startTime'].toStringAsFixed(2).replaceAll('.', ':')}'
            : slot['startTime'].toStringAsFixed(2).replaceAll('.', ':'));
        bookedSlot.add(
            double.parse(slot['endTime'].toString().split('.')[0]) < 10
                ? '0${slot['endTime'].toStringAsFixed(2).replaceAll('.', ':')}'
                : slot['endTime'].toStringAsFixed(2).replaceAll('.', ':'));
      });
      // }
      print('bookedSlot: $bookedSlot');
      if (DateTime.now().isAfter(widget.selectedDate)) {
        var blocked = getTimes(
          TimeOfDay(
            hour: int.parse(
              widget.selectedSlot.startTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[0],
            ),
            minute: int.parse(
              widget.selectedSlot.startTime
                  .toDouble()
                  .toStringAsFixed(2)
                  .split('.')[1],
            ),
          ),
          TimeOfDay(
            hour: DateTime.now().hour,
            minute: DateTime.now().minute,
          ),
          step,
        ).map((tod) => tod.format(context)).toList();
        List blocked1 = [];
        print('Blocked: $blocked');
        blocked.forEach((time) {
          if (time.contains('AM')) {
            blocked1.add(time.toString().replaceAll('AM', '').trim());
          } else if (time.contains('PM')) {
            blocked1.add(time.toString().replaceAll('PM', '').trim());
          } else {
            blocked1.add(time.toString());
          }
        });
        if (DateTime.now().minute >= 0 && DateTime.now().minute < 30) {
          blocked1.add("${DateTime.now().hour}:00");
          blocked1.add("${DateTime.now().hour}:30");
        } else if (DateTime.now().minute >= 30 && DateTime.now().minute <= 60) {
          blocked1.add("${DateTime.now().hour}:30");
          blocked1.add("${DateTime.now().hour + 1}:00");
        }
        bookedSlot.addAll(blocked1);
      }

      widget.turf.pauseDates!.forEach((date) {
        if (date['start']
            .contains(DateFormat('yyyy-MM-dd').format(widget.selectedDate))) {
          var endPauseDate = DateTime.parse(date['end']);
          var startPauseDate = DateTime.parse(date['start']);
          var blocked = getTimes(
            TimeOfDay(
              hour: startPauseDate.hour,
              minute: startPauseDate.minute,
            ),
            TimeOfDay(
              hour: endPauseDate.hour == 0 ? 24 : endPauseDate.hour,
              minute: endPauseDate.minute,
            ),
            step,
          ).map((tod) => tod.format(context)).toList();
          bookedSlot.addAll(blocked);
        }
      });

      print('bookedSlot2: $bookedSlot');

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getStartEndTime(startTime, endTime) {
    return getTimes(
            TimeOfDay(
                hour: int.parse(
                    startTime.toDouble().toStringAsFixed(2).split('.')[0]),
                minute: int.parse(
                    startTime.toDouble().toStringAsFixed(2).split('.')[1])),
            TimeOfDay(
                hour: int.parse(
                    endTime.toDouble().toStringAsFixed(2).split('.')[0]),
                minute: int.parse(
                    endTime.toDouble().toStringAsFixed(2).split('.')[1])),
            Duration(minutes: 30))
        .map((tod) => tod.format(context))
        .toList();
  }

  checkSlotAvailability(time, slot) {
    if (bookedSlot.contains(time)) {
      setState(() {
        slot['alreadyBooked'] = true;
      });
      return true;
    } else {
      return false;
    }
  }

  loopTimeSlot(slot, status) {
    for (var i = 0; i < timeSlot.length; i++) {
      if (timeSlot[i][0]['time'] == slot[0]['time']) {
        setState(() {
          timeSlot[i][0]['isSelected'] = status;
          timeSlot[i][1]['isSelected'] = status;
          if (status) {
            widget.selectedTime
                .addAll([timeSlot[i][0]['time'], timeSlot[i][1]['time']]);
          } else {
            widget.selectedTime.remove(timeSlot[i][0]['time']);
            widget.selectedTime.remove(timeSlot[i][1]['time']);
          }
        });
      }
    }
  }

  getValue(value) {
    if (value == '00:00') {
      return 24.00;
    } else if (value == '00:30') {
      return 24.30;
    } else if (value == '01:00') {
      return 25.00;
    } else if (value == '01:30') {
      return 25.30;
    } else if (value == '02:00') {
      return 26.00;
    } else if (value == '02:30') {
      return 26.30;
    } else if (value == '03:00') {
      return 27.00;
    } else if (value == '03:30') {
      return 27.30;
    } else if (value == '04:00') {
      return 28.00;
    } else if (value == '04:30') {
      return 28.30;
    } else if (value == '05:00') {
      return 29.00;
    } else {
      return 24.0;
    }
  }

  selectAndUnselectTimeSlot(slot, add) {
    // sortedTimeList = [];

    if (add) {
      if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
          double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0 &&
          widget.selectedSlot.session == 'Night') {
        sortedTimeList
            .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
      } else {
        sortedTimeList.addAll([
          double.parse(slot[0]['time'].replaceAll(":", ".")),
          double.parse(slot[1]['time'].replaceAll(":", "."))
        ]);
      }
      sortedTimeList.sort();

      var abc = [];
      for (var i = 1; i < sortedTimeList.length; i += 2) {
        abc.add(sortedTimeList[i]);
      }
      print(abc);
      var showAlert = false;
      for (var i = 0; i < abc.length; i++) {
        if (i + 1 < abc.length) {
          if (double.parse(
                  (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)) >
              0.7) {
            print(double.parse(
                (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)));
            showAlert = true;
          }
        }
      }
      if (!showAlert) {
        loopTimeSlot(slot, true);
      } else {
        if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
            double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0 &&
            widget.selectedSlot.session == 'Night') {
          sortedTimeList.remove(getValue(slot[0]['time']));
          sortedTimeList.remove(getValue(slot[1]['time']));
        } else {
          sortedTimeList
              .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
          sortedTimeList
              .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
        }

        callToastMessage('You can only add consecutive timing.');
      }
      // }
    } else {
      if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
          double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0 &&
          widget.selectedSlot.session == 'Night') {
        sortedTimeList.remove(getValue(slot[0]['time']));
        sortedTimeList.remove(getValue(slot[1]['time']));
      } else {
        sortedTimeList
            .remove(double.parse(slot[0]['time'].replaceAll(":", ".")));
        sortedTimeList
            .remove(double.parse(slot[1]['time'].replaceAll(":", ".")));
      }
      var abc = [];
      for (var i = 1; i < sortedTimeList.length; i += 2) {
        abc.add(sortedTimeList[i]);
      }
      var showAlert = false;
      for (var i = 0; i < abc.length; i++) {
        if (i + 1 < abc.length) {
          if (double.parse(
                  (abc[i + 1] - abc[i]).toDouble().toStringAsFixed(2)) >
              0.7) {
            showAlert = true;
          }
        }
      }
      if (!showAlert) {
        loopTimeSlot(slot, false);
      } else {
        if (double.parse(slot[0]['time'].replaceAll(":", ".")) >= 0.0 &&
            double.parse(slot[1]['time'].replaceAll(":", ".")) <= 5.0 &&
            widget.selectedSlot.session == 'Night') {
          sortedTimeList
              .addAll([getValue(slot[0]['time']), getValue(slot[1]['time'])]);
        } else {
          sortedTimeList.addAll([
            double.parse(slot[0]['time'].replaceAll(":", ".")),
            double.parse(slot[1]['time'].replaceAll(":", "."))
          ]);
        }
        sortedTimeList.sort();
        callToastMessage('You can only add consecutive timing.');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final deviceHeight = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: deviceHeight * 0.9,
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.053),
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: deviceWidth * 0.055),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Add Time Slot',
                  style: Theme.of(context)
                      .textTheme
                      .displaySmall!
                      .copyWith(fontSize: textScaleFactor * headlineMedium),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
              child: Text(
                'Minimum booking should be of atleast 1 hour then you can further increase the time by 30 min.',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * headline9,
                      color: Colors.black.withOpacity(0.42),
                      fontWeight: FontWeight.normal,
                    ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Pricing for this slot is: ",
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 13,
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                          ),
                    ),
                    TextSpan(
                      text: "\u20B9${widget.amount}",
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 14.5,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Roboto",
                          ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Total amount with selected time slots is: ",
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 13,
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                          ),
                    ),
                    TextSpan(
                      text:
                          "\u20B9${widget.amount * (sortedTimeList.length / 2)}",
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 15,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Roboto",
                          ),
                    ),
                  ],
                ),
              ),
            ),
            isLoading
                ? Container(
                    height: deviceHeight * 0.3,
                    alignment: Alignment.bottomCenter,
                    child: CircularProgressIndicator(),
                  )
                : Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...timeSlot
                              .asMap()
                              .map(
                                (i, slot) => MapEntry(
                                  i,
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: deviceWidth * 0.02),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        GestureDetector(
                                          onTap: slot[0]['isSelected'] &&
                                                  slot[1]['isSelected']
                                              ? () => selectAndUnselectTimeSlot(
                                                  slot, false)
                                              : checkSlotAvailability(
                                                          slot[0]['time'],
                                                          slot[0]) &&
                                                      checkSlotAvailability(
                                                          slot[1]['time'],
                                                          slot[1])
                                                  ? null
                                                  : () =>
                                                      selectAndUnselectTimeSlot(
                                                          slot, true),
                                          child: Container(
                                            width: deviceWidth * 0.45,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(50),
                                              color: slot[0]['isSelected'] &&
                                                      slot[1]['isSelected']
                                                  ? Theme.of(context)
                                                      .primaryColor
                                                  : Colors.grey.shade300,
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              horizontal: deviceWidth * 0.04,
                                              vertical: deviceWidth * 0.038,
                                            ),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                '${get12HrClockFormat(slot[0]['time'])}${getTimePeriod(double.parse(slot[0]['time'].split(':')[0]))} - ${get12HrClockFormat(slot[1]['time'])}${getTimePeriod(double.parse(slot[1]['time'].split(':')[0]))}',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .displaySmall!
                                                    .copyWith(
                                                      fontSize:
                                                          textScaleFactor *
                                                              displayMedium,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: slot[0][
                                                                  'isSelected'] &&
                                                              slot[1]
                                                                  ['isSelected']
                                                          ? Colors.white
                                                          : checkSlotAvailability(
                                                                      slot[0][
                                                                          'time'],
                                                                      slot[
                                                                          0]) &&
                                                                  checkSlotAvailability(
                                                                      slot[1][
                                                                          'time'],
                                                                      slot[1])
                                                              ? Colors
                                                                  .grey.shade400
                                                              : Color(
                                                                  0xff6E7271),
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        // Container(
                                        //   margin: slot[0]['isSelected'] &&
                                        //           slot[1]['isSelected']
                                        //       ? EdgeInsets.only(
                                        //           right: deviceWidth * 0.135,
                                        //         )
                                        //       : EdgeInsets.only(
                                        //           right: 0,
                                        //         ),
                                        //   child: Text(
                                        //     "\u20B9 ${widget.amount.toString()}",
                                        //     style: TextStyle(
                                        //       fontSize: textScaleFactor * 13.5,
                                        //       color: Theme.of(context)
                                        //           .primaryColor,
                                        //       letterSpacing: .15,
                                        //       fontWeight: FontWeight.w700,
                                        //       fontFamily: "Roboto",
                                        //     ),
                                        //   ),
                                        // ),
                                        slot[0]['isSelected'] &&
                                                slot[1]['isSelected']
                                            ? GestureDetector(
                                                onTap: () =>
                                                    selectAndUnselectTimeSlot(
                                                        slot, false),
                                                child: CircleAvatar(
                                                  radius: 15,
                                                  backgroundColor:
                                                      Colors.grey.shade300,
                                                  child: Icon(
                                                    Icons.clear,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                              )
                                            : TextButton.icon(
                                                onPressed: checkSlotAvailability(
                                                            slot[0]['time'],
                                                            slot[0]) &&
                                                        checkSlotAvailability(
                                                            slot[1]['time'],
                                                            slot[1])
                                                    ? null
                                                    : () =>
                                                        selectAndUnselectTimeSlot(
                                                            slot, true),
                                                icon: Icon(
                                                  Icons.add,
                                                  color: checkSlotAvailability(
                                                              slot[0]['time'],
                                                              slot[0]) &&
                                                          checkSlotAvailability(
                                                              slot[1]['time'],
                                                              slot[1])
                                                      ? Colors.grey
                                                      : Theme.of(context)
                                                          .primaryColor,
                                                ),
                                                label: Text(
                                                  'ADD',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .displayLarge!
                                                      .copyWith(
                                                        fontSize:
                                                            textScaleFactor *
                                                                displayLarge,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: checkSlotAvailability(
                                                                    slot[0][
                                                                        'time'],
                                                                    slot[0]) &&
                                                                checkSlotAvailability(
                                                                    slot[1][
                                                                        'time'],
                                                                    slot[1])
                                                            ? Colors.grey
                                                            : Theme.of(context)
                                                                .primaryColor,
                                                      ),
                                                ),
                                              )
                                      ],
                                    ),
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          SizedBox(height: deviceWidth * 0.2),
                        ],
                      ),
                    ),
                  )
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: isLoading
            ? SizedBox.shrink()
            : widget.selectedTime.length >= 2
                ? Container(
                    margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
                    child: buildRaisedButton(
                      deviceWidth,
                      deviceWidth * 0.12,
                      () {
                        if (widget.selectedTime.length < 4) {
                          callToastMessage(
                              'Minimum booking should be of atleast 1 hour');
                          return;
                        } else {
                          Navigator.of(context).pop(widget.selectedTime);
                        }
                      },
                      Text(
                        'Done',
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                              fontSize: textScaleFactor * 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: .50,
                              color: Colors.white,
                            ),
                      ),
                      TargetPlatform.android,
                      Theme.of(context).primaryColor,
                      7,
                    ),
                  )
                : SizedBox.shrink(),
      ),
    );
  }
}
