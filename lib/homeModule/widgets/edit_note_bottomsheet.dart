import 'package:bys_business/commonWidgets/text_widget.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../fontSizes.dart';
import '../../homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class EditNoteBottomSheet extends StatefulWidget {
  final UserModal user;
  final String bookingId;
  final String note;
  EditNoteBottomSheet({
    required this.user,
    required this.bookingId,
    required this.note,
  });

  @override
  _EditNoteBottomSheetState createState() => _EditNoteBottomSheetState();
}

class _EditNoteBottomSheetState extends State<EditNoteBottomSheet> {
  bool isLoading = false;

  TextEditingController noteController = TextEditingController();
  FocusNode noteNode = FocusNode();

  updateNote() async {
    try {
      setState(() {
        isLoading = true;
      });
      final data = await Provider.of<HomeProvider>(context, listen: false)
          .editNoteForBooking(
        accessToken: widget.user.accessToken,
        bookingId: widget.bookingId,
        note: noteController.text.trim(),
      );
      if (data) {
        callToastMessage('Note updated successfully');
        Navigator.of(context).pop(noteController.text.trim());
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    noteController.text = widget.note;
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {});
    }

    return Container(
      height: noteNode.hasFocus ? width * 1.65 : width * 0.9,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                TextWidget(title: 'Edit Note', fontSize: textScaleFactor * 16),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: width * 0.05),
            Text(
              'Note',
              style: TextStyle(
                fontSize: textScaleFactor * 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: width * 0.04),
            // Container(
            //   // width: width * 0.7,
            //   child: TextFormField(
            //     controller: noteController,
            //     style: TextStyle(
            //       fontSize: textScaleFactor * 16,
            //       fontWeight: FontWeight.w600,
            //     ),
            //     decoration: InputDecoration(
            //       hintText: 'Enter note',
            //       hintStyle: TextStyle(
            //         fontSize: textScaleFactor * 12,
            //         fontWeight: FontWeight.w500,
            //       ),
            //       counterText: '',
            //       contentPadding: EdgeInsets.only(left: width * 0.03),
            //       enabledBorder: OutlineInputBorder(
            //         borderRadius: BorderRadius.circular(7),
            //         borderSide: BorderSide(
            //           color: Colors.black38,
            //         ),
            //       ),
            //       focusedBorder: OutlineInputBorder(
            //         borderRadius: BorderRadius.circular(7),
            //         borderSide: BorderSide(
            //           color: Colors.black38,
            //         ),
            //       ),
            //       disabledBorder: OutlineInputBorder(
            //         borderRadius: BorderRadius.circular(7),
            //         borderSide: BorderSide(
            //           color: Colors.black38,
            //         ),
            //       ),
            //     ),
            //     keyboardType: TextInputType.multiline,
            //     focusNode: noteNode,
            //     onTap: () {
            //       setState(() {
            //         noteNode.requestFocus();
            //       });
            //     },
            //   ),
            // ),
            Container(
              margin: EdgeInsets.only(bottom: width * 0.02),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: Theme.of(context).primaryColor.withOpacity(0.08),
              ),
              constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.width * 0.3),
              child: TextFormField(
                style: TextStyle(
                  // color: Color(0xff6E7271),
                  fontWeight: FontWeight.w600,
                  fontSize: textScaleFactor * headline9,
                  letterSpacing: .30,
                ),
                focusNode: noteNode,
                decoration: InputDecoration(
                  counterText: '',
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        fontWeight: FontWeight.normal,
                        color: Color(0xff757371),
                      ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: width * 0.034,
                    vertical: width * 0.04,
                  ),
                  hintText: "Write venue description here...",
                  border: InputBorder.none,
                ),
                maxLines: null,
                maxLength: 400,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.sentences,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: noteController,
                keyboardType: TextInputType.streetAddress,
                textInputAction: TextInputAction.newline,
                onChanged: (value) {
                  setState(() {});
                },
                // validator: (value) {
                //   if (value!.isEmpty) {
                //     return 'Please enter venue description';
                //   }
                // },
              ),
            ),
            Container(
              margin: EdgeInsets.only(bottom: width * 0.09),
              alignment: Alignment.bottomRight,
              child: Text(
                '${noteController.text.length}/400 char',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * headline9,
                      fontWeight: FontWeight.normal,
                      color: Colors.black,
                    ),
              ),
            ),
            // SizedBox(height: width * 0.03),
            buildRaisedButton(
              width,
              width * 0.12,
              isLoading || noteController.text.trim() == ''
                  ? () {}
                  : () {
                      updateNote();
                    },
              isLoading
                  ? circularForButton(width)
                  : Text(
                      widget.note == '' ? "Add" : 'Update',
                      style: Theme.of(context).textTheme.displayMedium?.copyWith(
                            fontSize: textScaleFactor * 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: .50,
                            color: Colors.white,
                          ),
                    ),
              TargetPlatform.android,
              Theme.of(context).primaryColor,
              7,
            ),
          ],
        ),
      ),
    );
  }
}
