// ignore_for_file: must_be_immutable

import 'package:bys_business/colors.dart';
import 'package:provider/provider.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/screens/bookingScreenByStatus.dart';
import '../../homeModule/screens/search_booking.dart';
import '../../homeModule/widgets/filterDropdown.dart';
import '../../moreModule.dart/provider/moreProvider.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class BookingStatusWidget extends StatelessWidget {
  final double deviceWidth;
  final double textScaleFactor;
  final Function getStatusCount;
  final UserModal user;
  final bool fromBYSBusiness;
  BookingStatusWidget(
      {Key? key,
      required this.deviceWidth,
      required this.getStatusCount,
      required this.textScaleFactor,
      required this.user,
      this.fromBYSBusiness = false})
      : super(key: key);

  List bookingStatus = [
    {
      'title': "Upcoming",
      'index': 1,
      'color': Color(0xffFFFEDB1),
    },
    {
      'title': "Completed",
      'index': 2,
      'color': Color(0xffE3FEDB),
    },
    {
      'title': "Cancelled",
      'index': 3,
      'color': Color(0xffFFCBCB),
    }
  ];

  filterDropdown(BuildContext context) {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: FilterDropdown(user: user),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: deviceWidth * 0.07),
        Container(
          margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              fromBYSBusiness
                  ? TextWidget(
                      title: 'Bookings',
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    )
                  : TextWidget(
                      title: 'Total Bookings',
                      fontSize: 16,
                      color: Color(0xff434343),
                      fontWeight: FontWeight.w500,
                    ),
              Row(
                children: [
                  GestureDetector(
                    onTap: () => push(SearchBooking()),
                    child: Container(
                      padding: EdgeInsets.all(7),
                      child: SvgPicture.asset(
                        'assets/svgIcons/search.svg',
                        height: 22,
                        color: greenPrimary,
                      ),
                    ),
                  ),
                  SizedBox(width: deviceWidth * 0.02),
                  GestureDetector(
                    onTap: () {
                      filterDropdown(context);
                    },
                    child: Container(
                      padding: EdgeInsets.all(7),
                      child: SvgPicture.asset('assets/svgIcons/filter1.svg'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (fromBYSBusiness == false)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ...bookingStatus.map(
                (status) => GestureDetector(
                  onTap: () {
                    push(
                      BookingScreenByStatus(
                        status: status['title'],
                      ),
                    );
                  },
                  child: Container(
                    height: deviceWidth * 0.28,
                    width: deviceWidth * 0.28,
                    padding: EdgeInsets.symmetric(
                      horizontal: deviceWidth * 0.025,
                      vertical: deviceWidth * 0.005,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(17),
                      color: status['color'],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          width: deviceWidth * 0.26,
                          alignment: Alignment.topLeft,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: TextWidget(
                              title: '${status['title']}',
                              textAlign: TextAlign.left,
                              color: Color(0xff3E3E3E),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        TextWidget(
                          title:
                              '${getStatusCount(status['index']) > 9 ? getStatusCount(status['index']) : '0${getStatusCount(status['index'])}'}',
                          textAlign: TextAlign.center,
                          fontSize: 24,
                          color: Color(0xff3E3E3E),
                          fontWeight: FontWeight.w500,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )
      ],
    );
  }
}
