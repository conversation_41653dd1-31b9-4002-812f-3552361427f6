import 'package:bys_business/common_function.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/providers/homeProvider.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class GenerateReportBottomSheet extends StatefulWidget {
  final double dW;
  final double dH;
  final double tS;
  final UserModal user;
  const GenerateReportBottomSheet({
    Key? key,
    required this.dW,
    required this.dH,
    required this.tS,
    required this.user,
  }) : super(key: key);

  @override
  _GenerateReportBottomSheetState createState() =>
      _GenerateReportBottomSheetState();
}

class _GenerateReportBottomSheetState extends State<GenerateReportBottomSheet> {
  Widget? dialog;

  initialDateRange() {
    if (startDate == null && endDate == null) {
      return null;
    } else {
      return DateTimeRange(start: startDate!, end: endDate!);
    }
  }

  DateTime? startDate;
  DateTime? endDate;
  bool isLoading = false;
  bool fetchVenue = false;
  List venues = [];
  String turf = '0';

  generateReport() async {
    try {
      if (startDate == null || endDate == null) {
        callToastMessage('Please select start and end date');
        return;
      }
      setState(() {
        isLoading = true;
      });
      final result = await Provider.of<HomeProvider>(context, listen: false)
          .generateBookingReport(
        // businessId: widget.business == null
        //     ? widget.user.businessId
        //     : widget.business!.owner.businessId,
        businessId: widget.user.businessId,
        accessToken: widget.user.accessToken,
        startDate:
            DateTime(startDate!.year, startDate!.month, startDate!.day, 0, 0, 0)
                .toString(),
        endDate:
            DateTime(endDate!.year, endDate!.month, endDate!.day, 23, 59, 59)
                .toString(),
        generatedBy: '${widget.user.firstName} ${widget.user.lastName}',
        turfId: turf,
      );
      if (result) {
        Navigator.of(context).pop(true);
        callToastMessage('Report will generate shorlty');
      } else {
        callToastMessage('Something went wrong');
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  fetchVenues() async {
    try {
      setState(() {
        fetchVenue = false;
      });
      final data = await Provider.of<HomeProvider>(context, listen: false)
          .fetchTurfAdmin(
        accessToken: widget.user.accessToken,
      );
      venues = List.from(data);
      venues.insert(0, {'_id': '0', 'name': 'All'});
      print(venues);
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      setState(() {
        fetchVenue = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.user.business != null) {
      venues = [];
      venues.insert(0, {'_id': '0', 'name': 'All'});
      widget.user.business!.turfs.forEach((data) {
        venues.add(
          {
            '_id': data.id,
            'name': data.name,
          },
        );
      });
    } else {
      fetchVenues();
    }

    dialog = DateRangePickerDialog(
      firstDate: DateTime(2018),
      lastDate: DateTime(2200),
      currentDate: DateTime.now(),
      initialDateRange: initialDateRange(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.dW * 0.9,
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: widget.dW * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: widget.dW * 0.019),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Generate Report',
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                      fontSize: widget.tS * 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black),
                ),
                IconButton(
                  padding: EdgeInsets.all(0),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  icon: Icon(Icons.clear),
                )
              ],
            ),
            SizedBox(height: widget.dW * 0.03),
            Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    title: 'Select Venue:',
                    color: Colors.black,
                  ),
                  SizedBox(height: widget.dW * 0.03),
                  Container(
                    height: widget.dW * 0.12,
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.dW * 0.03,
                      vertical: widget.dH * 0.005,
                    ),
                    margin: EdgeInsets.only(bottom: widget.dW * 0.05),
                    decoration: BoxDecoration(
                      border: Border.all(color: getThemeColor()),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton(
                        value: turf,
                        isExpanded: true,
                        iconSize: 30,
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          color: Theme.of(context).primaryColor,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        iconEnabledColor: Colors.black,
                        hint: const Text(
                          'Select Venue:',
                          style: TextStyle(fontSize: 16),
                        ),
                        items: venues.map(
                          (item) {
                            return DropdownMenuItem(
                              value: item['_id'],
                              child: Text(
                                item['name'],
                                style: TextStyle(
                                  fontSize: widget.tS * 16,
                                  letterSpacing: 0.3,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            );
                          },
                        ).toList(),
                        onChanged: (value) {
                          setState(() {
                            turf = value.toString();
                          });
                        },
                      ),
                    ),
                  ),
                  TextWidget(
                    title: 'Start & End Date:',
                    color: Colors.black,
                  ),
                  SizedBox(height: widget.dW * 0.03),
                  GestureDetector(
                    onTap: () {
                      showDialog<DateTimeRange>(
                        context: context,
                        useSafeArea: false,
                        builder: (BuildContext context) {
                          return Theme(
                            data: ThemeData.light().copyWith(
                              colorScheme: ColorScheme.fromSwatch(
                                primarySwatch: Colors.green,
                                // primaryColorDark:
                                    // Theme.of(context).primaryColor,
                                accentColor: Theme.of(context).primaryColor,
                              ),
                              dialogBackgroundColor: Colors.white,
                            ),
                            child: dialog!,
                          );
                        },
                      ).then((value) {
                        if (value != null) {
                          setState(() {
                            startDate = value.start;
                            endDate = value.end;
                          });
                        }
                      });
                    },
                    child: Container(
                      height: widget.dW * 0.12,
                      padding: EdgeInsets.symmetric(
                        horizontal: widget.dW * 0.03,
                        vertical: widget.dH * 0.005,
                      ),
                      margin: EdgeInsets.only(bottom: widget.dW * 0.065),
                      decoration: BoxDecoration(
                        border: Border.all(color: getThemeColor()),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            startDate == null
                                ? 'Select date range'
                                : '${DateFormat('dd MMM yyyy').format(startDate!)} - ${DateFormat('dd MMM yyyy').format(endDate!)}',
                            style: TextStyle(
                              fontSize: widget.tS * 14,
                              letterSpacing: 0.3,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SvgPicture.asset(
                            'assets/svgIcons/Calendarr.svg',
                            color: Colors.black,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: widget.dW * 0.05),
            Align(
              alignment: Alignment.bottomRight,
              child: buildRaisedButton(
                widget.dW,
                widget.dW * 0.12,
                isLoading ? () {} : generateReport,
                isLoading
                    ? circularForButton(widget.dW)
                    : Text(
                        'Generate Report',
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                              fontSize: widget.tS * 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: .50,
                              color: Colors.white,
                            ),
                      ),
                TargetPlatform.android,
                Theme.of(context).primaryColor,
                7,
              ),
            )
          ],
        ),
      ),
    );
  }
}
