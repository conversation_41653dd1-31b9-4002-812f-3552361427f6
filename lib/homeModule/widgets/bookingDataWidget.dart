import 'dart:io';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';

class BookingDataWidget extends StatefulWidget {
  final double deviceWidth;
  final double textScaleFactor;
  final UserModal user;
  final DateTime startDate;
  final DateTime endDate;
  final String status;
  final ScrollController scrollController;
  const BookingDataWidget({
    Key? key,
    required this.deviceWidth,
    required this.textScaleFactor,
    required this.user,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<BookingDataWidget> createState() => _BookingDataWidgetState();
}

class _BookingDataWidgetState extends State<BookingDataWidget> {
  bool isLoading = false;
  bool lazyLoading = false;
  bool refresh = false;

  ScrollController _dataScrollController = ScrollController();

  List<BookingModel> listOfBookings = [];

  fetchBooking() async {
    try {
      setState(() => isLoading = true);
      await fetchData();
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  fetchData() async {
    List<String> turfId = [];

    if (widget.user.business != null) {
      widget.user.business!.turfs.forEach((turf) {
        turfId.add(turf.id);
      });
    }
    await Provider.of<HomeProvider>(context, listen: false)
        .fetchBusinessBookingNew(
      accessToken: widget.user.accessToken,
      businessId: widget.user.businessId,
      startDate: DateTime(
        widget.startDate.year,
        widget.startDate.month,
        widget.startDate.day,
        0,
        0,
        0,
      ).toString(),
      endDate: DateTime(
        widget.endDate.year,
        widget.endDate.month,
        widget.endDate.day,
        23,
        59,
        59,
      ).toString(),
      status: widget.status,
      turfs: turfId,
      role: widget.user.business == null ? 'Business' : 'Employee',
      refresh: refresh,
    );
  }

  lazyLoad() async {
    if (_dataScrollController.position.extentAfter == 0) {
      setState(() {
        lazyLoading = true;
      });
      await fetchData();
      setState(() {
        lazyLoading = false;
      });
    }
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    widget.scrollController.animateTo(
      widget.deviceWidth * 0.5,
      duration: Duration(
        milliseconds: 100,
      ),
      curve: Curves.decelerate,
    );
    if (notification is ScrollEndNotification) {
      lazyLoad();
    }
    return false;
  }

  @override
  void initState() {
    super.initState();
    Provider.of<HomeProvider>(context, listen: false).emptyBooking();
    fetchBooking();
  }

  @override
  Widget build(BuildContext context) {
    listOfBookings = Provider.of<HomeProvider>(context).bookings;

    return isLoading
        ? Center(
            child: Platform.isAndroid
                ? MaterialCircularLoader(
                    widget.deviceWidth * 0.07,
                  )
                : CupertinoCircularLoader(15.0),
          )
        : listOfBookings.isEmpty
            ? Container(
                padding: EdgeInsets.symmetric(
                  horizontal: widget.deviceWidth * 0.05,
                ),
                alignment: Alignment.center,
                child: Text(
                  'Bookings not found',
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                        fontSize: widget.textScaleFactor * displayMedium,
                        color: Colors.black,
                      ),
                ),
              )
            : RefreshIndicator(
                onRefresh: () async {
                  setState(() {
                    refresh = true;
                    fetchData();
                  });
                },
                color: Theme.of(context).primaryColor,
                triggerMode: RefreshIndicatorTriggerMode.anywhere,
                child: NotificationListener<ScrollNotification>(
                  onNotification: _handleScrollNotification,
                  child: SingleChildScrollView(
                    controller: _dataScrollController,
                    physics: AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(
                        horizontal: widget.deviceWidth * 0.05),
                    child: Column(
                      children: [
                        SizedBox(height: widget.deviceWidth * 0.06),
                        ...listOfBookings.map(
                          (booking) => BookingWidget(
                            booking: booking,
                            deviceWidth: widget.deviceWidth,
                            textScaleFactor: widget.textScaleFactor,
                            user: widget.user,
                          ),
                        ),
                        SizedBox(height: widget.deviceWidth * 0.02),
                        if (lazyLoading)
                          Center(
                            child: Padding(
                              padding: EdgeInsets.only(
                                  top: widget.deviceWidth * 0.08,
                                  bottom: widget.deviceWidth * 0.1),
                              child: Platform.isIOS
                                  ? CupertinoCircularLoader(10.0)
                                  : MaterialCircularLoader(
                                      widget.deviceWidth * 0.06,
                                    ),
                            ),
                          ),
                        SizedBox(height: widget.deviceWidth * 0.06),
                      ],
                    ),
                  ),
                ),
              );
  }
}
