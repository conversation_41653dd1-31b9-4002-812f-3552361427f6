import 'package:flutter/material.dart';

import '../../fontSizes.dart';

class BookingSummaryCard extends StatelessWidget {
  BookingSummaryCard({
    Key? key,
    required this.textScaleFactor,
    required this.deviceWidth,
    required this.title,
    required this.content,
    this.roboto = false,
    this.loadConvenienceCharges = false,
  }) : super(key: key);

  final double textScaleFactor;
  final double deviceWidth;
  final String title;
  final String content;
  bool roboto;
  bool loadConvenienceCharges;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.02),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            alignment: Alignment.topLeft,
            width: deviceWidth * 0.3,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      fontSize: textScaleFactor * displayLarge,
                      color:
                          loadConvenienceCharges ? Colors.grey : Colors.black,
                      fontWeight: title == "Total" || loadConvenienceCharges
                          ? FontWeight.w800
                          : FontWeight.w600,
                    ),
              ),
            ),
          ),
          // roboto
          //     ? RichText(
          //         text: TextSpan(
          //           children: [
          //             TextSpan(
          //               text: '\u20b9',
          //               style: TextStyle(
          //                 fontSize: textScaleFactor * displayLarge,
          //                 fontWeight: FontWeight.bold,
          //                 color: Colors.black,
          //               ),
          //             ),
          //             TextSpan(
          //               text: '$content',
          //               style: Theme.of(context).textTheme.displayLarge!.copyWith(
          //                     fontSize: textScaleFactor * displayLarge,
          //                     fontWeight: FontWeight.bold,
          //                     color: Colors.black,
          //                   ),
          //             ),
          //           ],
          //         ),
          //       )
          //     :
          Container(
            alignment: Alignment.topRight,
            width: deviceWidth * 0.5,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '$content',
                style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      fontSize: textScaleFactor * 15,
                      color:
                          loadConvenienceCharges ? Colors.grey : Colors.black,
                      fontWeight: loadConvenienceCharges
                          ? FontWeight.w800
                          : FontWeight.bold,
                    ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
