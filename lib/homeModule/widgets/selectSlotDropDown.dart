import 'package:flutter/material.dart';

import '../../fontSizes.dart';
import '../../venueModule/models/venue_model.dart';

class SelectSlotSizeDropdown extends StatefulWidget {
  final Venue turf;
  final DateTime selectedDate;
  SelectSlotSizeDropdown({required this.turf, required this.selectedDate});

  @override
  SselectSlotSizeDropdownState createState() => SselectSlotSizeDropdownState();
}

class SselectSlotSizeDropdownState extends State<SelectSlotSizeDropdown> {
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
  }

  checkSlotBlocking(String session) {
    // DateTime date = DateTime.now();
    // if (session == 'Morning') {
    //   if ((date.hour > 5 && date.hour < 12) ||
    //       date.isBefore(widget.selectedDate)) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // } else if (session == 'Afternoon') {
    //   if ((date.hour >= 12 && date.hour < 16) ||
    //       date.isBefore(widget.selectedDate)) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // } else if (session == 'Evening') {
    //   if ((date.hour >= 16 && date.hour < 20) ||
    //       date.isBefore(widget.selectedDate)) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // } else if (session == 'Night') {
    //   return true;
    // } else {
    return true;
    // }
  }

  checkSlotAvailability(Slot slot) {
    bool available = false;
    if (widget.selectedDate.weekday == 6 || widget.selectedDate.weekday == 7) {
      for (var i = 0; i < slot.priceAndQuantity.length; i++) {
        if (slot.priceAndQuantity[i].weekendPrice != 0) {
          available = true;
          return available;
        }
      }
    } else {
      for (var i = 0; i < slot.priceAndQuantity.length; i++) {
        if (slot.priceAndQuantity[i].price != 0) {
          available = true;
          return available;
        }
      }
    }
    return available;
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: width * 0.55,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Select Slot',
                  style: Theme.of(context)
                      .textTheme
                      .displaySmall!
                      .copyWith(fontSize: textScaleFactor * headlineMedium),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: width * 0.05),
            Wrap(
              children: [
                ...widget.turf.slots!.map(
                  (slot) => !checkSlotAvailability(slot)
                      ? SizedBox.shrink()
                      : GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop(slot);
                          },
                          child: Container(
                            margin: EdgeInsets.only(
                              right: width * 0.03,
                              bottom: width * 0.03,
                            ),
                            padding: EdgeInsets.symmetric(
                              vertical: width * 0.032,
                              horizontal: width * 0.04,
                            ),
                            decoration: BoxDecoration(
                              color: Color(0xffF3F3F4),
                              borderRadius: BorderRadius.circular(100),
                            ),
                            child: Text(
                              slot.session,
                              style: Theme.of(context)
                                  .textTheme
                                  .displaySmall!
                                  .copyWith(
                                    fontSize: textScaleFactor * displayMedium,
                                    color: Color(0xff6E7271),
                                  ),
                            ),
                          ),
                        ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
