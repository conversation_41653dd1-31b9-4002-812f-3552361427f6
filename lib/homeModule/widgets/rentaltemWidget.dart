import '../../homeModule/models/bookingModel.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../fontSizes.dart';
import 'package:flutter/material.dart';

class RentalItemsWidget extends StatefulWidget {
  const RentalItemsWidget({
    Key? key,
    required this.deviceWidth,
    required this.rentalItem,
    required this.textScaleFactor,
    required this.function,
  }) : super(key: key);

  final double deviceWidth;
  final RentalItem rentalItem;
  final double textScaleFactor;
  final Function function;

  @override
  _RentalItemsWidgetState createState() => _RentalItemsWidgetState();
}

class _RentalItemsWidgetState extends State<RentalItemsWidget> {
  // String getPriceWithQuantity(quantity){
  //   if(quantity == 0){
  //     return widget.rentalItem.price.toStringAsFixed(2);
  //   }

  //   return (double.parse( quantity.toString() )* widget.rentalItem.price).toStringAsFixed(2);

  // }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: widget.deviceWidth * 0.02,
        horizontal: widget.deviceWidth * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // GestureDetector(
          //   onTap: () {
          //     if (widget.rentalItem.isSelected) {
          //       widget.rentalItem.qunatity = 0;
          //       widget.function();
          //       setState(() {
          //         widget.rentalItem.isSelected = false;
          //       });
          //       return;
          //     }
          //     setState(() {
          //       widget.rentalItem.isSelected = true;
          //       widget.rentalItem.qunatity = 1;
          //       widget.function();
          //     });
          //     return;
          //   },
          //   child: Container(
          //     height: widget.deviceWidth * 0.07,
          //     width: widget.deviceWidth * 0.07,
          //     decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(6),
          //         border: Border.all(color: Colors.grey.shade300),
          //         color: widget.rentalItem.isSelected
          //             ? Colors.grey.shade300
          //             : Colors.transparent),
          //     child: widget.rentalItem.isSelected
          //         ? Icon(Icons.check, size: 18)
          //         : SizedBox.shrink(),
          //   ),
          // ),
          Container(
            margin: EdgeInsets.only(
              right: widget.deviceWidth * 0.04,
            ),
            width: widget.deviceWidth * 0.19,
            height: widget.deviceWidth * 0.19,
            padding: EdgeInsets.all(9),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey.shade400)),
            // child: FadeInImage(
            //   placeholder: AssetImage('assets/images/placeholder.jpg'),
            //   image: NetworkImage(widget.rentalItem.productImage),
            //   fit: BoxFit.fill,
            // ),
            child: CachedNetworkImage(
              fit: BoxFit.fill,
              imageUrl: widget.rentalItem.productImage,
              placeholder: (_, __) => Image.asset(
                'assets/images/placeholder.jpg',
                fit: BoxFit.cover,
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: widget.deviceWidth * .5),
                margin:
                    EdgeInsets.symmetric(vertical: widget.deviceWidth * 0.005),
                child: Text(
                  '${widget.rentalItem.productName}',
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: widget.textScaleFactor * displayLarge,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: widget.deviceWidth * 0.005),
                child: Text(
                  "For ${widget.rentalItem.duration.toString()} min",
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: widget.textScaleFactor * headline9,
                        color: Colors.black,
                        fontWeight: FontWeight.w400,
                      ),
                ),
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: widget.deviceWidth * 0.005),
                child: Text(
                  "\u{20B9}${widget.rentalItem.price.toStringAsFixed(2)}",
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: widget.textScaleFactor * displayLarge,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          Spacer(),
          // if (widget.rentalItem.isSelected)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              GestureDetector(
                onTap: widget.rentalItem.quantity == 0
                    ? null
                    : () {
                        if (widget.rentalItem.quantity == 1) {
                          setState(() {
                            widget.rentalItem.isSelected = false;
                          });
                        }
                        setState(() {
                          widget.rentalItem.quantity -= 1;
                          // widget.rentalItem.isSelected = false;
                          widget.function();
                        });
                      },
                child: Container(
                  height: widget.deviceWidth * 0.06,
                  width: widget.deviceWidth * 0.06,
                  decoration: BoxDecoration(
                    // borderRadius: BorderRadius.circular(5),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: widget.rentalItem.quantity == 0
                          ? Colors.grey
                          : Theme.of(context).primaryColor,
                    ),
                  ),
                  child: Icon(
                    Icons.remove,
                    size: 20,
                    color: widget.rentalItem.quantity == 0
                        ? Colors.grey
                        : Theme.of(context).primaryColor,
                  ),
                ),
              ),
              Container(
                constraints:
                    BoxConstraints(minWidth: widget.deviceWidth * 0.03),
                alignment: Alignment.center,
                margin: EdgeInsets.symmetric(
                    horizontal: widget.deviceWidth * 0.017),
                // decoration: BoxDecoration(
                //     borderRadius: BorderRadius.circular(5),
                //     border:
                //         Border.all(color: Colors.grey.shade300)),
                child: Text(
                  widget.rentalItem.quantity.toString(),
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: widget.rentalItem.quantity > 10
                            ? widget.textScaleFactor * 14
                            : widget.textScaleFactor * 15,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    widget.rentalItem.quantity += 1;
                    widget.function();
                  });
                },
                child: Container(
                  height: widget.deviceWidth * 0.06,
                  width: widget.deviceWidth * 0.06,
                  decoration: BoxDecoration(
                    // borderRadius: BorderRadius.circular(5),
                    shape: BoxShape.circle,
                    color: Theme.of(context).primaryColor,
                    border: Border.all(color: Theme.of(context).primaryColor),
                  ),
                  child: Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
