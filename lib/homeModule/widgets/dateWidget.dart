import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../fontSizes.dart';

class DateWidget extends StatelessWidget {
  final double deviceWidth;
  final double textScaleFactor;
  final DateTime selectedDate;
  final Function changeDate;
  final int listOfBookings;
  DateWidget({
    Key? key,
    required this.deviceWidth,
    required this.changeDate,
    required this.listOfBookings,
    required this.selectedDate,
    required this.textScaleFactor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              child: Text(
                '${DateFormat('dd MMM, yyyy').format(selectedDate)}',
                style: Theme.of(context)
                    .textTheme
                    .displaySmall!
                    .copyWith(fontSize: textScaleFactor * displaySmall),
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: deviceWidth * 0.005),
              child: Text(
                '$listOfBookings Bookings',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * headline9,
                      color: Color(0xff707070),
                      fontWeight: FontWeight.normal,
                    ),
              ),
            ),
          ],
        ),
        GestureDetector(
          onTap: () {
            changeDate();
          },
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.black,
              ),
              shape: BoxShape.circle,
            ),
            padding: EdgeInsets.all(9),
            margin: EdgeInsets.only(left: deviceWidth * 0.02),
            child: Icon(
              Icons.calendar_today_outlined,
              color: Colors.black,
              size: 20,
            ),
          ),
        )
      ],
    );
  }
}
