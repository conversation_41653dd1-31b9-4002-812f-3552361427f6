import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/text_widget.dart';
import '../models/extra_item_model.dart';
import '../providers/homeProvider.dart';

class AddExtra extends StatefulWidget {
  const AddExtra({super.key, this.onItemAdded});
  final Function(ExtraItem)? onItemAdded;

  @override
  AddExtraState createState() => AddExtraState();
}

class AddExtraState extends State<AddExtra> {
  TextEditingController itemNameController = TextEditingController();
  TextEditingController itemPriceController = TextEditingController();
  TextEditingController itemQuantityController = TextEditingController();

  FocusNode nameNode = FocusNode();
  FocusNode phoneNode = FocusNode();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  void submit() {
    final isValid = _formKey.currentState!.validate();
    if (isValid) {
      // Using HomeProvider
      Provider.of<HomeProvider>(context, listen: false).addExtraItem(
        ExtraItem(
          name: itemNameController.text.trim(),
          price: double.parse(itemPriceController.text.trim()),
          quantity: int.parse(itemQuantityController.text.trim()),
        ),
      );

      // Or return data to parent
      if (widget.onItemAdded != null) {
        widget.onItemAdded!(
          ExtraItem(
            name: itemNameController.text.trim(),
            price: double.parse(itemPriceController.text.trim()),
            quantity: int.parse(itemQuantityController.text.trim()),
          ),
        );
      }

      Navigator.of(context).pop();
    }
  }

  @override
  void initState() {
    super.initState();
    itemQuantityController.text = '1';
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;

    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {});
    }
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: Container(
        height:
            nameNode.hasFocus || phoneNode.hasFocus
                ? deviceWidth * 1.75
                : deviceWidth * 0.99,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.053),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  TextWidget(
                    title: 'User Details',
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop(null);
                    },
                    child: CircleAvatar(
                      radius: 13,
                      backgroundColor: Colors.grey.shade300,
                      child: Icon(Icons.clear, color: Colors.black54, size: 18),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Container(
                    margin: EdgeInsets.symmetric(
                      horizontal: deviceWidth * 0.05,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomContainer(
                          child: Column(
                            children: [
                              CustomTextFieldWithLabel(
                                label: 'Item Name',
                                controller: itemNameController,
                                hintText: 'Enter item name',
                                maxLines: null,
                                inputAction: TextInputAction.next,
                                inputType: TextInputType.text,
                                focusNode: nameNode,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Please enter item name';
                                  }
                                },
                              ),
                              SizedBox(height: deviceWidth * 0.05),
                              Row(
                                children: [
                                  Expanded(
                                    child: CustomTextFieldWithLabel(
                                      label: 'Item Price (per pc)',
                                      controller: itemPriceController,
                                      hintText: 'Enter item price',
                                      inputType: TextInputType.number,
                                      inputAction: TextInputAction.next,
                                      focusNode: phoneNode,
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return 'Please enter item price';
                                        }
                                      },
                                    ),
                                  ),
                                  SizedBox(width: deviceWidth * 0.05),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        TextWidget(
                                          title: 'Quantity',
                                          fontSize: 14,
                                        ),
                                        SizedBox(height: deviceWidth * 0.022),
                                        Row(
                                          children: [
                                            IconButton(
                                              icon: Icon(Icons.remove),
                                              onPressed: () {
                                                if (itemQuantityController
                                                        .text
                                                        .isNotEmpty &&
                                                    int.parse(
                                                          itemQuantityController
                                                              .text,
                                                        ) >
                                                        1) {
                                                  itemQuantityController.text =
                                                      (int.parse(
                                                                itemQuantityController
                                                                    .text,
                                                              ) -
                                                              1)
                                                          .toString();
                                                }
                                              },
                                            ),
                                            Expanded(
                                              child: CustomTextFieldWithLabel(
                                                controller:
                                                    itemQuantityController,
                                                hintText: '0',
                                                inputType: TextInputType.number,
                                                inputAction:
                                                    TextInputAction.done,

                                                validator: (value) {
                                                  if (value!.isEmpty) {
                                                    return 'Please enter quantity';
                                                  } else if (int.parse(value) <=
                                                      0) {
                                                    return 'Quantity must be greater than 0';
                                                  }
                                                },
                                                label: '',
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(Icons.add),
                                              onPressed: () {
                                                itemQuantityController.text =
                                                    (int.parse(
                                                              itemQuantityController
                                                                  .text,
                                                            ) +
                                                            1)
                                                        .toString();
                                              },
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: deviceWidth * 0.01),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                        CustomButton(
                          width: deviceWidth,
                          height: deviceWidth * 0.12,
                          buttonText: 'Next',
                          onPressed: submit,
                          radius: 7,
                        ),
                        SizedBox(height: deviceWidth * 0.05),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
