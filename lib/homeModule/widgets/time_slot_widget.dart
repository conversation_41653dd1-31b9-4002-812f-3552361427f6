import 'package:bys_business/venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';

import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';

class TimeSlotWidget extends StatelessWidget {
  final double dW;
  final List<Map<String, dynamic>> time;
  final Function checkSlotAvailability;
  final Function getPriceByTime;
  const TimeSlotWidget({
    required this.dW,
    required this.time,
    required this.checkSlotAvailability,
    required this.getPriceByTime,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.only(bottom: dW * .03),
      padding: EdgeInsets.all(dW * .03),
      decoration: BoxDecoration(
        color: checkSlotAvailability(time[0]['time']) &&
                checkSlotAvailability(time[1]['time'])
            ? const Color(0xFFD9D9D9).withOpacity(.9)
            : time[0]['isSelected'] && time[1]['isSelected']
                ? getThemeColor()
                : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: checkSlotAvailability(time[0]['time']) &&
                  checkSlotAvailability(time[1]['time'])
              ? const Color(0xFFD9D9D9).withOpacity(.9)
              : time[0]['isSelected'] && time[1]['isSelected']
                  ? getThemeColor()
                  : const Color(0xFFD9D9D9).withOpacity(.9),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(
            title:
                '${get12HrClockFormat(time[0]['time'])}${getTimePeriod(double.parse(time[0]['time'].split(':')[0]))} - ${get12HrClockFormat(time[1]['time']) == '00:00' ? '12:00' : get12HrClockFormat(time[1]['time'])}${getTimePeriod(double.parse(time[1]['time'].split(':')[0]))}',
            fontWeight: FontWeight.w600,
            color: checkSlotAvailability(time[0]['time']) &&
                    checkSlotAvailability(time[1]['time'])
                ? Colors.black.withOpacity(.4)
                : time[0]['isSelected'] && time[1]['isSelected']
                    ? Colors.white
                    : Colors.black.withOpacity(.8),
          ),
          if (getPriceByTime(time[0]['time']) > 0)
            TextWidget(
              title:
                  '\u20b9${getPriceByTime(time[0]['time']).toStringAsFixed(0)}/-',
              fontWeight: FontWeight.w600,
              color: checkSlotAvailability(time[0]['time']) &&
                      checkSlotAvailability(time[1]['time'])
                  ? Colors.black.withOpacity(.4)
                  : time[0]['isSelected'] && time[1]['isSelected']
                      ? Colors.white
                      : getThemeColor(),
            )
        ],
      ),
    );
  }
}

class SelectSportWidget extends StatelessWidget {
  final double dW;
  final int index;
  final SportType sport;
  final SportType? selectedSport;
  const SelectSportWidget({
    required this.dW,
    required this.index,
    required this.sport,
    required this.selectedSport,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
        color: selectedSport != null && sport.id == selectedSport!.id
            ? Theme.of(context).primaryColor
            : null,
        border: Border.all(
          color: selectedSport != null && sport.id == selectedSport!.id
              ? Theme.of(context).primaryColor
              : Colors.grey.shade400,
        ),
      ),
      constraints: BoxConstraints(minWidth: dW * 0.18),
      padding: EdgeInsets.symmetric(
        horizontal: dW * 0.023,
        vertical: dW * 0.018,
      ),
      margin: EdgeInsets.only(
        right: dW * 0.022,
        left: index == 0 ? dW * 0.03 : 0,
      ),
      child: Row(
        children: [
          Image.network(sport.image, height: 20),
          SizedBox(width: dW * 0.015),
          Text(
            sport.sport,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  fontWeight: FontWeight.w600,
                  color: selectedSport != null && sport.id == selectedSport!.id
                      ? Colors.white
                      : getThemeColor(),
                ),
          ),
        ],
      ),
    );
  }
}
