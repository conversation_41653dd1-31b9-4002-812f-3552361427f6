import 'package:flutter/material.dart';

import '../../fontSizes.dart';
import '../../venueModule/models/venue_model.dart';

class SelectSportDropdown extends StatefulWidget {
  final Venue turf;
  SelectSportDropdown({
    required this.turf,
  });

  @override
  _SelectSportDropdownState createState() => _SelectSportDropdownState();
}

class _SelectSportDropdownState extends State<SelectSportDropdown> {
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: width * 0.5,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Select Sport',
                  style: Theme.of(context)
                      .textTheme
                      .displaySmall!
                      .copyWith(fontSize: textScaleFactor * headlineMedium),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(null);
                  },
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey.shade300,
                    child: Icon(
                      Icons.clear,
                      color: Colors.black54,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: width * 0.05),
            Wrap(
              children: [
                ...widget.turf.sportsType!.map(
                  (sport) => GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop(sport);
                    },
                    child: Container(
                      margin: EdgeInsets.only(
                        right: width * 0.03,
                        bottom: width * 0.03,
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: width * 0.032,
                        horizontal: width * 0.04,
                      ),
                      decoration: BoxDecoration(
                        color: Color(0xffF3F3F4),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Text(
                        sport.sport,
                        style: Theme.of(context).textTheme.displaySmall!.copyWith(
                              fontSize: textScaleFactor * displayMedium,
                              color: Color(0xff6E7271),
                            ),
                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
