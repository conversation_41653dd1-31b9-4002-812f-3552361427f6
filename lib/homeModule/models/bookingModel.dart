import 'package:bys_business/homeModule/models/extra_item_model.dart';

import '../../venueModule/models/venue_model.dart';

class BookingModel {
  String id;
  String businessId;
  double totalAmount;
  double amountPaid;
  num onlinePaid;
  double remainingBalance;
  TurfUser? user;
  String paymentType;
  // String transactionId;
  String sizeOrSport;
  DateTime? bookingDate;
  DateTime? ogBookingDate;
  double bookingSlotStartTime;
  double bookingSlotEndTime;
  String sportType;
  double discountedAmount;
  String bookingStatus;
  String paymentStatus;
  List<RentalItem> rentedItem;
  String turfName;
  String turfId;
  String bookingMode;
  DateTime createdAt;
  SportCategory sportCategory;
  String sportImage;
  String label;
  String bookedBy;
  final String? employeeId;
  final EmployeeMapped? mappedEmployee;
  final double previousStartTime;
  final double previousEndTime;
  DateTime? previousBookingDate;
  DateTime? cancelledOn;
  String note;
  String option;
  double convenienceFee;
  double basicAmount;
  double gstOnConvenienceFee;
  double refundedAmount;
  final num tax;
  final String invoiceType;
  final String tab;
  // final num onlinePaidAmount;
  final String extendedPaymentStatus;
  final num extendedTotalAmount;
  final num extendedTime;
  final bool isExtended;
  List<ExtraItem>? extraItems = [];

  BookingModel({
    required this.id,
    required this.businessId,
    required this.totalAmount,
    required this.amountPaid,
    required this.onlinePaid,
    required this.remainingBalance,
    required this.bookingSlotStartTime,
    required this.bookingSlotEndTime,
    required this.paymentStatus,
    required this.user,
    required this.discountedAmount,
    required this.paymentType,
    // required this.transactionId,
    required this.sizeOrSport,
    required this.bookingDate,
    required this.ogBookingDate,
    required this.sportType,
    required this.bookingStatus,
    required this.rentedItem,
    required this.bookingMode,
    required this.turfId,
    required this.turfName,
    required this.sportCategory,
    required this.sportImage,
    required this.createdAt,
    required this.label,
    required this.bookedBy,
    this.employeeId,
    this.mappedEmployee,
    required this.convenienceFee,
    required this.gstOnConvenienceFee,
    required this.basicAmount,
    required this.previousStartTime,
    required this.previousEndTime,
    this.previousBookingDate,
    required this.cancelledOn,
    required this.note,
    required this.option,
    required this.refundedAmount,
    required this.tax,
    required this.invoiceType,
    this.tab = '',
    required this.extendedPaymentStatus,
    required this.extendedTotalAmount,
    required this.extendedTime,
    required this.isExtended,
    this.extraItems,
    // required this.onlinePaidAmount,
  });

  static BookingModel jsonToBooking(Map booking, {String selectedTab = ''}) {
    return BookingModel(
      id: booking['_id'] ?? '',
      paymentType: booking['paymentType'] ?? '',
      businessId:
          booking['business'] is Map ? booking['business']['_id'] ?? '' : '',
      amountPaid:
          booking['amountPaid'] != null
              ? booking['amountPaid'].toDouble()
              : 0.0,
      onlinePaid:
          booking['transactionId'] != null && booking['transactionId'] is Map
              ? booking['transactionId']['amount'] ?? 0
              : 0,
      note: booking['note'] ?? '',
      remainingBalance:
          booking['totalAmount'] != null && booking['amountPaid'] != null
              ? (booking['totalAmount'] - booking['amountPaid']).toDouble()
              : 0.0,
      extendedPaymentStatus: booking['extendedPaymentStatus'] ?? '',
      extendedTotalAmount:
          booking['extendedTotalAmount'] != null
              ? booking['extendedTotalAmount'].toDouble()
              : 0.0,
      extendedTime:
          booking['extendedTime'] != null
              ? booking['extendedTime'].toDouble()
              : 0.0,
      isExtended: booking['isExtended'] ?? false,
      totalAmount:
          booking['extendedPaymentStatus'] != null &&
                  booking['extendedPaymentStatus'] == 'SUCCESS'
              ? ((booking['totalAmount'] ?? 0) +
                      (booking['extendedTotalAmount'] ?? 0))
                  .toDouble()
              : booking['totalAmount'] != null
              ? booking['totalAmount'].toDouble()
              : 0.0,
      basicAmount:
          booking['basicAmount'] != null
              ? booking['basicAmount'].toDouble()
              : 0.0,
      refundedAmount:
          booking['refundedAmount'] != null
              ? booking['refundedAmount'].toDouble()
              : 0.0,
      convenienceFee:
          booking['convenienceFee'] != null
              ? booking['convenienceFee'].toDouble()
              : 0.0,
      gstOnConvenienceFee:
          booking['gstOnConvenienceFee'] != null
              ? booking['gstOnConvenienceFee'].toDouble()
              : 0.0,
      tax: booking['tax'] != null ? booking['tax'] : 0,
      invoiceType: booking['invoiceType'] ?? '',
      bookingSlotStartTime:
          booking['bookingSlotStartTime'] != null
              ? booking['bookingSlotStartTime'].toDouble()
              : 0.0,
      bookingSlotEndTime:
          booking['bookingSlotEndTime'] != null
              ? booking['bookingSlotEndTime'].toDouble()
              : 0.0,
      user:
          booking['user'] != null
              ? TurfUser.jsonToTurfUser(booking['user'])
              : null,
      sizeOrSport: booking['sizeOrSport'] ?? '',
      bookingDate:
          booking['bookingDate'] != null
              ? DateTime.parse(booking['bookingDate'])
              : null,
      ogBookingDate:
          booking['ogBookingDate'] != null
              ? DateTime.parse(booking['ogBookingDate']).toLocal()
              : null,
      cancelledOn:
          booking['cancelledOn'] != null
              ? DateTime.parse(booking['cancelledOn']).toLocal()
              : null,
      sportType:
          booking['sport'] != null && booking['sport']['sport'] != null
              ? booking['sport']['sport']
              : '',
      sportImage:
          booking['sport'] != null && booking['sport']['image'] != null
              ? booking['sport']['image']
              : '',
      sportCategory:
          booking['sportCategory'] != null
              ? SportCategory(
                categoryName: booking['sportCategory']['categoryName'] ?? '',
                id: booking['sportCategory']['_id'] ?? '',
              )
              : SportCategory(categoryName: '', id: ''),
      bookingStatus: booking['bookingStatus'] ?? '',
      paymentStatus: booking['paymentStatus'] ?? '',
      previousStartTime:
          booking['previousStartTime'] != null
              ? booking['previousStartTime'].toDouble()
              : 0.0,
      previousEndTime:
          booking['previousEndTime'] != null
              ? booking['previousEndTime'].toDouble()
              : 0.0,
      previousBookingDate:
          booking['previousBookingDate'] != null
              ? DateTime.parse(booking['previousBookingDate']).toLocal()
              : null,
      label: booking['label'] ?? '',
      option: booking['option'] ?? '',
      bookedBy: booking['bookedBy'] ?? '',
      employeeId: booking['employeeId'],
      mappedEmployee:
          booking['employeeMappingId'] != null
              ? EmployeeMapped.jsonToEmployeeMapped(
                booking['employeeMappingId'],
              )
              : null,
      rentedItem:
          booking['rentedItem'] != null
              ? (booking['rentedItem'] as List)
                  .map<RentalItem>(
                    (rental) => RentalItem.jsonToRentalItem(rental),
                  )
                  .toList()
              : [],
      bookingMode: booking['bookingMode'] ?? '',
      turfId:
          booking['turfId'] != null && booking['turfId']['_id'] != null
              ? booking['turfId']['_id']
              : '',
      turfName:
          booking['turfId'] != null && booking['turfId']['name'] != null
              ? booking['turfId']['name']
              : '',
      discountedAmount:
          booking['discountedAmount'] != null
              ? booking['discountedAmount'].toDouble()
              : 0.0,
      createdAt:
          booking['createdAt'] != null
              ? DateTime.parse(booking['createdAt']).toLocal()
              : DateTime.now(),
      tab: selectedTab,
      extraItems:
          booking['extraItems'] != null
              ? (booking['extraItems'] as List)
                  .map<ExtraItem>((item) => ExtraItem.fromJson(item))
                  .toList()
              : [],
    );
  }
}

class EmployeeMapped {
  final String id;
  final String name;

  EmployeeMapped({required this.id, required this.name});

  static EmployeeMapped jsonToEmployeeMapped(Map employee) {
    return EmployeeMapped(
      id: employee['_id'] ?? '',
      name:
          ('${employee['firstName'] ?? ''} ${employee['lastName'] ?? ''}')
              .trim(),
    );
  }
}

class BookingGroup {
  String turfName;
  String turfId;
  List<BookingModel> bookings;
  BookingGroup({
    required this.bookings,
    required this.turfId,
    required this.turfName,
  });
}

class TurfUser {
  String name;
  String avatar;
  String mobileNo;

  TurfUser({required this.mobileNo, required this.name, required this.avatar});

  static TurfUser jsonToTurfUser(Map user) {
    return TurfUser(
      mobileNo: user['phone'] ?? '',
      name: ('${user['firstName'] ?? ''} ${user['lastName'] ?? ''}').trim(),
      avatar: user['avatar'] ?? '',
    );
  }
}

class RentalItem {
  String id;
  // String productId;
  String productName;
  String productImage;
  double price;
  int duration;
  int quantity;
  bool isSelected;

  RentalItem({
    required this.id,
    required this.price,
    required this.duration,
    // required this.productId,
    required this.productImage,
    required this.productName,
    required this.quantity,
    this.isSelected = false,
  });

  static RentalItem jsonToRentalItem(Map rental) {
    return RentalItem(
      id: rental['_id'] ?? '',
      price: rental['price'].toDouble(),
      duration: rental['duration'] ?? '',
      productImage: rental['productImage'] ?? '',
      productName: rental['productName'] ?? '',
      quantity: rental['quantity'] ?? 0,
    );
  }
}

extension BookingModelCopy on BookingModel {
  BookingModel copyWith({
    String? id,
    String? businessId,
    double? totalAmount,
    double? amountPaid,
    num? onlinePaid,
    double? remainingBalance,
    TurfUser? user,
    String? paymentType,
    String? sizeOrSport,
    DateTime? bookingDate,
    DateTime? ogBookingDate,
    double? bookingSlotStartTime,
    double? bookingSlotEndTime,
    String? sportType,
    double? discountedAmount,
    String? bookingStatus,
    String? paymentStatus,
    List<RentalItem>? rentedItem,
    String? turfName,
    String? turfId,
    String? bookingMode,
    DateTime? createdAt,
    SportCategory? sportCategory,
    String? sportImage,
    String? label,
    String? bookedBy,
    String? employeeId,
    EmployeeMapped? mappedEmployee,
    double? previousStartTime,
    double? previousEndTime,
    DateTime? previousBookingDate,
    DateTime? cancelledOn,
    String? note,
    String? option,
    double? convenienceFee,
    double? basicAmount,
    double? gstOnConvenienceFee,
    double? refundedAmount,
    num? tax,
    String? invoiceType,
    String? tab,
    String? extendedPaymentStatus,
    num? extendedTotalAmount,
    num? extendedTime,
    bool? isExtended,
    List<ExtraItem>? extraItems,
  }) {
    return BookingModel(
      id: id ?? this.id,
      businessId: businessId ?? this.businessId,
      totalAmount: totalAmount ?? this.totalAmount,
      amountPaid: amountPaid ?? this.amountPaid,
      onlinePaid: onlinePaid ?? this.onlinePaid,
      remainingBalance: remainingBalance ?? this.remainingBalance,
      user: user ?? this.user,
      paymentType: paymentType ?? this.paymentType,
      sizeOrSport: sizeOrSport ?? this.sizeOrSport,
      bookingDate: bookingDate ?? this.bookingDate,
      ogBookingDate: ogBookingDate ?? this.ogBookingDate,
      bookingSlotStartTime: bookingSlotStartTime ?? this.bookingSlotStartTime,
      bookingSlotEndTime: bookingSlotEndTime ?? this.bookingSlotEndTime,
      sportType: sportType ?? this.sportType,
      discountedAmount: discountedAmount ?? this.discountedAmount,
      bookingStatus: bookingStatus ?? this.bookingStatus,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      rentedItem: rentedItem ?? this.rentedItem,
      turfName: turfName ?? this.turfName,
      turfId: turfId ?? this.turfId,
      bookingMode: bookingMode ?? this.bookingMode,
      createdAt: createdAt ?? this.createdAt,
      sportCategory: sportCategory ?? this.sportCategory,
      sportImage: sportImage ?? this.sportImage,
      label: label ?? this.label,
      bookedBy: bookedBy ?? this.bookedBy,
      employeeId: employeeId ?? this.employeeId,
      mappedEmployee: mappedEmployee ?? this.mappedEmployee,
      previousStartTime: previousStartTime ?? this.previousStartTime,
      previousEndTime: previousEndTime ?? this.previousEndTime,
      previousBookingDate: previousBookingDate ?? this.previousBookingDate,
      cancelledOn: cancelledOn ?? this.cancelledOn,
      note: note ?? this.note,
      option: option ?? this.option,
      convenienceFee: convenienceFee ?? this.convenienceFee,
      basicAmount: basicAmount ?? this.basicAmount,
      gstOnConvenienceFee: gstOnConvenienceFee ?? this.gstOnConvenienceFee,
      refundedAmount: refundedAmount ?? this.refundedAmount,
      tax: tax ?? this.tax,
      invoiceType: invoiceType ?? this.invoiceType,
      tab: tab ?? this.tab,
      extendedPaymentStatus:
          extendedPaymentStatus ?? this.extendedPaymentStatus,
      extendedTotalAmount: extendedTotalAmount ?? this.extendedTotalAmount,
      extendedTime: extendedTime ?? this.extendedTime,
      isExtended: isExtended ?? this.isExtended,
      extraItems: extraItems ?? this.extraItems,
    );
  }
}
