class ExtraItem {
  final String? id;
  final String name;
  final double price;
  int quantity;

  ExtraItem({
    required this.name,
    required this.price,
    this.quantity = 1,
    this.id,
  });

  Map<String, dynamic> toJson() {
    return {'name': name, 'price': price, 'quantity': quantity};
  }

  // Create ExtraItem from JSON with proper type conversion
  static ExtraItem fromJson(Map<String, dynamic> json) {
    return ExtraItem(
      id: json['id'] ?? json['_id']?.toString(),
      name: json['name'] ?? '',
      price: _parseDouble(json['price']), // Safe conversion
      quantity: _parseInt(json['quantity']), // Safe conversion
    );
  }

  // Helper method to safely convert to double
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper method to safely convert to int
  static int _parseInt(dynamic value) {
    if (value == null) return 1;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 1;
    return 1;
  }

  // Copy with method for easier updates
  ExtraItem copyWith({String? id, String? name, double? price, int? quantity}) {
    return ExtraItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  String toString() {
    return 'ExtraItem(id: $id, name: $name, price: $price, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExtraItem &&
        other.id == id &&
        other.name == name &&
        other.price == price &&
        other.quantity == quantity;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ price.hashCode ^ quantity.hashCode;
  }
}
