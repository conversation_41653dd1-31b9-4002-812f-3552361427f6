import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

const Color lightPrimaryColor = Color.fromARGB(255, 253, 236, 231);
const Color violetPrimaryColor = Color(0xFF677AF3);
const Color greenPrimary = Color(0xFF1DB854);
const Color greenSecondary = Color(0xFF007C53);
const Color greyTextColor = Color(0xFF3E3E3E);
const Color greyTextColor2 = Color(0xFF636363);
const Color greyTextColor3 = Color(0xFF6B6C75);
const Color greyTextColor4 = Color(0xFF9798A3);
const Color greyTextColor5 = Color(0xFF7E7E7E);
const Color greyTextColor6 = Color(0xFF838383);
const Color placeholderColor = Color(0xFFAAABB5);
const Color blackColor1 = Color(0xFF515259);
const Color blackColor2 = Color(0xFF37383F);
const Color blackColor3 = Color(0xFF3E3E3E);
const Color blackColor4 = Color(0xFF363636);
const Color blackColor5 = Color(0xFF636363);
const Color yellowColor1 = Color(0xFFFFC738);
Color disabledColor = getThemeColor().withOpacity(0.15);
const Color whiteColor = Colors.white;
const Color creditColor = Color(0xFF10B981);
const Color redColor = Color(0xFFEF4444);
const Color primaryFadeBorder = Color(0xFFFFF2E0);
const Color headerColor = Color(0xFF709489);
const Color backButtonColor = Color(0xff114C3A);
const Color dividerColor = Color(0xffD4D4D4);
const Color hintColor = Color(0xFF84858E);
const Color underlineBorderColor = Color(0x43434359);
