import 'package:bys_business/homeModule/models/bookingModel.dart';
import 'package:bys_business/http_helper.dart';
import 'package:flutter/cupertino.dart';

import '../../moreModule.dart/model/barChartModel.dart';
import '../../api.dart';

class MoreProvider with ChangeNotifier {
  List<PieData> _listOfSports = [];

  List<BarChartModel> _listOfBooking = [];

  List<PieData> get listOfSports {
    return [..._listOfSports];
  }

  List<BarChartModel> get listOfBooking {
    return [..._listOfBooking];
  }

  List<BookingModel> _bookings = [];

  List<BookingModel> get bookings {
    return _bookings;
  }

  emptyBooking() {
    _bookings = [];
    _listOfBooking = [];
  }

  fetchAnalyticsV2({
    required String accessToken,
    required String business,
    required String title,
  }) async {
    try {
      String duration = 'Weekly';
      if (title == 'All time') {
        duration = 'All time';
      } else if (title == 'Month') {
        duration = 'Monthly';
      } else {
        duration = 'Weekly';
      }
      final url = '${webApi['domain']}${endPoint['fetchAnalyticsV2']}';

      var response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: {"business": business, "duration": duration},
        accessToken: accessToken,
      );
      if (response['success']) {
        List<BarChartModel> loadedChart = [];
        response['result'].forEach((data) {
          loadedChart.add(
            BarChartModel(
              title: data['day'],
              value: data['count'],
              color: Color(0xff4E7D96),
            ),
          );
        });
        List<PieData> loadedSportChart = [];
        response['sport'].forEach((data) {
          loadedSportChart.add(
            PieData(
              xData: data['name'],
              yData: data['sportBooking'],
              text: '',
            ),
          );
        });
        _listOfSports = List.from(loadedSportChart);
        _listOfBooking = List.from(loadedChart);
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  getSkipCount(String selectedTab) {
    return _bookings.where((booking) => booking.tab == selectedTab).length;
  }

  List<BookingModel> getBookingByTab(String selectedTab) {

    switch (selectedTab) {
      case 'Upcoming':
        return _bookings.where((booking) => booking.tab == selectedTab && booking.bookingStatus != 'CANCELLED').toList();
      case 'Completed':
        return _bookings.where((booking) => booking.tab == selectedTab && booking.bookingStatus != 'CANCELLED').toList();
      case 'Cancelled':
        return _bookings.where((booking) => booking.tab == selectedTab && booking.bookingStatus == 'CANCELLED').toList();
     default:
    return _bookings.where((booking) => booking.tab == selectedTab).toList();
  }
  }

  updateCancelStatus(String bookingId, Map bookingData) {
    int index = _bookings.indexWhere((booking) => booking.id == bookingId);
    if (index != -1) {
      _bookings[index].bookingStatus = 'CANCELLED';
      _bookings[index].paymentStatus = bookingData['paymentStatus'];
      _bookings[index].refundedAmount = bookingData['refundedAmount'] == null
          ? 0
          : bookingData['refundedAmount'].toDouble();
      notifyListeners();
    }
  }

  fetchBusinessBookingV2({
    required String accessToken,
    required String selectedTab,
    required String businessId,
    required String role,
    List turfId = const [],
    bool refresh = false,
  }) async {
    try {
      if (refresh) {
        _bookings.removeWhere((booking) => booking.tab == selectedTab);
      }

      final url = '${webApi['domain']}${endPoint['fetchBusinessBookingV2']}';

      var body = {
        "businessId": businessId,
        "role": role,
        "status": selectedTab,
        "limit": 20,
        "skip": getSkipCount(selectedTab),
        "turfs": turfId,
      };

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<BookingModel> loadedBooking = [];

        response['result'].forEach(
          (booking) => loadedBooking.add(
              BookingModel.jsonToBooking(booking, selectedTab: selectedTab)),
        );

        if (loadedBooking.isNotEmpty) _bookings.addAll(loadedBooking);

        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }
}
