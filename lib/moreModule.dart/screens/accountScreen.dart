import 'dart:io';

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import 'package:provider/provider.dart';

import '../../common_function.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';

class AccountScreen extends StatefulWidget {
  final UserModal user;
  AccountScreen({Key? key, required this.user}) : super(key: key);

  @override
  AccountScreenState createState() => AccountScreenState();
}

class AccountScreenState extends State<AccountScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  //Controllers
  TextEditingController firstNameController = new TextEditingController();
  TextEditingController lastNameController = new TextEditingController();
  TextEditingController phoneController = new TextEditingController();
  TextEditingController emailNameController = new TextEditingController();
  TextEditingController addressController = new TextEditingController();

  final _formKey = GlobalKey<FormState>();
  bool isLoading = false;

  ImagePicker _picker = new ImagePicker();
  var pickedImage = '';
  var avatar;

  formSubmit() async {
    try {
      if (isLoading) return;
      hideKeyBoard(context);
      setState(() {
        isLoading = true;
      });

      var updated;
      if (widget.user.role == 'Employee') {
        updated =
            await Provider.of<Auth>(context, listen: false).updateEmployee(
          firstName: firstNameController.text.trim(),
          lastName: lastNameController.text.trim(),
          email: emailNameController.text.trim(),
          accessToken: widget.user.accessToken,
          avatar:
              avatar != null && avatar != '' ? avatar.path : widget.user.avatar,
          address: addressController.text.trim(),
        );
      } else {
        updated = await Provider.of<Auth>(context, listen: false).updateProfile(
          firstName: firstNameController.text.trim(),
          lastName: lastNameController.text.trim(),
          email: emailNameController.text.trim(),
          accessToken: widget.user.accessToken,
          avatar:
              avatar != null && avatar != '' ? avatar.path : widget.user.avatar,
        );
      }

      if (updated) {
        showSnackbar('Profile updated successfully', color: getThemeColor());
      } else {
        showSnackbar("Unable to save profile");
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      showSnackbar("Something went wrong");
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  imgFromCamera() async {
    XFile? image = await _picker.pickImage(
      source: ImageSource.camera,
    );

    setState(() {
      if (image != null) {
        avatar = File(image.path);
        Navigator.of(context).pop();
      }
    });
  }

  imgFromGallery() async {
    XFile? image = await _picker.pickImage(
      source: ImageSource.gallery,
    );

    setState(() {
      if (image != null) {
        avatar = File(image.path);
        print(avatar);
        Navigator.of(context).pop();
      }
    });
  }

  void showBottomModel() {
    showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: dW * 0.08),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(bottom: 20, top: 20),
                      child: Text(
                        "Profile Photo",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color.fromRGBO(41, 49, 49, 1),
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 25),
                  child: Row(
                    children: [
                      if (widget.user.avatar != '')
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              widget.user.avatar = '';
                              avatar = '';
                            });
                            Navigator.of(context).pop();
                          },
                          child: BottomSheetContent(
                            svgColor: Colors.red,
                            // svgColor: Theme.of(context).errorColor,
                            icon: Icons.delete,
                            // svgImage: "assets/svgIcons/delete.svg",
                            title: "Remove",
                            title2: " Photo",
                          ),
                        ),
                      GestureDetector(
                        onTap: imgFromGallery,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: widget.user.avatar != '' ? dW * 0.06 : 0),
                          child: BottomSheetContent(
                            svgColor: Color(0xffAF1196),
                            icon: Icons.photo,
                            title: "Gallery",
                            title2: "",
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: imgFromCamera,
                        child: Padding(
                          padding: EdgeInsets.only(left: dW * 0.06),
                          child: BottomSheetContent(
                            svgColor: Color(0xff0152BA),
                            icon: Icons.camera,
                            title: "Camera",
                            title2: "",
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  bool isButtonEnable() {
    if (firstNameController.text.trim().isEmpty ||
        lastNameController.text.trim().isEmpty ||
        emailNameController.text.trim().isEmpty ||
        !EmailValidator.validate(emailNameController.text) ||
        (widget.user.role == 'Employee' &&
            addressController.text.trim().isEmpty)) {
      return false;
    } else {
      return true;
    }
  }

  @override
  void initState() {
    super.initState();
    phoneController.text = widget.user.phone;
    firstNameController.text = widget.user.firstName;
    lastNameController.text = widget.user.lastName;
    emailNameController.text = widget.user.email;
    addressController.text = widget.user.address!;
    // genderController.text = widget.user.gender;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Padding(
                // SizedBox(height: dW * 0.05),
                padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
              // SizedBox(height: dW * 0.05),
              NewAppBar(dW: dW, title: 'Profile'),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: dW * 0.05),
                      GestureDetector(
                        onTap: showBottomModel,
                        child: Container(
                          padding: EdgeInsets.only(bottom: dW * 0.04),
                          width: dW * 0.28,
                          height: dW * 0.28,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.transparent,
                          ),
                          child: avatar == null || avatar == ''
                              ? (widget.user.avatar == ''
                                  ? SvgPicture.asset('assets/svgIcons/user.svg')
                                  : CircleAvatar(
                                      backgroundColor: Colors.grey[200],
                                      backgroundImage: NetworkImage(
                                        widget.user.avatar.toString(),
                                      ),
                                    ))
                              : CircleAvatar(
                                  backgroundColor: Colors.grey[200],
                                  backgroundImage: FileImage(avatar),
                                ),
                        ),
                      ),
                      GestureDetector(
                        onTap: showBottomModel,
                        child: Container(
                          color: Colors.transparent,
                          padding: EdgeInsets.symmetric(vertical: dW * 0.02),
                          child: TextWidget(
                            title: 'Change Profile Photo',
                            fontWeight: FontWeight.w600,
                            color: getThemeColor(),
                            letterSpacing: .4,
                          ),
                        ),
                      ),
                      SizedBox(height: dW * 0.05),
                      CustomContainer(
                        vPadding: 0.055,
                        hPadding: 0.045,
                        child: Column(
                          children: [
                            CustomTextFieldWithLabel(
                              label: 'Phone',
                              controller: phoneController,
                              hintText: 'Enter your phone number',
                              enabled: false,
                              optional: true,
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'First Name',
                              controller: firstNameController,
                              hintText: 'Enter your first name',
                              textCapitalization: TextCapitalization.words,
                              inputAction: TextInputAction.next,
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                              onChanged: (value) => setState(() {}),
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Last Name',
                              controller: lastNameController,
                              hintText: 'Enter your last name',
                              textCapitalization: TextCapitalization.words,
                              inputAction: TextInputAction.next,
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                              onChanged: (value) => setState(() {}),
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Email ID',
                              controller: emailNameController,
                              hintText: 'Enter your email ID',
                              inputAction: TextInputAction.done,
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                              onChanged: (value) => setState(() {}),
                            ),
                            if (widget.user.role == 'Employee') ...[
                              SizedBox(height: dW * 0.05),
                              CustomTextFieldWithLabel(
                                label: 'Address',
                                controller: addressController,
                                hintText: 'Enter your address',
                                inputAction: TextInputAction.done,
                                inputType: TextInputType.streetAddress,
                                suffixIcon: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [AssetSvgIcon(iconName: 'edit2')],
                                ),
                                onChanged: (value) => setState(() {}),
                              ),
                            ],
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.15),
                    ],
                  ),
                ),
              ),
              BottomAlignedWidget(
                dW: dW,
                dH: dW * 0.13,
                child: CustomButton(
                  width: dW,
                  height: dW * 0.13,
                  radius: 8,
                  buttonText: 'Save Changes',
                  onPressed: isButtonEnable() ? formSubmit : null,
                  isLoading: isLoading,
                  fontSize: 16,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class BottomSheetContent extends StatelessWidget {
  final String title;
  final String title2;
  // final String svgImage;
  final IconData icon;
  final Color svgColor;

  const BottomSheetContent({
    required this.svgColor,
    // required this.svgImage,
    required this.icon,
    required this.title,
    required this.title2,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Color.fromRGBO(233, 233, 233, 1),
          radius: 30,
          child: Container(
            height: 30,
            width: 30,
            child: Icon(
              icon,
              color: svgColor,
            ),
          ),
        ),
        Column(
          children: [
            Text(
              title,
              style: TextStyle(
                color: Color.fromRGBO(121, 120, 128, 1),
              ),
            ),
            Text(
              title2,
              style: TextStyle(
                color: Color.fromRGBO(121, 120, 128, 1),
              ),
            ),
          ],
        )
      ],
    );
  }
}
