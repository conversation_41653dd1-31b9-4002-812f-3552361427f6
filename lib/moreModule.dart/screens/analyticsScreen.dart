import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../fontSizes.dart';
import '../../moreModule.dart/model/barChartModel.dart';
import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../moreModule.dart/provider/moreProvider.dart';

class AnalyticsScreen extends StatefulWidget {
  final UserModal user;
  const AnalyticsScreen({Key? key, required this.user}) : super(key: key);

  @override
  _AnalyticsScreenState createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  List<BarChartModel> totalBookingData = [];
  List<PieData> individualTotalBookingOfSport = [];
  bool isLoading = false;

  List analytics = [
    {
      'title': 'All time',
      'isSelected': false,
      'index': 1,
    },
    {
      'title': 'Week',
      'isSelected': true,
      'index': 2,
    },
    {
      'title': 'Month',
      'isSelected': false,
      'index': 3,
    },
  ];

  selectAnalytic(int index) {
    analytics.forEach((analytic) {
      if (analytic['index'] == index) {
        setState(() {
          analytic['isSelected'] = true;
        });
        fetchSportBookingAnalytics(duration: analytic['title']);
      } else {
        setState(() {
          analytic['isSelected'] = false;
        });
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchSportBookingAnalytics();
  }

  fetchSportBookingAnalytics({String duration = 'Week'}) async {
    try {
      setState(() {
        isLoading = true;
      });
      await Provider.of<MoreProvider>(context, listen: false).fetchAnalyticsV2(
        accessToken: widget.user.accessToken,
        business: widget.user.businessId,
        title: duration,
      );

      individualTotalBookingOfSport =
          Provider.of<MoreProvider>(context, listen: false).listOfSports;
      totalBookingData =
          Provider.of<MoreProvider>(context, listen: false).listOfBooking;
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title: 'Analytics',
      deviceWidth: deviceWidth,
      elevation: 1.0,
    );

    return Scaffold(
      appBar: androidAppBar,
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: deviceWidth * 0.07),
              Container(
                padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ...analytics.map(
                      (analytic) => GestureDetector(
                        onTap: () => selectAnalytic(analytic['index']),
                        child: Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                            vertical: deviceWidth * 0.03,
                            horizontal: deviceWidth * 0.06,
                          ),
                          constraints: BoxConstraints(
                            minWidth: deviceWidth * 0.27,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: analytic['isSelected']
                                ? Theme.of(context).primaryColor
                                : Color(0xffEDEDED),
                          ),
                          child: Text(
                            analytic['title'],
                            style:
                                Theme.of(context).textTheme.displayLarge!.copyWith(
                                      fontSize: textScaleFactor * displayLarge,
                                      color: analytic['isSelected']
                                          ? Colors.white
                                          : Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(height: deviceWidth * 0.07),
              isLoading
                  ? Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(top: deviceWidth * 0.45),
                      child: MaterialCircularLoader(deviceWidth * 0.07),
                    )
                  // : totalBookingData.length == 0
                  //     ? Container(
                  //         alignment: Alignment.center,
                  //         margin: EdgeInsets.only(top: deviceWidth * 0.45),
                  //         child: Text(
                  //           'Analytic not found!',
                  //           style:
                  //               Theme.of(context).textTheme.displayLarge!.copyWith(
                  //                     fontSize: textScaleFactor * displayLarge,
                  //                     color: Colors.black,
                  //                     fontWeight: FontWeight.bold,
                  //                   ),
                  //         ),
                  //       )
                  : Expanded(
                      child: SingleChildScrollView(
                        physics: BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(
                            horizontal: deviceWidth * 0.05),
                        child: Column(
                          children: [
                            buildBarChart(
                              deviceWidth: deviceWidth,
                              textScaleFactor: textScaleFactor,
                              context: context,
                              title: 'Total Booking',
                              data: totalBookingData,
                              barWidth: 0.6,
                              interval: 5,
                            ),
                            // buildBarChart(
                            //   deviceWidth: deviceWidth,
                            //   textScaleFactor: textScaleFactor,
                            //   context: context,
                            //   title: 'Booked Count',
                            //   data: individualTotalBookingOfSport,
                            //   barWidth: 0.45,
                            //   interval: 1.0,
                            // ),
                            SizedBox(height: deviceWidth * 0.05),
                          ],
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget buildBarChart({
  required double deviceWidth,
  required double textScaleFactor,
  required BuildContext context,
  required String title,
  required List<BarChartModel> data,
  required double barWidth,
  required double interval,
}) {
  return Container(
    padding: EdgeInsets.symmetric(
      horizontal: deviceWidth * 0.02,
      vertical: deviceWidth * 0.02,
    ),
    margin: EdgeInsets.only(bottom: deviceWidth * 0.065),
    decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            blurRadius: 6,
            spreadRadius: 2,
            offset: Offset(0, 5),
            color: Color(0XFF30303033).withOpacity(0.06),
          ),
          BoxShadow(
            blurRadius: 6,
            spreadRadius: 2,
            offset: Offset(-5, 2),
            color: Color(0XFF30303033).withOpacity(0.06),
          ),
          BoxShadow(
            blurRadius: 6,
            spreadRadius: 2,
            offset: Offset(5, 2),
            color: Color(0XFF30303033).withOpacity(0.06),
          )
        ]),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          margin: EdgeInsets.only(
            bottom: deviceWidth * 0.05,
            left: deviceWidth * 0.02,
            top: deviceWidth * 0.03,
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * displayMedium,
                ),
          ),
        ),
        SfCartesianChart(
          selectionGesture: ActivationMode.singleTap,
          primaryXAxis: CategoryAxis(
            labelStyle: Theme.of(context).textTheme.displayLarge!.copyWith(
                  fontSize: textScaleFactor * displayLarge,
                  color: Colors.black,
                ),
            isVisible: true,
            maximumLabelWidth: deviceWidth * 0.16,
            majorGridLines:
                MajorGridLines(width: 0.5, color: Colors.transparent),
            axisLine: AxisLine(width: 0.5, color: Colors.transparent),
          ),
          primaryYAxis: NumericAxis(
            majorGridLines: MajorGridLines(width: 0.5),
            axisLine: AxisLine(width: 0.5),
            labelStyle: Theme.of(context).textTheme.displayLarge!.copyWith(
                  fontSize: textScaleFactor * displayLarge,
                  color: Colors.black,
                ),
            interval: interval,
          ),
          series: <CartesianSeries>[
            ColumnSeries<BarChartModel, String>(
              dataSource: data,
              xValueMapper: (BarChartModel views, _) => views.title.toString(),
              yValueMapper: (BarChartModel views, _) => views.value.toInt(),
              pointColorMapper: (BarChartModel views, _) => views.color,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(2),
                topRight: Radius.circular(2),
              ),
              width: barWidth,
              selectionBehavior:
                  SelectionBehavior(selectedColor: getThemeColor()),
            )
          ],
        ),
      ],
    ),
  );
}
