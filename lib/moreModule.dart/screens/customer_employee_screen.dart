// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/navigators.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../commonWidgets/circular_loader.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../customerModule/screens/customer_screen.dart';
import '../../employeeManagement/screens/all_employee_screen.dart';
import '../../new_colors.dart';

class CustomersAndEmployeesScreen extends StatelessWidget {
  CustomersAndEmployeesScreen({Key? key}) : super(key: key);

  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  TextTheme customTextTheme = const TextTheme();

  Map language = {};

  navigateFn(String type, BuildContext context) {
    UserModal user = Provider.of<Auth>(context, listen: false).user;
    if (type == 'Customers') {
      push(CustomerScreen(user: user));
    } else {
      push(AllEmployeeScreen(user: user));
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<Auth>(context).selectedLanguage;

    customTextTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: iOSCondition(dH)
          ? screenBody(context)
          : SafeArea(child: screenBody(context)),
    );
  }

  screenBody(BuildContext context) {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
           
            NewAppBar(dW: dW, title: 'Customer & Employee'),
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: dW * 0.07),
                    ...['Customers', 'Employees'].map(
                      (type) => GestureDetector(
                        onTap: () => navigateFn(type, context),
                        child: CustomContainer(
                          margin: EdgeInsets.only(bottom: dW * 0.06),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  AssetSvgIcon(
                                    iconName: type == 'Customers'
                                        ? 'customer_rounded'
                                        : 'employee_rounded',
                                  ),
                                  SizedBox(width: dW * 0.04),
                                  TextWidget(
                                    title: type,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 16,
                                    color: getGreyColor2(context),
                                  )
                                ],
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                color: Theme.of(context).primaryColor,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
