import 'dart:convert';
import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/radio_widget.dart';
import '../../common_function.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';

class BusinessProfileScreen extends StatefulWidget {
  BusinessProfileScreen({Key? key}) : super(key: key);

  @override
  BusinessProfileScreenState createState() => BusinessProfileScreenState();
}

class BusinessProfileScreenState extends State<BusinessProfileScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  late UserModal user;
  bool isLoading = false;

  //Controllers
  TextEditingController gstController = new TextEditingController();
  TextEditingController bankNameController = new TextEditingController();
  TextEditingController accountNoController = new TextEditingController();
  TextEditingController ifscController = new TextEditingController();
  TextEditingController upiController = new TextEditingController();

  String gstType = '';

  ImagePicker _picker = new ImagePicker();
  var pickedImage = '';

  formSubmit() async {
    try {
      if (isLoading) return;
      hideKeyBoard(context);
      setState(() {
        isLoading = true;
      });

      Map<String, String> body = {
        'businessId': user.businessId,
        'aadharImage': pickedImage,
        'gstDetails': json.encode({
          'gstType': gstType,
          'gstNo': gstController.text.trim(),
          'gstRegistered': true,
        }),
        'bankDetails': json.encode({
          'bankName': bankNameController.text.trim(),
          'accountNumber': accountNoController.text.trim(),
          'ifscCode': ifscController.text.trim(),
          'upiId': upiController.text.trim(),
        }),
      };
      Map<String, String> files = {};

      bool response = await Provider.of<Auth>(
        context,
        listen: false,
      ).updateBusinesProfile(
        accessToken: user.accessToken,
        body: body,
        files: files,
      );

      if (response) {
        showSnackbar('Profile updated successfully', color: getThemeColor());
      } else {
        showSnackbar("Unable to save profile");
      }
    } catch (e) {
      print(e);
      showSnackbar("Something went wrong");
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  imgFromCamera() async {
    XFile? image = await _picker.pickImage(source: ImageSource.camera);

    setState(() {
      if (image != null) {
        pickedImage = image.path;
        Navigator.of(context).pop();
      }
    });
  }

  imgFromGallery() async {
    XFile? image = await _picker.pickImage(source: ImageSource.gallery);

    setState(() {
      if (image != null) {
        pickedImage = image.path;
        Navigator.of(context).pop();
      }
    });
  }

  void showImageOption() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(left: dW * 0.05),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 20, top: 20),
                    child: Text(
                      "Profile Photo",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Color.fromRGBO(41, 49, 49, 1),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(bottom: 25),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: imgFromGallery,
                      child: BottomSheetContent(
                        svgColor: Color(0xffAF1196),
                        icon: Icons.photo,
                        title: "Gallery",
                        title2: "",
                      ),
                    ),
                    GestureDetector(
                      onTap: imgFromCamera,
                      child: Padding(
                        padding: EdgeInsets.only(left: dW * 0.06),
                        child: BottomSheetContent(
                          svgColor: Color(0xff0152BA),
                          icon: Icons.camera,
                          title: "Camera",
                          title2: "",
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  bool isButtonEnable() {
    if (bankNameController.text.trim().isEmpty ||
        gstController.text.trim().isEmpty ||
        gstType == '' ||
        accountNoController.text.trim().isEmpty ||
        ifscController.text.trim().isEmpty ||
        ifscController.text.trim().length != 11 ||
        upiController.text.trim().isEmpty) {
      return false;
    } else {
      return true;
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    if (user.gstDetails != null) {
      gstType = user.gstDetails!.gstType;
      gstController.text = user.gstDetails!.gstNo;
    }

    if (user.bankDetails != null) {
      bankNameController.text = user.bankDetails!.bankName;
      accountNoController.text = user.bankDetails!.accountNumber;
      ifscController.text = user.bankDetails!.ifscCode;
      upiController.text = user.bankDetails!.upiId;
    }

    pickedImage = user.aadharImage;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top,
            bottom: dH * 0.02,
          ),
          child: Column(
            children: [
              // SizedBox(height: dW * 0.05),
              NewAppBar(dW: dW, title: 'Business Profile'),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: dW * 0.05),
                      // GST Options
                      CustomContainer(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              title: 'GST Details',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            SizedBox(height: dW * 0.035),
                            Row(
                              children: [
                                TextWidget(
                                  title: 'GST Type',
                                  color: Color(0xffACACB4),
                                ),
                                TextWidget(title: '*', color: redColor),
                              ],
                            ),
                            SizedBox(height: dW * 0.025),
                            Row(
                              children: [
                                ...['Regular', 'Composite'].map(
                                  (value) => GestureDetector(
                                    onTap: () {
                                      gstType = value == gstType ? '' : value;
                                      setState(() {});
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(right: dW * 0.05),
                                      child: RadioWidget(
                                        active: gstType == value,
                                        textFontWeight: FontWeight.normal,
                                        activeColor:
                                            Theme.of(context).primaryColor,
                                        inActiveBorderColor:
                                            Theme.of(context).primaryColor,
                                        title: value,
                                        radius: 6,
                                        inactiveBorderRadius: 6,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: dW * 0.055),
                            CustomTextFieldWithLabel(
                              label: 'Business GST No',
                              controller: gstController,
                              hintText: 'Enter Gst  No. Here...',
                              inputAction: TextInputAction.done,
                              labelColor: Color(0xffACACB4),
                              labelFW: FontWeight.w500,
                              hintColor: Colors.black,
                              borderColor: Color(0xffACACB4),
                              fillColor: Color(0xffF8F9FD),
                              borderRadius: 5,
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.05),

                      CustomContainer(
                        vPadding: 0.055,
                        hPadding: 0.045,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              title: 'Bank Details',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            SizedBox(height: dW * 0.045),
                            CustomTextFieldWithLabel(
                              label: 'Bank Name',
                              controller: bankNameController,
                              hintText: 'Enter bank name',
                              textCapitalization: TextCapitalization.words,
                              inputAction: TextInputAction.next,
                              labelColor: Color(0xffACACB4),
                              labelFW: FontWeight.w500,
                              hintColor: Colors.black,
                              borderColor: Color(0xffACACB4),
                              fillColor: Color(0xffF8F9FD),
                              borderRadius: 5,
                              onChanged: (value) => setState(() {}),
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Account Number',
                              controller: accountNoController,
                              hintText: 'Enter account number',
                              inputType: TextInputType.number,
                              inputFormatter: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              inputAction: TextInputAction.next,
                              labelColor: Color(0xffACACB4),
                              labelFW: FontWeight.w500,
                              hintColor: Colors.black,
                              borderColor: Color(0xffACACB4),
                              fillColor: Color(0xffF8F9FD),
                              borderRadius: 5,
                              onChanged: (value) => setState(() {}),
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'IFSC Code',
                              controller: ifscController,
                              hintText: 'Enter IFSC code',
                              textCapitalization: TextCapitalization.characters,
                              inputType: TextInputType.text,
                              maxLength: 11,
                              inputAction: TextInputAction.next,
                              labelColor: Color(0xffACACB4),
                              labelFW: FontWeight.w500,
                              hintColor: Colors.black,
                              borderColor: Color(0xffACACB4),
                              fillColor: Color(0xffF8F9FD),
                              borderRadius: 5,
                              onChanged: (value) => setState(() {}),
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'UPI ID',
                              controller: upiController,
                              hintText: 'Enter UPI ID',
                              inputAction: TextInputAction.done,
                              inputType: TextInputType.emailAddress,
                              labelColor: Color(0xffACACB4),
                              labelFW: FontWeight.w500,
                              hintColor: Colors.black,
                              borderColor: Color(0xffACACB4),
                              fillColor: Color(0xffF8F9FD),
                              borderRadius: 5,
                              onChanged: (value) => setState(() {}),
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [AssetSvgIcon(iconName: 'edit2')],
                              ),
                            ),
                            SizedBox(height: dW * 0.05),
                            TextWidget(
                              title: 'Aadhar Image',
                              color: Color(0xffACACB4),
                            ),
                            SizedBox(height: dW * 0.025),
                            pickedImage != ''
                                ? Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Container(
                                        width: dW,
                                        height: dW * 0.5,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10,
                                          ),
                                          color: Colors.black,
                                        ),
                                        child:
                                            pickedImage.contains('https')
                                                ? Image.network(
                                                  pickedImage,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (
                                                    context,
                                                    error,
                                                    stackTrace,
                                                  ) {
                                                    return Container(
                                                      color: Colors.red,
                                                      alignment:
                                                          Alignment.center,
                                                      child: const Text(
                                                        'Image could not be loaded',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                )
                                                : Image.file(
                                                  File(pickedImage),
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (
                                                    context,
                                                    error,
                                                    stackTrace,
                                                  ) {
                                                    return Container(
                                                      color: Colors.red,
                                                      alignment:
                                                          Alignment.center,
                                                      child: const Text(
                                                        'Image could not be loaded',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ), ////////////////////
                                      ),
                                    ),
                                    Positioned(
                                      right: dW * 0.025,
                                      top: dW * 0.025,
                                      child: Row(
                                        children: [
                                          GestureDetector(
                                            onTap: showImageOption,
                                            child: Container(
                                              padding: EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: getThemeColor(),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              child: AssetSvgIcon(
                                                iconName: 'edit',
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: dW * 0.03),
                                          GestureDetector(
                                            onTap: () {
                                              pickedImage = '';
                                              setState(() {});
                                            },
                                            child: Container(
                                              padding: EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: getThemeColor(),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              child: AssetSvgIcon(
                                                iconName: 'delete1',
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                )
                                : GestureDetector(
                                  onTap: showImageOption,
                                  child: DottedBorder(
                                    borderType: BorderType.RRect,
                                    radius: Radius.circular(10),
                                    dashPattern: [10, 10],
                                    strokeCap: StrokeCap.butt,
                                    color: Colors.black,
                                    strokeWidth: 0.4,
                                    padding: EdgeInsets.zero,
                                    child: Container(
                                      width: dW * 0.9,
                                      height: dW * 0.45,
                                      decoration: BoxDecoration(
                                        color: Color(
                                          0xffACACB4,
                                        ).withOpacity(.2),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.camera_alt_outlined,
                                            color: getThemeColor(),
                                            size: 40,
                                          ),
                                          SizedBox(height: dW * 0.03),
                                          Text(
                                            'Click to upload a picture of Aadhar Card\n[Max: 5MB]\n(jpg/jpeg/png/ Only )',
                                            style: TextStyle(
                                              fontSize: tS * 11,
                                              letterSpacing: .3,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.15),
                    ],
                  ),
                ),
              ),
              BottomAlignedWidget(
                dW: dW,
                dH: dW * 0.13,
                child: CustomButton(
                  width: dW,
                  height: dW * 0.13,
                  radius: 8,
                  buttonText: 'Save Changes',
                  onPressed: isButtonEnable() ? formSubmit : null,
                  isLoading: isLoading,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BottomSheetContent extends StatelessWidget {
  final String title;
  final String title2;
  // final String svgImage;
  final IconData icon;
  final Color svgColor;

  const BottomSheetContent({
    required this.svgColor,
    // required this.svgImage,
    required this.icon,
    required this.title,
    required this.title2,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Color.fromRGBO(233, 233, 233, 1),
          radius: 30,
          child: Container(
            height: 30,
            width: 30,
            child: Icon(icon, color: svgColor),
          ),
        ),
        Column(
          children: [
            Text(
              title,
              style: TextStyle(color: Color.fromRGBO(121, 120, 128, 1)),
            ),
            Text(
              title2,
              style: TextStyle(color: Color.fromRGBO(121, 120, 128, 1)),
            ),
          ],
        ),
      ],
    );
  }
}
