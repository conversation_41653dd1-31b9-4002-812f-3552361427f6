import 'dart:io';

import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/moreModule.dart/provider/moreProvider.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_container.dart';
import '../../common_function.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../homeModule/widgets/select_venue_sheet.dart';

class NewBookingScreen extends StatefulWidget {
  const NewBookingScreen({Key? key}) : super(key: key);

  @override
  _NewBookingScreenState createState() => _NewBookingScreenState();
}

class _NewBookingScreenState extends State<NewBookingScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;

  String selectedTab = 'Upcoming';

  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  List<BookingModel> listOfBookings = [];

  selectTab(String tab) {
    selectedTab = tab;
    setState(() {});
    fetchBooking();
  }

  fetchBooking() async {
    try {
      if (Provider.of<MoreProvider>(context, listen: false)
          .getBookingByTab(selectedTab)
          .isEmpty) {
        List turfId = [];
        if (user.business != null) {
          user.business!.turfs.forEach((turf) {
            turfId.add(turf.id);
          });
        }
        setState(() => isLoading = true);
        await Provider.of<MoreProvider>(context, listen: false)
            .fetchBusinessBookingV2(
          accessToken: user.accessToken,
          selectedTab: selectedTab,
          businessId: user.businessId,
          role: user.role,
          refresh: true,
          turfId: turfId,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  lazyLoad() async {
    setState(() => lazyLoading = true);
    List turfId = [];
    if (user.business != null) {
      user.business!.turfs.forEach((turf) {
        turfId.add(turf.id);
      });
    }
    await Provider.of<MoreProvider>(context, listen: false)
        .fetchBusinessBookingV2(
      accessToken: user.accessToken,
      selectedTab: selectedTab,
      businessId: user.businessId,
      role: user.role,
      turfId: turfId,
    );
    setState(() => lazyLoading = false);
  }

  refreshBooking() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);
      List turfId = [];
      if (user.business != null) {
        user.business!.turfs.forEach((turf) {
          turfId.add(turf.id);
        });
      }
      await Provider.of<MoreProvider>(context, listen: false)
          .fetchBusinessBookingV2(
              accessToken: user.accessToken,
              selectedTab: selectedTab,
              businessId: user.businessId,
              role: user.role,
              refresh: true,
              turfId: turfId);
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) lazyLoad();
    }
    return false;
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SelectVenueSheet(user: user, bookingType: 'Single Booking'),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    Provider.of<MoreProvider>(context, listen: false).emptyBooking();
    fetchBooking();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    listOfBookings =
        Provider.of<MoreProvider>(context).getBookingByTab(selectedTab);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      floatingActionButton: isLoading
          ? null
          : FloatingActionButton(
              child: Icon(Icons.add),
              onPressed: openVenueBottomSheet,
              backgroundColor: getThemeColor(),
            ),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
            // SizedBox(height: dW * 0.05),
            NewAppBar(dW: dW, title: 'Bookings'),
            SizedBox(height: dW * 0.05),
            CustomContainer(
              hPadding: 0,
              margin: EdgeInsets.symmetric(horizontal: dW * 0.04),
              vPadding: .014,
              borderColor: Color(0xffF3F4F9),
              radius: 10,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ...['Upcoming', 'Completed', 'Cancelled'].map(
                    (tab) => GestureDetector(
                      onTap: () => selectTab(tab),
                      child: Container(
                        width: dW * 0.25,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(vertical: dW * 0.025),
                        decoration: BoxDecoration(
                          color: tab == selectedTab
                              ? getThemeColor()
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          tab,
                          style: TextStyle(
                            color:
                                tab != selectedTab ? Colors.black : Colors.white,
                            fontSize: tab == selectedTab ? tS * 14 : tS * 13,
                            fontWeight: tab == selectedTab
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: isLoading
                  ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                  : listOfBookings.isEmpty
                      ? EmptyListWidget(
                          text: '$selectedTab booking not found!',
                          topPadding: 0.7,
                        )
                      : RefreshIndicator(
                          onRefresh: () => refreshBooking(),
                          child: NotificationListener<ScrollNotification>(
                            onNotification: _handleScrollNotification,
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              padding:
                                  EdgeInsets.symmetric(horizontal: dW * 0.05),
                              physics: BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: dW * 0.04),
                                  ...listOfBookings.map(
                                    (booking) => BookingWidget(
                                      fromViewAll: true,
                                      booking: booking,
                                      user: user,
                                      deviceWidth: dW,
                                      textScaleFactor: tS,
                                    ),
                                  ),
                                  SizedBox(height: dW * 0.04),
                                  if (lazyLoading) lazyLoader(dW),
                                  SizedBox(height: dW * 0.12),
                                ],
                              ),
                            ),
                          ),
                        ),
            )
          ],
        ),
      ),
    );
  }
}
