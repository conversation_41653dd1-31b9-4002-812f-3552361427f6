import 'package:flutter/material.dart';

class BarChartModel {
  String title;
  int value;
  Color color;

  BarChartModel({
    required this.title,
    required this.value,
    required this.color,
  });
}

class PieData {
  final String xData;
  final num yData;
  final String text;
  PieData({
    required this.xData,
    required this.yData,
    required this.text,
  });

  static PieData jsonToPieChart(Map pie) {
    return PieData(
      xData: pie['name'],
      yData: pie['sportBooking'],
      text: pie['text'] ?? '',
    );
  }
}
