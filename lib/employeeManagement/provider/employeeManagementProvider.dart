import 'dart:convert';

import '../../employeeManagement/model/employeeManagemenrModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';

class EmployeeManagementProvider with ChangeNotifier {
  List<EmployeeManagementModel> _employees = [];

  List<EmployeeManagementModel> get employees {
    return [..._employees];
  }

  emptyEmployee() {
    _employees = [];
  }

  fetchBusinessEmployees({
    required String accessToken,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBusinessEmployees']}';

    try {
      final response = await http.get(Uri.parse(url), headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<EmployeeManagementModel> loadedEmployee = [];
        responseData['result'].forEach((employee) {
          List<Venues> loadedVenue = [];
          employee['turfs'].forEach((turf) {
            loadedVenue.add(
              Venues(
                id: turf['_id'],
                name: turf['name'],
              ),
            );
          });
          loadedEmployee.add(
            EmployeeManagementModel(
              id: employee['employee']['_id'],
              mappingId: employee['_id'],
              owner: employee['owner'],
              ownerBusinessId: employee['ownerBusinessId'],
              turfs: loadedVenue,
              roles: employee['roles'],
              isActive: employee['isActive'],
              firstName: employee['firstName'] ?? '',
              lastName: employee['lastName'] ?? '',
              phone: employee['phone'] ?? '',
              email: employee['email'] ?? '',
              address: employee['address'] ?? '',
              avatar: employee['employee']['avatar'] == null ||
                      employee['employee']['avatar'] == ''
                  ? ''
                  : employee['aadharImage'],
              aadharImage: employee['aadharImage'] == null ||
                      employee['aadharImage'] == ''
                  ? ''
                  : employee['aadharImage'],
              createdAt: DateTime.parse(employee['createdAt']).toLocal(),
            ),
          );
        });
        _employees = List.from(loadedEmployee);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  checkForEmployee({
    required String phone,
  }) async {
    final url = '${webApi['domain']}${endPoint['checkForEmployee']}';
    var str = json.encode({
      "phone": phone,
    });
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        // 'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        return responseData;
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  createEmployeeAndMapping({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String address,
    required List roles,
    required List turfs,
    required String ownerBusinessId,
    required String aadharImage,
    required String accessToken,
  }) async {
    final url = '${webApi['domain']}${endPoint['createEmployeeAndMapping']}';
    var request = http.MultipartRequest('POST', Uri.parse(url));
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $accessToken'
    };

    request.fields.addAll({
      "firstName": firstName,
      "lastName": lastName,
      "email": email,
      "phone": phone,
      "address": address,
      "roles": json.encode(roles),
      "turfs": json.encode(turfs),
      "ownerBusinessId": ownerBusinessId,
    });

    try {
      if (aadharImage != '' && !aadharImage.contains('https:')) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'aadharImage',
            aadharImage,
          ),
        );
      } else {
        request.fields.addAll({"aadharImage": aadharImage});
      }

      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        List<Venues> loadedVenue = [];
        responseData['mapping']['turfs'].forEach((turf) {
          loadedVenue.add(
            Venues(
              id: turf['_id'],
              name: turf['name'],
            ),
          );
        });
        _employees.insert(
          0,
          EmployeeManagementModel(
            id: responseData['result']['_id'],
            mappingId: responseData['mapping']['_id'],
            owner: responseData['mapping']['owner'],
            ownerBusinessId: responseData['mapping']['ownerBusinessId'],
            turfs: loadedVenue,
            roles: responseData['mapping']['roles'],
            isActive: responseData['mapping']['isActive'],
            firstName: responseData['mapping']['firstName'] ?? '',
            lastName: responseData['mapping']['lastName'] ?? '',
            phone: responseData['mapping']['phone'] ?? '',
            email: responseData['mapping']['email'] ?? '',
            address: responseData['mapping']['address'] ?? '',
            avatar: '',
            aadharImage: responseData['mapping']['aadharImage'] == null ||
                    responseData['mapping']['aadharImage'] == ''
                ? ''
                : responseData['mapping']['aadharImage'],
            createdAt:
                DateTime.parse(responseData['mapping']['createdAt']).toLocal(),
          ),
        );
        notifyListeners();
        return responseData;
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  updateEmployeeMapping({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String address,
    required List roles,
    required List turfs,
    required String aadharImage,
    required String accessToken,
    required String employeeId,
  }) async {
    final url = '${webApi['domain']}${endPoint['updateEmployeeMapping']}';
    var request = http.MultipartRequest('PUT', Uri.parse(url));
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $accessToken'
    };

    request.fields.addAll({
      "firstName": firstName,
      "lastName": lastName,
      "email": email,
      "employee": employeeId,
      "phone": phone,
      "address": address,
      "roles": json.encode(roles),
      "turfs": json.encode(turfs),
    });

    try {
      if (aadharImage != '' && !aadharImage.contains('https:')) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'aadharImage',
            aadharImage,
          ),
        );
      } else {
        request.fields.addAll({"aadharImage": aadharImage});
      }

      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        int index =
            _employees.indexWhere((employee) => employee.id == employeeId);
        if (index != -1) {
          List<Venues> loadedVenue = [];
          responseData['result']['turfs'].forEach((turf) {
            loadedVenue.add(
              Venues(
                id: turf['_id'],
                name: turf['name'],
              ),
            );
          });
          _employees[index].firstName = responseData['result']['firstName'];
          _employees[index].lastName = responseData['result']['lastName'];
          _employees[index].email = responseData['result']['email'];
          _employees[index].address = responseData['result']['address'];
          _employees[index].turfs = loadedVenue;
          _employees[index].roles = responseData['result']['roles'];
          _employees[index].aadharImage =
              responseData['result']['aadharImage'] == null
                  ? ''
                  : responseData['result']['aadharImage'];
          notifyListeners();
        }
        notifyListeners();
        return responseData;
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  activeOrDeleteEmployeeMapping({
    required String mappingId,
    required String action,
    required String accessToken,
    required bool isActive,
  }) async {
    final url =
        '${webApi['domain']}${endPoint['activeOrDeleteEmployeeMapping']}';
    var str = json.encode({
      "mappingId": mappingId,
      "isActive": isActive,
      "action": action,
    });
    try {
      final response = await http.put(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        if (action == 'Delete') {
          _employees.removeWhere((employee) => employee.mappingId == mappingId);
        } else {
          int index = _employees
              .indexWhere((employee) => employee.mappingId == mappingId);
          _employees[index].isActive = isActive;
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  checkIfEmployeeExists(String phone) {
    return _employees.indexWhere((employee) => employee.phone == phone);
  }

  getEmployeeById(String id) {
    int index = _employees.indexWhere((employee) => employee.id == id);
    if (index != -1) {
      return _employees[index];
    } else {
      return null;
    }
  }
}
