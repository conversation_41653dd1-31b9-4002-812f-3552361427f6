import 'package:bys_business/commonWidgets/circle_avatar_widget.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';

import '../../authModule/modals/userModel.dart';
import '../../common_function.dart';
import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../employeeManagement/screens/employee_detail_screen.dart';
import 'package:flutter/material.dart';

import '../../navigators.dart';
import '../../new_colors.dart';

class EmployeeWidget extends StatelessWidget {
  final double dW;
  final EmployeeManagementModel employee;
  final UserModal user;
  const EmployeeWidget({
    Key? key,
    required this.dW,
    required this.employee,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        push(EmployeeDetailScreen(employeeId: employee.id, user: user));
      },
      child: CustomContainer(
        margin: EdgeInsets.only(bottom: dW * 0.05),
        vPadding: 0,
        hPadding: 0,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: dW * .02),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8)),
                color: !employee.isActive
                    ? getLightRedColor1(context)
                    : getLightGreenColor1(context),
              ),
              width: dW,
              alignment: Alignment.center,
              child: TextWidget(
                title: employee.isActive ? 'Active' : 'Inactive',
                fontWeight: FontWeight.w600,
                color: !employee.isActive
                    ? getRedColor2(context)
                    : getThemeColor(),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: dW * 0.045,
                vertical: dW * 0.04,
              ),
              child: Row(
                children: [
                  CircleAvatarWidget(
                    avatar: employee.avatar,
                    userName: '${employee.firstName} ${employee.lastName}',
                    height: 0.16,
                    parentRadius: 30,
                    fontSize: 18,
                  ),
                  SizedBox(width: dW * 0.04),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: dW * 0.4),
                        child: TextWidget(
                          title: '${employee.firstName} ${employee.lastName}',
                          fontWeight: FontWeight.w600,
                          maxLines: 1,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(height: dW * 0.01),
                      ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: dW * 0.4),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: TextWidget(
                            title:
                                'Employee ID :- #${employee.id.substring(employee.id.length - 7).toUpperCase()}',
                            fontSize: 12,
                          ),
                        ),
                      ),
                      SizedBox(height: dW * 0.01),
                      ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: dW * 0.4),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: TextWidget(
                            title: 'Phone No :- ${employee.phone}',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
