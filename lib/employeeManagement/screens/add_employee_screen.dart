import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/new_check_box_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/imageValidator.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../common_function.dart';
import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../venueModule/models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';

class AddEmployeeScreen extends StatefulWidget {
  final UserModal user;
  final EmployeeManagementModel? employee;
  const AddEmployeeScreen({
    Key? key,
    required this.user,
    this.employee,
  }) : super(key: key);

  @override
  _AddEmployeeScreenState createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isLoading = false;
  bool checkForUser = false;
  bool isVerified = false;
  bool validating = false;
  bool submitting = false;
  bool enableTextField = false;

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController addressController = TextEditingController();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  FocusNode phoneFocusNode = FocusNode();
  FocusNode fistNameFocusNode = FocusNode();
  FocusNode lastNameFocusNode = FocusNode();
  FocusNode emailFocusNode = FocusNode();
  FocusNode addressFocusNode = FocusNode();

  List<Venue> listOfTurf = [];
  List selectedTurf = [];
  List selectedRoles = [];

  List employeeRoles = [
    "Booking",
    "Bulk Booking",
    "Cancel Booking",
    "Cancel Bulk Booking",
    "Venue",
    "Report",
    "Analytics",
  ];

  RegExp regexExp = RegExp(
    '(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])',
  );

  getHintStyle(textScaleFactor) {
    return Theme.of(context).textTheme.headlineSmall!.copyWith(
          fontSize: textScaleFactor * 13,
          color: Color(0xff737373),
          fontWeight: FontWeight.w500,
        );
  }

  getStyle(textScaleFactor) {
    return Theme.of(context).textTheme.headlineSmall!.copyWith(
          fontSize: textScaleFactor * displayMedium,
          color: Colors.black,
          fontWeight: FontWeight.w600,
        );
  }

  Widget buildHeader({
    required double deviceWidth,
    required double textScaleFactor,
    required String title,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
      constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
      child: Text(
        title,
        style: Theme.of(context).textTheme.headlineSmall!.copyWith(
              fontSize: textScaleFactor * 16,
              color: const Color(0xff434343),
            ),
      ),
    );
  }

  getInputDecoration({
    required double textScaleFactor,
    required String hintText,
  }) {
    return InputDecoration(
      hintStyle: getHintStyle(textScaleFactor),
      hintText: hintText,
      fillColor: Colors.grey.shade300,
      counterText: '',
      border: UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade500),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade500),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade500),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade500),
      ),
      disabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade500),
      ),
      filled: false,
    );
  }

  var fileMedia1;
  String imagePath1 = '';
  final _uploadImage1Error = MyValidator(hasError: false);
  RegExp regex = RegExp("[A-Z]{5}[0-9]{4}[A-Z]{1}");

  Future _capture(int i) async {
    setState(() {
      if (i == 1) {
        _uploadImage1Error.hasError = false;
      }
    });
    final getMedia = ImagePicker().pickImage;
    final media = await getMedia(source: ImageSource.gallery);
    final file = File(media!.path);

    if (file == null) {
      return;
    } else {
      setState(() {
        if (i == 1) {
          fileMedia1 = file;
          imagePath1 = media.path;
        }
      });
    }
  }

  Future<void> fetchTurf() async {
    try {
      if (Provider.of<TurfProvider>(context, listen: false).turfs.isEmpty) {
        setState(() {
          isLoading = true;
        });
        await Provider.of<TurfProvider>(context, listen: false)
            .fetchTurfsByBusinessId(
                widget.user.accessToken, widget.user.businessId);
        listOfTurf = Provider.of<TurfProvider>(context, listen: false)
            .getVerifiedTurf(1);
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  verifyEmployee() async {
    try {
      phoneFocusNode.unfocus();
      int index =
          Provider.of<EmployeeManagementProvider>(context, listen: false)
              .checkIfEmployeeExists(phoneController.text.trim());
      if (index != -1) {
        showSnackbar('Employee already added with this phone number');

        return;
      }
      setState(() {
        validating = true;
        checkForUser = true;
      });
      final data =
          await Provider.of<EmployeeManagementProvider>(context, listen: false)
              .checkForEmployee(phone: phoneController.text);
      if (data == null) {
        showSnackbar('Something went wrong');
      } else {
        if (data['message'].toString().toLowerCase() != 'new employee') {
          setState(() {
            firstNameController.text = data['result']['firstName'];
            lastNameController.text = data['result']['lastName'];
            emailController.text = data['result']['email'];
            addressController.text = data['result']['address'];
            isVerified = true;
            enableTextField = false;
          });
        } else {
          setState(() {
            lastNameController.clear();
            firstNameController.clear();
            emailController.clear();
            addressController.clear();
            isVerified = false;
            enableTextField = true;
          });
        }
      }
    } catch (e) {
      print(e);
      setState(() {
        isVerified = false;
        enableTextField = true;
      });
    } finally {
      setState(() {
        validating = false;
      });
    }
  }

  addEmployee() async {
    try {
      hideKeyBoard(context);

      if (!EmailValidator.validate(emailController.text.trim())) {
        return showSnackbar('Please eneter valid email id');
      }
      if (submitting) return;
      setState(() {
        submitting = true;
      });
      var data;
      if (widget.employee == null) {
        data = await Provider.of<EmployeeManagementProvider>(context,
                listen: false)
            .createEmployeeAndMapping(
          firstName: firstNameController.text.trim(),
          lastName: lastNameController.text.trim(),
          email: emailController.text.trim(),
          phone: phoneController.text.trim(),
          address: addressController.text.trim(),
          roles: selectedRoles,
          turfs: selectedTurf,
          aadharImage: imagePath1,
          accessToken: widget.user.accessToken,
          ownerBusinessId: widget.user.businessId,
        );
      } else {
        data = await Provider.of<EmployeeManagementProvider>(context,
                listen: false)
            .updateEmployeeMapping(
          firstName: firstNameController.text.trim(),
          lastName: lastNameController.text.trim(),
          email: emailController.text.trim(),
          phone: phoneController.text.trim(),
          address: addressController.text.trim(),
          roles: selectedRoles,
          turfs: selectedTurf,
          aadharImage: imagePath1,
          accessToken: widget.user.accessToken,
          employeeId: widget.employee!.id,
        );
      }
      if (data != null) {
        showSnackbar(
            'Employee ${widget.employee == null ? 'addedd' : 'updated'} successfully',
            color: greenPrimary);
        Navigator.of(context).pop(true);
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        submitting = false;
      });
    }
  }

  textfieldOnTap() {
    if (!checkForUser) {
      showSnackbar('Please verify phone number');
    }
  }

  bool isButtonEnable() {
    if (phoneController.text.isEmpty ||
        firstNameController.text.trim().isEmpty ||
        lastNameController.text.trim().isEmpty ||
        emailController.text.trim().isEmpty ||
        addressController.text.trim().isEmpty ||
        selectedRoles.isEmpty ||
        (selectedRoles.contains('Venue') && selectedTurf.isEmpty)) {
      return false;
    } else {
      return true;
    }
  }

  @override
  void initState() {
    super.initState();
    fetchTurf();
    if (widget.employee != null) {
      firstNameController.text = widget.employee!.firstName;
      lastNameController.text = widget.employee!.lastName;
      emailController.text = widget.employee!.email;
      phoneController.text = widget.employee!.phone;
      addressController.text = widget.employee!.address;
      widget.employee!.turfs.forEach((turf) {
        selectedTurf.add(turf.id);
      });
      selectedRoles = widget.employee!.roles;
      imagePath1 = widget.employee!.aadharImage;
      checkForUser = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    listOfTurf = Provider.of<TurfProvider>(context).getVerifiedTurf(1);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: deviceHeight,
        width: deviceWidth,
        child: isLoading
            ? CircularLoader(
                android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
            : Padding(
              padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
              child: Column(
                  children: [
                    // SizedBox(height: deviceWidth * 0.05),
                    NewAppBar(
                      dW: deviceWidth,
                      title: widget.employee == null
                          ? 'Add Employee'
                          : 'Update Employee',
                    ),
                    SizedBox(height: deviceWidth * 0.07),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: BouncingScrollPhysics(),
                        padding:
                            EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomContainer(
                              vPadding: .05,
                              child: Column(
                                children: [
                                  if (widget.employee == null) ...[
                                    CustomTextFieldWithLabel(
                                      label: 'Phone',
                                      controller: phoneController,
                                      hintText: 'Phone Number',
                                      inputFormatter: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      inputType: TextInputType.number,
                                      maxLength: 10,
                                      inputAction: TextInputAction.next,
                                      suffixIcon: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(
                                                right: deviceWidth * 0.03),
                                            child: TextWidget(
                                              title: checkForUser
                                                  ? isVerified
                                                      ? 'Existing'
                                                      : 'Non-Existing'
                                                  : 'Verify',
                                              color: !checkForUser
                                                  ? Colors.green
                                                  : isVerified
                                                      ? Colors.red
                                                      : Colors.green,
                                              fontSize: textScaleFactor * 13,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                      onChanged: (value) {
                                        if (value.length <= 9) {
                                          firstNameController.clear();
                                          lastNameController.clear();
                                          emailController.clear();
                                          addressController.clear();
                                          setState(() {
                                            isVerified = false;
                                            checkForUser = false;
                                            enableTextField = false;
                                          });
                                        } else if (value.length == 10) {
                                          verifyEmployee();
                                        }
                                      },
                                    ),
                                    SizedBox(height: deviceWidth * 0.05),
                                  ],
                                  CustomTextFieldWithLabel(
                                    label: 'First Name',
                                    controller: firstNameController,
                                    hintText: 'Enter first name',
                                    textCapitalization: TextCapitalization.words,
                                    inputAction: TextInputAction.next,
                                    enabled: checkForUser,
                                    onChanged: (value) => setState(() {}),
                                  ),
                                  SizedBox(height: deviceWidth * 0.05),
                                  CustomTextFieldWithLabel(
                                    label: 'Last Name',
                                    controller: lastNameController,
                                    hintText: 'Enter last name',
                                    textCapitalization: TextCapitalization.words,
                                    inputAction: TextInputAction.next,
                                    onChanged: (value) => setState(() {}),
                                    enabled: checkForUser,
                                  ),
                                  SizedBox(height: deviceWidth * 0.05),
                                  CustomTextFieldWithLabel(
                                    label: 'Email ID',
                                    controller: emailController,
                                    hintText: 'Enter emaid ID',
                                    textCapitalization: TextCapitalization.words,
                                    inputAction: TextInputAction.next,
                                    onChanged: (value) => setState(() {}),
                                    enabled: checkForUser,
                                  ),
                                  SizedBox(height: deviceWidth * 0.05),
                                  CustomTextFieldWithLabel(
                                    label: 'Full Address',
                                    controller: addressController,
                                    hintText: 'Enter enter full address',
                                    textCapitalization: TextCapitalization.words,
                                    inputAction: TextInputAction.next,
                                    onChanged: (value) => setState(() {}),
                                    enabled: checkForUser,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.07),
                            CustomContainer(
                              vPadding: 0,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: deviceWidth * 0.04),
                                  buildHeader(
                                    deviceWidth: deviceWidth,
                                    textScaleFactor: textScaleFactor,
                                    title: 'Select Privileges',
                                  ),
                                  SizedBox(height: deviceWidth * 0.04),
                                  Wrap(
                                    children: [
                                      ...Provider.of<Auth>(context, listen: false)
                                          .employeeRoles
                                          .map(
                                            (role) => Container(
                                              margin: EdgeInsets.only(
                                                bottom: deviceWidth * 0.035,
                                                right: deviceWidth * 0.035,
                                              ),
                                              color: Colors.transparent,
                                              child: CheckBoxWidget(
                                                active:
                                                    selectedRoles.contains(role),
                                                borderRadius: 4,
                                                height: deviceWidth * 0.046,
                                                iconSize: 14,
                                                onTap: () {
                                                  if (selectedRoles
                                                      .contains(role)) {
                                                    selectedRoles.remove(role);
                                                  } else {
                                                    selectedRoles.add(role);
                                                  }
                                                  setState(() {});
                                                },
                                                title: role,
                                                activeBorderColor:
                                                    Theme.of(context)
                                                        .primaryColor,
                                                activeColor: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                            ),
                                          ),
                                    ],
                                  ),
                                  // Wrap(
                                  //   children: [
                                  //     ...employeeRoles.map(
                                  //       (role) => Container(
                                  //         margin: EdgeInsets.only(
                                  //           bottom: deviceWidth * 0.035,
                                  //           right: deviceWidth * 0.035,
                                  //         ),
                                  //         color: Colors.transparent,
                                  //         child: CheckBoxWidget(
                                  //           active: selectedRoles.contains(role),
                                  //           borderRadius: 4,
                                  //           height: deviceWidth * 0.046,
                                  //           iconSize: 14,
                                  //           onTap: () {
                                  //             if (selectedRoles.contains(role)) {
                                  //               selectedRoles.remove(role);
                                  //             } else {
                                  //               selectedRoles.add(role);
                                  //             }
                                  //             setState(() {});
                                  //           },
                                  //           title: role,
                                  //           activeBorderColor:
                                  //               Theme.of(context).primaryColor,
                                  //           activeColor:
                                  //               Theme.of(context).primaryColor,
                                  //         ),
                                  //       ),
                                  //     ),
                                  //   ],
                                  // ),
                                  SizedBox(height: deviceWidth * 0.01),
                                ],
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.07),
                            CustomContainer(
                              vPadding: 0,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: deviceWidth * 0.04),
                                  buildHeader(
                                    deviceWidth: deviceWidth,
                                    textScaleFactor: textScaleFactor,
                                    title: 'Select Venues',
                                  ),
                                  SizedBox(height: deviceWidth * 0.04),
                                  Wrap(
                                    children: [
                                      ...listOfTurf.map(
                                        (turf) => Container(
                                          margin: EdgeInsets.only(
                                            bottom: deviceWidth * 0.035,
                                            right: deviceWidth * 0.035,
                                          ),
                                          color: Colors.transparent,
                                          child: CheckBoxWidget(
                                            active:
                                                selectedTurf.contains(turf.id),
                                            borderRadius: 4,
                                            height: deviceWidth * 0.046,
                                            iconSize: 14,
                                            onTap: () {
                                              if (selectedTurf
                                                  .contains(turf.id)) {
                                                selectedTurf.remove(turf.id);
                                              } else {
                                                selectedTurf.add(turf.id);
                                              }
                                              setState(() {});
                                            },
                                            title: turf.name,
                                            activeBorderColor:
                                                Theme.of(context).primaryColor,
                                            activeColor:
                                                Theme.of(context).primaryColor,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: deviceWidth * 0.01),
                                ],
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.07),
                            buildHeader(
                              deviceWidth: deviceWidth,
                              textScaleFactor: textScaleFactor,
                              title: 'Aadhar Card Image',
                            ),
                            SizedBox(height: deviceWidth * 0.03),
                            imagePath1 != ''
                                ? Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Container(
                                          width: deviceWidth * 0.9,
                                          height: deviceWidth * 0.6,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: Colors.black,
                                          ),
                                          child: imagePath1.contains('https')
                                              ? Image.network(
                                                  imagePath1,
                                                  fit: BoxFit.cover,
                                                )
                                              : Image.file(
                                                  fileMedia1,
                                                  fit: BoxFit.cover,
                                                ), ////////////////////
                                        ),
                                      ),
                                      Positioned(
                                        right: deviceWidth * 0.025,
                                        bottom: deviceWidth * 0.025,
                                        child: GestureDetector(
                                          onTap: () => _capture(1),
                                          child: Container(
                                            height: deviceHeight * 0.045,
                                            width: deviceWidth * 0.13,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(3),
                                                color: Colors.black54),
                                            child: Center(
                                              child: Text(
                                                'Edit',
                                                style: TextStyle(
                                                  letterSpacing: 0.1,
                                                  color: Theme.of(context)
                                                      .scaffoldBackgroundColor,
                                                  fontSize: deviceWidth * 0.03,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  )
                                : Container(
                                    child: DottedBorder(
                                      borderType: BorderType.RRect,
                                      radius: Radius.circular(10),
                                      dashPattern: [10, 10],
                                      strokeCap: StrokeCap.butt,
                                      color: Colors.black,
                                      strokeWidth: 0.4,
                                      padding: EdgeInsets.zero,
                                      child: Container(
                                        width: deviceWidth * 0.9,
                                        height: deviceWidth * 0.5,
                                        decoration: BoxDecoration(
                                          color:
                                              Color(0xffACACB4).withOpacity(.2),
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            IconButton(
                                              icon: Icon(
                                                Icons.camera_alt_outlined,
                                                color: getThemeColor(),
                                                size: 50,
                                              ),
                                              onPressed: () => _capture(1),
                                            ),
                                            SizedBox(height: deviceWidth * 0.03),
                                            Text(
                                              'Click to upload a picture of Aadhar Card\n[Max: 5MB]\n(jpg/jpeg/png/ Only )',
                                              style: TextStyle(
                                                fontSize: textScaleFactor * 12,
                                                letterSpacing: .3,
                                              ),
                                              textAlign: TextAlign.center,
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                            SizedBox(height: deviceWidth * 0.27)
                          ],
                        ),
                      ),
                    ),
                    BottomAlignedWidget(
                      dW: deviceWidth,
                      dH: deviceWidth * 0.13,
                      child: CustomButton(
                        width: deviceWidth,
                        height: deviceWidth * 0.13,
                        radius: 8,
                        isLoading: submitting,
                        buttonText: widget.employee == null
                            ? 'Add Employee'
                            : 'Update Employee',
                        onPressed: isButtonEnable() ? addEmployee : null,
                        fontSize: 16,
                      ),
                    )
                  ],
                ),
            ),
      ),
    );
  }
}
