import 'dart:io';

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../common_function.dart';
import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../employeeManagement/screens/add_employee_screen.dart';
import '../../employeeManagement/widget/employee_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AllEmployeeScreen extends StatefulWidget {
  final UserModal user;
  const AllEmployeeScreen({
    Key? key,
    required this.user,
  }) : super(key: key);

  @override
  _AllEmployeeScreenState createState() => _AllEmployeeScreenState();
}

class _AllEmployeeScreenState extends State<AllEmployeeScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  List<EmployeeManagementModel> listofEmployees = [];
  String selectedTab = 'Active';

  Future<void> fetchEmployees() async {
    try {
      if (Provider.of<EmployeeManagementProvider>(context, listen: false)
          .employees
          .isEmpty) {
        setState(() {
          isLoading = true;
        });
        await Provider.of<EmployeeManagementProvider>(context, listen: false)
            .fetchBusinessEmployees(
          accessToken: widget.user.accessToken,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  getEmployees() {
    if (selectedTab == 'Active') {
      return listofEmployees
          .where((employee) => employee.isActive == true)
          .toList();
    } else {
      return listofEmployees
          .where((employee) => employee.isActive == false)
          .toList();
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchEmployees();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listofEmployees =
        Provider.of<EmployeeManagementProvider>(context).employees;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
          : Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
            child: Column(
                children: [
                  // SizedBox(height: dW * 0.05),
                  NewAppBar(dW: dW, title: 'Employees'),
                  SizedBox(height: dW * 0.07),
                  CustomContainer(
                    hPadding: 0,
                    vPadding: .02,
                    margin: EdgeInsets.symmetric(horizontal: dW * 0.04),
                    borderColor: Color(0xffF3F4F9),
                    radius: 10,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        ...['Active', 'Inactive'].map(
                          (type) => GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedTab = type;
                              });
                            },
                            child: Container(
                              width: dW * 0.38,
                              alignment: Alignment.center,
                              padding: EdgeInsets.symmetric(
                                vertical: dW * 0.03,
                              ),
                              decoration: BoxDecoration(
                                color: type == selectedTab
                                    ? getThemeColor()
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: type != selectedTab
                                    ? []
                                    : [
                                        BoxShadow(
                                          blurRadius: 6,
                                          spreadRadius: 2,
                                          offset: Offset(0, 5),
                                          color: Color(0XFF30303033)
                                              .withOpacity(0.06),
                                        ),
                                        BoxShadow(
                                          blurRadius: 6,
                                          spreadRadius: 2,
                                          offset: Offset(-5, 2),
                                          color: Color(0XFF30303033)
                                              .withOpacity(0.06),
                                        ),
                                        BoxShadow(
                                          blurRadius: 6,
                                          spreadRadius: 2,
                                          offset: Offset(5, 2),
                                          color: Color(0XFF30303033)
                                              .withOpacity(0.06),
                                        )
                                      ],
                              ),
                              child: Text(
                                '$type',
                                style: TextStyle(
                                  color: type != selectedTab
                                      ? Colors.grey
                                      : Colors.white,
                                  fontSize:
                                      type == selectedTab ? tS * 15 : tS * 14,
                                  fontWeight: type == selectedTab
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: dW * 0.05),
                          if (getEmployees().isEmpty)
                            EmptyListWidget(
                              text: 'Employees not found!',
                              topPadding: .5,
                            ),
                          if (getEmployees().isNotEmpty)
                            ...getEmployees().map(
                              (employee) => EmployeeWidget(
                                dW: dW,
                                employee: employee,
                                user: widget.user,
                              ),
                            ),
                          SizedBox(height: dW * 0.15),
                        ],
                      ),
                    ),
                  ),
                  BottomAlignedWidget(
                    dW: dW,
                    dH: dW * 0.13,
                    child: CustomButton(
                      width: dW,
                      height: dW * 0.13,
                      radius: 8,
                      buttonText: 'Add New Employee',
                      onPressed: () {
                        push(AddEmployeeScreen(user: widget.user));
                      },
                      fontSize: 16,
                    ),
                  )
                ],
              ),
          ),
    );
  }
}
