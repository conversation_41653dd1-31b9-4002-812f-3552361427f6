import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/circle_avatar_widget.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../common_function.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../employeeManagement/screens/add_employee_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../navigators.dart';

class EmployeeDetailScreen extends StatefulWidget {
  final String employeeId;
  final UserModal user;
  EmployeeDetailScreen({
    Key? key,
    required this.employeeId,
    required this.user,
  }) : super(key: key);

  @override
  State<EmployeeDetailScreen> createState() => _EmployeeDetailScreenState();
}

class _EmployeeDetailScreenState extends State<EmployeeDetailScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  EmployeeManagementModel? employee;

  bool isLoading = false;
  bool listen = true;

  activeOrDeleteEmployee({required String action}) async {
    try {
      setState(() {
        isLoading = true;
        if (action == 'Delete') {
          listen = false;
        }
      });
      final data =
          await Provider.of<EmployeeManagementProvider>(context, listen: false)
              .activeOrDeleteEmployeeMapping(
        mappingId: employee!.mappingId,
        action: action,
        accessToken: widget.user.accessToken,
        isActive: !employee!.isActive,
      );
      if (!data) {
        showSnackbar('Something went wrong');
      } else {
        Navigator.of(context).pop();
        if (action == 'Delete') {
          await showSnackbar('Employee deleted successfully',
              color: greenPrimary);
        } else if (action == 'Active') {
          showSnackbar(
              'Employee ${employee!.isActive ? 'Activated' : 'Inactivated'} successfully',
              color: greenPrimary);
        }
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  showDialogBox({required String content, required String action}) {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title: content,
            noText: action == 'Delete'
                ? 'Yes, Delete'
                : 'Yes, ${employee!.isActive ? 'Inactive' : 'Active'}',
            yesText: 'Cancel',
            noFunction: () {
              pop();
              activeOrDeleteEmployee(action: action);
            },
            yesFunction: () => pop(),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    employee = Provider.of<EmployeeManagementProvider>(context, listen: listen)
        .getEmployeeById(widget.employeeId);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return employee == null
        ? SizedBox.shrink()
        : SizedBox(
            height: deviceHeight,
            width: deviceWidth,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
                child: Column(
                  children: [
                    // SizedBox(height: deviceWidth * 0.03),
                    //HEADER
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        NewAppBar(dW: deviceWidth, title: 'Employee Details'),
                        isLoading
                            ? Container(
                                margin:
                                    EdgeInsets.only(right: deviceWidth * 0.05),
                                child: circularForButton(
                                  deviceWidth * 0.8,
                                  color: Theme.of(context).primaryColor,
                                ),
                              )
                            : Align(
                                alignment: Alignment.bottomRight,
                                child: PopupMenuButton(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(7),
                                  ),
                                  icon: Icon(
                                    Icons.more_vert,
                                    color: Colors.black,
                                    size: 22,
                                  ),
                                  itemBuilder: (BuildContext bc) => [
                                    popupMenuItem(
                                      position: 1,
                                      title: 'Edit Details',
                                      icon: 'edit',
                                      dW: deviceWidth,
                                    ),
                                    popupMenuItem(
                                      position: 2,
                                      title: employee!.isActive
                                          ? "Inactive"
                                          : "Active",
                                      icon: 'active',
                                      dW: deviceWidth,
                                    ),
                                    popupMenuItem(
                                      position: 3,
                                      title: 'Delete',
                                      icon: 'delete1',
                                      dW: deviceWidth,
                                    ),
                                  ],
                                  onSelected: (value) {
                                    if (value == 1) {
                                      push(AddEmployeeScreen(
                                          user: widget.user, employee: employee));
                                    } else if (value == 2) {
                                      showDialogBox(
                                        content:
                                            'Are you sure you want to ${employee!.isActive ? 'Inactive' : 'Active'} this employee?',
                                        action: 'Active',
                                      );
                                    } else if (value == 3) {
                                      showDialogBox(
                                        content:
                                            'Are you sure you want to delete this employee?',
                                        action: 'Delete',
                                      );
                                    }
                                  },
                                ),
                              ),
                      ],
                    ),
                
                    SizedBox(height: deviceWidth * 0.04),
                
                    //AVATAR
                    CircleAvatarWidget(
                      avatar: employee!.avatar,
                      userName: '${employee!.firstName} ${employee!.lastName}',
                      height: 0.18,
                      parentRadius: 35,
                      fontSize: 18,
                    ),
                
                    SizedBox(height: deviceWidth * 0.07),
                
                    //NAME
                    CustomContainer(
                      margin:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Name',
                            subtitle:
                                '${employee!.firstName} ${employee!.lastName}',
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Email',
                            subtitle: employee!.email,
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Mobile Number',
                            subtitle: '+91 - ${employee!.phone}',
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Address',
                            subtitle: employee!.address,
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(
                                vertical: deviceWidth * 0.025),
                            child: Text(
                              'Privileges',
                              style:
                                  Theme.of(context).textTheme.displaySmall!.copyWith(
                                        fontSize: textScaleFactor * 13.2,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xff737373),
                                      ),
                            ),
                          ),
                          ...employee!.roles
                              .asMap()
                              .map(
                                (index, role) => MapEntry(
                                  index,
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: deviceWidth * 0.01),
                                    child: Text(
                                      '${index + 1}. $role',
                                      style: Theme.of(context)
                                          .textTheme
                                          .displaySmall!
                                          .copyWith(
                                            fontSize: textScaleFactor * 14.5,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                    ),
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          if (employee!.turfs.isNotEmpty) ...[
                            SizedBox(height: deviceWidth * 0.01),
                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.025),
                              child: Text(
                                'Assigned Venues',
                                style: Theme.of(context)
                                    .textTheme
                                    .displaySmall!
                                    .copyWith(
                                      fontSize: textScaleFactor * 13.2,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xff737373),
                                    ),
                              ),
                            ),
                            ...employee!.turfs
                                .asMap()
                                .map(
                                  (index, turf) => MapEntry(
                                    index,
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          vertical: deviceWidth * 0.01),
                                      child: Text(
                                        '${index + 1}. ${turf.name}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .displaySmall!
                                            .copyWith(
                                              fontSize: textScaleFactor * 14.5,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.black,
                                            ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                )
                                .values
                                .toList(),
                          ],
                          SizedBox(height: deviceWidth * 0.01),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Joinning Date',
                            subtitle: DateFormat('dd MMMM yyyy')
                                .format(employee!.createdAt),
                          ),
                        ],
                      ),
                    ),
                
                    if (employee!.aadharImage != '')
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.05,
                          vertical: deviceWidth * 0.03,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.025),
                              child: Text(
                                'Aadhar Image',
                                style: Theme.of(context)
                                    .textTheme
                                    .displaySmall!
                                    .copyWith(
                                      fontSize: textScaleFactor * 13,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xff737373),
                                    ),
                              ),
                            ),
                            ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: Container(
                                width: deviceWidth,
                                height: deviceWidth * 0.45,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.black,
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: CachedNetworkImage(
                                    repeat: ImageRepeat.repeat,
                                    fit: BoxFit.cover,
                                    imageUrl: employee!.aadharImage,
                                    placeholder: (_, __) => Image.asset(
                                      'assets/images/placeholder.jpg',
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.08),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
  }
}
