class EmployeeManagementModel {
  final String id;
  final String mappingId;
  final String owner;
  final String ownerBusinessId;
  List<Venues> turfs;
  List roles;
  String firstName;
  String lastName;
  String phone;
  String email;
  String address;
  String avatar;
  String aadharImage;
  bool isActive;
  final DateTime createdAt;

  EmployeeManagementModel({
    required this.id,
    required this.mappingId,
    required this.owner,
    required this.ownerBusinessId,
    required this.turfs,
    required this.roles,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.address,
    required this.avatar,
    required this.aadharImage,
    required this.createdAt,
    this.isActive = false,
  });
}

class Venues {
  final String id;
  final String name;

  Venues({
    required this.id,
    required this.name,
  });
}
