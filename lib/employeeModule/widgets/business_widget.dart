import '../../authModule/providers/auth.dart';
import '../../employeeModule/models/business_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../navigators.dart';
import '../screens/booking_screen.dart';

class BusinessWidget extends StatelessWidget {
  final double deviceWidth;
  final double textScale;
  final BusinessModel business;
  const BusinessWidget({
    Key? key,
    required this.deviceWidth,
    required this.textScale,
    required this.business,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Provider.of<Auth>(context, listen: false).setBusinessId(business);
        push(BookingScreen(
            user: Provider.of<Auth>(context, listen: false).user));
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: deviceWidth * 0.03,
          vertical: deviceWidth * 0.04,
        ),
        margin: EdgeInsets.only(bottom: deviceWidth * 0.045),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(9),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: deviceWidth * 0.65),
                  margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                  child: Text(
                    '${business.owner.firstName} ${business.owner.lastName}',
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                          fontSize: textScale * 15,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  constraints: BoxConstraints(maxWidth: deviceWidth * 0.5),
                  margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                  child: Text(
                    'Business Id: #${business.owner.id.substring(business.owner.id.length - 7)}',
                    style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                          color: Colors.black,
                          fontSize: textScale * 13.5,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                Text(
                  'Contact: ${business.owner.phone}',
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                        color: Colors.black,
                        fontSize: textScale * 13.5,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: deviceWidth * 0.1,
                  width: deviceWidth * 0.1,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: business.owner.avatar == ''
                        ? Image.asset('assets/images/user.png')
                        : CachedNetworkImage(
                            imageUrl: business.owner.avatar,
                            fit: BoxFit.cover,
                            placeholder: (_, __) => Image.asset(
                              'assets/images/user.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
