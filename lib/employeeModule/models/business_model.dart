import '../../employeeManagement/model/employeeManagemenrModel.dart';

class BusinessModel {
  final String mappingId;
  final List<Venues> turfs;
  final bool isActive;
  final String aadharImage;
  final Employee employee;
  final Owner owner;
  List roles;

  BusinessModel({
    required this.mappingId,
    required this.turfs,
    required this.roles,
    required this.isActive,
    required this.employee,
    required this.owner,
    required this.aadharImage,
  });
}

class Owner {
  final String id;
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String avatar;
  final String businessId;

  Owner({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.avatar,
    required this.businessId,
  });
}

class Employee {
  final String id;
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String address;

  Employee({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.address,
  });
}
