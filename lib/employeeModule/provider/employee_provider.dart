import 'dart:convert';

import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../employeeModule/models/business_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';

class EmployeeProvider with ChangeNotifier {
  List<BusinessModel> _businesses = [];

  List<BusinessModel> get businesses {
    return [..._businesses];
  }

  fetchEmployeeBusinesses({required String accessToken}) async {
    final url = '${webApi['domain']}${endPoint['fetchEmployeeBusinesses']}';

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<BusinessModel> loadedBusiness = [];
        responseData['result'].forEach(
          (business) {
            List<Venues> loadedVenue = [];
            business['turfs'].forEach((turf) {
              loadedVenue.add(
                Venues(
                  id: turf['_id'],
                  name: turf['name'],
                ),
              );
            });

            loadedBusiness.add(
              BusinessModel(
                mappingId: business['_id'],
                turfs: loadedVenue,
                roles: business['roles'],
                isActive: business['isActive'],
                owner: Owner(
                  id: business['owner']['_id'],
                  firstName: business['owner']['firstName'],
                  lastName: business['owner']['lastName'],
                  phone: business['owner']['phone'],
                  email: business['owner']['email'],
                  businessId: business['ownerBusinessId'],
                  avatar: business['owner']['avatar'] ?? '',
                ),
                employee: Employee(
                  id: business['employee'],
                  firstName: business['firstName'],
                  lastName: business['lastName'],
                  phone: business['phone'],
                  email: business['email'],
                  address: business['address'],
                ),
                aadharImage: business['aadharImage'] ?? '',
              ),
            );
          },
        );

        _businesses = List.from(loadedBusiness);
      } else {
        return false;
      }
    } catch (error) {
      print(error);
    }
  }
}
