import 'dart:convert';
import 'dart:io';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../employeeModule/models/business_model.dart';
import '../../employeeModule/provider/employee_provider.dart';
import '../../employeeModule/widgets/business_widget.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../homeModule/widgets/mainDrawer.dart';
import '../../pushNotification/localNotificationService.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../main.dart';

class EmployeeBusinessScreen extends StatefulWidget {
  const EmployeeBusinessScreen({Key? key}) : super(key: key);

  @override
  _EmployeeBusinessScreenState createState() => _EmployeeBusinessScreenState();
}

class _EmployeeBusinessScreenState extends State<EmployeeBusinessScreen> {
  late UserModal user;
  bool isLoading = false;

  final  storage = new LocalStorage('bysBusiness');
  String? notificationId;

  List<BusinessModel> listOfBusiness = [];
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  Future<void> fetchBusinesses() async {
    try {
      if (Provider.of<EmployeeManagementProvider>(context, listen: false)
          .employees
          .isEmpty) {
        setState(() {
          isLoading = true;
        });
        await Provider.of<EmployeeProvider>(context, listen: false)
            .fetchEmployeeBusinesses(
          accessToken: user.accessToken,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  handleNotificationClick(data) async {
    final notificationIdString = storage.getItem('fcmNotificationIds');
    if (notificationIdString != null) {
      notificationId = json.decode(notificationIdString);
      if (notificationId == data['notificationId']) {
        return;
      }
    }
    storage.setItem('fcmNotificationIds', json.encode(data['notificationId']));
    print('Handle  route');
    navigatorKey.currentState!.push(
      MaterialPageRoute(
        builder: (context) => BookingDescriptionScreen(
          user: user,
          bookingId: data['bookingId'],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;

    fetchBusinesses();
    LocalNotificationService.initialize(
        navigatorKey.currentContext!, handleNotificationClick);
    FirebaseMessaging.onBackgroundMessage(
        (RemoteMessage message) => handleNotificationClick(message));
    FirebaseMessaging.instance.getInitialMessage().then((message) async {
      if (message != null) {
        message.data['notificationId'] = message.messageId;
        handleNotificationClick(message.data);
      }
    });

    FirebaseMessaging.onMessage.listen((message) async {
      if (message.notification != null) {
        message.data['notificationId'] = message.messageId;
        LocalNotificationService.display(message);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((message) async {
      if (message.notification != null) {
        message.data['notificationId'] = message.messageId;
        handleNotificationClick(message.data);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;
    listOfBusiness = Provider.of<EmployeeProvider>(context).businesses;

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        toolbarHeight: deviceWidth * 0.15,
        elevation: 1,
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
        title: ListTile(
          contentPadding: EdgeInsets.all(0),
          title: Container(
            margin: EdgeInsets.only(right: deviceWidth * 0.08),
            child: Text(
              '${user.firstName} ${user.lastName}',
              style: Theme.of(context)
                  .textTheme
                  .displaySmall!
                  .copyWith(fontSize: textScaleFactor * displayMedium),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          subtitle: Container(
            margin: EdgeInsets.only(left: deviceWidth * 0.005),
            child: Text(
              '${user.email}',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: textScaleFactor * headline9,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
        ),
      ),
      // drawer: MainDrawer(),
      backgroundColor: Colors.white,
      body: SafeArea(
        child: isLoading
            ? Center(
                child: Platform.isAndroid
                    ? MaterialCircularLoader(deviceWidth * 0.07)
                    : CupertinoCircularLoader(15.0),
              )
            : listOfBusiness.isEmpty
                ? Container(
                    alignment: Alignment.center,
                    child: Text(
                      'Businesses not found!',
                      style: Theme.of(context).textTheme.displayLarge!.copyWith(
                            fontSize: textScaleFactor * 15,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                    ),
                  )
                : Container(
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      padding:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: deviceWidth * 0.05),
                          Text(
                            'Total Businesses (${listOfBusiness.length > 9 ? '${listOfBusiness.length}' : '0${listOfBusiness.length}'}) :',
                            style:
                                Theme.of(context).textTheme.displayMedium?.copyWith(
                                      fontSize: textScaleFactor * 15,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                          ),
                          SizedBox(height: deviceWidth * 0.05),
                          ...listOfBusiness.map(
                            (business) => BusinessWidget(
                              deviceWidth: deviceWidth,
                              textScale: textScaleFactor,
                              business: business,
                            ),
                          ),
                          SizedBox(height: deviceWidth * 0.07),
                        ],
                      ),
                    ),
                  ),
      ),
    );
  }
}
