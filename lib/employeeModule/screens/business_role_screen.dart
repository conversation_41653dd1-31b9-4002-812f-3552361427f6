import 'dart:io';

import 'package:bys_business/couponModule/screens/coupon_screen.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../bulkBookingModule/screens/bulk_booking_screen.dart';
import '../../common_function.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../employeeModule/models/business_model.dart';
import '../../employeeModule/screens/booking_screen.dart';
import '../../employeeModule/screens/employee_business_screen.dart';
import '../../employeeModule/screens/employee_profile_screen.dart';
import '../../homeModule/screens/reportScreen.dart';
import '../../moreModule.dart/screens/analyticsScreen.dart';
import '../../navigators.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import '../../notificationModule.dart/screens/notificationScreen.dart';
import '../../venueModule/screens/turfScreen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../../commonWidgets/andoridAppBar.dart';

class BusinessRoleScreen extends StatefulWidget {
  final BusinessModel business;
  const BusinessRoleScreen({Key? key, required this.business})
      : super(key: key);

  @override
  State<BusinessRoleScreen> createState() => _BusinessRoleScreenState();
}

class _BusinessRoleScreenState extends State<BusinessRoleScreen> {
  late UserModal user;
  bool isLoading = false;
  int notificationCount = 0;

  fetchNotification() async {
    try {
      if (notificationCount == 0) {
        setState(() {
          isLoading = false;
        });
        await Provider.of<NotificationProvider>(context, listen: false)
            .fetchEmployeeNotificationCount(
          accessToken: user.accessToken,
          owner: widget.business.owner.id,
          employee: widget.business.employee.id,
        );
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  svgIcon(int index) {
    return SvgPicture.asset(
      getIcons(widget.business.roles[index]),
      fit: BoxFit.fill,
      height: 30,
      color: Colors.black,
    );
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    notificationCount =
        Provider.of<NotificationProvider>(context, listen: false)
            .notificationCount;
    if (widget.business.roles.contains('Booking')) {
      // widget.business.roles.add('Notification');
    }
    widget.business.roles.add('Account');
    widget.business.roles = widget.business.roles.toSet().toList();
    widget.business.roles.sort((a, b) {
      return a.toLowerCase().compareTo(b.toLowerCase());
    });
    fetchNotification();
  }

  navigateTo(String role) {
    switch (role) {
      case 'Account':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => EmployeeProfileScreen(
              business: widget.business,
            ),
          ),
        );
      case 'Analytics':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AnalyticsScreen(
              user: user,
            ),
          ),
        );
      case 'Report':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ReportScreen(user: user),
          ),
        );
      case 'Coupon':
        return push(CouponScreen(user: user));
      case 'Bulk Booking':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BulkBookingScreen(),
          ),
        );
      // .then((value) => Navigator.of(context).pop()));

      case 'Booking':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BookingScreen(user: user),
          ),
        );

      case 'Venue':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TurfScreen(),
          ),
        );
      case 'Notification':
        return Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => NotificationScreen(user: user),
          ),
        );
    }
  }

  goBack() {
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => EmployeeBusinessScreen(),
        ),
        (route) => false);
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;
    notificationCount =
        Provider.of<NotificationProvider>(context).notificationCount;

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title:
          '${widget.business.owner.firstName} ${widget.business.owner.lastName}',
      deviceWidth: deviceWidth,
      elevation: 1.0,
      actionMethod: goBack,
      action: [
        Container(
          margin: EdgeInsets.only(right: deviceWidth * 0.025),
          child: GestureDetector(
            onTap: () => callLaunch(
              '+91${widget.business.owner.phone}',
            ),
            child: SvgPicture.asset('assets/svgIcons/Inquiry.svg'),
          ),
        )
      ],
    );

    return WillPopScope(
      onWillPop: () async {
        goBack();
        return true;
      },
      child: Scaffold(
        appBar: androidAppBar,
        backgroundColor: Colors.white,
        body: SafeArea(
          child: isLoading
              ? Center(
                  child: Platform.isAndroid
                      ? MaterialCircularLoader(deviceWidth * 0.07)
                      : CupertinoCircularLoader(15.0),
                )
              : Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: deviceWidth * 0.09,
                  ),
                  alignment: Alignment.center,
                  child: GridView.builder(
                    physics: BouncingScrollPhysics(),
                    itemCount: widget.business.roles.length,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      mainAxisSpacing: 0,
                      crossAxisSpacing: deviceWidth * 0.05,
                      crossAxisCount: 2,
                      childAspectRatio: 0.89,
                    ),
                    itemBuilder: (BuildContext context, int index) {
                      return GestureDetector(
                        onTap: () {
                          navigateTo(widget.business.roles[index]);
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                            top: deviceWidth * 0.03,
                            bottom: deviceWidth * 0.025,
                          ),
                          child: Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            color: Color(0xffd9f2db).withOpacity(0.5),
                            child: Container(
                              alignment: Alignment.center,
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  widget.business.roles[index]
                                              .toString()
                                              .toLowerCase() ==
                                          'notification'
                                      ? Stack(
                                          alignment: Alignment.center,
                                          // overflow: Overflow.visible,
                                          clipBehavior: Clip.none,
                                          children: [
                                            svgIcon(index),
                                            if (notificationCount > 0)
                                              Positioned(
                                                right: -3,
                                                top: -5,
                                                child: Container(
                                                  padding: EdgeInsets.all(2.0),
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      10.0,
                                                    ),
                                                    color: Theme.of(context)
                                                        .primaryColor,
                                                  ),
                                                  constraints: BoxConstraints(
                                                    minWidth: 18,
                                                    minHeight: 18,
                                                  ),
                                                  alignment: Alignment.center,
                                                  child: Text(
                                                    notificationCount
                                                        .toString(),
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      fontSize: notificationCount >=
                                                              100
                                                          ? textScaleFactor * 8
                                                          : notificationCount >=
                                                                  10
                                                              ? textScaleFactor *
                                                                  10
                                                              : textScaleFactor *
                                                                  12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        )
                                      : svgIcon(index),
                                  Text(
                                    widget.business.roles[index],
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: textScaleFactor * 14.5,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
        ),
      ),
    );
  }
}

getIcons(String role) {
  if (role == 'Venue') {
    return 'assets/svgIcons/turf.svg';
  } else if (role == 'Notification') {
    return 'assets/svgIcons/notification.svg';
  } else if (role == 'Booking') {
    return 'assets/svgIcons/Booking.svg';
  } else if (role == 'Account') {
    return 'assets/svgIcons/user2.svg';
  } else if (role == 'Analytics') {
    return 'assets/svgIcons/analytics.svg';
  } else if (role == 'Report') {
    return 'assets/svgIcons/collection.svg';
  } else if (role == 'Bulk Booking') {
    return 'assets/svgIcons/Booking.svg';
  } else if (role == 'Coupon') {
    return 'assets/svgIcons/coupon.svg';
  }
}
