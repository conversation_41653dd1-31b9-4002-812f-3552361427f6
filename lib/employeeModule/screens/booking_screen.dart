import 'dart:convert';
import 'dart:io';

import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/homeModule/widgets/badge_widget.dart';
import 'package:bys_business/homeModule/widgets/mainDrawer.dart';
import 'package:bys_business/moreModule.dart/provider/moreProvider.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_button_2.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../homeModule/screens/qrScannerScreen.dart';
import '../../homeModule/widgets/bookingStatusWidget.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import '../../homeModule/widgets/select_venue_sheet.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../moreModule.dart/screens/new_booking_screen.dart';
import '../../new_colors.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import '../../notificationModule.dart/screens/notificationScreen.dart';

class BookingScreen extends StatefulWidget {
  final UserModal user;

  const BookingScreen({Key? key, required this.user}) : super(key: key);

  @override
  _BookingScreenState createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;

  ScrollController scrollController = ScrollController();
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  List<BookingModel> listOfBookings = [];

  int selectedBooking = 0;

  List bookingIds = [];

  cancelConfirmation() {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title: 'Are you sure you want to cancel selected all bookings?',
            noText: 'Yes, Cancel',
            yesText: 'No',
            noFunction: () {
              pop();
              cancelBulkBooking();
            },
            yesFunction: () => pop(),
          )),
    );
  }

  cancelBulkBooking() async {
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .cancelBulkBooking(
      body: {
        'bookingIds': bookingIds,
      },
      accessToken: widget.user.accessToken,
    );

    if (response['success']) {
      bookingIds = [];
      selectedBooking = 0;
      setState(() {});
      fetchData();
    }
  }

  getStatusCount(int index) {
    int count = 0;
    if (index == 1) {
      count =
          count = Provider.of<HomeProvider>(context, listen: false).newCount;
    } else if (index == 2) {
      count = Provider.of<HomeProvider>(context, listen: false).completedCount;
    } else if (index == 3) {
      count = Provider.of<HomeProvider>(context, listen: false).cancelledCount;
    }
    return count;
  }

  fetchData() async {
    try {
      setState(() => isLoading = true);

      List<String> turfId = [];
      if (widget.user.business != null) {
        widget.user.business!.turfs.forEach((turf) => turfId.add(turf.id));
      }

      await Provider.of<HomeProvider>(context, listen: false)
          .fetchBookingsCount(
        businessId: widget.user.businessId,
        accessToken: widget.user.accessToken,
        role: 'Employee',
        turfs: turfId,
      );
//  await fetchAppUpdate();
      await fetchBookings();
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child:
            SelectVenueSheet(user: widget.user, bookingType: 'Single Booking'),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  scanQrCode() {
    push(QRScannerScreen()).then((value) async {
      if (value != null && value != '') {
        var data = json.decode(value);
        if (data['business'] != widget.user.businessId) {
          showSnackbar('This booking does not belong to your venue');
          return;
        }
        var bookingId = data['bookingId'];
        push(BookingDescriptionScreen(
            user: widget.user, comingFrom: 'qrScanner', bookingId: bookingId));
      }
    });
  }

  fetchBookings({bool refresh = true}) async {
    List<String> turfId = [];

    if (widget.user.business != null) {
      widget.user.business!.turfs.forEach((turf) => turfId.add(turf.id));
    }

    await Provider.of<HomeProvider>(context, listen: false)
        .fetchBusinessBookingNew(
      accessToken: widget.user.accessToken,
      businessId: widget.user.businessId,
      startDate: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        0,
        0,
        0,
      ).toString(),
      endDate: DateTime(
        DateTime(2200, 12, 12).year,
        DateTime(2200, 12, 12).month,
        DateTime(2200, 12, 12).day,
        23,
        59,
        59,
      ).toString(),
      status: 'Upcomings',
      turfs: turfId,
      role: 'Employee',
      refresh: refresh,
    );
  }

  lazyLoad() async {
    setState(() => lazyLoading = true);
    await fetchBookings(refresh: false);
    setState(() => lazyLoading = false);
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (scrollController.position.extentAfter == 0) lazyLoad();
    }
    return false;
  }

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfBookings = Provider.of<HomeProvider>(context).bookings;

    return Scaffold(
      key: _scaffoldKey,
      drawer: MainDrawer(),
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        children: [
          // SizedBox(height: dW * 0.05),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.04,
                right: dW * 0.04,
                top: MediaQuery.of(context).padding.top , bottom: dH * 0.02),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CustomButton2(
                      dW: dW,
                      svgIcon: 'menu',
                      onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    ),
                    SizedBox(width: dW * .05),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: dW * 0.52),
                          child: TextWidget(
                            title:
                                '${widget.user.firstName} ${widget.user.lastName}',
                            fontWeight: FontWeight.w600,
                            color: getGreyColor2(context),
                            maxLines: 2,
                            textOverflow: TextOverflow.ellipsis,
                          ),
                        ),
                        TextWidget(
                          title: widget.user.email,
                          fontWeight: FontWeight.w400,
                          color: getGreyColor2(context),
                        ),
                      ],
                    ),
                  ],
                ),
                Row(
                  children: [
                    CustomButton2(
                        dW: dW, svgIcon: 'scanner_green', onTap: scanQrCode),
                    SizedBox(width: dW * .03),
                    // BadgeWidget(
                    // value: Provider.of<NotificationProvider>(context)
                    //     .notificationCount,
                    // value: Provider.of<NotificationProvider>(context)
                    //     .fetchEmployeeNotificationCount(
                    //         accessToken: widget.user.accessToken,
                    //         employee: widget.user.business!.employee.id,
                    //         owner: widget.user.business!.owner.id),
                    // child:
                    CustomButton2(
                      dW: dW,
                      svgIcon: 'notification',
                      onTap: () => push(NotificationScreen(user: widget.user)),
                    ),
                    // )
                  ],
                )
              ],
            ),
          ),
          Expanded(
            child: isLoading
                ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                : NotificationListener<ScrollNotification>(
                    onNotification: _handleScrollNotification,
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      controller: scrollController,
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          BookingStatusWidget(
                            deviceWidth: dW,
                            getStatusCount: getStatusCount,
                            textScaleFactor: tS,
                            user: widget.user,
                          ),
                          SizedBox(height: dW * 0.05),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextWidget(
                                title: 'Upcoming Bookings',
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              GestureDetector(
                                onTap: () => push(NewBookingScreen()),
                                child: Container(
                                  color: Colors.transparent,
                                  padding: EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 2),
                                  child: TextWidget(
                                    title: 'View All',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: dW * 0.05),
                          if (listOfBookings.isEmpty)
                            EmptyListWidget(
                              text: 'No upcomings booking!!!',
                              topPadding: .4,
                            ),
                          if (selectedBooking > 0)
                            GestureDetector(
                              onTap: cancelConfirmation,
                              child: Text(
                                'Cancel Bookings : ${selectedBooking.toString()}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          if (selectedBooking > 0) SizedBox(height: dW * 0.04),
                          ...listOfBookings.map(
                            (booking) => BookingWidget(
                              fromViewAll: true,
                              onSelectionChanged: (int count, bool isSelected) {
                                setState(() {
                                  selectedBooking += count;
                                  if (isSelected) {
                                    bookingIds.add(booking.id);
                                  } else {
                                    bookingIds.remove(booking.id);
                                  }
                                });
                              },
                              booking: booking,
                              user: widget.user,
                              deviceWidth: dW,
                              textScaleFactor: tS,
                            ),
                          ),
                          SizedBox(height: dW * 0.04),
                          if (lazyLoading) lazyLoader(dW),
                          SizedBox(height: dW * 0.12),
                        ],
                      ),
                    ),
                  ),
          ),
          BottomAlignedWidget(
            dW: dW,
            dH: dH,
            child: CustomButton(
              width: dW,
              height: dW * 0.12,
              radius: 7,
              fontSize: 17,
              buttonText: 'Book Now',
              onPressed: openVenueBottomSheet,
            ),
          ),
        ],
      ),
    );
  }
}
