import 'dart:io';

import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/common_function.dart';

import '../../commonWidgets/custom_container.dart';
import '../../employeeModule/models/business_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EmployeeProfileScreen extends StatelessWidget {
  final BusinessModel business;
  EmployeeProfileScreen({Key? key, required this.business}) : super(key: key);

  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  Widget buildTitleAndSubtitle({
    required double deviceWidth,
    required double textScaleFactor,
    required BuildContext context,
    required String title,
    required String subtitle,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.025),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * 13,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xff737373),
                ),
          ),
          SizedBox(height: deviceWidth * 0.015),
          title == 'Mobile Number'
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    buildText(
                      textScaleFactor: textScaleFactor,
                      context: context,
                      subtitle: subtitle,
                    ),
                    SvgPicture.asset(
                      'assets/svgIcons/checked.svg',
                      height: 20,
                    ),
                  ],
                )
              : buildText(
                  textScaleFactor: textScaleFactor,
                  context: context,
                  subtitle: subtitle,
                )
        ],
      ),
    );
  }

  Widget buildText({
    required double textScaleFactor,
    required BuildContext context,
    required String subtitle,
  }) {
    return Text(
      subtitle,
      style: Theme.of(context).textTheme.displaySmall!.copyWith(
            fontSize: textScaleFactor * 14.5,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: iOSCondition(deviceHeight)
          ? screenBody(context)
          : SafeArea(child: screenBody(context)),
    );
  }

  Widget screenBody(BuildContext context) {
    return SizedBox(
      height: deviceHeight,
      width: deviceWidth,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
        child: Column(
          children: [
            // SizedBox(height: deviceWidth * 0.05),
            NewAppBar(dW: deviceWidth, title: 'Account'),
            SizedBox(height: deviceWidth * 0.05),
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: deviceWidth * 0.02),
        
                    //NAME
                    CustomContainer(
                      margin:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Name',
                            subtitle:
                                '${business.employee.firstName} ${business.employee.lastName}',
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Email',
                            subtitle: business.employee.email,
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Mobile Number',
                            subtitle: '+91 - ${business.employee.phone}',
                          ),
                          buildTitleAndSubtitle(
                            deviceWidth: deviceWidth,
                            textScaleFactor: textScaleFactor,
                            context: context,
                            title: 'Address',
                            subtitle: business.employee.address,
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(
                                vertical: deviceWidth * 0.025),
                            child: Text(
                              'Privileges',
                              style:
                                  Theme.of(context).textTheme.displaySmall!.copyWith(
                                        fontSize: textScaleFactor * 13.2,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xff737373),
                                      ),
                            ),
                          ),
                          ...business.roles
                              .asMap()
                              .map(
                                (index, role) => MapEntry(
                                  index,
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: deviceWidth * 0.01),
                                    child: Text(
                                      '${index + 1}. $role',
                                      style: Theme.of(context)
                                          .textTheme
                                          .displaySmall!
                                          .copyWith(
                                            fontSize: textScaleFactor * 14.5,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                    ),
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          if (business.turfs.isNotEmpty) ...[
                            SizedBox(height: deviceWidth * 0.01),
                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.025),
                              child: Text(
                                'Assigned Venues',
                                style: Theme.of(context)
                                    .textTheme
                                    .displaySmall!
                                    .copyWith(
                                      fontSize: textScaleFactor * 13.2,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xff737373),
                                    ),
                              ),
                            ),
                            ...business.turfs
                                .asMap()
                                .map(
                                  (index, turf) => MapEntry(
                                    index,
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          vertical: deviceWidth * 0.01),
                                      child: Text(
                                        '${index + 1}. ${turf.name}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .displaySmall!
                                            .copyWith(
                                              fontSize: textScaleFactor * 14.5,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.black,
                                            ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                )
                                .values
                                .toList(),
                          ],
                        ],
                      ),
                    ),
        
                    if (business.aadharImage != '')
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.05,
                          vertical: deviceWidth * 0.03,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.025),
                              child: Text(
                                'Aadhar Image',
                                style: Theme.of(context)
                                    .textTheme
                                    .displaySmall!
                                    .copyWith(
                                      fontSize: textScaleFactor * 13,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xff737373),
                                    ),
                              ),
                            ),
                            ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: Container(
                                width: deviceWidth,
                                height: deviceWidth * 0.45,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.black,
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: CachedNetworkImage(
                                    repeat: ImageRepeat.repeat,
                                    fit: BoxFit.cover,
                                    imageUrl: business.aadharImage,
                                    placeholder: (_, __) => Image.asset(
                                      'assets/images/placeholder.jpg',
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.08),
                          ],
                        ),
                      ),
        
                    SizedBox(height: deviceWidth * 0.05),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
