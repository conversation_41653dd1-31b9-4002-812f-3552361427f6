import 'package:bys_business/withdrawalModule/models/fund_account_model.dart';
import 'package:bys_business/withdrawalModule/models/withdrawal_model.dart';
import 'package:flutter/material.dart';

import '../../api.dart';
import '../../http_helper.dart';

class WithdrawalProvider with ChangeNotifier {
  List<Withdrawal> _withdrawals = [];

  List<Withdrawal> get withdrawals {
    return [..._withdrawals];
  }

  List<FundAccount> _fundAccounts = [];

  List<FundAccount> get fundAccounts {
    return [..._fundAccounts];
  }

  double totalEarnings = 0;

  Future<bool> fetchWithdrawals({
    required String accessToken,
    required String startDate,
    required String endDate,
    bool refresh = false,
  }) async {
    try {
      if (refresh) {
        _withdrawals = [];
      }

      final url =
          '${webApi['domain']}${endPoint['fetchWithdrawals']}?limit=10&skip=${_withdrawals.length}&startDate=$startDate&endDate=$endDate';

      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );
      List<Withdrawal> loadedWithdrawals = [];

      if (response['success'] && response['result'] != null) {
        response['result'].forEach((withdrawal) {
          loadedWithdrawals.add(Withdrawal.jsonToWithdrawal(withdrawal));
        });
        if (_withdrawals.isEmpty) {
          _withdrawals = List.from(loadedWithdrawals);
          totalEarnings = response['totalEarnings'].toDouble();
        } else if (loadedWithdrawals.isNotEmpty) {
          _withdrawals.addAll(loadedWithdrawals);
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  Future validateVPA({
    required String accessToken,
    required String vpa,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['validateVPA']}';

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: {'vpa': vpa},
        accessToken: accessToken,
      );

      if (response['success'] &&
          response['result'] != null &&
          response['result']['success']) {
        return response['result'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  Future validateBankAccount({
    required String accessToken,
    required String name,
    required String ifsc,
    required String accNo,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['validateBankAccount']}';

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: {'name': name, 'ifsc': ifsc, 'accNo': accNo},
        accessToken: accessToken,
      );

      return response;
    } catch (error) {
      print(error);
      return {
        'success': false,
        'result': {
          'error': {'code': 'Internal_Error'}
        }
      };
    }
  }

  Future createWithdrawal({
    required String accessToken,
    required Map<dynamic, dynamic> body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['createWithdrawal']}';

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        Withdrawal withdrawal = Withdrawal.jsonToWithdrawal(response['result']);
        _withdrawals.insert(0, withdrawal);
        notifyListeners();
      }
      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'error': "ERROR OCCURED!!!"};
    }
  }

  Future<bool> fetchFundAccounts({required String accessToken}) async {
    try {
      _fundAccounts = [];

      final url = '${webApi['domain']}${endPoint['fetchFundAccounts']}';

      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );
      List<FundAccount> loadedFundAcc = [];

      if (response['success'] && response['result'] != null) {
        response['result'].forEach((fundAccount) {
          loadedFundAcc.add(FundAccount.jsonToFundAcc(fundAccount));
        });
        if (_fundAccounts.isEmpty) {
          _fundAccounts = List.from(loadedFundAcc);
        } else if (loadedFundAcc.isNotEmpty) {
          _fundAccounts.addAll(loadedFundAcc);
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }
}
