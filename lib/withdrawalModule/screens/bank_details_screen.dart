import 'package:bys_business/withdrawalModule/models/fund_account_model.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';
import '../providers/withdrawal_provider.dart';
import 'withdrawal_summary_screen.dart';

class BankDetailsScreen extends StatefulWidget {
  final String selectedOption;
  final String withdrawalAmount;
  final FundAccount? fundAccount;
  BankDetailsScreen({
    Key? key,
    required this.selectedOption,
    required this.withdrawalAmount,
    this.fundAccount,
  }) : super(key: key);

  @override
  State<BankDetailsScreen> createState() => _BankDetailsScreenState();
}

class _BankDetailsScreenState extends State<BankDetailsScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  late UserModal user;

  GlobalKey<FormState> _formKey = GlobalKey();

  TextEditingController accountNoController = TextEditingController();
  TextEditingController ifscController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController confirmAccountController = TextEditingController();

  proceed() async {
    hideKeyBoard(context);
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      if (isLoading) {
        return;
      }
      setState(() {
        isLoading = true;
      });

      final result =
          await Provider.of<WithdrawalProvider>(context, listen: false)
              .validateBankAccount(
        accessToken: user.accessToken,
        accNo: confirmAccountController.text.trim(),
        ifsc: ifscController.text.trim(),
        name: nameController.text.trim(),
      );

      if (result['success']) {
        push(
          WithdrawalSummaryScreen(
            withdrawalAmount: double.parse(widget.withdrawalAmount),
            selectedOption: widget.selectedOption,
            bankDetails: {
              'accNo': confirmAccountController.text.trim(),
              'ifsc': ifscController.text.trim(),
              'name': nameController.text.trim(),
              'bankName': result['result']['bank_account']['bank_name'] ?? ''
            },
          ),
        );
      } else {
        if (result['result']['error']['code'] == 'BAD_REQUEST_ERROR') {
          showSnackbar(
              result['result']['error']['description'] ?? 'Invalid IFSC Code');
        } else if (result['result']['error']['code'] == 'Internal_Error') {
          showSnackbar('Unable to verify bank account');
        }
      }
    } catch (e) {
      showSnackbar('Unable to verify bank account');
      print(e);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    nameController.text = '${user.firstName} ${user.lastName}';
    if (widget.fundAccount != null) {
      accountNoController.text = widget.fundAccount!.bankDetails!.accNo;
      confirmAccountController.text = widget.fundAccount!.bankDetails!.accNo;
      ifscController.text = widget.fundAccount!.bankDetails!.ifsc;
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      appBar: CustomAppBar(dW: dW, title: 'Bank Details'),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: dW * 0.05),
                      TextWidget(title: 'Add Bank Details', fontSize: 16),
                      SizedBox(height: dW * 0.05),
                      CustomContainer(
                        vPadding: 0.045,
                        hPadding: .03,
                        borderColor: Colors.transparent,
                        child: Column(
                          children: [
                            CustomTextFieldWithLabel(
                              label: 'Account Number',
                              controller: accountNoController,
                              hintText: 'Enter account number',
                              inputType: TextInputType.number,
                              inputFormatter: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              obscureText: true,
                              maxLines: 1,
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please enter account number';
                                }
                              },
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Confirm Account Number',
                              controller: confirmAccountController,
                              hintText: 'Confirm account number',
                              inputType: TextInputType.number,
                              inputFormatter: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              enabled:
                                  accountNoController.text.trim().isNotEmpty,
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please validate account number';
                                } else if (value.trim() !=
                                    accountNoController.text.trim()) {
                                  return 'Please enter correct account number';
                                }
                              },
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'IFSC Code',
                              controller: ifscController,
                              hintText: 'Enter IFSC Code',
                              textCapitalization: TextCapitalization.characters,
                              inputType: TextInputType.text,
                              maxLength: 11,
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please enter IFSC Code';
                                } else if (value.trim().length != 11) {
                                  return 'IFSC Code length cannot be less than 11';
                                }
                              },
                              inputFormatter: [
                                FilteringTextInputFormatter.deny(RegExp('[ ]'))
                              ],
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Account Holder Name',
                              controller: nameController,
                              textCapitalization: TextCapitalization.words,
                              hintText: 'Enter holder name',
                              inputType: TextInputType.text,
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please enter account holder name';
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.1),
                    ],
                  ),
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Verify & Continue',
                onPressed: proceed,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
