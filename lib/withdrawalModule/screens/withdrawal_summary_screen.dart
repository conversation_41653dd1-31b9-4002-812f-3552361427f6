import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../navigators.dart';
import '../../withdrawalModule/providers/withdrawal_provider.dart';
import '../../withdrawalModule/screens/withdraw_success_screen.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/screens/homeScreen.dart';

class WithdrawalSummaryScreen extends StatefulWidget {
  final double withdrawalAmount;
  final String selectedOption;
  final Map? upi;
  final Map? bankDetails;
  WithdrawalSummaryScreen({
    Key? key,
    required this.withdrawalAmount,
    required this.selectedOption,
    this.upi,
    this.bankDetails,
  }) : super(key: key);

  @override
  State<WithdrawalSummaryScreen> createState() =>
      _WithdrawalSummaryScreenState();
}

class _WithdrawalSummaryScreenState extends State<WithdrawalSummaryScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;

  late UserModal user;

  withdraw() async {
    try {
      if (isLoading) return;

      setState(() => isLoading = true);

      Map body = {
        "business": user.businessId,
        "withdrawalOption": widget.selectedOption,
        "amount": widget.withdrawalAmount,
      };

      if (widget.selectedOption == 'UPI') {
        body['upi'] = widget.upi!;
      } else {
        body['bankDetails'] = widget.bankDetails;
      }

      final result =
          await Provider.of<WithdrawalProvider>(context, listen: false)
              .createWithdrawal(
        accessToken: user.accessToken,
        body: body,
      );

      if (result['success']) {
        Provider.of<Auth>(context, listen: false)
            .updateWallet(widget.withdrawalAmount);

        pushAndRemoveUntil(WithdrawalSuccessScreen());
      } else {
        if (result['error'] == 'insufficient_funds') {
          showDialog(
            context: context,
            builder: ((context) => CustomDialog(
                title:
                    'Cannot process this withdrawal due to insufficient balance in admin\'s account. Contact Admin: ${Provider.of<Auth>(context, listen: false).adminContact}',
                noText: 'Okay',
                yesText: 'Go to Home',
                noFunction: () {
                  pop();
                },
                yesFunction: () {
                  pushAndRemoveUntil(HomeScreen());
                })),
          );
        } else {
          showSnackbar(result['error']);
        }
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      appBar: CustomAppBar(dW: dW, title: 'Withdrawal Summary'),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: dW * 0.05),
                    TextWidget(
                      title:
                          '*Your money will be credited to your account within 2-3 business days.',
                      color: Color(0xffACACB4),
                      fontSize: 12,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: dW * 0.05),
                    CustomContainer(
                      borderColor: Colors.transparent,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildWithdrawalDetailRow(
                            dW: dW,
                            title: 'Withdrawal Amount',
                            value:
                                '\u20b9${convertAmountString(widget.withdrawalAmount)}',
                            textColor: getThemeColor(),
                            fontWeight: FontWeight.w600,
                          ),
                          buildWithdrawalDetailRow(
                            dW: dW,
                            title: 'Balance Amount',
                            value:
                                '\u20b9${convertAmountString(user.wallet - widget.withdrawalAmount)}',
                            textColor: Colors.black45,
                          ),
                          buildWithdrawalDetailRow(
                            dW: dW,
                            title: 'Selected Option',
                            value: widget.selectedOption,
                          ),
                          DividerWidget(top: 0, bottom: 5),
                          TextWidget(
                            title: widget.selectedOption == 'UPI'
                                ? 'UPI ID'
                                : 'Bank Details',
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                          ),
                          SizedBox(height: dW * 0.025),
                          if (widget.selectedOption == 'UPI') ...[
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'Name',
                              value: widget.upi!['name'],
                            ),
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'VPA',
                              value: widget.upi!['vpa'],
                            ),
                          ],
                          if (widget.selectedOption != 'UPI') ...[
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'Bank Name',
                              value: widget.bankDetails!['bankName'],
                            ),
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'Account No.',
                              value: widget.bankDetails!['accNo'],
                            ),
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'IFSC Code',
                              value: widget.bankDetails!['ifsc'],
                            ),
                            buildWithdrawalDetailRow(
                              dW: dW,
                              title: 'Name',
                              value: widget.bankDetails!['name'],
                            ),
                          ],
                          DividerWidget(top: 0, bottom: 5),
                          buildWithdrawalDetailRow(
                            dW: dW,
                            title: 'Date',
                            value: DateFormat('dd MMM yyyy')
                                .format(DateTime.now()),
                            bottomMargin: 0,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: dW * 0.1)
                  ],
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Withdraw',
                onPressed: withdraw,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget buildWithdrawalDetailRow({
  required String title,
  required String value,
  required double dW,
  Color textColor = Colors.black,
  FontWeight fontWeight = FontWeight.normal,
  double bottomMargin = 0.025,
  bool summaryScreen = true,
}) {
  return Container(
    margin: EdgeInsets.only(bottom: dW * bottomMargin),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextWidget(title: title, fontSize: summaryScreen ? 15 : 13),
        Container(
          constraints: BoxConstraints(maxWidth: dW * 0.5),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: TextWidget(
              title: value,
              fontSize: summaryScreen ? 16 : 14,
              fontWeight: fontWeight,
              color: textColor,
            ),
          ),
        ),
      ],
    ),
  );
}
