import 'package:flutter/material.dart';

import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import '../../withdrawalModule/screens/withdrawal_screen.dart';
import '../../common_function.dart';

class WithdrawalSuccessScreen extends StatefulWidget {
  WithdrawalSuccessScreen();

  @override
  State<WithdrawalSuccessScreen> createState() =>
      _WithdrawalSuccessScreenState();
}

class _WithdrawalSuccessScreenState extends State<WithdrawalSuccessScreen> {
  double dW = 0;
  double dH = 0;

  navigateToMainScreen() {
    pushAndRemoveUntil(WithdrawalScreen());
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 2))
        .then((value) => navigateToMainScreen());
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;

    return WillPopScope(
      onWillPop: () async {
        navigateToMainScreen();
        return true;
      },
      child: Scaffold(
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return Container(
      height: dH,
      width: dW,
      alignment: Alignment.center,
      margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
      child: Column(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/images/success.png', height: dW * 0.22),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.07),
                  child: TextWidget(
                    title: 'You have successfully withdrawn your money!',
                    textAlign: TextAlign.center,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: dW * 0.1),
            child: TextWidget(
              title:
                  'Your money will be credited to your account within 2-3 business days.',
              color: Color(0xffACACB4),
              fontSize: 14,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
