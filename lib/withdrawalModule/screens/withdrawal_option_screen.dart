import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/withdrawalModule/models/fund_account_model.dart';
import 'package:bys_business/withdrawalModule/providers/withdrawal_provider.dart';
import 'package:bys_business/withdrawalModule/screens/bank_details_screen.dart';
import 'package:bys_business/withdrawalModule/screens/withdrawal_summary_screen.dart';
import 'package:bys_business/withdrawalModule/widgets/fund_account_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';

class WithdrawalOptionScreen extends StatefulWidget {
  final String withdrawalAmount;
  WithdrawalOptionScreen({Key? key, required this.withdrawalAmount})
      : super(key: key);

  @override
  State<WithdrawalOptionScreen> createState() => _WithdrawalOptionScreenState();
}

class _WithdrawalOptionScreenState extends State<WithdrawalOptionScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  bool fetchingFundAcc = false;

  List<FundAccount> listOfFundAccount = [];

  late UserModal user;
  late WithdrawalProvider withdrawalProvider;

  FundAccount? selectedFundAccount;

  TextEditingController upiController = TextEditingController();
  String selectedOption = '';

  Widget buildOptionWidget({required String title}) {
    return GestureDetector(
      onTap: () => setState(() {
        selectedOption = title;
        selectedFundAccount = null;
      }),
      child: CustomContainer(
        vPadding: 0.045,
        hPadding: 0.035,
        radius: selectedOption == 'UPI' ? 10 : 8,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    AssetSvgIcon(
                      iconName: title == 'UPI' ? 'upi' : 'bank',
                      height: 22,
                    ),
                    SizedBox(width: dW * 0.03),
                    TextWidget(
                      title: title,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.all(selectedOption != title ? 10 : 2),
                  decoration: BoxDecoration(
                    border: Border.all(color: Color(0xff838383)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: selectedOption == title
                      ? CircleAvatar(
                          radius: 9,
                          backgroundColor: getThemeColor(),
                        )
                      : null,
                )
              ],
            ),
            if (selectedOption == 'UPI' && title == 'UPI') ...[
              SizedBox(height: dW * 0.04),
              CustomTextFieldWithLabel(
                label: '',
                controller: upiController,
                hintText: 'Enter UPI ID',
                inputType: TextInputType.emailAddress,
                onChanged: (value) => setState(() {}),
              ),
            ],
          ],
        ),
      ),
    );
  }

  isValid() {
    if (selectedFundAccount != null) {
      return true;
    } else if (selectedOption == '') {
      return false;
    } else if (selectedOption == 'UPI' && upiController.text.trim().isEmpty) {
      return false;
    } else {
      return true;
    }
  }

  validateUPI() async {
    try {
      hideKeyBoard(context);
      if (isLoading) return;

      setState(() {
        isLoading = true;
      });

      final result = await withdrawalProvider.validateVPA(
        accessToken: user.accessToken,
        vpa: upiController.text.trim(),
      );

      if (result != null) {
        push(
          WithdrawalSummaryScreen(
            withdrawalAmount: double.parse(widget.withdrawalAmount),
            selectedOption: selectedOption,
            upi: {
              'vpa': result['vpa'] ?? '',
              'name': result['customer_name'] ?? '',
            },
          ),
        );
      } else {
        showSnackbar('Invalid UPI ID');
      }
    } catch (e) {
      showSnackbar('Unable to verify UPI');
      print(e);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  proceed() {
    if (selectedFundAccount == null) {
      if (selectedOption == 'UPI') {
        validateUPI();
      } else {
        goToBankDetails();
      }
    } else {
      if (selectedFundAccount!.type == 'UPI') {
        push(
          WithdrawalSummaryScreen(
            withdrawalAmount: double.parse(widget.withdrawalAmount),
            selectedOption: 'UPI',
            upi: {
              'vpa': selectedFundAccount!.upi!.vpa,
              'name': selectedFundAccount!.upi!.name,
            },
          ),
        );
      } else {
        goToBankDetails();
      }
    }
  }

  goToBankDetails() {
    push(
      BankDetailsScreen(
        withdrawalAmount: widget.withdrawalAmount,
        selectedOption:
            selectedFundAccount == null ? selectedOption : 'Bank Transfer',
        fundAccount: selectedFundAccount,
      ),
    );
  }

  fetchFundAccount() async {
    try {
      setState(() => fetchingFundAcc = true);

      await withdrawalProvider.fetchFundAccounts(accessToken: user.accessToken);
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => fetchingFundAcc = false);
    }
  }

  @override
  void initState() {
    super.initState();
    withdrawalProvider =
        Provider.of<WithdrawalProvider>(context, listen: false);
    user = Provider.of<Auth>(context, listen: false).user;
    fetchFundAccount();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfFundAccount = Provider.of<WithdrawalProvider>(context).fundAccounts;

    return Scaffold(
      appBar: CustomAppBar(dW: dW, title: 'Withdrawal Option'),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: fetchingFundAcc
            ? CircularLoader(android: dW * 0.002, iOS: dW * 0.035)
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: dW * 0.05),
                          buildOptionWidget(title: 'UPI'),
                          SizedBox(height: dW * 0.05),
                          buildOptionWidget(title: 'Bank Transfer'),
                          SizedBox(height: dW * 0.05),
                        ],
                      ),
                    ),
                  ),
                  if (listOfFundAccount.isNotEmpty) ...[
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: TextWidget(
                        title: 'Withdrawal Accounts',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: blackColor5,
                      ),
                    ),
                    SizedBox(height: dW * 0.02),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SizedBox(height: dW * 0.03),
                            ...listOfFundAccount.map(
                              (fundAccount) => FundAccountWidget(
                                fundAccount: fundAccount,
                                selectAccount: () {
                                  setState(() {
                                    selectedOption = '';
                                    selectedFundAccount = fundAccount;
                                  });
                                },
                                selectedFundAccount: selectedFundAccount,
                                dW: dW,
                              ),
                            ),
                            SizedBox(height: dW * 0.06),
                          ],
                        ),
                      ),
                    ),
                  ],
                  BottomAlignedWidget(
                    dW: dW,
                    dH: dH,
                    child: CustomButton(
                      width: dW,
                      height: dW * 0.14,
                      buttonText: selectedOption == 'UPI'
                          ? 'Verify & Continue'
                          : 'Continue',
                      onPressed: isValid() ? proceed : null,
                      isLoading: isLoading,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
