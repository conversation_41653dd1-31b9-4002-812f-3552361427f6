import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/withdrawalModule/screens/withdrawal_option_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';

class WithdrawalAmountScreen extends StatefulWidget {
  final double balanceAmount;
  WithdrawalAmountScreen({Key? key, required this.balanceAmount})
      : super(key: key);

  @override
  State<WithdrawalAmountScreen> createState() => _WithdrawalAmountScreenState();
}

class _WithdrawalAmountScreenState extends State<WithdrawalAmountScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  int minimumWithdrawalAmount = 500;

  TextEditingController amountController = TextEditingController();

  proceed() {
    if (double.parse(amountController.text) > widget.balanceAmount) {
      return showSnackbar('Amount cannot be greater than withdrawal amount');
    }
    push(
      WithdrawalOptionScreen(withdrawalAmount: amountController.text),
    );
  }

  setAmount() {
    minimumWithdrawalAmount =
        Provider.of<Auth>(context, listen: false).minimumWithdrawalAmount;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    setAmount();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Scaffold(
      appBar: CustomAppBar(dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: dW * 0.05),
                    Container(
                      decoration: BoxDecoration(
                        color: Color(0xffE3FEDB),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: dW * 0.04,
                        vertical: dW * 0.037,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextWidget(title: 'Withdrawal Amount', fontSize: 16),
                          Container(
                            constraints: BoxConstraints(maxWidth: dW * 0.42),
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: TextWidget(
                                title: amountController.text.isEmpty
                                    ? '\u20b9${widget.balanceAmount.toStringAsFixed(2)}'
                                    : double.parse(amountController.text) >
                                            widget.balanceAmount
                                        ? '\u20b90'
                                        : '\u20b9${(widget.balanceAmount - double.parse(amountController.text)).toStringAsFixed(2)}',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: dW * 0.05),
                    CustomContainer(
                      vPadding: 0.045,
                      hPadding: .03,
                      borderColor: Colors.transparent,
                      child: CustomTextFieldWithLabel(
                        label: 'Enter Amount',
                        controller: amountController,
                        hintText: '\u20b900',
                        inputType: TextInputType.number,
                        prefixIcon: amountController.text.isNotEmpty
                            ? Icon(
                                Icons.currency_rupee,
                                color: Colors.black,
                              )
                            : null,
                        inputFormatter: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        onChanged: (value) => setState(() {}),
                      ),
                    ),
                    SizedBox(height: dW * 0.05),
                    TextWidget(
                      title:
                          '1. Minimial withdrawal amount is $minimumWithdrawalAmount.',
                      color: Colors.black87,
                    ),
                    SizedBox(height: dW * 0.01),
                    TextWidget(
                      title:
                          '2. Please review your amount before you withdraw.',
                      color: Colors.black87,
                    ),
                  ],
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Continue',
                onPressed: amountController.text.isEmpty ||
                        double.parse(amountController.text) <
                            minimumWithdrawalAmount
                    ? null
                    : proceed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
