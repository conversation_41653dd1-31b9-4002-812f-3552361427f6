import 'package:bys_business/bulkBookingModule/screens/bulk_booking_description_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import '../../homeModule/screens/homeScreen.dart';
import '../../navigators.dart';
import '../../withdrawalModule/models/withdrawal_model.dart';
import '../../withdrawalModule/providers/withdrawal_provider.dart';
import '../../withdrawalModule/screens/withdrawal_amount_screen.dart';
import '../../withdrawalModule/widgets/withdrawal_widget.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_function.dart';
import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../withdrawalModule/widgets/withdrawal_statistics_widget.dart';
import '../widgets/date_filter_bottom_sheet.dart';

class WithdrawalScreen extends StatefulWidget {
  const WithdrawalScreen({Key? key}) : super(key: key);

  @override
  WithdrawalScreenState createState() => WithdrawalScreenState();
}

class WithdrawalScreenState extends State<WithdrawalScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  bool lazyLoading = false;

  List<Withdrawal> listOfWithdrawals = [];

  late UserModal user;
  late WithdrawalProvider withdrawalProvider;

  final ScrollController _scrollController = ScrollController();

  DateTime? startDate;
  DateTime? endDate;

  fetchData({bool refresh = false}) async {
    try {
      if (!refresh) setState(() => isLoading = true);

      Provider.of<Auth>(context, listen: false)
          .fetchUserWallet(accessToken: user.accessToken);

      await withdrawalProvider.fetchWithdrawals(
        accessToken: user.accessToken,
        refresh: true,
        startDate: startDate == null ? '' : startDate.toString(),
        endDate: endDate == null ? '' : endDate.toString(),
      );
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  applyFilter({DateTime? stDate, DateTime? edDate, required apply}) {
    if (apply) {
      startDate = stDate;
      endDate = edDate;
    } else {
      startDate = null;
      endDate = null;
    }
    fetchData();
  }

  openFilter() {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      context: context,
      builder: ((context) => DateFilterBottomSheet(
            startDate: startDate,
            endDate: endDate,
          )),
    ).then((value) {
      if (value != null) {
        applyFilter(
          apply: value['apply'],
          stDate: value['startDate'],
          edDate: value['endDate'],
        );
      }
    });
  }

  lazyLoad() async {
    if (_scrollController.position.extentAfter == 0) {
      setState(() => lazyLoading = true);
      await withdrawalProvider.fetchWithdrawals(
        accessToken: user.accessToken,
        startDate: startDate == null ? '' : startDate.toString(),
        endDate: endDate == null ? '' : endDate.toString(),
      );
      setState(() => lazyLoading = false);
    }
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      lazyLoad();
    }
    return false;
  }

  navigateHome() {
    pushAndRemoveUntil(HomeScreen());
  }

  navigateToBooking(Withdrawal withdrawal) {
    if (withdrawal.booking != '') {
      push(
        BookingDescriptionScreen(
          user: Provider.of<Auth>(context, listen: false).user,
          bookingId: withdrawal.booking,
        ),
      );
    } else if (withdrawal.bulkBooking != '') {
      push(BulkBookingDescriptionScreen(bulkBookingId: withdrawal.bulkBooking));
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    withdrawalProvider =
        Provider.of<WithdrawalProvider>(context, listen: false);
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfWithdrawals =
        Provider.of<WithdrawalProvider>(context, listen: false).withdrawals;

    return WillPopScope(
      onWillPop: () async {
        navigateHome();
        return true;
      },
      child: Scaffold(
        appBar: CustomAppBar(
          dW: dW,
          title: 'Money Withdrawal',
          actionMethod: navigateHome,
        ),
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: dW * 0.05),
                WithdrawalStatisticsWidget(
                  dW: dW,
                  tS: tS,
                  totalEarnings: withdrawalProvider.totalEarnings,
                  withdrawalAmount: user.wallet,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextWidget(
                      title: 'Withdrawal History',
                      fontSize: 20,
                    ),
                    AssetSvgIcon(iconName: 'new_filter', onTap: openFilter),
                  ],
                ),
                SizedBox(height: dW * 0.03),
              ],
            ),
          ),
          isLoading
              ? Container(
                  margin: EdgeInsets.only(top: dW * 0.5),
                  child: CircularLoader(android: dW * 0.002, iOS: dW * 0.035),
                )
              : Expanded(
                  child: RefreshIndicator(
                    color: getThemeColor(),
                    onRefresh: () => fetchData(refresh: true),
                    child: NotificationListener<ScrollNotification>(
                      onNotification: _handleScrollNotification,
                      child: ListView(
                        controller: _scrollController,
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics(),
                        ),
                        children: [
                          SizedBox(height: dW * 0.05),
                          if (listOfWithdrawals.isEmpty)
                            EmptyListWidget(
                              text:
                                  'You Don\'t have any withdrawal history yet.',
                              topPadding: 0.18,
                              image: 'no_transaction',
                            ),
                          if (listOfWithdrawals.isNotEmpty)
                            ...listOfWithdrawals.map(
                              (withdrawal) => GestureDetector(
                                onTap: () => navigateToBooking(withdrawal),
                                child: WithdrawalWidget(
                                  dW: dW,
                                  withdrawal: withdrawal,
                                ),
                              ),
                            ),
                          if (lazyLoading)
                            Container(
                              margin: EdgeInsets.only(top: dW * 0.06),
                              child:
                                  CircularLoader(android: dW * 0.07, iOS: 11),
                            ),
                          SizedBox(height: dW * 0.18),
                        ],
                      ),
                    ),
                  ),
                ),
          if (!isLoading)
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Withdraw',
                onPressed: () {
                  push(WithdrawalAmountScreen(balanceAmount: user.wallet));
                },
              ),
            ),
        ],
      ),
    );
  }
}
