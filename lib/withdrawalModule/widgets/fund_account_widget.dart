import 'package:bys_business/withdrawalModule/models/fund_account_model.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../screens/withdrawal_summary_screen.dart';

class FundAccountWidget extends StatelessWidget {
  final FundAccount fundAccount;
  final FundAccount? selectedFundAccount;
  final Function selectAccount;
  final double dW;
  const FundAccountWidget({
    Key? key,
    required this.fundAccount,
    required this.selectAccount,
    required this.selectedFundAccount,
    required this.dW,
  }) : super(key: key);

  Widget buildPreviousOptionWidget(FundAccount fundAccount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            AssetSvgIcon(
              iconName: fundAccount.type == 'UPI' ? 'upi' : 'bank',
              height: 22,
            ),
            SizedBox(width: dW * 0.03),
            TextWidget(
              title: fundAccount.type == 'UPI' ? 'UPI' : 'Bank Transfer',
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.all(
            selectedFundAccount == null ||
                    selectedFundAccount!.id != fundAccount.id
                ? 10
                : 2,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xff838383)),
            borderRadius: BorderRadius.circular(50),
          ),
          child: selectedFundAccount != null &&
                  selectedFundAccount!.id == fundAccount.id
              ? CircleAvatar(
                  radius: 9,
                  backgroundColor: getThemeColor(),
                )
              : null,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => selectAccount(),
      child: CustomContainer(
        margin: EdgeInsets.only(bottom: dW * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildPreviousOptionWidget(fundAccount),
            DividerWidget(bottom: 5),
            if (fundAccount.type != 'UPI') ...[
              buildWithdrawalDetailRow(
                title: 'Account No:',
                value: fundAccount.bankDetails!.accNo,
                dW: dW,
                summaryScreen: false,
              ),
              buildWithdrawalDetailRow(
                title: 'IFSC Code:',
                value: fundAccount.bankDetails!.ifsc,
                dW: dW,
                summaryScreen: false,
              ),
              buildWithdrawalDetailRow(
                title: 'Bank Name:',
                value: fundAccount.bankDetails!.bankName,
                dW: dW,
                summaryScreen: false,
              ),
            ],
            if (fundAccount.type == 'UPI') ...[
              buildWithdrawalDetailRow(
                dW: dW,
                title: 'Name',
                value: fundAccount.upi!.name,
                summaryScreen: false,
              ),
              buildWithdrawalDetailRow(
                dW: dW,
                title: 'VPA',
                value: fundAccount.upi!.vpa,
                summaryScreen: false,
              ),
            ],
            buildWithdrawalDetailRow(
              title: 'Last Withdrawal Amount:',
              value:
                  '\u20b9 ${convertAmountString(fundAccount.lastWithdrawalAmount)}',
              dW: dW,
              summaryScreen: false,
            ),
            buildWithdrawalDetailRow(
              title: 'Date',
              value: DateFormat('dd MMM yyyy').format(fundAccount.updatedAt),
              dW: dW,
              summaryScreen: false,
            ),
          ],
        ),
      ),
    );
  }
}
