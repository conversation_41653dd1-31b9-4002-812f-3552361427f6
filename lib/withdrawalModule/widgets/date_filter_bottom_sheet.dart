import 'dart:io';

import '../../../commonWidgets/asset_svg_icon.dart';
import '../../../commonWidgets/custom_container.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../colors.dart';
import '../../../commonWidgets/custom_button.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../navigators.dart';

class DateFilterBottomSheet extends StatefulWidget {
  final DateTime? startDate;
  final DateTime? endDate;

  const DateFilterBottomSheet({
    required this.startDate,
    required this.endDate,
  });

  @override
  State<DateFilterBottomSheet> createState() => _DateFilterBottomSheetState();
}

class _DateFilterBottomSheetState extends State<DateFilterBottomSheet> {
  double dW = 0;
  double tS = 0;
  bool isLoading = false;

  DateTime? startDate;
  DateTime? endDate;

  Widget? dialog;

  initialDateRange() {
    return DateTimeRange(
      start: startDate == null ? DateTime.now() : startDate!,
      end: endDate == null ? DateTime.now() : endDate!,
    );
  }

  @override
  void initState() {
    super.initState();
    startDate = widget.startDate;
    endDate = widget.endDate;

    dialog = DateRangePickerDialog(
      firstDate: DateTime(2020),
      lastDate: DateTime(2200),
      currentDate: DateTime.now(),
      initialDateRange: initialDateRange(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      decoration: const BoxDecoration(
        color: whiteColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      padding: EdgeInsets.all(dW * 0.06),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              pop({'startDate': null, 'endDate': null, 'apply': false});
            },
            child: Container(
              alignment: Alignment.bottomRight,
              child: TextWidget(
                title: 'Reset Filter',
                fontSize: tS * 16,
                color: greyTextColor5,
              ),
            ),
          ),
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Date Range',
            fontSize: tS * 18,
            color: greyTextColor5,
          ),
          SizedBox(height: dW * 0.025),
          GestureDetector(
            onTap: () {
              showDialog<DateTimeRange>(
                context: context,
                useSafeArea: false,
                builder: (BuildContext context) {
                  return Theme(
                    data: ThemeData.light().copyWith(
                      colorScheme: ColorScheme.fromSwatch(
                        primarySwatch: Colors.green,
                        // primaryColorDark: getThemeColor(),
                        accentColor: getThemeColor(),
                      ),
                      dialogBackgroundColor: Colors.white,
                    ),
                    child: dialog!,
                  );
                },
              ).then((value) {
                if (value != null) {
                  setState(() {
                    startDate = value.start;
                    endDate = value.end;
                  });
                }
              });
            },
            child: CustomContainer(
              vPadding: 0.045,
              boxShadow: const [],
              borderColor: getThemeColor(),
              borderWidth: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    startDate == null || endDate == null
                        ? 'Select Date Range'
                        : '${DateFormat('dd MMM yyyy').format(startDate!)} - ${DateFormat('dd MMM yyyy').format(endDate!)}',
                    style: TextStyle(
                      fontSize: tS * 16,
                      letterSpacing: 0.3,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const AssetSvgIcon(iconName: 'calendar')
                ],
              ),
            ),
          ),
          SizedBox(height: dW * 0.09),
          CustomButton(
            width: dW,
            height: dW * 0.14,
            buttonText: "Apply Filter",
            isLoading: isLoading,
            onPressed: startDate == null
                ? null
                : () {
                    pop(
                      {
                        'startDate': DateTime(startDate!.year, startDate!.month,
                            startDate!.day, 0, 0, 0),
                        'endDate': DateTime(endDate!.year, endDate!.month,
                            endDate!.day, 23, 59, 59),
                        'apply': true,
                      },
                    );
                  },
          ),
          if (Platform.isIOS) SizedBox(height: dW * 0.03),
        ],
      ),
    );
  }
}
