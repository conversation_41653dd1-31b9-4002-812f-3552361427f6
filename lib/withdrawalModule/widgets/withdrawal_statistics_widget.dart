import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:flutter/material.dart';

import '../../common_function.dart';
import '../../commonWidgets/text_widget.dart';

class WithdrawalStatisticsWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final double totalEarnings;
  final double withdrawalAmount;
  const WithdrawalStatisticsWidget({
    Key? key,
    required this.dW,
    required this.tS,
    required this.totalEarnings,
    required this.withdrawalAmount,
  }) : super(key: key);

  double get sectionWidth => (dW - (dW * 0.08) - (1 * 2)) / 2;
  Widget column(
    BuildContext context,
    String label,
    String value, {
    Color textColor = greenPrimary,
  }) =>
      SizedBox(
        width: dW * 0.43,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              constraints: BoxConstraints(maxWidth: dW * 0.35),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.displayMedium!.copyWith(
                        fontSize: tS * 24,
                        color: textColor,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ),
            SizedBox(height: dW * 0.03),
            Container(
              constraints: BoxConstraints(maxWidth: dW * 0.35),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: TextWidget(
                  title: label,
                  color: Color(0xFF515259),
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      margin: EdgeInsets.only(bottom: dW * 0.085),
      hPadding: 0,
      vPadding: .055,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              column(
                context,
                'Total Earnings',
                '\u20b9${convertAmountString(totalEarnings)}',
                textColor: Colors.grey.shade600,
              ),
              column(
                context,
                'Withdrawal Amount',
                '\u20b9${convertAmountString(withdrawalAmount)}',
              ),
            ],
          ),
          Positioned(
            left: sectionWidth,
            right: sectionWidth,
            top: -5,
            bottom: -5,
            child: VerticalDivider(
              thickness: 2,
              color: getThemeColor().withOpacity(.2),
            ),
          ),
        ],
      ),
    );
  }
}
