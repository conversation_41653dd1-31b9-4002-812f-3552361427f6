import 'package:bys_business/colors.dart';
import 'package:bys_business/withdrawalModule/models/withdrawal_model.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../common_function.dart';
import '../../commonWidgets/text_widget.dart';

class WithdrawalWidget extends StatelessWidget {
  final double dW;
  final Withdrawal withdrawal;
  const WithdrawalWidget({Key? key, required this.dW, required this.withdrawal})
      : super(key: key);

  String getStatus() {
    if (withdrawal.status == 'Credit Inprogress') {
      return 'PROCESSING';
    } else if (withdrawal.status == 'Credit Success') {
      return 'SUCCESS';
    } else if (withdrawal.status == 'Debit Inprogress') {
      return 'PROCESSING';
    } else if (withdrawal.status == 'Debit Success') {
      return 'SUCCESS';
    } else if (withdrawal.status == 'Debit Failed') {
      return 'FAILED';
    } else if (withdrawal.status == 'Refund') {
      return 'SUCCESS';
    } else {
      return '';
    }
  }

  Color getColor() {
    if (getStatus() == 'PROCESSING') {
      return Color(0xffFFC400);
    } else if (getStatus() == 'SUCCESS') {
      return greenPrimary;
    } else if (getStatus() == 'FAILED') {
      return redColor;
    } else {
      return Colors.black;
    }
  }

  String getMessage() {
    if (withdrawal.withdrawalOption == 'UPI') {
      return '${withdrawal.upi!.name} bank account';
    } else {
      return '${withdrawal.bankDetails!.bankName} account';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      margin: EdgeInsets.only(bottom: dW * 0.05),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Color(0xffE3FEDB).withOpacity(0.7),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  children: [
                    TextWidget(title: withdrawal.createdAt.day.toString()),
                    TextWidget(
                      title: DateFormat('MMM')
                          .format(withdrawal.createdAt)
                          .toUpperCase(),
                      fontSize: 12,
                    ),
                  ],
                ),
              ),
              SizedBox(width: dW * 0.04),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    constraints: BoxConstraints(maxWidth: dW * 0.5),
                    child: TextWidget(
                      title: withdrawal.type == 'Credit'
                          ? withdrawal.status == 'Refund'
                              ? 'Money has been refunded to your wallet'
                              : 'Money Added to wallet from ${withdrawal.bulkBooking != '' ? 'bulk ' : ''}booking'
                          : 'Transfered ${withdrawal.status == 'Debit Failed' ? 'failed' : ''} to ${getMessage()}',
                      maxLines: 3,
                      fontSize: 12,
                      textOverflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: dW * 0.01),
                  TextWidget(
                    title:
                        'BYS${withdrawal.id.substring(withdrawal.id.length - 10).toUpperCase()}',
                    fontSize: 12,
                    color: Colors.black54,
                  ),
                ],
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: dW * 0.55),
                child: TextWidget(
                  title:
                      '${withdrawal.type == 'Debit' ? '- ' : '+ '}\u20b9${convertAmountString(withdrawal.amount)}',
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: withdrawal.type == 'Credit' ? greenPrimary : redColor,
                ),
              ),
              SizedBox(height: dW * 0.01),
              TextWidget(
                title: getStatus(),
                fontSize: 12,
                color: getColor(),
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
