class Withdrawal {
  final String id;
  final double amount;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime createdAt;
  final String type;
  final String status;
  final String withdrawalOption;
  final UPI? upi;
  final BankDetails? bankDetails;
  final String booking;
  final String bulkBooking;

  Withdrawal({
    required this.id,
    required this.amount,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    required this.type,
    required this.status,
    required this.withdrawalOption,
    required this.upi,
    required this.bankDetails,
    required this.booking,
    required this.bulkBooking,
  });

  static Withdrawal jsonToWithdrawal(Map withdrawal) {
    return Withdrawal(
      id: withdrawal['_id'],
      amount: withdrawal['amount'].toDouble(),
      startDate: DateTime.parse(withdrawal['startDate']).toLocal(),
      endDate: DateTime.parse(withdrawal['endDate']).toLocal(),
      createdAt: DateTime.parse(withdrawal['createdAt']).toLocal(),
      type: withdrawal['type'] ?? '',
      booking: withdrawal['booking'] ?? '',
      bulkBooking: withdrawal['bulkBooking'] ?? '',
      status: withdrawal['status'] ?? '',
      withdrawalOption: withdrawal['withdrawalOption'] ?? '',
      upi: withdrawal['fundAccount'] == null
          ? null
          : withdrawal['fundAccount']['upi'] == null
              ? null
              : UPI.jsonToUPI(withdrawal['fundAccount']['upi']),
      bankDetails: withdrawal['fundAccount'] == null
          ? null
          : withdrawal['fundAccount']['bankDetails'] == null
              ? null
              : BankDetails.jsonToBankDetails(
                  withdrawal['fundAccount']['bankDetails']),
    );
  }
}

class UPI {
  final String name;
  final String vpa;

  UPI({
    required this.name,
    required this.vpa,
  });

  static UPI jsonToUPI(Map upi) {
    return UPI(
      name: upi['name'] ?? '',
      vpa: upi['vpa'] ?? '',
    );
  }
}

class BankDetails {
  final String accNo;
  final String ifsc;
  final String name;
  final String bankName;

  BankDetails({
    required this.accNo,
    required this.ifsc,
    required this.name,
    required this.bankName,
  });

  static BankDetails jsonToBankDetails(Map bankDetails) {
    return BankDetails(
      accNo: bankDetails['accNo'] ?? '',
      ifsc: bankDetails['ifsc'] ?? '',
      name: bankDetails['name'] ?? '',
      bankName: bankDetails['bankName'] ?? '',
    );
  }
}
