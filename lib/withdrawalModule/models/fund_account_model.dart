import 'package:bys_business/withdrawalModule/models/withdrawal_model.dart';

class FundAccount {
  final String id;
  final String type;
  final UPI? upi;
  final BankDetails? bankDetails;
  final double lastWithdrawalAmount;
  final DateTime updatedAt;

  FundAccount({
    required this.id,
    required this.type,
    required this.upi,
    required this.bankDetails,
    required this.lastWithdrawalAmount,
    required this.updatedAt,
  });

  static FundAccount jsonToFundAcc(Map fundAccount) {
    return FundAccount(
      id: fundAccount['_id'],
      type: fundAccount['type'],
      upi:
          fundAccount['upi'] == null ? null : UPI.jsonToUPI(fundAccount['upi']),
      bankDetails: fundAccount['bankDetails'] == null
          ? null
          : BankDetails.jsonToBankDetails(fundAccount['bankDetails']),
      lastWithdrawalAmount: fundAccount['lastWithdrawalAmount'].toDouble(),
      updatedAt: DateTime.parse(fundAccount['updatedAt']).toLocal(),
    );
  }
}
