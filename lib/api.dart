var webApi = {'domain': 'https://devapiv2.bookyourslot.co'}; //DEVELOPMENT V2
// var webApi = {'domain': 'http://************:7090'};
// var webApi = {'domain': 'http://localhost:7090'};
 //Salman
// var webApi = {'domain': 'https://api.bookyourslot.co'}; //PROD
// var webApi = {'domain': 'https://apiv2.bookyourslot.co'}; //PROD v2

var endPoint = {
  //Authentication
  'isUserExists': '/api/user/isUserExists',
  'register': '/api/user/register',
  'updateUser': '/api/user/updateUser',
  'sendOTPtoUser': '/api/auth/sendOTPtoUser',
  'verifyOTPofUser': '/api/auth/verifyOTPofUser',
  'resendOTPtoUser': '/api/auth/resendOTPtoUser',
  'createUserAndBusinessAndTurf': '/api/business/createUserAndBusinessAndTurf',
  'updateProfile': '/api/user/updateProfile',
  'deleteFCMToken': '/api/user/deleteFCMToken',
  'deleteBusiness': '/api/business/deleteBusiness',
  'fetchUserWallet': '/api/user/fetchUserWallet',
  'registerBusiness': '/api/business/registerBusiness',
  'searchLocationFromGoogle': '/api/appConfig/searchLocationFromGoogle',
  'fetchAddressDetails': '/api/appConfig/fetchAddressDetails',
  'updateBusinesProfile': '/api/business/updateBusinesProfile',

  //TURF
  'fetchTurfsByBusinessId': '/api/turf/fetchTurfsByBusinessId',
  'addNewTurf': '/api/turf/addNewTurf',
  'addTurfDetails': '/api/turf/addTurfDetails',
  'pauseAndUnPauseTurf': '/api/turf/pauseAndUnPauseTurf',
  'fetchTurfAdmin': '/api/turf/fetchTurfAdmin',
  // 'updateVenueLocation': '/api/turf/updateVenueLocation',
  'fetchTurfById': '/api/turf/fetchTurfById',
  'addVenueAndDetailsV2': '/api/turf/addVenueAndDetailsV2',
  'addVenueAndDetailsIndoor': '/api/turf/addVenueAndDetailsIndoor',

  'deleteVenue': '/api/turf/deleteVenue',

  //BOOKING
  'fetchBusinessBooking': '/api/booking/fetchBusinessBooking',
  'cancelAndRescheduleBooking': '/api/booking/cancelAndRescheduleBooking',
  'createBooking': '/api/booking/createBooking',
  'markBookingCompleted': '/api/booking/markBookingCompleted',
  'fetchBookingById': '/api/booking/fetchBookingById',
  'fetchRentalItemBySport': '/api/rental/fetchRentalItemBySport',
  'fetchRentalItemBySelectedSport':
      '/api/rental/fetchRentalItemBySelectedSport',
  'fetchBookingsCount': '/api/booking/fetchBookingsCount',
  'editNoteForBooking': '/api/booking/editNoteForBooking',
  'verifyBookingByOTP': '/api/booking/verifyBookingByOTP',
  'updatePaymentStatus': '/api/booking/updatePaymentStatus',
  'updateBookingTime': '/api/booking/updateBookingTime',
  'addOrUpdateExtraItems': '/api/booking/addOrUpdateExtraItem',
  'fetchAllBookingItems': '/api/booking/getExtraItems',
  //NEW BOOKING
  "fetchBusinessBookingNew": "/api/booking/fetchBusinessBookingNew",
  "fetchBookingByBulkIdNew": "/api/booking/fetchBookingByBulkIdNew",
  "fetchBookingByIdNew": "/api/booking/fetchBookingByIdNew",
  "fetchBookingsByStatusNew": "/api/booking/fetchBookingsByStatusNew",
  "searchBookingNew": "/api/booking/searchBookingNew",
  "fetchUserBookingForBusinessNew":
      "/api/booking/fetchUserBookingForBusinessNew",
  "removeExtraItemFromBooking": "/api/booking/deleteExtraItem",

  //Booking V2
  'fetchBusinessBookingV2': '/api/booking/fetchBusinessBookingV2',

  //BOOKING SLOT
  'fetchTurfBookingSlot': '/api/bookingSlot/fetchTurfBookingSlot',
  'insertBookingSlot': '/api/bookingSlot/insertBookingSlot',
  'updateBlockStatus': '/api/bookingSlot/updateBlockStatus',
  'updateTurfQuantity': "/api/bookingSlot/updateTurfQuantity",
  "fetchBlockedSlot": '/api/bookingSlot/fetchBlockedSlot',
  "fetchBusinessCustomers": "/api/booking/fetchBusinessCustomers",
  "fetchUserBookingForBusiness": "/api/booking/fetchUserBookingForBusiness",
  'fetchBookingsByStatus': '/api/booking/fetchBookingsByStatus',
  'searchBooking': '/api/booking/searchBooking',
  'fetchBookingByBulkId': '/api/booking/fetchBookingByBulkId',

  //SPORTS
  'fetchSport': '/api/sport/fetchSport',

  //SPORT CATEGORY
  'fetchSportCategory': '/api/sportCategory/fetchSportCategory',

  //APP CONFIG
  'fetchAppConfig': '/api/appConfig/fetchAppConfig',
  'fetchPolicy': '/api/appGeneralInfo/fetchPolicy',
  'fetchCommonAppConfig': '/api/appConfig/fetchCommonAppConfig',

  //NOTIFICATION
  'fetchUserNotifcation': '/api/notification/fetchUserNotifcation',
  'updateViewState': '/api/notification/updateViewState',
  'fetchUserNotifcationNew': '/api/notification/fetchUserNotifcationNew',
  'fetchEmployeeNotifcationNew':
      '/api/notification/fetchEmployeeNotifcationNew',

  //ANALYTICS
  'fetchSportBookingAnalytics': '/api/business/fetchSportBookingAnalytics',
  'fetchTotalBookingAnalytics': '/api/business/fetchTotalBookingAnalytics',
  'fetchAnalyticsV2': '/api/business/fetchAnalyticsV2',
  'fetchUserNewNotifcation': '/api/notification/fetchUserNewNotifcation',

  //REPORT
  'fetchBusinessReport': '/api/report/fetchBusinessReport',
  'generateBookingReport': '/api/report/generateBookingReport',
  'deleteReport': '/api/report/deleteReport',

  //EMPLOYEE
  "createEmployeeAndMapping": "/api/employee/createEmployeeAndMapping",
  "checkForEmployee": "/api/employee/checkForEmployee",
  "isEmployeeExists": "/api/employee/isEmployeeExists",
  "updateEmployeeMapping": "/api/mapping/updateEmployeeMapping",
  "activeOrDeleteEmployeeMapping": "/api/mapping/activeOrDeleteEmployeeMapping",
  "fetchBusinessEmployees": "/api/mapping/fetchBusinessEmployees",
  "fetchEmployeeBusinesses": "/api/mapping/fetchEmployeeBusinesses",
  'updateEmployee': '/api/employee/updateEmployee',
  "fetchEmployeeNotificationCount":
      "/api/notification/fetchEmployeeNotificationCount",
  "fetchEmployeeNotifcation": "/api/notification/fetchEmployeeNotifcation",

  //GET CITY AND STATE
  'getStateCity': '/api/turf/getStateCity',

  //BULK BOOKING
  "checkBulkBookingAvailability":
      "/api/bulkBooking/checkBulkBookingAvailability",
  "createBulkBooking": "/api/bulkBooking/createBulkBooking",
  "fetchBulkBookingByBusinessId":
      "/api/bulkBooking/fetchBulkBookingByBusinessId",
  'fetchBulkBookingById': '/api/bulkBooking/fetchBulkBookingById',
  'cancelBulkBooking': '/api/booking/cancelBulkBooking',

  //APPVERSION
  'insertAppVersion': '/api/appVersion/insertAppVersion',

  //COUPON
  'fetchCouponsForBusiness': '/api/coupon/fetchCouponsForBusiness',
  'createAndUpdateCoupon': '/api/coupon/createAndUpdateCoupon',
  'activeOrDeleteCoupon': '/api/coupon/activeOrDeleteCoupon',

  //Withdrawal
  'validateVPA': '/api/withdrawal/validateVPA',
  'validateBankAccount': '/api/withdrawal/validateBankAccount',
  'fetchWithdrawals': '/api/withdrawal/fetchWithdrawals',
  'createWithdrawal': '/api/withdrawal/createWithdrawal',
  'fetchFundAccounts': '/api/withdrawal/fetchFundAccounts',

  //Group
  "createAndEditGroup": "/api/group/createAndEditGroup",
  "fetchAllGroups": "/api/group/fetchAllGroups",
  "deleteGroup": "/api/group/deleteGroup",
};
