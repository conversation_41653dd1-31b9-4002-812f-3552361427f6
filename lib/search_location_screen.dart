import 'dart:async';
import 'dart:io' show Platform;
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'authModule/providers/auth.dart';
import 'commonWidgets/asset_svg_icon.dart';
import 'commonWidgets/circular_loader.dart';
import 'commonWidgets/custom_app_bar.dart';
import 'common_function.dart';
import 'formatted_address_widget.dart';
import 'venueModule/models/venue_model.dart';

class SearchLocationScreen extends StatefulWidget {
  const SearchLocationScreen({Key? key}) : super(key: key);

  @override
  SearchLocationScreenState createState() => SearchLocationScreenState();
}

class SearchLocationScreenState extends State<SearchLocationScreen> {
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  bool isSearchLoading = false;

  TextEditingController searchController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();

  Timer? _debounce;

  List<Address> listOfAddress = [];

  searchLocationFromGoogle(String query) async {
    try {
      setState(() {
        listOfAddress = [];
      });
      List<Address> data = await Provider.of<Auth>(context, listen: false)
          .searchLocationFromGoogle(queryString: query);

      listOfAddress = data;
    } catch (e) {
      print(e);
    }
  }

  onSearchChanged(String query) {
    if (query.length < 2) return;

    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(seconds: 1), () async {
      setState(() {
        isSearchLoading = true;
      });
      await searchLocationFromGoogle(query);
      setState(() {
        isSearchLoading = false;
      });
    });
  }

  searchResultsTextStyle() => TextStyle(
        fontSize: tS * 13,
        fontWeight: FontWeight.w500,
        color: Colors.blueGrey,
      );

  @override
  void initState() {
    super.initState();
    searchFocusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Container(
        color: Colors.transparent,
        height: dH,
        width: dW,
        child: isLoading
            ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
            : Padding(
              padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
              child: Column(
                  children: [
                    // SizedBox(height: dW * 0.05),
                    NewAppBar(dW: dW, title: 'Search Loction'),
                    SizedBox(height: dW * 0.05),
                    Container(
                      height: dW * 0.135,
                      alignment: Alignment.center,
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: getThemeColor().withOpacity(.2),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.shade300,
                              offset: const Offset(3, 3),
                              blurRadius: 5.0,
                              spreadRadius: 1.0,
                            ),
                            const BoxShadow(
                              color: Colors.white,
                              offset: Offset(0.0, 0.0),
                              blurRadius: 0.0,
                              spreadRadius: 0.0,
                            ), //
                          ]),
                      child: TextFormField(
                          controller: searchController,
                          focusNode: searchFocusNode,
                          cursorColor: getThemeColor(),
                          style: TextStyle(
                              color: Colors.black,
                              fontSize: tS * 13,
                              fontWeight: FontWeight.w500),
                          decoration: InputDecoration(
                            hintText: 'Search',
                            hintStyle: TextStyle(
                              color: Colors.black,
                              fontSize: tS * 13,
                            ),
                            prefixIcon: const Padding(
                              padding: EdgeInsets.all(15.0),
                              child: AssetSvgIcon(
                                iconName: 'search1',
                                color: Colors.black,
                                height: 0,
                                width: 0,
                              ),
                            ),
                            suffixIcon: IconButton(
                              focusColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onPressed: () {
                                setState(() {
                                  searchFocusNode.unfocus();
                                  listOfAddress = [];
                                  searchController.text = '';
                                });
                              },
                              icon: searchController.text.isEmpty
                                  ? const SizedBox.shrink()
                                  : const Icon(
                                      Icons.clear,
                                      color: Colors.black87,
                                    ),
                            ),
                            border: InputBorder.none,
                          ),
                          onChanged: (value) {
                            if (value.trim().isEmpty) {
                              setState(() {
                                listOfAddress = [];
                              });
                            } else {
                              onSearchChanged(value);
                            }
                          }),
                    ),
                    isSearchLoading
                        ? Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(top: dW * 0.6),
                            child: CircularLoader(
                                android: dW * 0.08, iOS: dW * 0.035),
                          )
                        : Expanded(
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (listOfAddress.isNotEmpty)
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: dW * 0.075,
                                            bottom: dW * 0.02,
                                            left: dW * 0.065),
                                        child: Text(
                                          'SEARCH RESULT',
                                          style: searchResultsTextStyle(),
                                        )),
                                  ...listOfAddress
                                      .map<FormattedAddressWidget>(
                                          (fa) => FormattedAddressWidget(
                                                fa: fa,
                                                dW: dW,
                                                textScale: tS,
                                              ))
                                      .toList(),
                                  SizedBox(height: dW * 0.12)
                                ],
                              ),
                            ),
                          ),
                  ],
                ),
            ),
      ),
    );
  }
}
