import 'dart:convert';
import 'dart:io';

import '../../employeeModule/screens/employee_business_screen.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter_svg/svg.dart';

import '../../homeModule/screens/homeScreen.dart';

import '../../authModule/screens/onBoardScreen.dart';
import 'package:flutter/material.dart';
import '/authModule/providers/auth.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../authModule/screens/suspendScreen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final LocalStorage storage = new LocalStorage('bysBusiness');
  bool isLoggedOut = true;
  bool _seen = false;
  var userDetailString;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 0)).then((value) async {
      // await initDynamicLinks();
      _checkForactiveUser();
    });
  }

  var referralCode = '';
  var referredByUserId = '';

  initDynamicLinks() async {
    final PendingDynamicLinkData? data =
        await FirebaseDynamicLinks.instance.getInitialLink();
    final Uri? deepLink = data?.link;

    if (deepLink != null) {
      final queryParams = deepLink.queryParameters;
      if (queryParams.length > 0) {
        referralCode = queryParams["referralCode"]!;
        referredByUserId = queryParams["referredByUserId"]!;
        // verify the username is parsed correctly
        print("My Article Id  is: $referralCode");
        print("Deep link path  is: ${deepLink.path}");
      }
    }
  }

  goToOnBoardingScreen() {
    Future.delayed(Duration(seconds: 2, milliseconds: 500)).then((value) {
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => OnBoardScreen(
              referralCode,
              referredByUserId,
            ),
          ),
          (route) => false);
    });
  }

  _checkForactiveUser() async {
    try {
      await storage.ready;

      final auth = Provider.of<Auth>(context, listen: false);

      await auth.fetchSport();
      var result = await auth.fetchCommonAppConfig("Splash");

      result.forEach((data) {
        if (data['type'] == 'AppValidatio2') {
          auth.appValidation = json.decode(data['value']);
        } else if (data['type'] == 'AdminContact') {
          auth.adminContact = int.parse(data['value']);
        } else if (data['type'] == 'Help') {
          auth.helpLink = data['value'];
        } else if (data['type'] == 'EmployeeRole') {
          auth.employeeRoles = data['value'];
        } else if (data['type'] == 'DeleteAccount6') {
          auth.deleteAccount = data['value'];
        } else if (data['type'] == 'Commission') {
          auth.commision = data['value'];
        } else if (data['type'] == 'MinimumWithdrawalAmount') {
          auth.minimumWithdrawalAmount = data['value'].toInt();
        } else if (data['type'] == 'CancellationTemplate') {
          auth.cancellationTemplate = data['value'];
        } else if (data['type'] == 'Update') {
          auth.update = Platform.isAndroid
              ? data['value']['android']['business']
              : data['value']['ios']['business'];
        }
      });

      final accessTokenString = storage.getItem('accessToken');
      if (accessTokenString != null) {
        var accessToken = json.decode(accessTokenString);
        if (accessToken != null) {
          if (accessToken['role'] == 'Business') {
            final user = await auth.isUserExists(accessToken['phone']);

            if (user['message'] != "New User") {
              Future.delayed(Duration(seconds: 2, milliseconds: 500))
                  .then((value) {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => user['business']['isActive']
                          ? HomeScreen()
                          : SuspendScreen(),
                    ),
                    (route) => false);
              });
            } else {
              goToOnBoardingScreen();
            }
          } else if (accessToken['role'] == 'Employee') {
            final user = await auth.isEmployeeExists(accessToken['phone']);

            if (user['message'] != "New Employee") {
              Future.delayed(Duration(seconds: 2, milliseconds: 500))
                  .then((value) {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EmployeeBusinessScreen(),
                    ),
                    (route) => false);
              });
            } else {
              goToOnBoardingScreen();
            }
          }
        } else {
          goToOnBoardingScreen();
        }
      } else {
        goToOnBoardingScreen();
      }
    } catch (e) {
      print(e);
      goToOnBoardingScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: Center(
        child: SvgPicture.asset(
          'assets/svgIcons/BysLogo1.svg',
          // width: width * 0.6,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: width * 0.13),
        child: Text(
          'By Mechcity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xff0e412b),
          ),
        ),
      ),
    );
  }
}
