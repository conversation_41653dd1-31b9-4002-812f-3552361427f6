import 'dart:io';

import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../common_function.dart';
import '../../navigators.dart';
import 'verifyOtpScreen.dart';
import '../../commonWidgets/policy_text_widget.dart';
import '../../authModule/providers/auth.dart';

class LoginScreen extends StatefulWidget {
  final String? referralCode;
  final String? referredByUserId;
  final String role;
  const LoginScreen({
    Key? key,
    this.referralCode,
    this.referredByUserId,
    this.role = 'Business',
  }) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool isLoading = false;
  bool valid = true;
  ScrollController _scrollController = ScrollController();
  bool scroll = false;
  bool disableControllerJump = true;

  TextEditingController mobileNo = new TextEditingController();
  FocusNode mobileFocus = FocusNode();
  Future<bool> _willPopCallback() async {
    SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    return true;
  }

  var employeeExistance;
  bool isEmployeeAlreadyExist = false;

  _checkForEmployee(String mobileNo) async {
    employeeExistance =
        await Provider.of<Auth>(context, listen: false).isEmployeeExists(
      mobileNo,
    );
    if (employeeExistance['message'] == "Employee Already exists") {
      isEmployeeAlreadyExist = true;
    } else {
      isEmployeeAlreadyExist = false;
    }
  }

  @override
  void initState() {
    super.initState();
  }

  getOTP() async {
    if (isLoading) return;
    if (mobileNo.text.length == 0 ||
        (mobileNo.text.length > 10 || mobileNo.text.length < 10)) {
      setState(() {
        valid = false;
      });
      return;
    }
    setState(() {
      valid = true;
    });

    try {
      setState(() {
        isLoading = true;
      });
      // final data = await Provider.of<Auth>(context, listen: false)
      //     .sendOTPtoUser(mobileNo.text.toString());
      // if (data['result']['type'] != "success") {
      //   showSnackbar('Something went wrong');
      // } else {
      Provider.of<Auth>(context, listen: false).turfDetails = null;
      Provider.of<Auth>(context, listen: false).bankDetails = null;
      Provider.of<Auth>(context, listen: false).ownerDetails = null;
      Provider.of<Auth>(context, listen: false).panDetails = null;
      Provider.of<Auth>(context, listen: false).commissionAccepeted = false;
      push(
        VerifyOtpScreen(
          mobileNo: mobileNo.text,
          referralCode: widget.referralCode,
          referredByUserId: widget.referredByUserId,
          role: widget.role,
        ),
      );
      // }
      //   setState(() {
      //     isLoading = false;
      //   });
      // }
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _redirectToGetOtpScreen() async {
    if (isLoading) return;
    final value = mobileNo.value.text;

    String pattern = r'([6,7,8,9][0-9]{9})';
    RegExp regExp = RegExp(pattern);
    if (value.isEmpty) {
      showSnackbar("Please enter mobile number");

      return;
    } else if (!regExp.hasMatch(value)) {
      showSnackbar("Please enter valid mobile number");

      return;
    }
    try {
      setState(() {
        isLoading = true;
      });
      await _checkForEmployee(mobileNo.text);
      var response = await Provider.of<Auth>(context, listen: false)
          .sendOTPtoUser(mobileNo.value.text);

      if (response['result']['type'] == 'success') {
        if (widget.role == 'Business') {
          push(
            VerifyOtpScreen(
              mobileNo: mobileNo.text,
              referralCode: widget.referralCode,
              referredByUserId: widget.referredByUserId,
              role: widget.role,
            ),
          );
        } else if (widget.role == 'Employee') {
          if (isEmployeeAlreadyExist) {
            push(
              VerifyOtpScreen(
                mobileNo: mobileNo.text,
                referralCode: widget.referralCode,
                referredByUserId: widget.referredByUserId,
                role: widget.role,
              ),
            );
          } else {
            showSnackbar('Employee is not registered with this phone number');
          }
        }
      } else {
        showSnackbar('Unable to send OTP');
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    mobileNo.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {
        scroll = false;
      });
      if (!disableControllerJump) {
        _scrollController.jumpTo(0);
      }
    } else {
      setState(() {
        scroll = true;
      });
    }
    disableControllerJump = false;

    return WillPopScope(
      onWillPop: _willPopCallback,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.white,
        body: LayoutBuilder(
          builder: (context, constraints) {
            return SafeArea(
              child: GestureDetector(
                onTap: () {
                  mobileFocus.unfocus();
                },
                child: Container(
                  height: height,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: scroll
                        ? AlwaysScrollableScrollPhysics()
                        : NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: width * 0.05),
                    child: ConstrainedBox(
                      constraints:
                          BoxConstraints(minHeight: constraints.maxHeight),
                      child: IntrinsicHeight(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: width * 0.05),
                            InkWell(
                              onTap: pop,
                              child: Icon(Icons.arrow_back),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                top: width * 0.25,
                                bottom: width * 0.03,
                              ),
                              child: Image.asset('assets/images/Phone.png',
                                  scale: 2),
                            ),
                            Text(
                              "Phone Number",
                              style: TextStyle(
                                fontSize: textScaleFactor * 24,
                                fontWeight: FontWeight.w500,
                                letterSpacing: .56,
                              ),
                            ),
                            SizedBox(height: width * 0.014),
                            Text(
                              "Please enter your phone number to proceed",
                              style: TextStyle(
                                color: Color(0XFF77838F),
                                fontSize: textScaleFactor * 12,
                                fontWeight: FontWeight.w400,
                                letterSpacing: .48,
                              ),
                            ),
                            SizedBox(
                              height: width * 0.15,
                            ),
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(width * 0.02),
                                    child: Text(
                                      "+91",
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600,
                                        fontSize: textScaleFactor * 16,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: width * 0.7,
                                    child: TextFormField(
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600,
                                        fontSize: textScaleFactor * 16,
                                      ),
                                      decoration: InputDecoration(
                                        hintStyle: TextStyle(
                                          color: const Color(0xff77838F),
                                          fontSize: textScaleFactor * 14,
                                        ),
                                        hintText: 'Enter your phone number',
                                        counterStyle: const TextStyle(
                                          height: double.minPositive,
                                        ),
                                        counterText: "",
                                        border: InputBorder.none,
                                      ),
                                      controller: mobileNo,
                                      focusNode: mobileFocus,
                                      keyboardType: TextInputType.phone,
                                      maxLength: 10,
                                      onChanged: (value) {
                                        if (value.length == 10) {
                                          mobileFocus.unfocus();
                                          _scrollController.jumpTo(0);
                                          scroll = false;
                                          setState(() {
                                            valid = true;
                                          });
                                        }
                                      },
                                      onTap: () {
                                        setState(() {
                                          scroll = true;
                                        });
                                      },
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return 'Please enter a phone number';
                                        } else if (value.length < 10) {
                                          return 'Please enter a valid phone number';
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            valid
                                ? SizedBox.shrink()
                                : Container(
                                    margin: EdgeInsets.only(
                                      left: width * 0.125,
                                      top: width * 0.02,
                                    ),
                                    child: Row(
                                      children: [
                                        Text(
                                          mobileNo.text.length == 0
                                              ? 'Please enter your phone number'
                                              : 'Please enter valid phone number',
                                          style: TextStyle(
                                            color: Colors.red[700],
                                            fontSize: textScaleFactor * 11.5,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                            SizedBox(
                                height: valid ? width * 0.08 : width * 0.05),
                            CustomButton(
                              width: width,
                              height: width * 0.12,
                              radius: 7,
                              buttonText: 'Get OTP',
                              isLoading: isLoading,
                              onPressed: _redirectToGetOtpScreen,
                            ),
                            Spacer(),
                            Align(
                              alignment: Alignment.bottomCenter,
                              child: Container(
                                margin: EdgeInsets.only(
                                  bottom: Platform.isAndroid
                                      ? width * 0.1
                                      : width * 0.2,
                                ),
                                child: PolicyTextWidget(
                                    dW: width, tS: textScaleFactor),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
