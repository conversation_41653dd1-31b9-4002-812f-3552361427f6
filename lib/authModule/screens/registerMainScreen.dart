import 'dart:convert';

import '../../employeeModule/models/business_model.dart';
import 'package:localstorage/localstorage.dart';

import '../../commonWidgets/raisedButton.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../widgets/turfDetailsForm.dart';
import '../../authModule/widgets/ownerDetailForm.dart';
import '../../authModule/widgets/commosionOnBoardingWidget.dart';
import '../../authModule/screens/onBoardScreen.dart';
import '../../commonWidgets/dialogBox.dart';
import '../../authModule/providers/auth.dart';

class RegisterMainScreen extends StatefulWidget {
  final referralCode;
  final referredByUserId;
  final bool fromTurfScreen;
  String? accessToken;
  String? businessId;
  final BusinessModel? business;
  RegisterMainScreen({
    Key? key,
    this.fromTurfScreen = false,
    this.accessToken,
    this.businessId,
    this.business,
    this.referralCode = '',
    this.referredByUserId = '',
  }) : super(key: key);

  @override
  _RegisterMainScreenState createState() => _RegisterMainScreenState();
}

class _RegisterMainScreenState extends State<RegisterMainScreen> {
  bool isLoading = true;
  bool dataEntered = false;
  final LocalStorage storage = new LocalStorage('bysBusiness');
  bool isValidatePanAndBank = false;

  TextEditingController turfNameController = TextEditingController();
  TextEditingController turfContactController = TextEditingController();
  TextEditingController streetNameController = TextEditingController();
  TextEditingController landMarkController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController pincodeController = TextEditingController();
  int currentIndex = 1;
  List<Map<String, dynamic>> steps = [];

  setSteps(int index, {markComplete = true}) {
    if (markComplete) {
      int previousIndex = index - 1;
      if (previousIndex != 0) {
        changeIsCompletedStatus(
          previousIndex,
          callSetState: true,
        );
      }
      if (index == 6) {
        index = 5;
      }
    }
    steps.forEach((step) {
      if (step['index'] == index) {
        setState(() {
          currentIndex = index;
          step['isSelected'] = true;
        });
      } else {
        setState(() {
          step['isSelected'] = false;
        });
      }
    });
  }

  checkPreviousStepIsCompleted(int previousIndex) {
    bool completed = false;
    if (previousIndex != 1) {
      steps.forEach((step) {
        if (step['index'] == previousIndex - 1) {
          if (step['isCompleted']) {
            setState(() {
              completed = true;
            });
          }
        }
      });
    } else {
      completed = true;
    }
    return completed;
  }

  changeIsCompletedStatus(int previousIndex, {bool callSetState = false}) {
    steps.forEach((step) {
      if (step['index'] == previousIndex) {
        if (callSetState) {
          setState(() {
            step['isCompleted'] = true;
          });
        } else {
          step['isCompleted'] = true;
        }
      }
    });
  }

  setExitValue() {
    setState(() {
      dataEntered = true;
    });
  }

  dialogBoxFunction() {
    final auth = Provider.of<Auth>(context, listen: false);

    auth.turfDetails = null;
    auth.bankDetails = null;
    auth.ownerDetails = null;
    auth.panDetails = null;
    auth.commissionAccepeted = false;
    auth.gstNo = '';
    auth.gstRegistered = '';
    auth.gstType = '';

    if (widget.fromTurfScreen) {
      Navigator.of(context).pop();
      Navigator.of(context).pop();
    } else {
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => OnBoardScreen(
              '',
              '',
            ),
          ),
          (route) => false);
    }
    ;
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _checkForValidationNeedsOrNot();
  }

  _checkForValidationNeedsOrNot() async {
    setState(() {
      isLoading = false;
    });
    try {
      await storage.ready;

      isValidatePanAndBank =
          Provider.of<Auth>(context, listen: false).appValidation;
      if (!Provider.of<Auth>(context, listen: false).appValidation) {
        steps = [
          {
            'index': 1,
            'title': 'Venue Details',
            'isSelected': true,
            'isCompleted': false,
          },
          {
            'index': 2,
            'title': 'Owner Details',
            'isSelected': false,
            'isCompleted': false,
          },
        ];
      } else {
        if (widget.fromTurfScreen) {
          steps = [
            {
              'index': 1,
              'title': 'Venue Details',
              'isSelected': true,
              'isCompleted': false,
            },
            {
              'index': 2,
              'title': 'Commission & Onboarding',
              'isSelected': false,
              'isCompleted': false,
            },
          ];
        } else {
          steps = [
            {
              'index': 1,
              'title': 'Venue Details',
              'isSelected': true,
              'isCompleted': false,
            },
            {
              'index': 2,
              'title': 'Owner Details',
              'isSelected': false,
              'isCompleted': false,
            },
            {
              'index': 3,
              'title': 'Commission & Onboarding',
              'isSelected': false,
              'isCompleted': false,
            },
            // {
            //   'index': 4,
            //   'title': 'Pan Details',
            //   'isSelected': false,
            //   'isCompleted': false,
            // },
            // {
            //   'index': 5,
            //   'title': 'Bank Details',
            //   'isSelected': false,
            //   'isCompleted': false,
            // },
          ];
        }
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      if (widget.fromTurfScreen) {
        steps = [
          {
            'index': 1,
            'title': 'Venue Details',
            'isSelected': true,
            'isCompleted': false,
          },
          {
            'index': 2,
            'title': 'Commission & Onboarding',
            'isSelected': false,
            'isCompleted': false,
          },
        ];
      } else {
        steps = [
          {
            'index': 1,
            'title': 'Venue Details',
            'isSelected': true,
            'isCompleted': false,
          },
          {
            'index': 2,
            'title': 'Owner Details',
            'isSelected': false,
            'isCompleted': false,
          },
          {
            'index': 3,
            'title': 'Commission & Onboarding',
            'isSelected': false,
            'isCompleted': false,
          },
          // {
          //   'index': 4,
          //   'title': 'Pan Details',
          //   'isSelected': false,
          //   'isCompleted': false,
          // },
          // {
          //   'index': 5,
          //   'title': 'Bank Details',
          //   'isSelected': false,
          //   'isCompleted': false,
          // },
        ];
      }
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final deviceHeight = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Scaffold(
      body: SafeArea(
        child: isLoading
            ? Container(
                height: deviceHeight,
                width: deviceWidth,
                alignment: Alignment.center,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor),
                ),
              )
            : Container(
                height: deviceHeight,
                width: deviceWidth,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Stack(
                        // overflow: Overflow.visible,
                        clipBehavior: Clip.none,
                        children: <Widget>[
                          Container(
                            height: deviceWidth * 0.57,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: NetworkImage(
                                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQOebST_HEMjwQERtiZThLmE1PUNi84W2vGafgSOxR4VPsaAxmhUwqmM8xQ7jLDSGHceQ0&usqp=CAU',
                                ),
                                colorFilter: ColorFilter.mode(
                                  Colors.black38,
                                  BlendMode.color,
                                ),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: Container(
                              margin: EdgeInsets.only(
                                top: deviceWidth * 0.06,
                                left: deviceWidth * 0.05,
                                bottom: deviceWidth * 0.18,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      InkWell(
                                        onTap: !dataEntered
                                            ? () {
                                                if (widget.fromTurfScreen) {
                                                  Navigator.of(context).pop();
                                                } else {
                                                  Navigator.pushAndRemoveUntil(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            OnBoardScreen(
                                                          '',
                                                          '',
                                                        ),
                                                      ),
                                                      (route) => false);
                                                }
                                              }
                                            : () {
                                                return dialogBoxCustom(
                                                  context: context,
                                                  cancelBtnPress: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  cancelBtnText: 'No',
                                                  okBtnPress: () =>
                                                      dialogBoxFunction(),
                                                  okBtnText: 'Yes',
                                                  platform:
                                                      TargetPlatform.android,
                                                  title: 'Exit Alert!',
                                                  content:
                                                      'Are you sure you want to exit. All the entered information will be lost.',
                                                  titleStyle: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 14.5,
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.black,
                                                  ),
                                                  contentStyle: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 16,
                                                    color: Colors.black,
                                                  ),
                                                  cancelBtnStyle: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 15,
                                                    color: Colors.black,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  okBtnStyle: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 15,
                                                    color: Colors.grey,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                );
                                              },
                                        child: Icon(
                                          Icons.arrow_back,
                                          color: Colors.white,
                                        ),
                                      ),
                                      Container(
                                        alignment: Alignment.topCenter,
                                        margin: EdgeInsets.only(
                                            right: deviceWidth * 0.03),
                                        child: Text(
                                          'Need Help? Contact Us: +91 ${Provider.of<Auth>(context, listen: false).adminContact}',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: textScaleFactor * 13,
                                            letterSpacing: .20,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  SizedBox(height: deviceWidth * 0.06),
                                  Container(
                                    constraints: BoxConstraints(
                                        maxWidth: deviceWidth * 0.9),
                                    margin: EdgeInsets.only(
                                        bottom: deviceWidth * 0.01),
                                    child: Text(
                                      'Partner with BookYourSlot',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: textScaleFactor * 20,
                                        letterSpacing: .20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: deviceWidth * 0.03),
                                    constraints: BoxConstraints(
                                        maxWidth: deviceWidth * 0.9),
                                    child: Text(
                                      'Get listed on online turf marketplace today',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: textScaleFactor * 12,
                                        letterSpacing: .20,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: deviceWidth * 0.07),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: -deviceWidth * 0.16,
                            right: 0,
                            left: 0,
                            child: Container(
                              width: deviceWidth,
                              height: deviceWidth * 0.32,
                              alignment: Alignment.bottomCenter,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(15),
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 6,
                                      spreadRadius: 2,
                                      offset: Offset(0, 5),
                                      color:
                                          Color(0XFF30303033).withOpacity(0.06),
                                    ),
                                    BoxShadow(
                                      blurRadius: 6,
                                      spreadRadius: 2,
                                      offset: Offset(-5, 2),
                                      color:
                                          Color(0XFF30303033).withOpacity(0.06),
                                    ),
                                    BoxShadow(
                                      blurRadius: 6,
                                      spreadRadius: 2,
                                      offset: Offset(5, 2),
                                      color:
                                          Color(0XFF30303033).withOpacity(0.06),
                                    )
                                  ]),
                              margin: EdgeInsets.symmetric(
                                horizontal: deviceWidth * 0.05,
                              ),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        ...steps.map(
                                          (step) => InkWell(
                                            onTap: () {
                                              if (!checkPreviousStepIsCompleted(
                                                  step['index'])) {
                                                callToastMessage(
                                                  step['index'] == 4
                                                      ? 'Please accept the policy'
                                                      : 'Please fill the form',
                                                );
                                              } else {
                                                setSteps(
                                                  step['index'],
                                                  markComplete: false,
                                                );
                                              }
                                            },
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                if (!widget.fromTurfScreen)
                                                  DottedBorder(
                                                    dashPattern: [2, 5],
                                                    padding: EdgeInsets.all(0),
                                                    child: Container(
                                                      width: deviceWidth * 0.26,
                                                    ),
                                                  ),
                                                Container(
                                                  width: deviceWidth * 0.26,
                                                  decoration: BoxDecoration(
                                                    color: step['isSelected']
                                                        ? Theme.of(context)
                                                            .colorScheme.secondary
                                                        : step['isCompleted']
                                                            ? Theme.of(context)
                                                                .primaryColor
                                                            : Color(0xffF3F3F4),
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: step['isSelected']
                                                          ? Theme.of(context)
                                                              .colorScheme.secondary
                                                          : step['isCompleted']
                                                              ? Theme.of(
                                                                      context)
                                                                  .primaryColor
                                                              : Color(
                                                                  0xffF3F3F4),
                                                    ),
                                                  ),
                                                  padding: EdgeInsets.all(
                                                      deviceWidth * 0.021),
                                                  // margin: EdgeInsets.symmetric(
                                                  //     horizontal:
                                                  //         deviceWidth * 0.03),
                                                  constraints: BoxConstraints(
                                                      maxWidth:
                                                          deviceWidth * 0.26),
                                                  child: Text(
                                                    step['index'].toString(),
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: step['isCompleted'] ||
                                                              step['isSelected']
                                                          ? Colors.white
                                                          : Colors.black,
                                                      fontSize:
                                                          textScaleFactor * 20,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: deviceWidth * 0.035),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ...steps.map(
                                          (step) => Container(
                                            width: deviceWidth * 0.26,
                                            // margin: EdgeInsets.symmetric(
                                            //     horizontal: deviceWidth * 0.03),
                                            constraints: BoxConstraints(
                                                maxWidth: deviceWidth * 0.26),
                                            child: Text(
                                              step['title'].toString(),
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontSize: textScaleFactor * 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: deviceWidth * 0.2),
                      if (currentIndex == 1)
                        TurfDetailsForm(
                          setSteps: setSteps,
                          changeIsCompletedStatus: changeIsCompletedStatus,
                          setExitValue: setExitValue,
                        ),
                      widget.fromTurfScreen
                          ? SizedBox.shrink()
                          : currentIndex == 2
                              ? OwnerDetailsForm(
                                  setSteps: setSteps,
                                  isValidatePanAndBank: isValidatePanAndBank,
                                  changeIsCompletedStatus:
                                      changeIsCompletedStatus,
                                  setExitValue: setExitValue,
                                )
                              : SizedBox.shrink(),
                      widget.fromTurfScreen && currentIndex == 2
                          ? CommisionOnBoarding(
                              setSteps: setSteps,
                              changeIsCompletedStatus: changeIsCompletedStatus,
                              fromTurfScreen: widget.fromTurfScreen,
                              accessToken: widget.accessToken,
                              businessId: widget.businessId,
                              business: widget.business,
                            )
                          : currentIndex == 3
                              ? CommisionOnBoarding(
                                  setSteps: setSteps,
                                  changeIsCompletedStatus:
                                      changeIsCompletedStatus,
                                  fromTurfScreen: widget.fromTurfScreen,
                                )
                              : SizedBox.shrink(),
                      // if (currentIndex == 4)
                      //   PanDetailsForm(
                      //     isValidatePanAndBank: isValidatePanAndBank,
                      //     setSteps: setSteps,
                      //     changeIsCompletedStatus: changeIsCompletedStatus,
                      //   ),
                      // if (currentIndex == 5)
                      //   BankDetailsForm(
                      //     isValidatePanAndBank: isValidatePanAndBank,
                      //     setSteps: setSteps,
                      //     changeIsCompletedStatus: changeIsCompletedStatus,
                      //     referralCode: widget.referralCode,
                      //     referredByUserId: widget.referredByUserId,
                      //   ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
