import 'dart:io';

import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_button.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/raisedButton.dart';
import '../../common_function.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../homeModule/screens/homeScreen.dart';
import '../../moreModule.dart/screens/accountScreen.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../widgets/gender_dropdown.dart';
import 'registration_success_screen.dart';

class PersonalInfoScreen extends StatefulWidget {
  final String mobileNumber;
  PersonalInfoScreen({Key? key, required this.mobileNumber}) : super(key: key);

  @override
  _PersonalInfoScreenState createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  //Controllers
  TextEditingController firstNameController = new TextEditingController();
  TextEditingController lastNameController = new TextEditingController();
  TextEditingController emailNameController = new TextEditingController();
  TextEditingController dobController = new TextEditingController();
  TextEditingController genderController = new TextEditingController();

  bool isLoading = false;
  bool accountValidation = false;

  DateTime? selectedDate;
  String selectedGender = '';
  String? imageUrl;

  setImageData(ImageSource source) async {
    // XFile? selectedImage = await pickImage(source);
    XFile? selectedImage = await pickCropImage(imageSource: source);
    pop();
    if (selectedImage != null) {
      File pickedImage = File(selectedImage.path);
      if (getImageSize(pickedImage) > 5) {
        showSnackbar('Image size should be less than 5 MB');
      } else {
        final jpegData = await convertToJpeg(pickedImage, selectedImage.name);
        imageUrl = jpegData.path;
        setState(() {});
      }
    }
  }

  void showImagePickOption() {
    hideKeyBoard(context);
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return Container(
            padding: EdgeInsets.all(dW * 0.05),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    Text(
                      "Profile Photo",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Color.fromRGBO(41, 49, 49, 1),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.05),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () => setImageData(ImageSource.gallery),
                      child: BottomSheetContent(
                        svgColor: Theme.of(context).primaryColor,
                        icon: Icons.image,
                        title: "Gallery",
                        title2: "",
                      ),
                    ),
                    SizedBox(width: dW * 0.1),
                    GestureDetector(
                      onTap: () => setImageData(ImageSource.camera),
                      child: BottomSheetContent(
                        svgColor: Theme.of(context).primaryColor,
                        icon: Icons.camera_alt,
                        title: "Camera",
                        title2: "",
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }

  formSubmit() async {
    if (isLoading) return;
    if (accountValidation) genderController.text = 'Male';

    setState(() => isLoading = true);
    try {
      String? fcmToken = await FirebaseMessaging.instance.getToken();

      Map<String, String> body = {
        "firstName": firstNameController.text.trim(),
        "lastName": lastNameController.text.trim(),
        "fcmTokens": fcmToken ?? '',
        "phone": widget.mobileNumber,
        "email": emailNameController.text.trim(),
        "dob": accountValidation ? 'null' : selectedDate.toString(),
        "gender": accountValidation ? 'null' : selectedGender,
        "avatar": imageUrl ?? ''
      };

      Map<String, String> files = {};
      if (imageUrl != null) files['avatar'] = imageUrl!;

      final response = await Provider.of<Auth>(context, listen: false)
          .registerBusiness(body: body, files: files);
      if (response['success']) {
        // pushAndRemoveUntil(HomeScreen());
        pushAndRemoveUntil(RegistrationSuccessScreen());
      } else {
        if (response['message'] == 'EMAIL_EXIST') {
          showSnackbar('Email address is already in use.');
        } else {
          showSnackbar('Unable to register');
        }
      }
    } catch (e) {
      callToastMessage("Something went wrong $e");
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  selectGenderDropDown() {
    hideKeyBoard(context);

    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: GenderDropDown(
          selectedGender: selectedGender,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        setState(() {
          selectedGender = value.toString();
          genderController.text = selectedGender;
        });
      }
    });
  }

  chooseDOB() {
    if (selectedDate == null) {
      selectedDate = DateTime(
        DateTime.now().year - 18,
        DateTime.now().month,
        DateTime.now().day,
      );
      setState(() {
        dobController.text = DateFormat('dd-MMM-yyyy').format(selectedDate!);
      });
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      elevation: 1.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      builder: (builder) => Container(
        height: dH * 0.3,
        child: Stack(
          children: [
            CupertinoDatePicker(
              initialDateTime: selectedDate != null
                  ? selectedDate
                  : DateTime(
                      DateTime.now().year - 18,
                      DateTime.now().month,
                      DateTime.now().day,
                    ),
              onDateTimeChanged: (DateTime date) {
                selectedDate = date;
                dobController.text =
                    DateFormat('dd-MMM-yyyy').format(selectedDate!);
              },
              minimumYear: 1950,
              maximumYear: 2250,
              mode: CupertinoDatePickerMode.date,
            ),
            Positioned(
              right: 0,
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Done',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    ).then((value) {
      setState(() {});
    });
  }

  bool isButtonEnable() {
    bool isValid = firstNameController.text.trim().isEmpty ||
        lastNameController.text.trim().isEmpty ||
        emailNameController.text.trim().isEmpty ||
        !EmailValidator.validate(emailNameController.text.trim()) ||
        selectedDate == null ||
        (selectedGender == '' && !accountValidation);

    return isValid;
  }

  @override
  void initState() {
    super.initState();
    accountValidation = Provider.of<Auth>(context, listen: false).deleteAccount;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
                 
              NewAppBar(dW: dW, title: 'Create Profile', hideLeading: true),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  physics: BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        children: [
                          Center(
                            child: Padding(
                              padding: EdgeInsets.only(top: dW * 0.06),
                              child: CircleAvatar(
                                radius: 50,
                                backgroundImage: imageUrl != null
                                    ? FileImage(File(imageUrl!)) as ImageProvider
                                    : const AssetImage(
                                        'assets/images/user_placeholder.jpeg'),
                              ),
                            ),
                          ),
                          Center(
                            child: Padding(
                              padding: EdgeInsets.only(bottom: dW * 0.05),
                              child: TextButton(
                                  onPressed: showImagePickOption,
                                  child: Text(
                                    'Upload',
                                    style: Theme.of(context)
                                        .textTheme
                                        .displayMedium!
                                        .copyWith(
                                            fontSize: tS * 14,
                                            color:
                                                Theme.of(context).primaryColor),
                                  )),
                            ),
                          ),
                        ],
                      ),
                      CustomTextFieldWithLabel(
                        label: 'First Name',
                        controller: firstNameController,
                        hintText: 'Enter first name',
                        textCapitalization: TextCapitalization.words,
                      ),
                      SizedBox(height: dW * 0.05),
                      CustomTextFieldWithLabel(
                        label: 'Last Name',
                        controller: lastNameController,
                        hintText: 'Enter last name',
                        textCapitalization: TextCapitalization.words,
                      ),
                      SizedBox(height: dW * 0.05),
                      CustomTextFieldWithLabel(
                        label: 'Email ID',
                        controller: emailNameController,
                        hintText: 'Enter email id',
                        inputType: TextInputType.emailAddress,
                        maxLines: null,
                      ),
                      if (!accountValidation)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: dW * 0.055),
                            GestureDetector(
                              onTap: chooseDOB,
                              child: CustomTextFieldWithLabel(
                                label: 'DOB',
                                controller: dobController,
                                hintText: "Select your birth date ",
                                enabled: false,
                                suffixIcon: Padding(
                                  padding: EdgeInsets.all(12),
                                  child: AssetSvgIcon(
                                      iconName: 'calendar1', height: 20),
                                ),
                              ),
                            ),
                            SizedBox(height: dW * .05),
                            //Gender
                            Row(
                              children: [
                                Text('Gender'),
                                Text("*", style: TextStyle(color: Colors.red))
                              ],
                            ),
                            SizedBox(height: dW * .025),
                            GestureDetector(
                              onTap: selectGenderDropDown,
                              child: CustomContainer(
                                  borderColor: getThemeColor(),
                                  vPadding: 0.035,
                                  hPadding: .035,
                                  boxShadow: [],
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      TextWidget(
                                        title: selectedGender == ''
                                            ? 'Select Gender'
                                            : selectedGender,
                                        color: selectedGender == ''
                                            ? placeholderColor
                                            : Colors.black,
                                        fontWeight: selectedGender == ''
                                            ? FontWeight.normal
                                            : FontWeight.w600,
                                      ),
                                      Icon(
                                        Icons.keyboard_arrow_down,
                                        color: getThemeColor(),
                                      )
                                    ],
                                  )),
                            ),
                          ],
                        ),
                      SizedBox(height: dW * 0.12)
                    ],
                  ),
                ),
              ),
              BottomAlignedWidget(
                dW: dW,
                dH: dH,
                child: CustomButton(
                  width: dW,
                  height: dW * 0.12,
                  radius: 7,
                  fontSize: 17,
                  buttonText: 'Create Profile',
                  onPressed: isButtonEnable()
                      ? null
                      : isLoading
                          ? () {}
                          : formSubmit,
                  isLoading: isLoading,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
