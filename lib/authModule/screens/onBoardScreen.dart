import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/authModule/screens/registerMainScreen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/circular_loader.dart';
import '../../common_function.dart';
import '../../navigators.dart';
import 'loginScreen.dart';

class OnBoardScreen extends StatelessWidget {
  final String referralCode;
  final String referredByUserId;

  OnBoardScreen(this.referralCode, this.referredByUserId, {Key? key})
      : super(key: key);

  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme customTextTheme = const TextTheme();
  Map language = {};
  bool isLoading = false;

  Widget getButton({
    required bool isFilled,
    required BuildContext context,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: dW,
        height: dW * .13,
        decoration: BoxDecoration(
            border: Border.all(color: getThemeColor()),
            borderRadius: BorderRadius.circular(121),
            color: isFilled ? getThemeColor() : Colors.white),
        child: Center(
            child: Text(
          text,
          style: customTextTheme.headlineSmall!.copyWith(
              fontWeight: FontWeight.w600,
              color: isFilled ? Colors.white : getThemeColor()),
        )),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<Auth>(context).selectedLanguage;
    customTextTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: iOSCondition(dH)
          ? screenBody(context: context)
          : SafeArea(child: screenBody(context: context)),
    );
  }

  screenBody({required BuildContext context}) {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
          : Padding(
              padding: EdgeInsets.all(dW * .05),
              child: Stack(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        "assets/images/on_boarding.png",
                        scale: 2,
                      ),
                      SizedBox(height: dW * .015),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: dW * .08),
                        child: Text(
                          language['onBoardingSubTitle'],
                          style: customTextTheme.displaySmall!
                              .copyWith(fontWeight: FontWeight.w500),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(height: dW * .25),
                    ],
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(height: dW * .2),
                      getButton(
                        isFilled: true,
                        context: context,
                        text: language['businessLogin'],
                        onTap: () {
                          push(
                            LoginScreen(
                              role: 'Business',
                              referralCode: referralCode,
                              referredByUserId: referredByUserId,
                            ),
                          );
                        },
                      ),
                      SizedBox(height: dW * .07),
                      getButton(
                        isFilled: false,
                        context: context,
                        text: language['employeeLogin'],
                        onTap: () {
                          push(LoginScreen(role: 'Employee'));
                          // push(LoginScreen(
                          //   role: 'Employee',
                          //   referralCode: referralCode,
                          //   referredByUserId: referredByUserId,
                          // ));
                        },
                      ),
                      SizedBox(height: dW * .05),
                    ],
                  )
                ],
              ),
            ),
    );
  }
}
