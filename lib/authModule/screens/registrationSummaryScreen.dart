import 'dart:io';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';
import '../../authModule/modals/registration.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../homeModule/screens/homeScreen.dart';
import '../../fontSizes.dart';

class RegistrationSummaryScreen extends StatefulWidget {
  final bool isValidatePanAndBank;
  final referralCode;
  final referredByUserId;

  const RegistrationSummaryScreen({
    Key? key,
    required this.isValidatePanAndBank,
    this.referralCode = '',
    this.referredByUserId = '',
  });

  @override
  _RegistrationSummaryScreenState createState() =>
      _RegistrationSummaryScreenState();
}

class _RegistrationSummaryScreenState extends State<RegistrationSummaryScreen> {
  bool isLoading = false;
  late TurfDetails turfDetails;
  BankDetails? bankDetails;
  late OwnerDetails ownerDetails;
  PanDetails? panDetails;

  String gstType = '';
  String gstRegistered = '';
  String gstNo = '';

  @override
  void initState() {
    super.initState();
    final auth = Provider.of<Auth>(context, listen: false);
    turfDetails = auth.turfDetails;
    bankDetails = auth.bankDetails;
    ownerDetails = auth.ownerDetails;
    panDetails = auth.panDetails;
    gstType = auth.gstType;
    gstRegistered = auth.gstRegistered;
    gstNo = auth.gstNo;
  }

  register() async {
    try {
      setState(() {
        isLoading = true;
      });
      final data = await Provider.of<Auth>(context, listen: false)
          .createUserAndBusinessAndTurf(
        referralCode: widget.referralCode,
        referredByUserId: widget.referredByUserId,
      );
      if (data != null) {
        final auth = Provider.of<Auth>(context, listen: false);

        auth.turfDetails = null;
        auth.bankDetails = null;
        auth.ownerDetails = null;
        auth.panDetails = null;
        auth.commissionAccepeted = false;
        auth.gstNo = '';
        auth.gstRegistered = '';
        auth.gstType = '';

        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => HomeScreen()),
          (Route<dynamic> route) => false,
        );
        setState(() {
          isLoading = false;
        });
      } else {
        callToastMessage('Something went wrong');
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    PreferredSizeWidget? androidAppBar = AndroidAppBar(
      title: 'Form Review',
      deviceWidth: deviceWidth,
      elevation: 1.0,
    );

    return Scaffold(
      appBar: androidAppBar,
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Container(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * 0.02),
                //TURF DETAILS
                buildHeadingText(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Venue Details',
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Venue Name',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.turfName,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Venue Contact Number',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.mobileNo,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Street Name',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.streetName,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Landmark',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.landmark,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Pincode',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.pincode.toString(),
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'City',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.city,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'State',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  turfDetails.state,
                ),
                //OWNER DETAILS
                buildHeadingText(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Owner Details',
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Owner Name',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  ownerDetails.ownerName,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Owner Email',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  ownerDetails.email,
                ),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Owner Number',
                ),
                buildEnteredValueContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  ownerDetails.mobileNo,
                ),

                !widget.isValidatePanAndBank
                    ? SizedBox.shrink()
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          //COMMISSION DETAILS
                          buildHeadingText(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            'Commission',
                          ),
                          buildTitleContainer(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            'Commission Acceptable',
                          ),
                          buildEnteredValueContainer(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            'Accepted',
                          ),

                          //GST DETAILS
                          buildHeadingText(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            'GST Details',
                          ),
                          buildTitleContainer(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            'Is your business GST registered?',
                          ),
                          buildEnteredValueContainer(
                            deviceWidth,
                            textScaleFactor,
                            context,
                            gstRegistered,
                          ),

                          if (gstRegistered == 'Yes') ...[
                            buildTitleContainer(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              'GST Type',
                            ),
                            buildEnteredValueContainer(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              gstType,
                            ),
                            buildTitleContainer(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              'GST Number',
                            ),
                            buildEnteredValueContainer(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              gstNo,
                            ),
                          ],

                          //PAN CARD DETAILS
                          // buildHeadingText(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'PAN Card Details',
                          // ),
                          // buildTitleContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'PAN Card Number',
                          // ),
                          // buildEnteredValueContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   panDetails == null ? '' : panDetails!.panNumber,
                          // ),
                          // if (panDetails!.panImage != null)
                          //   buildTitleContainer(
                          //     deviceWidth,
                          //     textScaleFactor,
                          //     context,
                          //     'PAN Card Image',
                          //   ),
                          // if (panDetails!.panImage != null)
                          //   buildImageContainer(
                          //     deviceWidth,
                          //     panDetails!.panImage!,
                          //   ),
                          //BANK DETAILS
                          // buildHeadingText(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'Bank Details',
                          // ),
                          // buildTitleContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'Account Holder Name',
                          // ),
                          // buildEnteredValueContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   bankDetails == null
                          //       ? ''
                          //       : bankDetails!.accountHolderName,
                          // ),
                          // buildTitleContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'Bank Name',
                          // ),
                          // buildEnteredValueContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   bankDetails == null ? '' : bankDetails!.bankName,
                          // ),
                          // buildTitleContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'Account Number',
                          // ),
                          // buildEnteredValueContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   bankDetails == null
                          //       ? ''
                          //       : bankDetails!.accountNumber.toString(),
                          // ),
                          // buildTitleContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   'IFSC Code',
                          // ),
                          // buildEnteredValueContainer(
                          //   deviceWidth,
                          //   textScaleFactor,
                          //   context,
                          //   bankDetails == null ? '' : bankDetails!.ifscCode,
                          // ),
                          // if (bankDetails!.chequeImageUrl != null)
                          //   buildTitleContainer(
                          //     deviceWidth,
                          //     textScaleFactor,
                          //     context,
                          //     'Cancelled Cheque Image',
                          //   ),
                          // if (bankDetails!.chequeImageUrl != null)
                          //   buildImageContainer(
                          //     deviceWidth,
                          //     bankDetails!.chequeImageUrl!,
                          //   ),
                        ],
                      ),

                SizedBox(height: deviceWidth * 0.35),
              ],
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: deviceWidth * 0.08),
        child: buildRaisedButton(
          deviceWidth * 0.9,
          deviceWidth * 0.12,
          isLoading ? () {} : register,
          isLoading
              ? circularForButton(deviceWidth)
              : Text(
                  'Proceed & Confirm',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                        fontSize: textScaleFactor * 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: .50,
                        color: Colors.white,
                      ),
                ),
          TargetPlatform.android,
          Theme.of(context).primaryColor,
          7,
        ),
      ),
    );
  }
}

Widget buildHeadingText(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String text,
) {
  return Container(
    margin: EdgeInsets.only(
      bottom: deviceWidth * 0.05,
      top: deviceWidth * 0.03,
    ),
    alignment: Alignment.topLeft,
    child: Text(
      text,
      textAlign: TextAlign.center,
      style: Theme.of(context)
          .textTheme
          .displaySmall!
          .copyWith(fontSize: textScaleFactor * displayMedium),
    ),
  );
}

Widget buildTitleContainer(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String text,
) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
    constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
    child: Text(
      text,
      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
            fontSize: textScaleFactor * 14,
            color: const Color(0xff434343),
          ),
    ),
  );
}

Widget buildEnteredValueContainer(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String text,
) {
  return Container(
    margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
    width: deviceWidth,
    padding: EdgeInsets.symmetric(
      vertical: deviceWidth * 0.02,
      // horizontal: deviceWidth * 0.04,
    ),
    decoration: BoxDecoration(
      // color: Colors.grey.shade300,
      // borderRadius: BorderRadius.circular(100),
      border: Border(
        bottom: BorderSide(color: Colors.grey.shade500),
      ),
    ),
    child: Text(
      text,
      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
            fontSize: textScaleFactor * 15,
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
    ),
  );
}

Widget buildImageContainer(double deviceWidth, File image) {
  return Container(
    height: deviceWidth * 0.4,
    width: deviceWidth,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(7),
      border: Border.all(color: Colors.black38),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(7),
      child: Image.file(
        image,
        fit: BoxFit.cover,
      ),
    ),
  );
}
