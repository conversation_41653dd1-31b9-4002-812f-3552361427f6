// ignore_for_file: must_be_immutable

import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/homeModule/screens/homeScreen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../common_function.dart';
import '../../navigators.dart';

class RegistrationSuccessScreen extends StatelessWidget {
  RegistrationSuccessScreen({Key? key}) : super(key: key);

  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme customTextTheme = const TextTheme();
  Map language = {};

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<Auth>(context).selectedLanguage;
    customTextTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: iOSCondition(dH)
          ? screenBody(context: context)
          : SafeArea(child: screenBody(context: context)),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: dW * 0.05),
        child: CustomButton(
          width: dW * 0.9,
          height: dW * 0.12,
          fontSize: 16,
          buttonText: 'Continue',
          onPressed: () => push(HomeScreen()),
        ),
      ),
    );
  }

  screenBody({required BuildContext context}) {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
        padding: EdgeInsets.all(dW * .05),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: dW * 0.07),
              child: TextWidget(
                title: 'Welcome to BYS',
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Color(0xff3E3E3E),
              ),
            ),
            SizedBox(height: dW * 0.3),
            Column(
              children: [
                Image.asset("assets/images/registration_success.png",
                    scale: 2.5),
                SizedBox(height: dW * .08),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: dW * .08),
                  child: Text(
                    'Onboard your venues or turf on our platform and start earning from today.',
                    style: customTextTheme.displaySmall!.copyWith(
                      color: Colors.black,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
