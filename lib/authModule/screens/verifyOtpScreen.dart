import 'dart:async';
import 'dart:io';

import 'package:bys_business/authModule/screens/personal_info_screen.dart';
import 'package:bys_business/common_function.dart';

import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/policy_text_widget.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../employeeModule/screens/employee_business_screen.dart';
import 'package:otp_autofill/otp_autofill.dart';

import 'package:flutter/material.dart';

import '../../authModule/providers/auth.dart';
import 'package:provider/provider.dart';
import '../../navigators.dart';
import '/commonWidgets/dialogBox.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../homeModule/screens/homeScreen.dart';
import '../../authModule/screens/suspendScreen.dart';

class VerifyOtpScreen extends StatefulWidget {
  final String? mobileNo;
  final String? referralCode;
  final String? referredByUserId;
  final String role;
  const VerifyOtpScreen({
    this.mobileNo,
    this.referralCode,
    this.referredByUserId,
    this.role = 'Business',
  });
  @override
  _VerifyOtpScreenState createState() => _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends State<VerifyOtpScreen> {
  TextEditingController _otpController = new TextEditingController();
  var userDetails;
  bool isMobileNoVerified = true;
  bool _isLoading = false;
  FocusNode otpFocusNode = FocusNode();
  bool isReadyToResend = false;
  bool inCorrect = false;
  var userExistance;
  var employeeExistance;
  bool isUsernameAlreadyExist = false;
  bool isEmployeeAlreadyExist = false;
  String errorText = 'Incorrect otp entered..';
  int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
  // int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 5;
  late OTPInteractor _otpInteractor;
  late OTPTextEditController controller;

  ScrollController _scrollController = ScrollController();
  bool scroll = false;
  bool disableControllerJump = true;

  late Timer _timer;
  int _start = 50;
  int otpSentCount = 1;

  void startTimer() {
    _start = 50;
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
            isReadyToResend = true;
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  _checkForUserName(String mobileNo) async {
    userExistance =
        await Provider.of<Auth>(context, listen: false).isUserExists(
      mobileNo,
    );
    if (userExistance['message'] == "User Already exists") {
      isUsernameAlreadyExist = true;
    } else {
      isUsernameAlreadyExist = false;
    }
  }

  _checkForEmployee(String mobileNo) async {
    employeeExistance =
        await Provider.of<Auth>(context, listen: false).isEmployeeExists(
      mobileNo,
    );
    if (employeeExistance['message'] == "Employee Already exists") {
      isEmployeeAlreadyExist = true;
    } else {
      isEmployeeAlreadyExist = false;
    }
  }

  // _verifyOTP() async {
  //   try {
  //     await _checkForUserName(widget.mobileNo!);
  //     if (isUsernameAlreadyExist) {
  //       pushAndRemoveUntil(userExistance['business']['isActive']
  //           ? HomeScreen()
  //           : SuspendScreen());
  //     } else {
  //       pushReplacement(
  //           PersonalInfoScreen(mobileNumber: widget.mobileNo ?? ''));
  //       _isLoading = false;
  //       setState(() {});
  //     }
  //   } catch (e) {
  //     print(e);
  //     _isLoading = false;
  //     setState(() {});
  //   }
  // }
// **********
  _verifyBusinessOTP() async {
    setState(() {
      _isLoading = true;
    });
    final data =
        await Provider.of<Auth>(context, listen: false).verifyOTPofUser(
      widget.mobileNo.toString(),
      _otpController.text.trim(),
    );
    if (data == 'success') {
      await _checkForUserName(widget.mobileNo!);
      if (isUsernameAlreadyExist) {
        pushAndRemoveUntil(userExistance['business']['isActive']
            ? HomeScreen()
            : SuspendScreen());
      } else {
        pushReplacement(
            PersonalInfoScreen(mobileNumber: widget.mobileNo ?? ''));
        _isLoading = false;
        setState(() {});
      }
    } else {
      callToastMessage('Incorrect Otp');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // _verifyEmployeeOTP() async {
  //   try {
  //     await _checkForEmployee(widget.mobileNo!);
  //     if (isEmployeeAlreadyExist) {
  //       pushAndRemoveUntil(EmployeeBusinessScreen());
  //     } else {
  //       showSnackbar('Employee is not registered with this phone number');
  //       setState(() {
  //         _isLoading = false;
  //       });
  //     }
  //   } catch (e) {
  //     print(e);
  //     setState(() {
  //       _isLoading = false;
  //     });
  //   }
  // }

  _verifyEmployeeOTP() async {
    setState(() {
      _isLoading = true;
    });

    final data =
        await Provider.of<Auth>(context, listen: false).verifyOTPofUser(
      widget.mobileNo.toString(),
      _otpController.text.trim(),
    );
    if (data == 'success') {
      pushAndRemoveUntil(EmployeeBusinessScreen());
    } else {
      callToastMessage('Incorrect Otp');
      setState(() {
        _isLoading = false;
      });
    }
  }

  showDialogBox(String error) {
    return dialogBoxCustom(
      title: 'Alert',
      titleStyle: TextStyle(
          fontSize: 22, fontWeight: FontWeight.w500, color: Colors.black),
      content: error,
      contentStyle: TextStyle(
          fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black),
      okBtnText: 'Okay',
      okBtnStyle: TextStyle(
          fontSize: 15, fontWeight: FontWeight.w400, color: Colors.lightBlue),
      okBtnPress: () => {Navigator.of(context).pop()},
      platform: Theme.of(context).platform,
      context: context,
    );
  }

  Future<void> save() async {
    if (_isLoading) return;
    if (_otpController.text.isEmpty) {
      setState(() {
        inCorrect = true;
        errorText = 'Please enter otp';
      });
      showSnackbar('Please enter otp');
      return;
    } else if (_otpController.text.length < 6) {
      setState(() {
        inCorrect = true;
        errorText = 'Please enter valid otp';
      });
      showSnackbar('Please enter valid otp');

      return;
    }
    final otp = _otpController.text;
    try {
      setState(() {
        inCorrect = false;
        _isLoading = true;
      });

      // final data = await Provider.of<Auth>(context, listen: false)
      //     .verifyOTPofUser(widget.mobileNo.toString(), otp);
      // if (data == 'success') {
      if (widget.role == 'Business') {
        await _verifyBusinessOTP();
      } else if (widget.role == 'Employee') {
        await _verifyEmployeeOTP();
      }
      // } else {
      //   showSnackbar('Incorrect OTP entered');
      //   setState(() {
      //     _isLoading = false;
      //     inCorrect = true;
      //     errorText = 'Incorrect otp entered.';
      //   });
      // }
    } catch (e) {
      setState(() {
        _isLoading = false;
        inCorrect = true;
        errorText = 'Incorrect otp entered.';
      });
      showSnackbar('Incorrect OTP entered');
    }
  }

  resendOTPToUser() async {
    try {
      setState(() {
        otpSentCount += 1;
        if (otpSentCount > 3) {
          isReadyToResend = false;
        }
      });
      if (isReadyToResend) {
        setState(() {
          isReadyToResend = false;
          startTimer();
        });
        final data = await Provider.of<Auth>(context, listen: false)
            .resendOTPtoUser(widget.mobileNo.toString(), 'text');
        if (data != "success") {
          showSnackbar('Something went wrong');
        }
      }
    } catch (e) {
      print(e);
    }
  }

  Future<bool> _willPopCallback() async {
    // SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    Navigator.of(context).pop();
    return true;
  }

  @override
  void dispose() {
    super.dispose();
    _timer.cancel();
    if (Platform.isAndroid) {
      _otpInteractor.stopListenForCode();
    }
  }

  @override
  void initState() {
    super.initState();
    startTimer();
    if (Platform.isAndroid) {
      _otpInteractor = OTPInteractor();
      _otpInteractor
          .getAppSignature()
          //ignore: avoid_print
          .then((value) => print('signature - $value'));

      controller = OTPTextEditController(
        codeLength: 6,
        //ignore: avoid_print
        onCodeReceive: (code) {
          print('Your Application receive code - $code');
          _otpController.text = code;
        },
        otpInteractor: _otpInteractor,
      )..startListenUserConsent(
          (code) {
            final exp = RegExp(r'(\d{6})');
            return exp.stringMatch(code ?? '') ?? '';
          },
          strategies: [
            // SampleStrategy(),
          ],
        );
    }
  }

  void onEnd() {
    setState(() {
      isReadyToResend = true;
    });
    print('onEnd');
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    if (MediaQuery.of(context).viewInsets.bottom == 0) {
      setState(() {
        scroll = false;
      });
      if (!disableControllerJump) {
        _scrollController.jumpTo(0);
      }
    } else {
      setState(() {
        scroll = true;
      });
    }
    disableControllerJump = false;

    return Scaffold(
        resizeToAvoidBottomInset: true,
        body: LayoutBuilder(
          builder: (context, constraints) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  otpFocusNode.unfocus();
                });
              },
              child: SafeArea(
                child: Container(
                  height: height,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: scroll
                        ? AlwaysScrollableScrollPhysics()
                        : NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: width * 0.05),
                    child: ConstrainedBox(
                      constraints:
                          BoxConstraints(minHeight: constraints.maxHeight),
                      child: IntrinsicHeight(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: width * 0.05),
                            InkWell(
                              onTap: pop,
                              child: Icon(Icons.arrow_back),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                top: width * 0.25,
                                bottom: width * 0.03,
                              ),
                              child: Image.asset('assets/images/verify_OTP.png',
                                  scale: 2),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 3),
                              child: Text(
                                "OTP Verification",
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            SizedBox(height: width * 0.03),
                            Container(
                              alignment: Alignment.topLeft,
                              child: Text(
                                "Please enter the 6-digit code send you at",
                                style: TextStyle(
                                  color: Color(0xff77838F),
                                  fontSize: textScaleFactor * 12,
                                  fontWeight: FontWeight.w400,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: width * 0.015),
                              alignment: Alignment.topLeft,
                              child: Text(
                                "+91 ${widget.mobileNo}",
                                style: TextStyle(
                                  fontSize: textScaleFactor * 12,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            SizedBox(
                              height: width * 0.07,
                            ),
                            Container(
                              // height: width * 0.12,
                              width: width * 0.87,
                              margin: EdgeInsets.only(left: 5),
                              child: PinCodeTextField(
                                appContext: context,
                                backgroundColor: Colors.transparent,
                                enablePinAutofill: true,
                                pastedTextStyle: TextStyle(
                                  color: Colors.black,
                                  fontSize: textScaleFactor * 22,
                                  fontWeight: FontWeight.w600,
                                ),
                                length: 6,
                                controller: _otpController,
                                autoDismissKeyboard: true,
                                cursorWidth: 1.5,
                                cursorHeight: width * 0.05,
                                cursorColor: Colors.black,
                                focusNode: otpFocusNode,
                                enableActiveFill: true,
                                animationType: AnimationType.fade,
                                keyboardType: TextInputType.number,
                                textStyle: TextStyle(
                                  color: Colors.black,
                                  fontSize: textScaleFactor * 22,
                                  letterSpacing: 0,
                                  fontWeight: FontWeight.w600,
                                ),
                                pinTheme: PinTheme(
                                  fieldHeight: width * 0.12,
                                  fieldWidth: width * 0.12,
                                  shape: PinCodeFieldShape.circle,
                                  // borderRadius: BorderRadius.circular(30),
                                  borderWidth: 0.8,
                                  activeColor: Colors.black,
                                  activeFillColor: Colors.transparent,
                                  selectedColor: Colors.black,
                                  selectedFillColor: Colors.transparent,
                                  disabledColor: Color(0xffEAEAEA),
                                  inactiveColor: Color(0xffEAEAEA),
                                  inactiveFillColor: Color(0xffEAEAEA),
                                ),
                                onChanged: (val) {
                                  if (val.length > 0) {
                                    setState(() {
                                      inCorrect = false;
                                    });
                                  }
                                },
                                validator: (value) {
                                  if (value!.length == 0) {
                                    return '';
                                  }
                                },
                              ),
                            ),
                            SizedBox(height: width * 0.06),
                            CustomButton(
                              width: width,
                              height: width * 0.12,
                              radius: 7,
                              buttonText: 'Verify OTP',
                              isLoading: _isLoading,
                              onPressed: save,
                            ),
                            SizedBox(height: width * 0.03),
                            Container(
                                margin: EdgeInsets.only(bottom: width * 0.09),
                                alignment: Alignment.bottomCenter,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    if (_start != 0)
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: width * 0.02,
                                            right: width * 0.02),
                                        child: Text(
                                          _start == 0
                                              ? '0:00'
                                              : _start > 9
                                                  ? 'Resend code in: 0:$_start'
                                                  : 'Resend code in: 0:0$_start',
                                          style: TextStyle(
                                            fontSize: textScaleFactor * 13.5,
                                            fontWeight: FontWeight.w600,
                                            color: otpSentCount > 3
                                                ? Colors.grey
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                    if (_start == 0)
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            vertical: width * 0.024),
                                        child: InkWell(
                                          onTap: !isReadyToResend
                                              ? null
                                              : otpSentCount > 3
                                                  ? () {}
                                                  : () {
                                                      resendOTPToUser();
                                                    },
                                          child: RichText(
                                            textAlign: TextAlign.center,
                                            text: TextSpan(
                                              style: TextStyle(
                                                  fontFamily: 'Poppins'),
                                              children: [
                                                TextSpan(
                                                  text: 'Didn\'t get it? ',
                                                  style: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 15,
                                                    color: otpSentCount >= 3
                                                        ? Colors.grey
                                                        : Colors.black87,
                                                  ),
                                                ),
                                                TextSpan(
                                                  text: 'Resend',
                                                  style: TextStyle(
                                                    fontSize:
                                                        textScaleFactor * 15,
                                                    color: !isReadyToResend
                                                        ? Colors.grey
                                                        : otpSentCount >= 3
                                                            ? Colors.grey
                                                            : Theme.of(context)
                                                                .primaryColor,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                )),
                            if (otpSentCount > 1)
                              Align(
                                alignment: Alignment.center,
                                child: Container(
                                  margin: EdgeInsets.only(top: width * 0.04),
                                  child: Text(
                                    'A new OTP has been sent\n($otpSentCount out of 3 attempts)',
                                    style: TextStyle(
                                      fontSize: textScaleFactor * 13,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            Expanded(
                              child: Align(
                                alignment: Alignment.bottomCenter,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    bottom: Platform.isAndroid
                                        ? width * 0.1
                                        : width * 0.2,
                                  ),
                                  child: PolicyTextWidget(
                                      dW: width, tS: textScaleFactor),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ));
  }
}
