import 'package:bys_business/notificationModule.dart/provider/notificationProvider.dart';

import '../../authModule/screens/onBoardScreen.dart';
import '../../common_function.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../authModule/providers/auth.dart';

class SuspendScreen extends StatelessWidget {
  final LocalStorage storage = new LocalStorage('bysBusiness');

  SuspendScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              child: Image.asset(
                'assets/images/suspended.png',
                height: deviceWidth * 0.32,
              ),
            ),
            SizedBox(
              height: deviceWidth * 0.08,
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
              margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.055),
              child: Text(
                'Your account has been suspended by the admin. Kindly contact admin for more information.',
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: textScaleFactor * (16),
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
            ),
            GestureDetector(
              onTap: () => callLaunch(
                '+91${Provider.of<Auth>(context, listen: false).adminContact}',
              ),
              child: Container(
                margin: EdgeInsets.all(deviceWidth * 0.04),
                padding: EdgeInsets.all(deviceWidth * 0.02),
                width: deviceWidth * 0.5,
                color: Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('assets/svgIcons/Inquiry.svg'),
                    SizedBox(width: deviceWidth * 0.03),
                    Text(
                      'Admin',
                      style: Theme.of(context).textTheme.displaySmall!.copyWith(
                            fontSize: textScaleFactor * displayMedium,
                            color: Colors.black,
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
        child: FloatingActionButton.extended(
          onPressed: () async {
            await Provider.of<Auth>(context, listen: false).deleteFCMToken();
            await storage.clear();
            Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => OnBoardScreen('', ''),
                ),
                (route) => false);
          },
          label: Row(
            children: [
              Text(
                'Logout',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: textScaleFactor * 15,
                ),
              ),
              SizedBox(width: deviceWidth * 0.02),
              Icon(
                Icons.logout,
                size: 18,
                color: Colors.white,
              ),
            ],
          ),
          backgroundColor: Theme.of(context).primaryColor,
        ),
      ),
    );
  }
}
