import 'dart:io';

import '../../authModule/modals/registration.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../authModule/providers/auth.dart';
import '../../authModule/widgets/messageWidget.dart';
import '../../commonWidgets/imageValidator.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../authModule/screens/registrationSummaryScreen.dart';

class BankDetailsForm extends StatefulWidget {
  final Function setSteps;
  final bool isValidatePanAndBank;
  final referralCode;
  final referredByUserId;

  final Function changeIsCompletedStatus;
  const BankDetailsForm({
    Key? key,
    required this.isValidatePanAndBank,
    required this.setSteps,
    required this.changeIsCompletedStatus,
    this.referralCode = '',
    this.referredByUserId = '',
  }) : super(key: key);

  @override
  _BankDetailsFormState createState() => _BankDetailsFormState();
}

class _BankDetailsFormState extends State<BankDetailsForm> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController accountNameController = TextEditingController();
  TextEditingController bankNameController = TextEditingController();
  TextEditingController accountNumberController = TextEditingController();
  TextEditingController ifscController = TextEditingController();
  // RegExp ifscReg = RegExp('^[A-Z]{4}[0][A-Z0-9]{6}$');

  var fileMedia1;
  String imagePath1 = '';
  final _uploadImage1Error = MyValidator(hasError: false);
  Future _capture(int i) async {
    setState(() {
      if (i == 1) {
        _uploadImage1Error.hasError = false;
      }
    });
    final getMedia = ImagePicker().pickImage;
    final media = await getMedia(source: ImageSource.gallery);
    final file = File(media!.path);
    if (file == null) {
      return;
    } else {
      setState(() {
        if (i == 1) {
          fileMedia1 = file;
          imagePath1 = media.path;
        }
      });
    }
  }

  void _uploadImageValidator() {
    if (fileMedia1 == null) {
      setState(() {
        _uploadImage1Error.hasError = true;
        _uploadImage1Error.message = 'Please upload your License photo';
      });
    } else {
      setState(() {
        _uploadImage1Error.hasError = false;
      });
    }
  }

  saveDetails() {
    final isValid = _formKey.currentState!.validate();
    if (isValid) {
      widget.setSteps(6);
      Provider.of<Auth>(context, listen: false).bankDetails = BankDetails(
        bankName: bankNameController.text.trim(),
        accountNumber: accountNumberController.text == ""
            ? 0
            : int.parse(accountNumberController.text.trim()),
        accountHolderName: accountNameController.text.trim(),
        ifscCode: ifscController.text.trim(),
        chequeImageUrl: fileMedia1 ?? null,
      );
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => RegistrationSummaryScreen(
            isValidatePanAndBank: widget.isValidatePanAndBank,
            referralCode: widget.referralCode,
            referredByUserId: widget.referredByUserId,
          ),
        ),
      );
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    var data = Provider.of<Auth>(context, listen: false).bankDetails;
    if (data != null) {
      accountNameController.text = data.accountHolderName;
      fileMedia1 = data.chequeImageUrl;
      bankNameController.text = data.bankName;
      accountNumberController.text = data.accountNumber.toString();
      ifscController.text = data.ifscCode;
      widget.changeIsCompletedStatus(5);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final deviceHeight = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.055),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
              alignment: Alignment.topLeft,
              child: Text(
                'Bank Details',
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .displaySmall!
                    .copyWith(fontSize: textScaleFactor * displayMedium),
              ),
            ),
            MessageWidget(
              deviceWidth: deviceWidth,
              text:
                  'Please provide valid Bank account details. The booking fees will be transferred to this bank account.',
              textScaleFactor: textScaleFactor,
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Account Holder Name*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Holder Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.words,
                controller: accountNameController,
                keyboardType: TextInputType.text,
                onChanged: (value) {},
                validator: (value) {
                  if (widget.isValidatePanAndBank) {
                    if (value!.trim().isEmpty) {
                      return 'Please enter account holder name';
                    }
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Bank Name*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Bank Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.words,
                controller: bankNameController,
                keyboardType: TextInputType.text,
                onChanged: (value) {},
                validator: (value) {
                  if (widget.isValidatePanAndBank) {
                    if (value!.trim().isEmpty) {
                      return 'Please select bank name';
                    }
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Account Number*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  hintText: "Account Number",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                maxLength: 18,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cursorColor: Colors.black,
                controller: accountNumberController,
                keyboardType: TextInputType.number,
                onChanged: (value) {},
                validator: (value) {
                  if (widget.isValidatePanAndBank) {
                    if (value!.trim().isEmpty) {
                      return 'Please enter account number';
                    } else if (value.length < 9 || value.length > 18) {
                      return 'Please enter valid account number';
                    }
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'IFSC Code*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "IFSC Code",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                textCapitalization: TextCapitalization.characters,
                cursorColor: Colors.black,
                controller: ifscController,
                keyboardType: TextInputType.text,
                onChanged: (value) {},
                maxLength: 11,
                validator: (value) {
                  if (widget.isValidatePanAndBank) {
                    if (value!.trim().isEmpty) {
                      return 'Please enter ifsc code';
                    } else if (value.trim().length < 11)
                      return 'Please enter valid IFSC Code';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Cancelled Cheque Image *',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            SizedBox(height: deviceWidth * 0.01),
            fileMedia1 != null
                ? Stack(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: Container(
                            width: deviceWidth * 0.9,
                            height: deviceWidth * 0.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.black,
                            ),
                            child: Image.file(
                              fileMedia1,
                              fit: BoxFit.cover,
                            ), ////////////////////
                          )),
                      Positioned(
                        right: deviceWidth * 0.025,
                        bottom: deviceWidth * 0.025,
                        child: GestureDetector(
                          onTap: () => _capture(1),
                          child: Container(
                            height: deviceHeight * 0.045,
                            width: deviceWidth * 0.13,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                color: Colors.black54),
                            child: Center(
                              child: Text('Edit',
                                  style: TextStyle(
                                      letterSpacing: 0.1,
                                      color: Theme.of(context).scaffoldBackgroundColor,
                                      fontSize: deviceWidth * 0.03,
                                      fontWeight: FontWeight.w400)),
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                : Container(
                    child: DottedBorder(
                      borderType: BorderType.RRect,
                      radius: Radius.circular(10),
                      dashPattern: [3, 3],
                      strokeCap: StrokeCap.butt,
                      color: Colors.black,
                      strokeWidth: 0.4,
                      child: Container(
                        width: deviceWidth * 0.9,
                        height: deviceWidth * 0.45,
                        // alignment: Alignment.center,
                        child: Container(
                          // alignment: Alignment.,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Card(
                                elevation: 4,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    Icons.camera_alt,
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                  onPressed: () => _capture(1),
                                ),
                              ),
                              SizedBox(height: deviceWidth * 0.02),
                              Text(
                                'Click to upload a picture of Cancelled Cheque\n[Max: 5MB]\n(jpg/jpeg/png/ only )',
                                style: TextStyle(
                                  fontSize: textScaleFactor * 12,
                                  letterSpacing: .3,
                                ),
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
            if (_uploadImage1Error.hasError && widget.isValidatePanAndBank)
              TextFieldError(
                deviceWidth,
                _uploadImage1Error.message,
                fontSize: deviceWidth * 0.035,
              ),
            SizedBox(height: deviceWidth * 0.075),
            // if (fileMedia1 != null)
            buildRaisedButton(
              deviceWidth,
              deviceWidth * 0.12,
              saveDetails,
              Text(
                'Proceed',
                style: Theme.of(context).textTheme.displayMedium?.copyWith(
                      fontSize: textScaleFactor * 16,
                      fontWeight: FontWeight.w600,
                      letterSpacing: .50,
                      color: Colors.white,
                    ),
              ),
              TargetPlatform.android,
              Theme.of(context).primaryColor,
              7,
            ),
            SizedBox(height: deviceWidth * 0.1),
          ],
        ),
      ),
    );
  }
}
