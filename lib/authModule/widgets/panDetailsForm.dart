import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../authModule/modals/registration.dart';
import '../../authModule/providers/auth.dart';
import '../../authModule/widgets/messageWidget.dart';
import '../../commonWidgets/imageValidator.dart';
import '../../commonWidgets/raisedButton.dart';

class PanDetailsForm extends StatefulWidget {
  final bool isValidatePanAndBank;
  final Function setSteps;
  final Function changeIsCompletedStatus;
  const PanDetailsForm({
    Key? key,
    required this.isValidatePanAndBank,
    required this.setSteps,
    required this.changeIsCompletedStatus,
  }) : super(key: key);

  @override
  _PanDetailsFormState createState() => _PanDetailsFormState();
}

class _PanDetailsFormState extends State<PanDetailsForm> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController panController = TextEditingController();
  var fileMedia1;
  String imagePath1 = '';
  final _uploadImage1Error = MyValidator(hasError: false);
  RegExp regex = RegExp("[A-Z]{5}[0-9]{4}[A-Z]{1}");
  Future _capture(int i) async {
    setState(() {
      if (i == 1) {
        _uploadImage1Error.hasError = false;
      }
    });
    final getMedia = ImagePicker().pickImage;
    final media = await getMedia(source: ImageSource.gallery);
    final file = File(media!.path);

    if (file == null) {
      return;
    } else {
      setState(() {
        if (i == 1) {
          fileMedia1 = file;
          imagePath1 = media.path;
        }
      });
    }
  }

  void _uploadImageValidator() {
    if (fileMedia1 == null) {
      setState(() {
        _uploadImage1Error.hasError = true;
        _uploadImage1Error.message = 'Please upload your License photo';
      });
    } else {
      setState(() {
        _uploadImage1Error.hasError = false;
      });
    }
  }

  // saveDetails() {
  //   _uploadImageValidator();
  //   if (_uploadImage1Error.hasError) {
  //     return;
  //   }
  // }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    var data = Provider.of<Auth>(context, listen: false).panDetails;
    if (data != null) {
      panController.text = data.panNumber;
      fileMedia1 = data.panImage;
      widget.changeIsCompletedStatus(4);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final deviceHeight = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.055),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
              alignment: Alignment.topLeft,
              child: Text(
                'PAN Details',
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .displaySmall!
                    .copyWith(fontSize: textScaleFactor * displayMedium),
              ),
            ),
            MessageWidget(
              deviceWidth: deviceWidth,
              text:
                  'Please provide valid PAN details. As per Govt. of India\'s guidelines you have to give ID proof.',
              textScaleFactor: textScaleFactor,
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.001),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Enter PAN Field*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "PAN Card Number",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                maxLines: null,
                maxLength: 10,
                textCapitalization: TextCapitalization.characters,
                cursorColor: Colors.black,
                controller: panController,
                keyboardType: TextInputType.text,
                onChanged: (value) {},
                validator: (value) {
                  if (widget.isValidatePanAndBank) {
                    if (value!.trim().isEmpty) {
                      return 'Please enter pan card number';
                    } else if (!regex.hasMatch(value.trim())) {
                      return 'Please enter valid pan number';
                    }
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'PAN Image Upload*',
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(fontSize: textScaleFactor * headline9),
              ),
            ),
            SizedBox(height: deviceWidth * 0.01),
            fileMedia1 != null
                ? Stack(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: Container(
                            width: deviceWidth * 0.9,
                            height: deviceWidth * 0.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.black,
                            ),
                            child: Image.file(
                              fileMedia1,
                              fit: BoxFit.cover,
                            ), ////////////////////
                          )),
                      Positioned(
                        right: deviceWidth * 0.025,
                        bottom: deviceWidth * 0.025,
                        child: GestureDetector(
                          onTap: () => _capture(1),
                          child: Container(
                            height: deviceHeight * 0.045,
                            width: deviceWidth * 0.13,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                color: Colors.black54),
                            child: Center(
                              child: Text('Edit',
                                  style: TextStyle(
                                      letterSpacing: 0.1,
                                      color: Theme.of(context).colorScheme.background,
                                      fontSize: deviceWidth * 0.03,
                                      fontWeight: FontWeight.w400)),
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                : Container(
                    child: DottedBorder(
                      borderType: BorderType.RRect,
                      radius: Radius.circular(10),
                      dashPattern: [3, 3],
                      strokeCap: StrokeCap.butt,
                      color: Colors.black,
                      strokeWidth: 0.4,
                      child: Container(
                        width: deviceWidth * 0.9,
                        height: deviceWidth * 0.45,
                        // alignment: Alignment.center,
                        child: Container(
                          // alignment: Alignment.,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Card(
                                elevation: 4,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    Icons.camera_alt,
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                  onPressed: () => _capture(1),
                                ),
                              ),
                              SizedBox(height: deviceWidth * 0.02),
                              Text(
                                'Click to upload a picture of PAN Card\n[Max: 5MB]\n(jpg/jpeg/png/ only )',
                                style: TextStyle(
                                  fontSize: textScaleFactor * 12,
                                  letterSpacing: .3,
                                ),
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
            if (_uploadImage1Error.hasError && widget.isValidatePanAndBank)
              TextFieldError(
                deviceWidth,
                _uploadImage1Error.message,
                fontSize: deviceWidth * 0.035,
              ),
            SizedBox(height: deviceWidth * 0.075),
            // if (fileMedia1 != null)
            buildRaisedButton(
              deviceWidth,
              deviceWidth * 0.12,
              () {
                final isValid = _formKey.currentState!.validate();
                if (isValid) {
                  Provider.of<Auth>(context, listen: false).panDetails =
                      PanDetails(
                    panNumber: panController.text.trim(),
                    panImage: fileMedia1 ?? null,
                  );
                  widget.setSteps(5);
                }
              },
              Text(
                'Proceed',
                style: Theme.of(context).textTheme.displayMedium?.copyWith(
                      fontSize: textScaleFactor * 16,
                      fontWeight: FontWeight.w600,
                      letterSpacing: .50,
                      color: Colors.white,
                    ),
              ),
              TargetPlatform.android,
              Theme.of(context).primaryColor,
              7,
            ),
            SizedBox(height: deviceWidth * 0.1),
          ],
        ),
      ),
    );
  }
}
