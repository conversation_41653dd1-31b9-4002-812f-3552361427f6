import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';

import '../../authModule/screens/registrationSummaryScreen.dart';
import '../../commonWidgets/radio_widget.dart';
import '../../employeeModule/models/business_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../authModule/screens/onBoardScreen.dart';
import '../../commonWidgets/dialogBox.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../fontSizes.dart';
import '../../authModule/providers/auth.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../../commonWidgets/materialCircularLoader.dart';

class CommisionOnBoarding extends StatefulWidget {
  final Function setSteps;
  String? accessToken;
  String? businessId;
  final Function changeIsCompletedStatus;
  bool fromTurfScreen;
  final BusinessModel? business;
  CommisionOnBoarding({
    Key? key,
    required this.setSteps,
    required this.changeIsCompletedStatus,
    this.accessToken,
    this.businessId,
    this.business,
    this.fromTurfScreen = false,
  }) : super(key: key);

  @override
  _CommisionOnBoardingState createState() => _CommisionOnBoardingState();
}

class _CommisionOnBoardingState extends State<CommisionOnBoarding> {
  bool accepted = false;
  bool isLoading = false;

  String gstRegistered = '';
  String gstType = 'Regular';
  TextEditingController gstController = TextEditingController();

  actionButton() async {
    if (widget.fromTurfScreen) {
      setState(() {
        isLoading = true;
      });
      final data =
          await Provider.of<TurfProvider>(context, listen: false).addNewTurf(
        accessToken: widget.accessToken!,
        businessId: widget.businessId!,
        turfDetails: Provider.of<Auth>(context, listen: false).turfDetails,
        employeeId:
            widget.business == null ? null : widget.business!.employee.id,
        userId: widget.business == null ? '' : widget.business!.owner.id,
      );
      if (data) {
        setState(() {
          isLoading = false;
        });
        Provider.of<Auth>(context, listen: false).turfDetails = null;
        Provider.of<Auth>(context, listen: false).bankDetails = null;
        Provider.of<Auth>(context, listen: false).ownerDetails = null;
        Provider.of<Auth>(context, listen: false).panDetails = null;
        Provider.of<Auth>(context, listen: false).commissionAccepeted = false;
        Navigator.of(context).pop(true);
      } else {
        callToastMessage('Something went wrong');
        setState(() {
          isLoading = false;
        });
      }
    } else {
      final auth = Provider.of<Auth>(context, listen: false);
      auth.commissionAccepeted = true;
      if (gstRegistered == '') {
        return showSnackbar('Please select gst option');
      }
      auth.gstRegistered = gstRegistered;

      if (gstRegistered == 'Yes') {
        auth.gstType = gstType;
        if (gstController.text.trim().isEmpty) {
          return showSnackbar('Please enter gst number');
        }
        auth.gstNo = gstController.text.trim();
      }

      widget.setSteps(6);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => RegistrationSummaryScreen(
            isValidatePanAndBank: true,
            referralCode: '',
            referredByUserId: '',
          ),
        ),
      );
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    accepted = Provider.of<Auth>(context, listen: false).commissionAccepeted;
    if (accepted) {
      widget.changeIsCompletedStatus(3);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
                  alignment: Alignment.topLeft,
                  child: Text(
                    'Commission & Onboarding',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: textScaleFactor * displayMedium,
                        ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: deviceWidth * 0.05,
                    horizontal: deviceWidth * 0.04,
                  ),
                  width: deviceWidth,
                  decoration: BoxDecoration(
                    color: Color(0xff42536E),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                        constraints:
                            BoxConstraints(maxWidth: deviceWidth * 0.9),
                        child: Text(
                          'Commission Charged by BookYourSlot',
                          style: TextStyle(
                            color: Colors.white,
                            letterSpacing: .50,
                            fontWeight: FontWeight.w600,
                            fontSize: textScaleFactor * 14,
                          ),
                        ),
                      ),
                      Container(
                        constraints:
                            BoxConstraints(maxWidth: deviceWidth * 0.9),
                        child: Text(
                          Provider.of<Auth>(context, listen: false).commision,
                          style: TextStyle(
                            color: Colors.white,
                            letterSpacing: .50,
                            fontSize: textScaleFactor * 12,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: deviceWidth * 0.04),
                RichText(
                  text: TextSpan(
                    style: TextStyle(
                      color: Color(0xff757371),
                      fontSize: textScaleFactor * 14,
                    ),
                    children: [
                      TextSpan(
                        text: 'You agree to register for the first time for \n',
                      ),
                      TextSpan(
                        text:
                            'BookYourSlot membership, BoolYourSlot verifies \n',
                        style: TextStyle(
                          height: deviceWidth * 0.003,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      TextSpan(
                        text:
                            'credentials from RBI. As the registration process also\ntakes the involvement of RBI.',
                        style: TextStyle(
                          height: deviceWidth * 0.003,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: deviceWidth * 0.06),
                Container(
                  constraints: BoxConstraints(maxWidth: deviceWidth * 0.8),
                  child: Text(
                    'Are you agreeing to the commission and onboarding field.*',
                    style: TextStyle(
                      color: Colors.black,
                      letterSpacing: .50,
                      fontWeight: FontWeight.w600,
                      fontSize: textScaleFactor * 14,
                    ),
                  ),
                ),
                accepted
                    ? Container(
                        margin:
                            EdgeInsets.symmetric(vertical: deviceWidth * 0.04),
                        child: Text(
                          'Accepted',
                          style: TextStyle(
                            fontSize: textScaleFactor * 15,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : Row(
                        children: [
                          commisionApprovalButton(
                            deviceWidth,
                            context,
                            textScaleFactor,
                            'Decline',
                            () {
                              return dialogBoxCustom(
                                context: context,
                                cancelBtnPress: () {
                                  Navigator.of(context).pop();
                                },
                                cancelBtnText: 'No',
                                okBtnPress: () {
                                  Provider.of<Auth>(context, listen: false)
                                      .turfDetails = null;
                                  Provider.of<Auth>(context, listen: false)
                                      .bankDetails = null;
                                  Provider.of<Auth>(context, listen: false)
                                      .ownerDetails = null;
                                  Provider.of<Auth>(context, listen: false)
                                      .panDetails = null;
                                  if (widget.fromTurfScreen) {
                                    Navigator.of(context).pop();
                                    Navigator.of(context).pop();
                                  } else {
                                    Navigator.pushAndRemoveUntil(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => OnBoardScreen(
                                            '',
                                            '',
                                          ),
                                        ),
                                        (route) => false);
                                  }
                                },
                                okBtnText: 'Yes',
                                platform: TargetPlatform.android,
                                title: 'Commision & OnBoarding',
                                content:
                                    'Are you sure you don\'t want to agree the commision and onboarding policy. All the entered information will be lost.',
                                titleStyle: TextStyle(
                                  fontSize: textScaleFactor * 14.5,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                contentStyle: TextStyle(
                                  fontSize: textScaleFactor * 16,
                                  color: Colors.black,
                                ),
                                cancelBtnStyle: TextStyle(
                                  fontSize: textScaleFactor * 15,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                ),
                                okBtnStyle: TextStyle(
                                  fontSize: textScaleFactor * 15,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.w600,
                                ),
                              );
                            },
                          ),
                          SizedBox(width: deviceWidth * 0.05),
                          commisionApprovalButton(
                            deviceWidth,
                            context,
                            textScaleFactor,
                            'Accept',
                            () {
                              setState(() {
                                accepted = true;
                              });
                            },
                          ),
                        ],
                      ),
              ],
            ),
          ),
          DividerWidget(top: 5, bottom: 5),
          Container(
            margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
            alignment: Alignment.topLeft,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  title: 'GST Details',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                SizedBox(height: deviceWidth * 0.035),
                TextWidget(title: 'Is your business GST registered?'),
                SizedBox(height: deviceWidth * 0.025),
                Row(
                  children: [
                    ...['Yes', 'No'].map(
                      (value) => GestureDetector(
                        onTap: () {
                          gstRegistered = value;
                          setState(() {});
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: deviceWidth * 0.05),
                          child: RadioWidget(
                            active: gstRegistered == value,
                            activeColor: Theme.of(context).primaryColor,
                            inActiveBorderColor: Theme.of(context).primaryColor,
                            title: value,
                            radius: 6,
                            inactiveBorderRadius: 6,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                if (gstRegistered == 'Yes') ...[
                  SizedBox(height: deviceWidth * 0.055),
                  TextWidget(title: 'GST type :-'),
                  SizedBox(height: deviceWidth * 0.025),
                  Row(
                    children: [
                      ...['Regular', 'Composite'].map(
                        (value) => GestureDetector(
                          onTap: () {
                            gstType = value;
                            setState(() {});
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: deviceWidth * 0.05),
                            child: RadioWidget(
                              active: gstType == value,
                              activeColor: Theme.of(context).primaryColor,
                              inActiveBorderColor:
                                  Theme.of(context).primaryColor,
                              title: value,
                              radius: 6,
                              inactiveBorderRadius: 6,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: deviceWidth * 0.055),
                  CustomTextFieldWithLabel(
                    label: 'Business GST No.',
                    controller: gstController,
                    hintText: 'Enter Business Gst  No. Here...',
                    inputAction: TextInputAction.done,
                  )
                ],
              ],
            ),
          ),
          SizedBox(height: deviceWidth * 0.1),
          if (accepted)
            buildRaisedButton(
              deviceWidth * 0.9,
              deviceWidth * 0.12,
              isLoading ? () {} : actionButton,
              isLoading
                  ? circularForButton(deviceWidth)
                  : Text(
                      'Proceed',
                      style: Theme.of(context).textTheme.displayMedium?.copyWith(
                            fontSize: textScaleFactor * 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: .50,
                            color: Colors.white,
                          ),
                    ),
              TargetPlatform.android,
              Theme.of(context).primaryColor,
              7,
            ),
          SizedBox(height: deviceWidth * 0.1),
        ],
      ),
    );
  }
}

Widget commisionApprovalButton(
  double deviceWidth,
  BuildContext context,
  double textScaleFactor,
  String text,
  Function onPress,
) {
  return GestureDetector(
    onTap: () => onPress(),
    child: Container(
      margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.04),
      child: Text(
        text,
        style: TextStyle(
          fontSize: textScaleFactor * 14,
          color:
              text == "Accept" ? Theme.of(context).primaryColor : Colors.grey,
          fontWeight: FontWeight.bold,
        ),
      ),
    ),
  );
}
