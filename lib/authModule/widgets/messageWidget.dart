import 'package:flutter/material.dart';

class MessageWidget extends StatelessWidget {
  final double deviceWidth;
  final double textScaleFactor;
  final String text;

  const MessageWidget({
    Key? key,
    required this.deviceWidth,
    required this.text,
    required this.textScaleFactor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: deviceWidth * 0.05,
        horizontal: deviceWidth * 0.04,
      ),
      width: deviceWidth,
      decoration: BoxDecoration(
        color: Color(0xff42536E),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info,
            color: Colors.white,
          ),
          SizedBox(width: deviceWidth * 0.03),
          Container(
            constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white,
                letterSpacing: .50,
                fontSize: textScaleFactor * 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
