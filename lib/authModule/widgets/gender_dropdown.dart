import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

import '../../fontSizes.dart';

class GenderDropDown extends StatefulWidget {
  final String selectedGender;
  const GenderDropDown({
    required this.selectedGender,
  });

  @override
  GenderDropDownState createState() => GenderDropDownState();
}

class GenderDropDownState extends State<GenderDropDown> {
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: width * 0.7,
      margin: EdgeInsets.symmetric(horizontal: width * 0.053),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: width * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  'Select Gender',
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                        fontSize: textScaleFactor * 15,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ],
            ),
            SizedBox(height: width * 0.07),
            Column(
              children: [
                ...['Male', 'Female', 'Other'].map(
                  (gender) => GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop(gender);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                        right: width * 0.032,
                        bottom: width * 0.03,
                      ),
                      width: width,
                      padding: EdgeInsets.symmetric(
                        vertical: width * 0.03,
                        horizontal: width * 0.04,
                      ),
                      decoration: BoxDecoration(
                        color: widget.selectedGender != '' &&
                                gender == widget.selectedGender
                            ? getThemeColor()
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(7),
                      ),
                      child: Text(
                        gender,
                        style: Theme.of(context).textTheme.displaySmall!.copyWith(
                              fontSize: textScaleFactor * displayMedium,
                              color: gender == widget.selectedGender
                                  ? Colors.white
                                  : Colors.black,
                              fontWeight: gender == widget.selectedGender
                                  ? FontWeight.w500
                                  : FontWeight.normal,
                            ),
                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
