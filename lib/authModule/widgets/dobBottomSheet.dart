import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class DobBottomSheet extends StatelessWidget {
  DobBottomSheet({Key? key}) : super(key: key);
  TextEditingController dobController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    return Container(
      height: height * 0.4,
      margin: EdgeInsets.symmetric(horizontal: width * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(left: 5),
            child: PinCodeTextField(
              appContext: context,
              backgroundColor: Colors.transparent,
              enablePinAutofill: true,
              pastedTextStyle: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: width * 0.06,
                fontWeight: FontWeight.w400,
              ),
              length: 6,
              controller: dobController,
              // hintCharacter: 'DD',
              autoDismissKeyboard: true,
              cursorWidth: 1.5,
              cursorHeight: width * 0.065,
              cursorColor: Colors.black,
              animationType: AnimationType.fade,
              keyboardType: TextInputType.number,
              textStyle: TextStyle(
                color: Colors.black,
                fontSize: width * 0.07,
                letterSpacing: 0,
                fontWeight: FontWeight.w400,
              ),
              pinTheme: PinTheme(
                fieldHeight: width * 0.12,
                fieldWidth: width * 0.11,
                shape: PinCodeFieldShape.box,
                borderRadius: BorderRadius.circular(4),
                borderWidth: 0.5,
                activeColor: Colors.grey.shade500,
                activeFillColor: Colors.grey[500],
                selectedColor: Colors.grey[500],
                selectedFillColor: Colors.transparent,
                inactiveColor: Colors.grey[500],
                inactiveFillColor: Colors.transparent,
              ),
              onChanged: (_) {},
            ),
          ),
        ],
      ),
    );
  }
}
