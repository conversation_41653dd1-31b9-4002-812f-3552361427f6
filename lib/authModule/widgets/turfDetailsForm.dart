import 'dart:async';

import '../../authModule/modals/registration.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/materialCircularLoader.dart';
import '../../authModule/widgets/messageWidget.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../authModule/providers/auth.dart';
import '../../fontSizes.dart';

class TurfDetailsForm extends StatefulWidget {
  final Function setSteps;
  final Function setExitValue;
  final Function changeIsCompletedStatus;
  const TurfDetailsForm({
    Key? key,
    required this.setSteps,
    required this.setExitValue,
    required this.changeIsCompletedStatus,
  }) : super(key: key);

  @override
  _TurfDetailsFormState createState() => _TurfDetailsFormState();
}

class _TurfDetailsFormState extends State<TurfDetailsForm> {
  bool verify = false;
  bool verifyLoader = false;
  bool isLoading = false;
  bool showField = false;
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController turfNameController = TextEditingController();
  TextEditingController turfContactController = TextEditingController();
  TextEditingController streetNameController = TextEditingController();
  TextEditingController landMarkController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController pincodeController = TextEditingController();
  Timer? _debounce;
  FocusNode pincode = FocusNode();

  searchCityState() {
    if (pincodeController.text.length == 0) {
      callToastMessage('Please enter pincode');
      return;
    } else if (pincodeController.text.length < 6) {
      callToastMessage('Please enter valid pincode');
      return;
    }
    setState(() {
      verifyLoader = true;
    });
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      final response = await Provider.of<Auth>(context, listen: false)
          .getCityStateData(pincodeController.text);
      if (response != null) {
        cityController.text = response['city'] ?? '';
        stateController.text = response['state'] ?? '';
        setState(() {
          verify = true;
          verifyLoader = false;
          showField = true;
        });
      } else {
        callToastMessage('Invalid Pincode');
        setState(() {
          verify = false;
          verifyLoader = false;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    var data = Provider.of<Auth>(context, listen: false).turfDetails;
    if (data != null) {
      turfNameController.text = data.turfName;
      turfContactController.text = data.mobileNo;
      streetNameController.text = data.streetName;
      landMarkController.text = data.landmark;
      cityController.text = data.city;
      stateController.text = data.state;
      pincodeController.text = data.pincode.toString();
      verify = true;
      showField = true;
      widget.changeIsCompletedStatus(1);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.055),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
              alignment: Alignment.topLeft,
              child: Text(
                'Venue Details',
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .displaySmall!
                    .copyWith(fontSize: textScaleFactor * displayMedium),
              ),
            ),
            MessageWidget(
              deviceWidth: deviceWidth,
              text:
                  'Please provide a valid mobile number. This number will be registered to send all important communication from BookYourSlot.',
              textScaleFactor: textScaleFactor,
            ),
            SizedBox(height: deviceWidth * 0.07),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Venue Name*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                // inputFormatters: [
                //   FilteringTextInputFormatter.digitsOnly
                // ],
                style: TextStyle(
                  fontSize: textScaleFactor * 16,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Venue Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  // border: OutlineInputBorder(
                  //   borderRadius: BorderRadius.circular(30),
                  //   borderSide: BorderSide(color: Colors.grey.shade300),
                  // ),
                  // focusedBorder: OutlineInputBorder(
                  //   borderRadius: BorderRadius.circular(30),
                  //   borderSide: BorderSide(color: Colors.grey.shade300),
                  // ),
                  // enabledBorder: OutlineInputBorder(
                  //   borderSide: BorderSide(color: Colors.grey.shade300),
                  //   borderRadius: BorderRadius.circular(30),
                  // ),
                  // errorBorder: OutlineInputBorder(
                  //   borderRadius: BorderRadius.circular(30),
                  //   borderSide: BorderSide(color: Colors.grey.shade300),
                  // ),
                  // disabledBorder: OutlineInputBorder(
                  //   borderRadius: BorderRadius.circular(30),
                  // ),
                  // filled: true,
                  // focusedErrorBorder: OutlineInputBorder(
                  //   borderRadius: BorderRadius.circular(30),
                  //   borderSide: BorderSide(color: Colors.grey.shade300),
                  // ),
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.words,
                controller: turfNameController,
                keyboardType: TextInputType.text,
                maxLines: null,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter turf name';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Venue Contact Number',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                style: TextStyle(
                  // color: Color(0xff6E7271),
                  fontSize: textScaleFactor * 16,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  hintText: "Venue Contact Number",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                cursorColor: Colors.black,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: turfContactController,
                keyboardType: TextInputType.phone,
                maxLength: 10,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter a phone number';
                  } else if (value.trim().length < 10) {
                    return 'Please enter a valid phone number';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Venue Street Name*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: TextStyle(
                  // color: Color(0xff6E7271),
                  fontSize: textScaleFactor * 16,

                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Venue Street Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
                cursorColor: Colors.black,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: streetNameController,
                keyboardType: TextInputType.text,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter street name';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Venue Landmark*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: TextStyle(
                  // color: Color(0xff6E7271),
                  fontSize: textScaleFactor * 16,

                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Venue Landmark",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                maxLines: null,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.sentences,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: landMarkController,
                keyboardType: TextInputType.streetAddress,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter landmark';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Pincode*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                style: TextStyle(
                  // color: Color(0xff6E7271),
                  fontSize: textScaleFactor * 16,

                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Pincode",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                cursorColor: Colors.black,
                focusNode: pincode,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: pincodeController,
                keyboardType: TextInputType.number,
                maxLength: 6,
                onChanged: (value) {
                  widget.setExitValue();
                  if (value.length == 6) {
                    pincode.unfocus();
                    searchCityState();
                  }
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter pincode';
                  } else if (value.trim().length < 6) {
                    return 'Please enter valid pincode';
                  }
                },
              ),
            ),
            // Align(
            //   alignment: Alignment.bottomRight,
            //   child: verifyLoader
            //       ? Container(
            //           margin: EdgeInsets.symmetric(
            //             vertical: deviceWidth * 0.02,
            //             horizontal: deviceWidth * 0.02,
            //           ),
            //           child: Row(
            //             mainAxisAlignment: MainAxisAlignment.end,
            //             children: [
            //               circularForButton(
            //                 deviceWidth,
            //                 color: Theme.of(context).primaryColor,
            //               )
            //             ],
            //           ),
            //         )
            //       : TextButton(
            //           style: TextButton.styleFrom(
            //             padding: EdgeInsets.all(0),
            //           ),
            //           onPressed: !verify ? searchCityState : null,
            //           child: Text(
            //             !verify ? 'Verify Pincode' : 'Verified',
            //             style: TextStyle(
            //               color: Color(0xffECA215),
            //               fontSize: textScaleFactor * 16,
            //               fontWeight: FontWeight.w600,
            //             ),
            //           ),
            //         ),
            // ),
            // if (!showField) SizedBox(height: deviceWidth * 0.03),
            // if (showField)
            SizedBox(height: deviceWidth * 0.05),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'City*',
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 14,
                            color: const Color(0xff434343),
                          ),
                    ),
                    if (verifyLoader)
                      circularForButton(
                        deviceWidth * 0.5,
                        color: Theme.of(context).primaryColor,
                      ),
                  ],
                ),
                Container(
                  child: TextFormField(
                    style: TextStyle(
                      fontSize: textScaleFactor * 16,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: InputDecoration(
                      hintStyle:
                          Theme.of(context).textTheme.headlineSmall!.copyWith(
                                fontSize: textScaleFactor * 13,
                                color: Color(0xff737373),
                                fontWeight: FontWeight.normal,
                              ),
                      // contentPadding: EdgeInsets.symmetric(
                      //   horizontal: deviceWidth * 0.034,
                      //   vertical: deviceWidth * 0.04,
                      // ),
                      hintText: "City",
                      counterText: "",
                      fillColor: Colors.grey.shade300,
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      errorBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      disabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      filled: false,
                      focusedErrorBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                    ),
                    maxLines: null,
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    controller: cityController,
                    keyboardType: TextInputType.text,
                    readOnly: true,
                    onChanged: (value) {
                      widget.setExitValue();
                    },
                    validator: (value) {
                      if (value!.isEmpty) {
                        return 'Please enter city';
                      }
                    },
                  ),
                ),
                SizedBox(height: deviceWidth * 0.05),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'State*',
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 14,
                            color: const Color(0xff434343),
                          ),
                    ),
                    if (verifyLoader)
                      circularForButton(
                        deviceWidth * 0.5,
                        color: Theme.of(context).primaryColor,
                      ),
                  ],
                ),
                Container(
                  child: TextFormField(
                    style: TextStyle(
                      // color: Color(0xff6E7271),
                      fontSize: textScaleFactor * 16,

                      fontWeight: FontWeight.w600,
                    ),
                    decoration: InputDecoration(
                      hintStyle:
                          Theme.of(context).textTheme.headlineSmall!.copyWith(
                                fontSize: textScaleFactor * 13,
                                color: Color(0xff737373),
                                fontWeight: FontWeight.normal,
                              ),
                      hintText: "State",
                      counterText: "",
                      fillColor: Colors.grey.shade300,
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      errorBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      disabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                      filled: false,
                      focusedErrorBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade500),
                      ),
                    ),
                    maxLines: null,
                    readOnly: true,
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    controller: stateController,
                    keyboardType: TextInputType.streetAddress,
                    onChanged: (value) {
                      widget.setExitValue();
                    },
                    validator: (value) {
                      if (value!.isEmpty) {
                        return 'Please enter state';
                      }
                    },
                  ),
                ),
                SizedBox(height: deviceWidth * 0.08),
              ],
            ),
            if (verify)
              buildRaisedButton(
                deviceWidth,
                deviceWidth * 0.12,
                isLoading
                    ? () {}
                    : () {
                        final isValid = _formKey.currentState!.validate();
                        if (isValid) {
                          if (showField) {
                            widget.setSteps(2);
                            Provider.of<Auth>(context, listen: false)
                                .turfDetails = TurfDetails(
                              turfName: turfNameController.text.trim(),
                              mobileNo: turfContactController.text.trim(),
                              streetName: streetNameController.text.trim(),
                              landmark: landMarkController.text.trim(),
                              pincode: int.parse(pincodeController.text.trim()),
                              city: cityController.text.trim(),
                              state: stateController.text.trim(),
                            );
                          } else {
                            callToastMessage('Please verify pincode');
                          }
                        }
                      },
                isLoading
                    ? circularForButton(deviceWidth)
                    : Text(
                        'Proceed',
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                              fontSize: textScaleFactor * 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: .50,
                              color: Colors.white,
                            ),
                      ),
                TargetPlatform.android,
                Theme.of(context).primaryColor,
                10,
              ),
            SizedBox(height: deviceWidth * 0.1),
          ],
        ),
      ),
    );
  }
}
