import 'dart:async';

import '../../authModule/screens/registrationSummaryScreen.dart';

import '../../authModule/modals/registration.dart';
import '../../authModule/providers/auth.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/widgets/messageWidget.dart';
import '../../fontSizes.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';

class OwnerDetailsForm extends StatefulWidget {
  final Function setSteps;
  final bool isValidatePanAndBank;

  final Function setExitValue;
  final Function changeIsCompletedStatus;
  const OwnerDetailsForm({
    Key? key,
    required this.setSteps,
    required this.changeIsCompletedStatus,
    required this.isValidatePanAndBank,
    required this.setExitValue,
  }) : super(key: key);

  @override
  _OwnerDetailsFormState createState() => _OwnerDetailsFormState();
}

class _OwnerDetailsFormState extends State<OwnerDetailsForm> {
  bool isLoading = false;
  bool verify = false;
  bool showOtpField = false;
  bool verifyLoader = false;
  bool sendOtp = false;
  int otpSentCount = 1;
  bool isReadyToResend = false;

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController mobileNoController = TextEditingController();
  TextEditingController otpController = TextEditingController();

  RegExp regexExp = RegExp(
      '(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])');
  var data;
  late Timer _timer;
  int _start = 30;

  void startTimer() {
    _start = 30;
    const oneSec = const Duration(seconds: 1);
    _timer = new Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
            isReadyToResend = true;
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  generateOtp() async {
    if (mobileNoController.text.length == 0) {
      callToastMessage('Please enter phone number');
      return;
    } else if (mobileNoController.text.length < 10) {
      callToastMessage('Please enter a valid phone number');
      return;
    } else {
      try {
        startTimer();
        final data =
            await Provider.of<Auth>(context, listen: false).sendOTPtoUser(
          mobileNoController.text.toString(),
          business: true,
        );
        if (!data['success']) {
          callToastMessage(
              'Business is already registered with this phone number.');
        } else {
          setState(() {
            showOtpField = true;
            sendOtp = true;
          });
        }
      } catch (e) {
        print(e);
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  verifyOtp() async {
    setState(() {
      verifyLoader = true;
    });
    final data =
        await Provider.of<Auth>(context, listen: false).verifyOTPofUser(
      mobileNoController.text.toString(),
      otpController.text,
    );
    if (data == 'success') {
      setState(() {
        verify = true;
        verifyLoader = false;
      });
    } else {
      callToastMessage('Incorrect Otp');
      setState(() {
        verify = false;
        verifyLoader = false;
      });
    }
  }

  resendOTPToUser() async {
    try {
      // setState(() {
      //   otpSentCount += 1;
      //   if (otpSentCount > 3) {
      //     isReadyToResend = false;
      //   }
      // });
      // if (isReadyToResend) {
      setState(() {
        isReadyToResend = false;
        startTimer();
        otpSentCount += 1;
      });
      final data = await Provider.of<Auth>(context, listen: false)
          .resendOTPtoUser(mobileNoController.text.toString(), 'text');
      if (data != "success") {
        callToastMessage('Something went wrong');
      }

      // }
    } catch (e) {
      print(e);
    }
  }

  @override
  void initState() {
    super.initState();
    data = Provider.of<Auth>(context, listen: false).ownerDetails;
    if (data != null) {
      nameController.text = data.ownerName;
      mobileNoController.text = data.mobileNo;
      otpController.text = data.otp.toString();
      emailController.text = data.email;
      verify = true;
      sendOtp = true;
      showOtpField = true;
      widget.changeIsCompletedStatus(2);
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.055),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
              alignment: Alignment.topLeft,
              child: Text(
                'Owner Details',
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .displaySmall!
                    .copyWith(fontSize: textScaleFactor * displayMedium),
              ),
            ),
            MessageWidget(
              deviceWidth: deviceWidth,
              text:
                  'Please provide a valid mobile number. This number will be registered to send all important communication from BookYourSlot.',
              textScaleFactor: textScaleFactor,
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Owner Name*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Owner Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                cursorColor: Colors.black,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                textCapitalization: TextCapitalization.words,
                controller: nameController,
                keyboardType: TextInputType.text,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter owner name';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Owner Email*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                inputFormatters: [
                  FilteringTextInputFormatter(
                    regexExp,
                    allow: false,
                  )
                ],
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Owner Email",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                maxLines: null,
                cursorColor: Colors.black,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                onChanged: (value) {
                  widget.setExitValue();
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter your email address';
                  } else if (!EmailValidator.validate(value.trim())) {
                    return 'Please provide a valid email address';
                  }
                },
              ),
            ),
            SizedBox(height: deviceWidth * 0.05),
            Container(
              margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
              constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
              child: Text(
                'Owner Mobile Number*',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * 14,
                      color: const Color(0xff434343),
                    ),
              ),
            ),
            Container(
              child: TextFormField(
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * displayMedium,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  // contentPadding: EdgeInsets.symmetric(
                  //   horizontal: deviceWidth * 0.034,
                  //   vertical: deviceWidth * 0.04,
                  // ),
                  hintText: "Mobile Number",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                ),
                cursorColor: Colors.black,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                controller: mobileNoController,
                keyboardType: TextInputType.number,
                readOnly: data == null ? false : true,
                maxLength: 10,
                onChanged: (value) {
                  if (value.length < 10) {
                    setState(() {
                      showOtpField = false;
                      verify = false;
                      sendOtp = false;
                    });
                    widget.setExitValue();
                  }
                },
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter a phone number';
                  } else if (value.trim().length < 10) {
                    return 'Please enter a valid phone number';
                  }
                },
              ),
            ),
            data != null
                ? SizedBox(height: deviceWidth * 0.05)
                : verify
                    ? SizedBox(height: deviceWidth * 0.05)
                    : Row(
                        mainAxisAlignment: otpSentCount > 1
                            ? MainAxisAlignment.spaceBetween
                            : MainAxisAlignment.end,
                        children: [
                          if (otpSentCount > 1)
                            Container(
                              width: deviceWidth * 0.4,
                              margin: EdgeInsets.only(left: deviceWidth * 0.01),
                              child: FittedBox(
                                child: Text(
                                  'Resend OTP ($otpSentCount out of 3 attempts)',
                                  style: TextStyle(
                                    fontSize: 11 * textScaleFactor,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Poppins',
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          TextButton(
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.all(0),
                            ),
                            onPressed: !sendOtp
                                ? generateOtp
                                : otpSentCount >= 3
                                    ? null
                                    : _start == 0
                                        ? resendOTPToUser
                                        : null,
                            child: Text(
                              !sendOtp
                                  ? 'Generate OTP'
                                  : isReadyToResend
                                      ? 'Resend OTP'
                                      : _start == 0
                                          ? '0:00'
                                          : _start > 9
                                              ? '0:$_start'
                                              : '0:0$_start',
                              style: TextStyle(
                                color: otpSentCount >= 3 && _start == 0
                                    ? Colors.grey
                                    : Color(0xffECA215),
                                fontSize: otpSentCount >= 3 && _start == 0
                                    ? textScaleFactor * 14
                                    : textScaleFactor * 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
            if (showOtpField)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
                    constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
                    child: Text(
                      'Verify OTP*',
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * 14,
                            color: const Color(0xff434343),
                          ),
                    ),
                  ),
                  Container(
                    child: TextFormField(
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                            fontSize: textScaleFactor * displayMedium,
                            color: Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                      decoration: InputDecoration(
                        hintStyle:
                            Theme.of(context).textTheme.headlineSmall!.copyWith(
                                  fontSize: textScaleFactor * 13,
                                  color: Color(0xff737373),
                                  fontWeight: FontWeight.normal,
                                ),
                        // contentPadding: EdgeInsets.symmetric(
                        //   horizontal: deviceWidth * 0.034,
                        //   vertical: deviceWidth * 0.04,
                        // ),
                        hintText: "Enter OTP",
                        counterText: "",
                        fillColor: Colors.grey.shade300,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade500),
                        ),
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade500),
                        ),
                        enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade500),
                        ),
                        errorBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade500),
                        ),
                        disabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade500),
                        ),
                        filled: false,
                      ),
                      cursorColor: Colors.black,
                      readOnly: verify ? true : false,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      controller: otpController,
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      onChanged: (value) {
                        widget.setExitValue();
                      },
                      validator: (value) {
                        if (value!.trim().isEmpty) {
                          return 'Please enter otp';
                        } else if (value.length < 6) {
                          return 'Please enter 6 digit otp';
                        }
                      },
                    ),
                  ),
                  data != null
                      ? SizedBox(height: deviceWidth * 0.05)
                      : Align(
                          alignment: Alignment.bottomRight,
                          child: verifyLoader
                              ? Container(
                                  margin: EdgeInsets.symmetric(
                                    vertical: deviceWidth * 0.02,
                                    horizontal: deviceWidth * 0.02,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      circularForButton(
                                        deviceWidth,
                                        color: Theme.of(context).primaryColor,
                                      )
                                    ],
                                  ),
                                )
                              : TextButton(
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.all(0),
                                  ),
                                  onPressed: () {
                                    if (otpController.text.isEmpty) {
                                      callToastMessage('Please enter otp');
                                      return;
                                    } else if (otpController.text.length < 6) {
                                      callToastMessage(
                                          'Please enter 6 digit otp');
                                      return;
                                    } else {
                                      if (!verify) {
                                        verifyOtp();
                                      }
                                    }
                                  },
                                  child: Text(
                                    !verify ? 'Verify OTP' : 'Verified',
                                    style: TextStyle(
                                      color: Color(0xffECA215),
                                      fontSize: textScaleFactor * 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                        ),
                  SizedBox(height: deviceWidth * 0.03),
                  if (verify)
                    buildRaisedButton(
                      deviceWidth,
                      deviceWidth * 0.12,
                      () {
                        final isValid = _formKey.currentState!.validate();
                        if (isValid) {
                          Provider.of<Auth>(context, listen: false)
                              .ownerDetails = OwnerDetails(
                            ownerName: nameController.text.trim(),
                            mobileNo: mobileNoController.text.trim(),
                            email: emailController.text.trim(),
                            otp: int.parse(otpController.text),
                          );
                          if (!widget.isValidatePanAndBank) {
                            Provider.of<Auth>(context, listen: false)
                                .commissionAccepeted = true;
                            Provider.of<Auth>(context, listen: false)
                                .panDetails = PanDetails(
                              panNumber: '',
                              panImage: null,
                            );
                            Provider.of<Auth>(context, listen: false)
                                .bankDetails = BankDetails(
                              bankName: '',
                              accountNumber: 0,
                              accountHolderName: '',
                              ifscCode: '',
                              chequeImageUrl: null,
                            );
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => RegistrationSummaryScreen(
                                    isValidatePanAndBank:
                                        widget.isValidatePanAndBank),
                              ),
                            );
                          } else {
                            widget.setSteps(3);
                          }
                        }
                      },
                      Text(
                        'Proceed',
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                              fontSize: textScaleFactor * 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: .50,
                              color: Colors.white,
                            ),
                      ),
                      TargetPlatform.android,
                      Theme.of(context).primaryColor,
                      10,
                    ),
                  SizedBox(height: deviceWidth * 0.1),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
