import 'package:bys_business/employeeModule/models/business_model.dart';

class UserModal {
  String firstName;
  String lastName;
  String phone;
  String email;
  String businessId;
  String accessToken;
  String? avatar;
  String? businessStatus;
  bool? businessActiveStatus;
  String? fcmToken;
  String role;
  String? address;
  String referralCode;
  double wallet;
  BusinessModel? business;
  GSTDetails? gstDetails;
  BankDetails? bankDetails;
  String aadharImage;

  UserModal({
    required this.firstName,
    required this.businessStatus,
    required this.lastName,
    required this.accessToken,
    required this.phone,
    required this.email,
    required this.businessId,
    required this.role,
    this.address = '',
    required this.businessActiveStatus,
    this.fcmToken,
    this.avatar,
    required this.referralCode,
    required this.wallet,
    required this.gstDetails,
    required this.bankDetails,
    required this.aadharImage,
    this.business,
  });
}

class GSTDetails {
  final String gstType;
  final String gstNo;

  GSTDetails({
    required this.gstType,
    required this.gstNo,
  });

  static GSTDetails jsonToGST(Map gstDetails) {
    return GSTDetails(
      gstType: gstDetails['gstType'] ?? '',
      gstNo: gstDetails['gstNo'] ?? '',
    );
  }
}

class BankDetails {
  final String bankName;
  final String accountNumber;
  final String ifscCode;
  final String upiId;

  BankDetails({
    required this.bankName,
    required this.accountNumber,
    required this.ifscCode,
    required this.upiId,
  });

  static BankDetails jsonToBankDetails(Map bankDetails) {
    return BankDetails(
      bankName: bankDetails['bankName'] ?? '',
      accountNumber: bankDetails['accountNumber'] ?? '',
      ifscCode: bankDetails['ifscCode'] ?? '',
      upiId: bankDetails['upiId'] ?? '',
    );
  }
}
