import 'dart:io';

class TurfDetails {
  String turfName;
  String mobileNo;
  String streetName;
  String landmark;
  int pincode;
  String city;
  String state;

  TurfDetails({
    required this.turfName,
    required this.mobileNo,
    required this.streetName,
    required this.landmark,
    required this.pincode,
    required this.city,
    required this.state,
  });
}

class OwnerDetails {
  String ownerName;
  String mobileNo;
  String email;
  int otp;

  OwnerDetails({
    required this.ownerName,
    required this.mobileNo,
    required this.email,
    required this.otp,
  });
}

class PanDetails {
  String panNumber;
  File? panImage;

  PanDetails({
    required this.panNumber,
    required this.panImage,
  });
}

class BankDetails {
  String bankName;
  int accountNumber;
  String accountHolderName;
  String ifscCode;
  File? chequeImageUrl;

  BankDetails({
    required this.bankName,
    required this.accountNumber,
    required this.accountHolderName,
    required this.ifscCode,
    required this.chequeImageUrl,
  });
}
