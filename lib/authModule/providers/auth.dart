import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../authModule/modals/userModel.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:localstorage/localstorage.dart';
import '../../api.dart';
import '../../employeeModule/models/business_model.dart';
import '../../http_helper.dart';
import '../../venueModule/models/venue_model.dart';

class Auth with ChangeNotifier {
  final LocalStorage storage = new LocalStorage('bysBusiness');

  String helpLink = '';
  String commision = '';
  Map update = {};
  bool callOnces = false;
  bool detailsInserted = false;
  List freshchatDetail = [];
  List listOfSports = [];
  List listOfSportCategory = [];
  List employeeRoles = [
    "Booking",
    "Bulk Booking",
    "Cancel Booking",
    "Cancel Bulk Booking",
    "Venue",
    "Report",
    "Analytics",
  ];
  int adminContact = 0;

  var turfDetails;
  var ownerDetails;
  var panDetails;
  var bankDetails;
  bool commissionAccepeted = false;
  String gstType = '';
  String gstRegistered = '';
  String gstNo = '';

  String cancellationTemplate = '';

  bool appValidation = false;
  bool deleteAccount = false;
  late UserModal user;
  int minimumWithdrawalAmount = 500;

  Map selectedLanguage = {
    //OnBoarding Screen
    "onBoardingSubTitle": "Manage your venue from just one place.",
    "registerBusiness": "Register For Business",
    "employeeLogin": "Employee Login",
    "businessLogin": "Business Login",

    //Register 1 Screen
    "registerTitle": "Partner with BookYourSlot",
    "registerSubTitle": "Get listed on online turf marketplace today",
    "proceed": "Proceed",
    "registerInfo":
        "Please provide a valid mobile number. This number will be registered to send all important communication from BookYourSlot.",
    "contactUs": "Need Help? Contact Us: +91 **********",
    "venueDetails": "Venue Details",
    "venueName": "Venue Name",
    "venueNameHint": "Add venue name",
    "venueContactNo": "Venue Contact No.",
    "venueContactNoHint": "Add venue contact no.",
    "venueStreetName": "Venue street name",
    "venueStreetNameHint": "Add venue street name",
    "venueLandmark": "Venue Landmark",
    "venueLandmarkHint": "Add venue landmark",
    "pincode": "Pincode",
    "pincodeHint": "Add pincode",
    "city": "City",
    "cityHint": "Add city",
    "state": "State",
    "stateHint": "Add state",

    //Register 2 Screen
    "ownerDetails": "Owner Details",
    "ownerName": "Owner Name",
    "ownerNameHint": "Add owner name",
    "ownerEmail": "Owner Email ID",
    "ownerEmailHint": "Add owner email ID",
    "ownerBankDetails": "Owner Bank Details",
    "bankName": "Bank Name",
    "bankNameHint": "Add bank name",
    "accountNumber": "Account Number",
    "accountNumberHint": "Add account number",
    "ifscCode": "IFSC code",
    "ifscCodeHint": "Add IFSC code",
    "upiId": "UPI ID",
    "upiIdHint": "Add UPI ID",
    "aadharImage": "Aadhaar Card Image ",
    "aadharImageSubTitle": "Click to upload a picture of aadhar card",
    "maxUpload": "(MAX: 5MB)",
    "uploadTypes": "( jpg/jpeg/png Only)",

    //Register 3 Screen
    "ownerContactNumber": "Owner Contact No.",
    "ownerContactNumberHint": "Add Owner Contact No.",
    "enterOtp": "Enter OTP",
    "enterOtpHint": "Add OTP",
    "getOtp": "Get OTP",
    "resendText1": "Didn't get it?",
    "resendText2": " Resend",
    "resendText3": " in",
    "verifyOtp": "Verify OTP",

    //Register 4 screen
    "gstDetails": "GST Details",
    "gstRegister": "Is your business GST registered?",
    "yes": "Yes",
    "no": "No",
    "gstType": "GST type :-",
    "regular": "Regular",
    "composite": "Composite",
    "gstNo": "Business GST No.",
    "gstNoHint": "Enter  Business Gst  No. Here...",
    "commissionAndOnboarding": "Commission & Onboarding",
    "commissionAndOnboardingWarning":
        "Are you agreeing to the commission and onboarding field*",
    "decline": "Decline",
    "accept": "Accept",
    "commissionAndOnboardingAgreement":
        "You agree to register for the first time for BookYourSlot membership, BoolYourSlot verifies credentials from RBI. As the registration process also takes the involvement of RBI.",
    "accepted": "Accepted",

    //Form Review Screen
    "formReview": "Form Review",
    "streetName": "Street name",
    "landmark": "Landmark",
    "proceedAndConfirm": "Proceed & Confirm",

    "exitAlertSubTitle":
        "Are you sure you want to exit. All the entered information will be lost.",
    "exitAlert": "Exit Alert!",

    //Business Login Screen
    "phoneNumber": "Phone Number",
    "phoneNumberSubTitle": "Please enter your phone number to proceed",
    "hintText": "Enter your mobile number",

    //Privacy Policy Text Widget
    "privacyPolicyText1": "By continuing, you agree to the ",
    "privacyPolicyText2": "Terms of services ",
    "privacyPolicyText3": "and ",
    "privacyPolicyText4": "Privacy policy",

    //Business Verify Otp Screen
    "otpVerification": "OTP Verification",
    "optVerificationSubTitle": "Please enter the 6-digit code sent to you on",

    //Business Home Screen
    "bookings": "Bookings",
    "allTime": "All Time",
    "weekly": "Weekly",
    "monthly": "Monthly",
    "noBookings": "You don’t have any bookings yet.",
    "scanNow": "Scan Now",
    "bookNow": "Book Now",

    //Side Menu Widget
    "businessProfile": "Business Profile",
    "bookingAndVenues": "Bookings & Venues",
    "customersAndEmployees": "Customers & Employees",
    "businessReport": "Business Report",
    "": "",

    //Notification screen
    "notifications": "Notifications",
    "emptyNotificationText": "You don’t have any notifications yet.",

    // Account screen
    "profile": "Profile",
    "moneyWithdrawal": "Money Withdrawal",
    "venues": "Venues",
    "analytics": "Analytics",
    "customers": "Customers",
    "employee": "Employee",
    "report": "Report",
    "coupons": "Coupons",
    "logout": "Logout",
    "logoutMessage": "Are you sure you want to Log Out?",

    //Business Profile screen
    "changeProfilePic": "Change Profile Photo",
    "name": "Name",
    "emailId": "Email ID",
    "phoneNo": "Phone No.",
    "dob": "DOB",
    "aadharCard": "Aadhar Card",
    "saveChanges": "Save Changes",
    "bankDetails": "Bank Details",

    //Coupon Screen
    "noCouponSubtitle": "There is no active coupons.",

    //Active coupon Screen
    "couponDetails": "Coupon Details",
    "couponCode": "Coupon Code",
    "discountPercentage": "Discount Percentage",
    "description": "Description",
    "venue": "Venue",
    "validity": "Validity",
    "createdDateAndTime": "Created Date & Time",

    //Business Booking Screen
    "upcoming": "Upcoming",
    "completed": "Completed",
    "cancelled": "Cancelled",
    "paid": "Paid",
    "partiallyPaid": "Partially Paid",

    //Business Booking detail Screen
    "bookingDetails": "Booking Details",
    "venueBookedBy": "Venue booked by: ",
    "bookingId": "Booking ID: ",
    "dateAndTime": "Date & Time",
    "SportsAndTurf": "Sports & Turf",
    "paidCapital": "PAID",
    "scanQrCode": "Scan QR Code",
    "paymentSummary": "Payment Summary",
    "subTotal": "Sub Total",
    "convenienceFee": "Convenience Fee ",
    "total": "Total",
    "cancelBooking": "Cancel Booking",
    "sportsAndTurf": "Sports & Turf",
    "turfBookedBy": "Turf Booked By : ",
    "baseAmount": "Base Amount",
    "gst": "GST (18%)",
    "cancel": "Cancel",
    "balancePayment": "Balance Payment",

    //Customer Booking Screen
    "customersBookings": "Customers Bookings",
    "sports": "Sports",
    "dateRange": "Date Range",
    "apply": "Apply",

    //Business employees Screen
    "employees": "Employees",
    "active": "Active",
    "inactive": "Inactive",
    "addNewEmployee": "Add New Employee",

    //Add Edit venue screen
    "addVenueDetails": "Add Venue Details",
    "selectSportType": "Select Sport Type",
    "aboutVenue": "About Venue",
    "sportsDetails": "Sports Details",
    "sportsAvailability": "Sports Availability",
    "selectSportCategory": "Select Sport Category",
    "addNewVenue": "Add New Venue",
    "outdoor": "Outdoor",
    "indoor": "Indoor",
    "esports": "Esports",
    "morningSlot": "Morning Slot",
    "afternoonSlot": "Afternoon Slot",
    "selectSlotTiming": "Select Slot Timing",
    "startTime": "Start Time",
    "selectWeekdays": "Select Weekdays",
    "weekendDays": "Weekend Days",
    "selectWeekendDays": "Select weekend days",
    "selectSlots": "Select Slots",
    "selectTurfOptions": "Select Turf Options",
    "selectTurfSizes": "Select Turf Sizes",
    "endTime": "End Time",
    "enterSportsPricing": "Enter Sports Pricing ",
    "sport": "Sport",
    "weekdays": "Weekdays",
    "weekends": "Weekends",
    "enterPrice": "Enter Price",
    "venueImages": "Venue Images",
    "addPhoto": "Add Photo",
    "addVideo": "Add Video",
    "addMorePhotos": "Add more photos",
    "advanceAmount": "Advance Amount",
    "enterQuantity": "Enter Quantity",
    "facilities": "Facilities",
    "washroom": "Washroom",
    "cafe&FoodCourt": "Cafe & Food Court",
    "powerBackup": "Power Backup",
    "changingRoom": "Changing Room",
    "firstAid": "First Aid",
    "inventory": "Inventory",
    "addItemName": "Add Item Name",
    "addPrice": "Add Price",
    "addQuantity": "Add Quantity",
    "addedImage1": "Added Image 1",
    "addNewItem": "Add New Item",
    "save&Continue": "Save & Continue",
    "turfSize": "Turf Size",
    "addOther": "Add Other",
    "bookingAvailabilityDuration": "Booking Availability Duration",
    "period": "Period",
    "count": "Count",
    "cancellationCharges": "Cancellation Charges",
    "addHours": "Add Hours",
    "hours": "Hours",
    "to": "To",
    "edit": "Edit",
    "remove": "Remove",
    "addRefundPercentage": "Add Refund Percentage",
    "addHourRangeAndUserRefundPercentage.":
        "Add hour range and user refund percentage.",
    "addMore": "Add More",
    "label": "Label",
    "fromHours": "From Hours",
    "toHours": "To Hours",
    "refundPercentage": "Refund Percentage",

    //Add Edit employee screen
    "addEmployee": "Add Employee",
    "addEmployeeDetails": "Add Employee Details",
    "firstName": "First Name",
    "firstNameHint": "Enter first name",
    "lastName": "Last Name",
    "lastNameHint": "Enter last name ",
    "emailAddress": "Email Address ",
    "emailAddressHint": "Enter email address",
    "address": "Address ",
    "addressHint": "Enter address",
    "addBankDetails": "Add Bank Details",
    "selectPrivileges": "Select Privileges",
    "booking": "Booking",
    "venue": "Venue",
    "bulkBooking": "Bulk Booking",
    "coupon": "Coupon",
    "selectVenues": "Select Venues",
    "addProfileImage": "Add Profile Photo",
    "aadharCardImage": "Aadhar Card Image ",

//Business Employee Detail Screen
    "employeeDetails": "Employee Details",
    "privileges": "Privileges",
    "assignedVenues": "Assigned Venues",
    "joiningDate": "Joining Date",
    //
  };

  setRegistrationValues(int index, value) {
    if (index == 1) {
      turfDetails = value;
    } else if (index == 2) {
      ownerDetails = value;
    } else if (index == 3) {
      panDetails = value;
    } else if (index == 4) {
      bankDetails = value;
    }
  }

  List images = [];
  String topImageHomeScreen =
      'https://godayly.com/wp-content/uploads/2021/06/oat-milk-S96XZ2N-scaled.jpg';

// get app config from DB
  getAppConfig(String type) async {
    final url = '${webApi['domain']}${endPoint['fetchAppConfig']}';
    var str = json.encode({"type": type});
    print(str);
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData['result'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchCommonAppConfig(String commonType) async {
    final url = '${webApi['domain']}${endPoint['fetchCommonAppConfig']}';
    var str = json.encode({"commonType": commonType});
    print(str);
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData['result'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchPolicy(String state) async {
    final url = '${webApi['domain']}${endPoint['fetchPolicy']}';
    var str = json.encode({"state": state});
    print(str);
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData['result'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  // get app config from DB
  getFreshChatConfig() async {
    final url = '${webApi['domain']}${endPoint['fetchFreshChatDetail']}';
    var str = json.encode({});
    print(str);
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      print(responseData['detail']);
      return responseData['detail'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  isUserExists(String phone) async {
    String? fcmToken = await FirebaseMessaging.instance.getToken();

    final url = '${webApi['domain']}${endPoint['isUserExists']}';
    var str = json.encode({
      'phone': phone,
      'business': true,
      'fcmToken': fcmToken,
    });
    print(str);
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      if (responseData['message'] == 'User Already exists') {
        UserModal fetchedUser = UserModal(
          firstName: responseData['result']['firstName'],
          lastName: responseData['result']['lastName'],
          phone: responseData['result']['phone'],
          email: responseData['result']['email'],
          businessId: responseData['business']['_id'],
          businessStatus: responseData['business']['status'],
          businessActiveStatus: responseData['business']['isActive'],
          accessToken: responseData['accessToken'],
          role: 'Business',
          referralCode: responseData['result']['referralCode'].toString(),
          wallet: responseData['result']['wallet'] == null
              ? 0
              : responseData['result']['wallet'].toDouble(),
          avatar: responseData['result']['avatar'] == null
              ? ''
              : responseData['result']['avatar'],
          fcmToken: fcmToken,
          aadharImage: responseData['business']['aadharImage'] ?? '',
          bankDetails: responseData['business']['bankDetails'] == null ||
                  responseData['business']['bankDetails'].isEmpty
              ? null
              : BankDetails.jsonToBankDetails(
                  responseData['business']['bankDetails'][0]),
          gstDetails: responseData['business']['gstDetails'] == null
              ? null
              : GSTDetails.jsonToGST(responseData['business']['gstDetails']),
        );
        await storage.ready;
        storage.setItem(
            'accessToken',
            json.encode(
              {
                "token": responseData['accessToken'],
                "phone": responseData['result']['phone'],
                'role': 'Business',
              },
            ));
        user = fetchedUser;
      }
      return responseData;
    } catch (error) {
      print(error);
    }
  }

  fetchSport() async {
    final url = '${webApi['domain']}${endPoint['fetchSport']}';

    try {
      List loadedSport = [];
      final response = await http
          .get(Uri.parse(url), headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        responseData['result'].forEach((data) {
          loadedSport.add({
            'isSelected': false,
            'title': data['sport'],
            'id': data['_id'],
            'image': data['image'] ?? '',
            'categoryId': data['sportCategory']['_id'],
            'categoryName': data['sportCategory']['categoryName'],
          });
          int index = listOfSportCategory
              .indexWhere((cat) => cat['id'] == data['sportCategory']['_id']);
          if (index == -1) {
            listOfSportCategory.add({
              'id': data['sportCategory']['_id'],
              'title': data['sportCategory']['categoryName'],
              'isSelected': false,
            });
          }
        });
        print(listOfSportCategory);
        listOfSports = loadedSport;
        notifyListeners();
      }
    } catch (error) {
      print(error);
    }
  }

  String getImageBySportName(String name) {
    int index = listOfSports.indexWhere((data) => data['title'] == name);
    return index == -1 ? '' : listOfSports[index]['image'] ?? '';
  }

  resetSports() {
    listOfSports.forEach((data) {
      data['isSelected'] = false;
    });
    listOfSportCategory.forEach((data) {
      data['isSelected'] = false;
    });
    notifyListeners();
  }

  getSportByTitle(String title) {
    return listOfSports.firstWhere((sport) => sport['title'] == title);
  }

  createUserAndBusinessAndTurf({
    required String referralCode,
    required String referredByUserId,
  }) async {
    String? fcmToken = await FirebaseMessaging.instance.getToken();

    final url =
        '${webApi['domain']}${endPoint['createUserAndBusinessAndTurf']}';
    var request = http.MultipartRequest('POST', Uri.parse(url));
    var headers = {
      'Content-Type': 'application/json',
    };

    String firstName = '';
    String lastName = '';
    if (ownerDetails.ownerName.split(' ').length > 1) {
      firstName = ownerDetails.ownerName.split(' ')[0];
      lastName = ownerDetails.ownerName.split(' ')[1];
    } else {
      firstName = ownerDetails.ownerName;
      lastName = '';
    }

    request.fields.addAll({
      "firstName": firstName,
      "lastName": lastName,
      "email": ownerDetails.email,
      "phone": ownerDetails.mobileNo,
      "panCardNumber": '',
      "accountHolderName": '',
      "bankName": '',
      "accountNumber": '',
      "ifscCode": '',
      "turfName": turfDetails.turfName,
      "turfContact": turfDetails.mobileNo,
      "streetName": turfDetails.streetName,
      "landmark": turfDetails.landmark,
      "pincode": turfDetails.pincode.toString(),
      "city": turfDetails.city,
      "fcmToken": fcmToken!,
      "state": turfDetails.state,
      "referredBy": referralCode,
      "referredByPhone": referredByUserId,
      "gstType": gstType,
      "gstRegistered": gstRegistered,
      "gstNo": gstNo,
    });

    try {
      // if (panDetails.panImage != null) {
      //   request.files.add(
      //     await http.MultipartFile.fromPath(
      //         'panCardUrl', panDetails.panImage.path),
      //   );
      // }
      // if (bankDetails.chequeImageUrl != null) {
      //   request.files.add(
      //     await http.MultipartFile.fromPath(
      //         'chequeImageUrl', bankDetails.chequeImageUrl.path),
      //   );
      // }

      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        UserModal fetchedUser = UserModal(
          firstName: responseData['user']['firstName'],
          lastName: responseData['user']['lastName'],
          phone: responseData['user']['phone'],
          email: responseData['user']['email'],
          businessId: responseData['business']['_id'],
          businessActiveStatus: true,
          accessToken: responseData['accessToken'],
          businessStatus: 'PENDING',
          fcmToken: responseData['user']['fcmTokens'][0],
          referralCode: responseData['user']['referralCode'].toString(),
          role: 'Business',
          wallet: 0,
          avatar: '',
          aadharImage: '',
          bankDetails: null,
          gstDetails: null,
        );
        user = fetchedUser;
        await storage.ready;
        storage.setItem(
            'accessToken',
            json.encode(
              {
                "token": responseData['accessToken'],
                "phone": responseData['user']['phone'],
                "role": "Business",
              },
            ));
        return responseData['turf'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  deleteFCMToken() async {
    var body = json.encode({
      'fcmToken': user.fcmToken,
      'role': user.role,
    });
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${user.accessToken}'
    };
    final url = Uri.parse('${webApi['domain']}${endPoint['deleteFCMToken']}');
    try {
      final response = await http.put(url, body: body, headers: headers);
      final responseData = json.decode(response.body);
      if (!responseData['success']) {
      } else {
        notifyListeners();
        return;
      }
    } catch (e) {
      print(e);
    }
  }

  updateProfile({
    required String firstName,
    required String lastName,
    required String email,
    required String accessToken,
    required String avatar,
  }) async {
    final body = json.encode({});
    try {
      final url = '${webApi['domain']}${endPoint['updateProfile']}';
      var request = http.MultipartRequest('PUT', Uri.parse(url));
      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken'
      };
      request.fields.addAll({
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
      });
      if (avatar != '' && !avatar.contains('https:')) {
        request.files.add(await http.MultipartFile.fromPath('avatar', avatar));
      } else {
        request.fields.addAll({'avatar': avatar});
      }
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        UserModal fetchedUser = UserModal(
          firstName: responseData['result']['firstName'],
          lastName: responseData['result']['lastName'],
          phone: responseData['result']['phone'],
          email: responseData['result']['email'],
          businessId: user.businessId,
          businessStatus: user.businessStatus,
          businessActiveStatus: user.businessActiveStatus,
          accessToken: accessToken,
          fcmToken: user.fcmToken,
          role: user.role,
          wallet: user.wallet,
          referralCode: user.referralCode,
          bankDetails: user.bankDetails,
          gstDetails: user.gstDetails,
          aadharImage: user.aadharImage,
          avatar: responseData['result']['avatar'] == null
              ? ''
              : responseData['result']['avatar'],
        );
        user = fetchedUser;
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      throw e;
    }
  }

  getCityStateData(String pincode) async {
    try {
      final url = '${webApi['domain']}${endPoint['getStateCity']}';

      final response = await http.post(Uri.parse(url),
          body: json.encode({
            'pincode': pincode,
          }),
          headers: {
            "Content-Type": "application/json",
          });
      final responseData = json.decode(response.body);
      print(responseData);
      if (responseData['success']) {
        return {
          'city': responseData['result'][0]['city'],
          'state': responseData['result'][0]['administrativeLevels']
              ['level1long'],
        };
      } else {
        return null;
      }
    } catch (e) {
      print(e);
      return null;
    }
  }

  sendOTPtoUser(String mobileNo, {bool business = false}) async {
    final url = '${webApi['domain']}${endPoint['sendOTPtoUser']}';
    var str = json.encode({
      'mobileNo': mobileNo,
      "business": business,
    });
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData;
    } catch (error) {
      print(error);
      throw error;
    }
  }

  resendOTPtoUser(String mobileNo, String type) async {
    final url = '${webApi['domain']}${endPoint['resendOTPtoUser']}';
    var str = json.encode({
      'mobileNo': mobileNo,
      "type": type,
    });
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData['result']['type'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  verifyOTPofUser(String mobileNo, String otp) async {
    final url = '${webApi['domain']}${endPoint['verifyOTPofUser']}';
    var str = json.encode({
      'mobileNo': mobileNo,
      "otp": otp,
    });
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      return responseData['result']['type'];
    } catch (error) {
      print(error);
      throw error;
    }
  }

  deleteBusiness(String businessId) async {
    final url = '${webApi['domain']}${endPoint['deleteBusiness']}';
    var str = json.encode({
      'businessId': businessId,
    });
    try {
      final response = await http.post(Uri.parse(url),
          body: str, headers: {"Content-Type": "application/json"});
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return true;
    }
  }

  isEmployeeExists(String phone) async {
    String? fcmToken = await FirebaseMessaging.instance.getToken();

    final url = '${webApi['domain']}${endPoint['isEmployeeExists']}';
    var str = json.encode({
      'phone': phone,
      'fcmToken': fcmToken,
    });
    print(str);
    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {"Content-Type": "application/json"},
      );
      final responseData = json.decode(response.body);
      if (responseData['message'] == 'Employee Already exists') {
        UserModal fetchedUser = UserModal(
          firstName: responseData['result']['firstName'],
          lastName: responseData['result']['lastName'],
          phone: responseData['result']['phone'],
          email: responseData['result']['email'],
          address: responseData['result']['address'] ?? '',
          businessId: '',
          businessStatus: '',
          businessActiveStatus: responseData['result']['isActive'],
          accessToken: responseData['accessToken'],
          role: 'Employee',
          avatar: responseData['result']['avatar'] == null
              ? ''
              : responseData['result']['avatar'],
          referralCode: '',
          aadharImage: '',
          wallet: 0,
          bankDetails: null,
          gstDetails: null,
          fcmToken: fcmToken,
        );
        await storage.ready;
        storage.setItem(
            'accessToken',
            json.encode(
              {
                "token": responseData['accessToken'],
                "phone": responseData['result']['phone'],
                'role': 'Employee',
              },
            ));
        user = fetchedUser;
      }
      return responseData;
    } catch (error) {
      print(error);
    }
  }

  updateEmployee({
    required String firstName,
    required String lastName,
    required String email,
    required String address,
    required String accessToken,
    required String avatar,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateEmployee']}';
      var request = http.MultipartRequest('PUT', Uri.parse(url));
      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken'
      };
      request.fields.addAll({
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'address': address,
      });
      if (avatar != '' && !avatar.contains('https:')) {
        request.files.add(await http.MultipartFile.fromPath('avatar', avatar));
      } else {
        request.fields.addAll({'avatar': avatar});
      }
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        UserModal fetchedUser = UserModal(
          firstName: responseData['result']['firstName'],
          lastName: responseData['result']['lastName'],
          phone: user.phone,
          email: responseData['result']['email'],
          address: responseData['result']['address'],
          businessId: user.businessId,
          businessStatus: user.businessStatus,
          businessActiveStatus: user.businessActiveStatus,
          accessToken: accessToken,
          fcmToken: user.fcmToken,
          role: user.role,
          gstDetails: user.gstDetails,
          bankDetails: user.bankDetails,
          aadharImage: user.aadharImage,
          referralCode: user.referralCode,
          wallet: user.wallet,
          avatar: responseData['result']['avatar'] == null
              ? ''
              : responseData['result']['avatar'],
        );
        user = fetchedUser;
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      throw e;
    }
  }

  setBusinessId(BusinessModel business) {
    user.businessId = business.owner.businessId;
    user.business = business;
    notifyListeners();
  }

  insertAppVersion({
    required String accessToken,
  }) async {
    try {
      if (!detailsInserted) {
        var deviceInfo;
        final info = await PackageInfo.fromPlatform();
        if (Platform.isAndroid) {
          deviceInfo = await DeviceInfoPlugin().androidInfo;
        } else {
          deviceInfo = await DeviceInfoPlugin().iosInfo;
        }
        final url = '${webApi['domain']}${endPoint['insertAppVersion']}';
        var str = json.encode({
          // ignore: prefer_null_aware_operators
          "version": info.version,
          "os": Platform.isAndroid ? 'Android' : 'IOS',
          "deviceId": Platform.isAndroid
              ? deviceInfo.androidId
              : deviceInfo.identifierForVendor,
          "accessToken": accessToken,
          "deviceName": Platform.isAndroid
              ? '${deviceInfo.manufacturer} ${deviceInfo.model}'
              : '${deviceInfo.name} ${deviceInfo.model}',
          'app': 'BYS Business',
        });

        final response = await http.post(Uri.parse(url), body: str, headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        });
        final responseData = json.decode(response.body);
        detailsInserted = true;
        print(responseData['result']);
      }
    } catch (e) {
      print(e);
    }
  }

  void updateWallet(double amount) {
    user.wallet = (user.wallet - amount).abs();
    notifyListeners();
  }

  Future fetchUserWallet({required String accessToken}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchUserWallet']}';

      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        user.wallet = response['result']['wallet'] == null
            ? user.wallet
            : response['result']['wallet'].toDouble();
        notifyListeners();
      }
      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'error': "ERROR OCCURED!!!"};
    }
  }

  registerBusiness({
    required Map<String, String> body,
    required Map<String, String> files,
  }) async {
    try {
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken != null && fcmToken != '') {
        body['fcmToken'] = fcmToken;
      }
      final url = '${webApi['domain']}${endPoint['registerBusiness']}';
      final response = await RemoteServices.formDataRequest(
          method: 'POST', url: url, body: body, files: files);
      if (response['success'] && response['result'] != null) {
        user = UserModal(
          firstName: response['result']['firstName'],
          lastName: response['result']['lastName'],
          phone: response['result']['phone'],
          email: response['result']['email'],
          businessId: response['business']['_id'],
          businessStatus: response['business']['status'],
          businessActiveStatus: response['business']['isActive'],
          accessToken: response['accessToken'],
          role: 'Business',
          referralCode: response['result']['referralCode'].toString(),
          wallet: response['result']['wallet'] == null
              ? 0
              : response['result']['wallet'].toDouble(),
          avatar: response['result']['avatar'] == null
              ? ''
              : response['result']['avatar'],
          aadharImage: '',
          fcmToken: fcmToken,
          bankDetails: null,
          gstDetails: null,
        );
        await storage.ready;
        storage.setItem(
            'accessToken',
            json.encode({
              "token": response['accessToken'],
              "phone": response['result']['phone'],
              "role": "Business"
            }));
        notifyListeners();
      }
      return response;
    } catch (error) {
      return {'success': false, 'message': 'userCreateFailed'};
    }
  }

  LatLng? _coordinates;

  LatLng get myCordinates {
    return _coordinates!;
  }

  fetchMyLocation() async {
    late LatLng coord;
    final location = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.medium)
        .catchError((e, stackTrace) {
      print(e);
    });
    coord = LatLng(location.latitude, location.longitude);
    _coordinates = coord;
    notifyListeners();
    return true;
  }

  Future searchLocationFromGoogle({required String queryString}) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['searchLocationFromGoogle']}?queryString=$queryString';

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${user.accessToken}'
      };

      final response = await http.get(Uri.parse(url), headers: headers);

      final responseData = json.decode(response.body);

      if (responseData['success'] && responseData['result'] != null) {
        List<Address> loadedAddress = [];
        responseData['result'].forEach((address) {
          loadedAddress.add(Address(
            id: '',
            fullAddress: address['formatted_address'],
            streetName: address['name'],
            area: address['name'],
            coordinates: LatLng(
              address['geometry']['location']['lat'],
              address['geometry']['location']['lng'],
            ),
            city: '',
            state: '',
            pincode: 0,
            landmark: '',
          ));
        });
        return loadedAddress;
      } else {
        return [];
      }
    } catch (error, stackTrace) {
      debugPrint(error.toString());
      return [];
    }
  }

  Future<Address?> fetchAddressDetails({required String queryString}) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchAddressDetails']}?addressString=$queryString';

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${user.accessToken}'
      };

      final response = await http.get(Uri.parse(url), headers: headers);

      final responseData = json.decode(response.body);

      if (responseData['success'] &&
          responseData['result'] != null &&
          responseData['result'].isNotEmpty) {
        Address address = Address(
          id: responseData['result']['_id'] ?? '',
          streetName: responseData['result']['streetName'] ?? '',
          landmark: responseData['result']['landmark'] ?? '',
          fullAddress: responseData['result']['fullAddress'] ?? '',
          area: responseData['result']['area'] ?? '',
          city: responseData['result']['city'] ?? '',
          coordinates: LatLng(
            responseData['result']['coordinates'][0],
            responseData['result']['coordinates'][1],
          ),
          pincode: responseData['result']['pincode'] ?? 0,
          state: responseData['result']['state'] ?? '',
        );
        return address;
      } else {
        return null;
      }
    } catch (error) {
      debugPrint(error.toString());
      return null;
    }
  }

  Future<bool> updateBusinesProfile({
    required String accessToken,
    required Map<String, String> body,
    required Map<String, String> files,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateBusinesProfile']}';
      final response = await RemoteServices.formDataRequest(
        method: 'PUT',
        url: url,
        body: body,
        files: files,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        user.gstDetails = response['result']['gstDetails'] != null
            ? GSTDetails.jsonToGST(response['result']['gstDetails'])
            : null;
        user.bankDetails = response['result']['bankDetails'] == null ||
                response['result']['bankDetails'].isEmpty
            ? null
            : BankDetails.jsonToBankDetails(
                response['result']['bankDetails'][0]);
        user.aadharImage = response['result']['aadharImage'] ?? '';

        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }
}
