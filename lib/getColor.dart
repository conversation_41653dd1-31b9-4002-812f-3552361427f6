import 'package:flutter/material.dart';

// class GetColor {
List<Color> _listbgColor = [
  Color.fromRGBO(220, 157, 10, 1),
  Color.fromRGBO(96, 178, 70, 1),
  Color.fromRGBO(252, 112, 86, 1),
  Color.fromRGBO(130, 194, 251, 1),
  Color.fromRGBO(255, 139, 53, 1),
  Colors.blue,
  Colors.purple,
  Colors.purple.shade200,
];

getColorForName(String name) {
  var firstLetterOfName = (name[0]).toLowerCase();
  if (firstLetterOfName == 'a' || firstLetterOfName == 'b') {
    return _listbgColor[0];
  } else if (firstLetterOfName == 'd' ||
      firstLetterOfName == 'c' ||
      firstLetterOfName == 'e') {
    return _listbgColor[6];
  } else if (firstLetterOfName == 'f' ||
      firstLetterOfName == 'g' ||
      firstLetterOfName == 'h' ||
      firstLetterOfName == 'i' ||
      firstLetterOfName == 'j' ||
      firstLetterOfName == 'k' ||
      firstLetterOfName == 'l') {
    return _listbgColor[1];
  } else if (firstLetterOfName == 'm' ||
      firstLetterOfName == 'n' ||
      firstLetterOfName == 'o' ||
      firstLetterOfName == 'p' ||
      firstLetterOfName == 'q' ||
      firstLetterOfName == 'r') {
    return _listbgColor[2];
  } else if (firstLetterOfName == 's') {
    return _listbgColor[3];
  } else if (firstLetterOfName == 't' ||
      firstLetterOfName == 'u' ||
      firstLetterOfName == 'v' ||
      firstLetterOfName == 'w' ||
      firstLetterOfName == 'x' ||
      firstLetterOfName == 'y' ||
      firstLetterOfName == 'z') {
    return _listbgColor[4];
  } else {
    return _listbgColor[5];
  }
}
// }
