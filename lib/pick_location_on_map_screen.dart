// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/search_location_screen.dart';

import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:flutter/material.dart';

import 'authModule/providers/auth.dart';
import 'commonWidgets/circular_loader.dart';
import 'commonWidgets/search_box_widget.dart';
import 'common_function.dart';
import 'venueModule/models/venue_model.dart';

class PickLocationOnMapScreen extends StatefulWidget {
  final LatLng? coordinates;
  const PickLocationOnMapScreen({Key? key, required this.coordinates})
      : super(key: key);

  @override
  PickLocationOnMapScreenState createState() => PickLocationOnMapScreenState();
}

class PickLocationOnMapScreenState extends State<PickLocationOnMapScreen> {
  LatLng? currentCoordinates;
  LatLng? defaultCoordinates;

  List<Placemark> address = [];
  CameraPosition? currentSelectedPosition;
  late GoogleMapController _mapController;
  bool _isSet = false;
  bool _moving = false;
  bool isLoading = false;

  bool selectingLocation = false;
  bool isGettingAddressPlacemark = false;
  double tS = 0.0;
  double dW = 0.0;
  double dH = 0.0;
  Address? selectedAddress;

  static const double pinHeightF = 0.12;

  setLocation(LatLng coord) async {
    setState(() {
      currentCoordinates = coord;
      isLoading = true;
    });

    address = await placemarkFromCoordinates(coord.latitude, coord.longitude);
    setState(() {
      isLoading = false;
    });
  }

  String getAddressText() {
    String textToReturn = '';
    bool firstTaken = false;

    Placemark place = address[0];
    if (checkIfValid(place.street)) {
      textToReturn = place.street!;
      firstTaken = true;
    }
    if (checkIfValid(place.subLocality)) {
      textToReturn +=
          firstTaken ? ', ${place.subLocality!}' : place.subLocality!;
      firstTaken = true;
    }
    if (checkIfValid(place.locality)) {
      textToReturn += firstTaken ? ', ${place.locality!}' : place.locality!;
      firstTaken = true;
    }
    if (checkIfValid(place.administrativeArea)) {
      String stateToReturn = areaCode[place.administrativeArea] != null
          ? areaCode[place.administrativeArea!]!
          : place.administrativeArea!;
      //
      textToReturn += firstTaken ? ', $stateToReturn' : stateToReturn;
      firstTaken = true;
    }
    if (checkIfValid(place.postalCode)) {
      textToReturn += firstTaken ? ', ${place.postalCode!}' : place.postalCode!;
      firstTaken = true;
    }

    textToReturn += '.';
    return textToReturn;
  }

  selectLocationTextStyle() => TextStyle(
        fontSize: tS * 12,
        letterSpacing: 1,
        fontWeight: FontWeight.w600,
        color: Colors.grey.shade600,
      );

  locationNameStyle() => TextStyle(
        fontSize: tS * 18,
        letterSpacing: 0.3,
        fontWeight: FontWeight.w700,
        color: getThemeColor(),
      );

  addressTextStyle() => TextStyle(
        fontSize: tS * 14,
        fontWeight: FontWeight.w400,
        color: Colors.grey.shade600,
      );

  confirmTextStyle() => TextStyle(
        fontSize: tS * 13.5,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      );

  checkPermissionAndGetLocation() async {
    setState(() {
      isGettingAddressPlacemark = true;
    });

    try {
      await handlePermissionsFunction();

      if (await Permission.location.isGranted) {
        await Provider.of<Auth>(context, listen: false).fetchMyLocation();

        defaultCoordinates = currentCoordinates =
            Provider.of<Auth>(context, listen: false).myCordinates;

        if (widget.coordinates != null) {
          address = await placemarkFromCoordinates(
            widget.coordinates!.latitude,
            widget.coordinates!.longitude,
          );
          Future.delayed(const Duration(seconds: 0))
              .then((value) => setLocation(widget.coordinates!));
        } else {
          address = await placemarkFromCoordinates(
            currentCoordinates!.latitude,
            currentCoordinates!.longitude,
          );
        }

        setState(() {
          isGettingAddressPlacemark = false;
        });

        //
      } else {
        setState(() {
          isGettingAddressPlacemark = false;
        });
        showSnackbar('Please give location access');

        return;
      }
    } catch (e, stackTrace) {
      setState(() {
        isGettingAddressPlacemark = false;
      });
      showSnackbar('Please enable location access');
    } finally {
      setState(() {
        isGettingAddressPlacemark = false;
      });
    }
  }

  confirmLocation() {
    try {
      setState(() {
        isLoading = true;
      });
      print('Hello');
      Future.delayed(const Duration(seconds: 1)).then((value) {
        final Address pickedLocation = Address(
          coordinates: currentCoordinates!,
          fullAddress: getAddressText(),
          pincode: address[0].postalCode == null
              ? 0
              : address[0].postalCode == ''
                  ? 0
                  : int.parse(address[0].postalCode ?? '0'),
          streetName: address[0].street ?? '',
          area: address[0].street ?? '',
          city: address[0].locality ?? '',
          state: address[0].administrativeArea != null
              ? areaCode[address[0].administrativeArea] ??
                  address[0].administrativeArea ??
                  ''
              : '',
          id: '',
          landmark: '',
        );

        pop(pickedLocation);
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      print(e);
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    checkPermissionAndGetLocation();
  }

  @override
  void dispose() {
    super.dispose();
    _mapController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: SizedBox(
        height: dH,
        width: dW,
        child: isGettingAddressPlacemark
            ? Center(child: CircularLoader(android: dW * 0.07, iOS: 11.0))
            : Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        currentCoordinates == null
                            ? Container(
                                alignment: Alignment.center,
                                child: Text('Unable to detect location',
                                    style: Theme.of(context)
                                        .textTheme
                                        .displayMedium!
                                        .copyWith(
                                          fontSize: tS * 22,
                                          color: Colors.grey.shade600,
                                        )),
                              )
                            : GoogleMap(
                                mapType: MapType.normal,
                                onMapCreated: (GoogleMapController controller) {
                                  _mapController = controller;
                                },
                                onCameraMove: (position) {
                                  setState(() {
                                    if (!_moving) {
                                      _isSet = false;
                                      selectedAddress = null;
                                    }
                                    currentSelectedPosition = position;
                                    currentCoordinates = LatLng(
                                      currentSelectedPosition!.target.latitude,
                                      currentSelectedPosition!.target.longitude,
                                    );
                                  });
                                },
                                onCameraIdle: () {
                                  _moving = false;
                                  if (currentSelectedPosition != null &&
                                      !isLoading) {
                                    setLocation(
                                      LatLng(
                                        currentSelectedPosition!
                                            .target.latitude,
                                        currentSelectedPosition!
                                            .target.longitude,
                                      ),
                                    );
                                  }
                                },
                                myLocationButtonEnabled: true,
                                myLocationEnabled: true,
                                initialCameraPosition: CameraPosition(
                                  target: LatLng(currentCoordinates!.latitude,
                                      currentCoordinates!.longitude),
                                  zoom: 20,
                                ),
                                tiltGesturesEnabled: false,
                              ),
                        if (!_isSet && currentCoordinates != null)
                          Positioned(
                            top: 0,
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.only(
                                    bottom: dW * (pinHeightF / 2)),
                                child: Image.asset(
                                  'assets/images/destination_pin.png',
                                  height: dW * pinHeightF,
                                ),
                              ),
                            ),
                          ),
                        Positioned(
                          top: dW * (iOSCondition(dH) ? 0.15 : 0.09),
                          left: dW * 0.04,
                          right: dW * 0.04,
                          child: Padding(
                            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Back button
                                NewAppBar(dW: dW, title: ''),
                                SizedBox(height: dW * 0.03),
                            
                                SearchBoxWidget(
                                  bgColor: Colors.white,
                                  vPadding: .025,
                                  borderColor: getThemeColor(),
                                  onTap: () {
                                    push(SearchLocationScreen()).then((value) {
                                      if (value != null) {
                                        Future.delayed(
                                            const Duration(milliseconds: 100),
                                            () async {
                                          await _mapController.animateCamera(
                                              CameraUpdate.newCameraPosition(
                                                  CameraPosition(
                                            target: value,
                                            zoom: 20,
                                          )));
                                        });
                                        setLocation(value);
                                      }
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (address.isNotEmpty)
                    Column(
                      children: [
                        Container(
                          width: dW,
                          color: Colors.white,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: dW * 0.04,
                              vertical: dW * 0.035,
                            ),
                            color: Colors.white,
                            // decoration: BoxDecoration(
                            // boxShadow: [
                            // BoxShadow(
                            //   color: Colors.grey.shade300,
                            //   spreadRadius: 1.5,
                            //   blurRadius: 4.0,
                            // ),
                            // ],
                            // ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  alignment: Alignment.topLeft,
                                  width: dW * 0.65,
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      '${address[0].name}',
                                      textAlign: TextAlign.left,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: locationNameStyle(),
                                    ),
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                      bottom: dW * 0.035, top: dW * 0.02),
                                  child: TextWidget(
                                    title: getAddressText(),
                                    maxLines: 3,
                                    textOverflow: TextOverflow.ellipsis,
                                    fontSize: 15,
                                    color: Colors.black45,
                                  ),
                                ),
                                CustomButton(
                                  width: dW,
                                  height: dW * 0.14,
                                  buttonText: 'Confirm Location',
                                  onPressed: confirmLocation,
                                  isLoading: isLoading,
                                  bottomMargin:
                                      !Platform.isAndroid ? dW * 0.03 : 0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
      ),
    );
  }
}
