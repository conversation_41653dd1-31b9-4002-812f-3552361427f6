import 'dart:convert';

import '../../employeeModule/models/business_model.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';
import '../../notificationModule.dart/models/notificationModel.dart';

class NotificationProvider with ChangeNotifier {
  int notificationCount = 0;

  List<NotificationModel> _notification = [];

  List<NotificationModel> get notification {
    return [..._notification];
  }

  incrementAndDecrementNotificationCount(bool add) {
    if (add) {
      notificationCount += 1;
    } else {
      notificationCount -= 1;
    }
    notifyListeners();
  }

  resetNotification() {
    _notification = [];
  }

  fetchUserNotifcationNew({
    required String accessToken,
    required int limit,
    required int skip,
    BusinessModel? business,
  }) async {
    var url;
    var str;
    if (business != null) {
      url = '${webApi['domain']}${endPoint['fetchEmployeeNotifcationNew']}';
      str = json.encode({
        "owner": business.owner.id,
        "user": limit,
        "skip": skip,
      });
    } else {
      url = '${webApi['domain']}${endPoint['fetchUserNotifcationNew']}';
      str = json.encode({
        "userType": "Business",
        "limit": limit,
        "skip": skip,
      });
    }
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<NotificationModel> loadedNotification = [];
        responseData['result'].forEach((data) {
          List<RentalItem> rentedItem = [];
          if (data['bookingId'] != null) {
            data['bookingId']['rentedItem'].forEach((data) {
              rentedItem.add(
                RentalItem(
                  id: data['id'],
                  price: data['price'].toDouble(),
                  duration: data['duration'],
                  // productId: '',
                  productImage: data['productImage'],
                  productName: data['productName'],
                  quantity: data['quantity'],
                ),
              );
            });
          }
          loadedNotification.add(
            NotificationModel(
              id: data['_id'],
              title: data['title'],
              createdAt: DateTime.parse(data['createdAt']).toLocal(),
              userName:
                  '${data['user']['firstName']} ${data['user']['lastName']}',
              isViewed: data['isViewed'],
              type: data['type'] ?? '',
              booking: data['bookingId'] != null && data['bookingId'] is Map
                  ? BookingModel.jsonToBooking(data['bookingId'])
                  : null,
            ),
          );
        });
        _notification.addAll(loadedNotification);
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  updateViewState({
    required String accessToken,
    required String notificationId,
  }) async {
    final url = '${webApi['domain']}${endPoint['updateViewState']}';
    var str = json.encode({
      "notificationId": notificationId,
    });
    try {
      final response = await http.put(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        _notification.forEach((notify) {
          if (notify.id == notificationId) {
            notify.isViewed = true;
          }
        });
        incrementAndDecrementNotificationCount(false);
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchUserNewNotifcation({
    required String accessToken,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchUserNewNotifcation']}';
    var str = json.encode({
      "userType": "Business",
    });
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notificationCount = responseData['result'];
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchEmployeeNotificationCount({
    required String accessToken,
    required String owner,
    required String employee,
  }) async {
    final url =
        '${webApi['domain']}${endPoint['fetchEmployeeNotificationCount']}';
    var str = json.encode({
      "owner": owner,
      "employee": employee,
    });
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        notificationCount = responseData['result'];
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }
}
