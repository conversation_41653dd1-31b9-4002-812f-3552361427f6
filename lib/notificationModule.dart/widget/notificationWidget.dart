import 'package:intl/intl.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../notificationModule.dart/models/notificationModel.dart';

class NotificationWidget extends StatelessWidget {
  final NotificationModel notification;
  final UserModal user;
  final double dW;
  final double tS;
  final int index;
  const NotificationWidget({
    Key? key,
    required this.notification,
    required this.user,
    required this.dW,
    required this.tS,
    required this.index,
  }) : super(key: key);

  String getInitials(String inputString) => inputString.isNotEmpty
      ? inputString.trim().split(' ').map((l) => l[0]).take(2).join()
      : '';

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!notification.isViewed) {
          Provider.of<NotificationProvider>(context, listen: false)
              .updateViewState(
            accessToken: user.accessToken,
            notificationId: notification.id,
          );
        }
        if (notification.booking != null) {
          push(
            BookingDescriptionScreen(
                booking: notification.booking!, user: user),
          );
        } else if (notification.type == 'BUSINESS REJECTED' ||
            notification.type == 'Venue REJECTED') {
          customLaunch(Provider.of<Auth>(context, listen: false).helpLink);
        } else if (notification.type == 'BUSINESS APPROVED') {
          Navigator.of(context).pop();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          border: index != 0
              ? Border(bottom: BorderSide(color: Color(0xffDDDDDD)))
              : Border(
                  bottom: BorderSide(color: Color(0xffDDDDDD)),
                  top: BorderSide(color: Color(0xffDDDDDD))),
          color: notification.isViewed
              ? getWhiteColor(context)
              : getLightGreenColor1(context),
          boxShadow: notification.isViewed
              ? [
                  BoxShadow(
                      color: getLightGreyColor8(context), offset: Offset(0, 1))
                ]
              : null,
        ),
        padding: EdgeInsets.symmetric(horizontal: dW * .05, vertical: dW * .05),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 8,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.all(dW * .01),
                        height: dW * .09,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: getWhiteColor(context),
                            border: Border.all(
                              color: getThemeColor(),
                            )),
                        child: AssetSvgIcon(
                            iconName: 'notification_profile', height: dW * .08),
                      ),
                      SizedBox(width: dW * .04),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: dW * 0.68),
                            child: TextWidget(
                              title: notification.title,
                              fontWeight: FontWeight.w500,
                              fontSize: 13,
                              color: getGreyColor8(context),
                            ),
                          ),
                          SizedBox(height: dW * .02),
                          TextWidget(
                            title: DateFormat('dd MMM yyyy , hh:mm a')
                                .format(notification.createdAt),
                            fontWeight: FontWeight.w500,
                            fontSize: 11,
                            color: getLightGreyColor1(context),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (!notification.isViewed)
                  Container(
                      margin: EdgeInsets.only(top: dW * 0.04),
                      child: CircleAvatar(
                          radius: 4.5, backgroundColor: getThemeColor()))
              ],
            ),
          ],
        ),
      ),
    );
  }
}
