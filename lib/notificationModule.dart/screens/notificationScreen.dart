import 'dart:io';

import 'package:bys_business/commonWidgets/new_appbar.dart';

import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../common_function.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../fontSizes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import '../../notificationModule.dart/models/notificationModel.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';
import '../../notificationModule.dart/widget/notificationWidget.dart';

class NotificationScreen extends StatefulWidget {
  final UserModal user;
  const NotificationScreen({Key? key, required this.user}) : super(key: key);

  @override
  _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isLoading = false;
  bool lazyLoading = false;
  int skip = 0;
  static const limit = 10;
  List<NotificationModel> listOfNotification = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    Provider.of<NotificationProvider>(context, listen: false)
        .resetNotification();
    fetchNotification();
  }

  fetchNotification() async {
    try {
      setState(() {
        isLoading = true;
      });
      await getNotifications();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getNotifications() async {
    await Provider.of<NotificationProvider>(context, listen: false)
        .fetchUserNotifcationNew(
      accessToken: widget.user.accessToken,
      limit: limit,
      skip: skip,
      business: widget.user.business,
    );
  }

  lazyLoad() async {
    setState(() {
      lazyLoading = true;
    });
    await getNotifications();
    setState(() {
      lazyLoading = false;
    });
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        skip += limit;
        lazyLoad();
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    listOfNotification =
        Provider.of<NotificationProvider>(context).notification;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: deviceHeight,
      width: deviceWidth,
      child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
        child: Column(
          children: [
            // SizedBox(height: deviceWidth * 0.05),
            NewAppBar(dW: deviceWidth, title: 'Notifications'),
            SizedBox(height: deviceWidth * 0.02),
            Expanded(
              child: isLoading
                  ? Center(
                      child: Platform.isAndroid
                          ? MaterialCircularLoader(deviceWidth * 0.07)
                          : CupertinoCircularLoader(15.0),
                    )
                  : listOfNotification.length == 0
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Align(
                              alignment: Alignment.center,
                              child: SvgPicture.asset(
                                  'assets/svgIcons/Nonotification.svg'),
                            ),
                            SizedBox(height: deviceWidth * 0.04),
                            Container(
                              child: Text(
                                'No Notification!',
                                style: Theme.of(context)
                                    .textTheme
                                    .displayLarge!
                                    .copyWith(
                                      fontSize: textScaleFactor * displayLarge,
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            )
                          ],
                        )
                      : NotificationListener<ScrollNotification>(
                          onNotification: _handleScrollNotification,
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            physics: BouncingScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: deviceWidth * 0.04),
                                ...listOfNotification
                                    .asMap()
                                    .map(
                                      (i, notification) => MapEntry(
                                        i,
                                        NotificationWidget(
                                          notification: notification,
                                          user: widget.user,
                                          dW: deviceWidth,
                                          tS: textScaleFactor,
                                          index: i,
                                        ),
                                      ),
                                    )
                                    .values
                                    .toList(),
                                SizedBox(height: deviceWidth * 0.04),
                                if (lazyLoading)
                                  Center(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        bottom: deviceWidth * 0.1,
                                      ),
                                      child: Platform.isIOS
                                          ? CupertinoCircularLoader(10.0)
                                          : MaterialCircularLoader(
                                              deviceWidth * 0.06),
                                    ),
                                  ),
                                SizedBox(height: deviceWidth * 0.12),
                              ],
                            ),
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
