import 'package:flutter/material.dart';

import '../../fontSizes.dart';

class SelectCountBottomSheet extends StatefulWidget {
  final String selectedDuration;
  final int selectedCount;
  const SelectCountBottomSheet({
    required this.selectedDuration,
    required this.selectedCount,
  });

  @override
  SelectCountBottomSheetState createState() => SelectCountBottomSheetState();
}

class SelectCountBottomSheetState extends State<SelectCountBottomSheet> {
  List<int> counts = [];

  getWidth(width) {
    if (widget.selectedDuration == 'Month') {
      return width * 1.4;
    } else if (widget.selectedDuration == 'Year') {
      return width * 0.8;
    } else {
      return width * 0.63;
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.selectedDuration == 'Month') {
      counts = List<int>.generate(11, (i) => i + 1);
    } else if (widget.selectedDuration == 'Year') {
      counts = List<int>.generate(4, (i) => i + 1);
    } else if (widget.selectedDuration == 'Days') {
      // counts = List<int>.generate(31, (i) => i + 1);
      counts = [7, 15, 21];
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: getWidth(width),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: width * 0.05),
          Container(
            margin: EdgeInsets.symmetric(horizontal: width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Select ${widget.selectedDuration}',
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                        fontSize: textScaleFactor * 16,
                        color: Colors.black,
                      ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: CircleAvatar(
                    radius: 13,
                    backgroundColor: Colors.grey.shade400,
                    child: Icon(
                      Icons.clear,
                      size: 15,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: width * 0.05),
                  ...counts.map(
                    (count) => GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop(count);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(
                          bottom: width * 0.03,
                        ),
                        width: width,
                        padding: EdgeInsets.symmetric(
                          vertical: width * 0.035,
                          horizontal: width * 0.04,
                        ),
                        decoration: BoxDecoration(
                          color: count == widget.selectedCount
                              ? Theme.of(context).primaryColor.withOpacity(0.25)
                              : Colors.transparent,
                        ),
                        child: Text(
                          count.toString(),
                          style:
                              Theme.of(context).textTheme.displaySmall!.copyWith(
                                    fontSize: textScaleFactor * displayMedium,
                                    color: Colors.black,
                                  ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
