import '../../bulkBookingModule/model/bulk_model.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circle_avatar_widget.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../new_colors.dart';
import '../screens/bulk_booking_description_screen.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BulkWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final BulkBookingModel booking;
  const BulkWidget({
    Key? key,
    required this.dW,
    required this.tS,
    required this.booking,
  }) : super(key: key);

  buildTextWidget({
    required String text,
    required BuildContext context,
    FontWeight? fontWeight = FontWeight.w600,
    double? fontSize = 12.5,
  }) {
    return Container(
      constraints: BoxConstraints(maxWidth: dW * 0.6),
      margin: EdgeInsets.only(bottom: dW * 0.01),
      child: Text(
        text,
        style: Theme.of(context).textTheme.headlineMedium!.copyWith(
              color: Colors.black,
              fontSize: tS * fontSize!,
              fontWeight: fontWeight,
            ),
      ),
    );
  }

  String getPaymentStatus() {
    if (booking.amountPaid == 0) {
      return 'Unpaid';
    } else if (booking.amountPaid <
        booking.totalAmount - booking.discountedAmount) {
      return "Partially Paid";
    } else {
      return "Paid";
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => push(BulkBookingDescriptionScreen(booking: booking)),
      child: CustomContainer(
        margin: EdgeInsets.only(bottom: dW * 0.05),
        vPadding: 0,
        hPadding: 0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Stack(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      left: dW * 0.045,
                      right: dW * 0.045,
                      top: dW * 0.025,
                      bottom: dW * 0.04),
                  child: Row(
                    children: [
                      CircleAvatarWidget(
                        avatar: booking.userAvatar,
                        userName: '${booking.userName}',
                        height: 0.13,
                        parentRadius: 24,
                        fontSize: 15,
                      ),
                      SizedBox(width: dW * 0.04),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: dW * 0.56),
                            child: TextWidget(
                              title: '${booking.userName}',
                              fontWeight: FontWeight.w600,
                              maxLines: 1,
                              textOverflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(height: dW * 0.02),
                          Row(
                            children: [
                              AssetSvgIcon(iconName: 'venue'),
                              SizedBox(
                                width: dW * 0.01,
                              ),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: dW * 0.5),
                                child: TextWidget(
                                  title: booking.venue.name,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: dW * 0.02),
                          Row(
                            children: [
                              AssetSvgIcon(iconName: 'calendar1'),
                              SizedBox(width: dW * 0.01),
                              ConstrainedBox(
                                constraints:
                                    BoxConstraints(maxWidth: dW * 0.35),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title:
                                        // '${DateFormat('dd MMM yyyy').format(booking.startDate)}, ${get12HrFormat(booking.bookingSlotStartTime).toStringAsFixed(2)} ${getTimePeriod(booking.bookingSlotStartTime)} - ${get12HrFormat(booking.bookingSlotEndTime).toStringAsFixed(2)} ${getTimePeriod(booking.bookingSlotEndTime)}',
                                        '${DateFormat('dd MMM').format(booking.startDate)} - ${DateFormat('dd MMM yyyy').format(booking.endDate)}',
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                              // SizedBox(width: dW * 0.01),
                              // Container(
                              //   height: dW * 0.04,
                              //   width: 1,
                              //   decoration: BoxDecoration(color: Colors.grey),
                              // ),
                              // SizedBox(width: dW * 0.01),
                            ],
                          ),
                          SizedBox(height: dW * 0.02),
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: dW * 0.2),
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: SizedBox(
                                child: TextWidget(
                                  title:
                                      '${booking.totalBookingCount} ${booking.totalBookingCount > 1 ? 'Bookings' : 'Booking'}',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: dW * 0.02),
                          Row(
                            children: [
                              AssetSvgIcon(iconName: 'clock2'),
                              SizedBox(width: dW * 0.01),
                              ConstrainedBox(
                                constraints:
                                    BoxConstraints(maxWidth: dW * 0.35),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title:
                                        '${get12HrFormat(booking.startTime).toStringAsFixed(2)} ${getTimePeriod(booking.startTime)} - ${get12HrFormat(booking.endTime).toStringAsFixed(2)} ${getTimePeriod(booking.endTime)}',
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: dW * 0.02),
                          Row(
                            children: [
                              Image.network(booking.sport.image, height: 15),
                              SizedBox(width: dW * 0.02),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: dW * 0.2),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title: '${booking.sizeOrSport}',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              SizedBox(width: dW * 0.03),
                              TextWidget(
                                title: '|',
                                fontSize: 12,
                                color: Color(0xffB9B9B9),
                              ),
                              SizedBox(width: dW * 0.03),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: dW * 0.2),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title:
                                        '\u20b9${booking.totalAmount.toStringAsFixed(2)}',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Positioned(
                  right: 0,
                  child: Container(
                    alignment: Alignment.center,
                    constraints:
                        BoxConstraints(maxWidth: dW * 0.25, minWidth: dW * 0.2),
                    padding: EdgeInsets.symmetric(vertical: dW * 0.01),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        topRight: Radius.circular(8),
                      ),
                      color: getPaymentStatus() == 'Paid'
                          ? getThemeColor()
                          : getYellowColor1(context),
                    ),
                    child: TextWidget(
                      title: getPaymentStatus(),
                      fontSize: 11,
                      textAlign: TextAlign.center,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
