import 'dart:io';

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/venueModule/models/venue_model.dart';
import 'package:flutter/services.dart';

import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/radio_widget.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import '../screens/booked_slots_screen.dart';
import '../screens/bulk_booking_description_screen.dart';
import '../../common_function.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class Step3BulkBooking extends StatefulWidget {
  final double deviceWidth;
  final double textScaleFactor;
  final String name;
  final String phone;
  final String venue;
  final String sizeOrSport;
  final String option;
  final bool isNet;
  final String label;
  final String categoryName;
  final double startTime;
  final double endTime;
  final String totalBooking;
  final List selectedDays;
  final DateTime startDate;
  final DateTime endDate;
  final int durationCount;
  final String duration;
  final List availableDates;
  final List notAvailableDates;
  final num totalAmount;
  // final num amountPaid;
  final num convenienceFee;
  final num gstOnConvenienceFee;
  final num basicAmount;
  int currentIndex;
  final Function setCurrentIndex;
  final TextEditingController amountController;
  final SportType sport;
  final TextEditingController discountController;
  final TextEditingController gstController;
  final String invoiceType;
  final Function selectInvoiceType;
  final Function getTotalGST;
  final Function removeBookingDate;
  Step3BulkBooking({
    Key? key,
    required this.deviceWidth,
    required this.textScaleFactor,
    required this.name,
    required this.phone,
    required this.venue,
    required this.sizeOrSport,
    required this.option,
    required this.isNet,
    required this.label,
    required this.setCurrentIndex,
    required this.categoryName,
    required this.startTime,
    required this.endTime,
    required this.totalBooking,
    required this.selectedDays,
    required this.startDate,
    required this.endDate,
    required this.durationCount,
    required this.duration,
    required this.availableDates,
    required this.notAvailableDates,
    required this.sport,
    required this.totalAmount,
    required this.convenienceFee,
    required this.gstOnConvenienceFee,
    required this.basicAmount,
    required this.amountController,
    required this.discountController,
    required this.currentIndex,
    required this.gstController,
    required this.invoiceType,
    required this.selectInvoiceType,
    required this.getTotalGST,
    required this.removeBookingDate,
  }) : super(key: key);

  @override
  State<Step3BulkBooking> createState() => _Step3BulkBookingState();
}

class _Step3BulkBookingState extends State<Step3BulkBooking> {
  bool showDiscription = false;

  FocusNode amountNode = FocusNode();
  bool loadConvenienceCharges = false;

  double discountAmount = 0;
  double dW = 0;
  double tS = 0;

  List paymentStatus = [
    {
      'index': 1,
      'title': 'Fully Paid',
      'isSelected': true,
    },
    {
      'index': 2,
      'title': 'Partially Paid/Advance',
      'isSelected': false,
    },
    {
      'index': 3,
      'title': 'Unpaid/Cash',
      'isSelected': false,
    },
  ];

  setPaymentStatus(int index) {
    paymentStatus.forEach((status) {
      if (status['index'] == index) {
        setState(() {
          status['isSelected'] = true;
          widget.currentIndex = index;
        });
      } else {
        setState(() {
          status['isSelected'] = false;
        });
      }
    });
    widget.setCurrentIndex(index);
    widget.amountController.clear();
  }

  getAmountPaid() {
    if (widget.currentIndex == 1) {
      return widget.totalAmount - discountAmount;
    } else if (widget.currentIndex == 2) {
      if (widget.amountController.text.isEmpty) {
        return 0.0;
      } else {
        return double.parse(widget.amountController.text.trim());
      }
    } else if (widget.currentIndex == 3) {
      return 0.0;
    }
  }

  Widget buildColumnWidget({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: title,
          fontWeight: FontWeight.w500,
          fontSize: 13,
          color: getLightGreyColor1(context),
        ),
        SizedBox(height: dW * .015),
        TextWidget(
          title: value,
          fontWeight: FontWeight.w600,
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    dW = widget.deviceWidth;
    tS = widget.textScaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: dW * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomContainer(
            margin: EdgeInsets.only(bottom: dW * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomContainer(
                  hPadding: 0.03,
                  vPadding: 0.027,
                  boxShadow: [],
                  bgColor: Color(0xffE3FEDB),
                  borderColor: Color(0xffE3FEDB),
                  radius: 8,
                  margin: EdgeInsets.only(bottom: dW * 0.035),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Player name:', fontSize: 12),
                          SizedBox(height: dW * 0.01),
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: dW * 0.6),
                            child: TextWidget(
                              title: widget.name,
                              fontWeight: FontWeight.w600,
                              color: getThemeColor(),
                            ),
                          ),
                          SizedBox(height: dW * 0.01),
                        ],
                      ),
                      GestureDetector(
                        onTap: () => customLaunch('tel:${widget.phone}'),
                        child: Container(
                          padding: EdgeInsets.all(dW * 0.017),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: const AssetSvgIcon(iconName: 'phone2'),
                        ),
                      )
                    ],
                  ),
                ),
                buildColumnWidget(title: 'Venue Name', value: widget.venue),
                SizedBox(height: dW * 0.035),
                TextWidget(
                  title: 'Date & Time',
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                  color: getLightGreyColor1(context),
                ),
                SizedBox(height: dW * .03),
                Row(
                  children: [
                    AssetSvgIcon(iconName: 'calendar1', height: dW * .05),
                    SizedBox(width: dW * .02),
                    TextWidget(
                      title:
                          '${DateFormat('dd MMM yyyy').format(widget.startDate)} - ${DateFormat('dd MMM yyyy').format(widget.endDate)}',
                      fontWeight: FontWeight.w600,
                      color: getGreyColor2(context),
                    ),
                  ],
                ),
                SizedBox(height: dW * .025),
                Row(
                  children: [
                    AssetSvgIcon(iconName: 'clock2', height: dW * .05),
                    SizedBox(width: dW * .02),
                    // TextWidget(
                    //   title:
                    //       '${get12HrFormat(widget.startTime).toStringAsFixed(2)} ${getTimePeriod(widget.startTime)} - ${get12HrFormat(widget.endTime).toStringAsFixed(2)} ${getTimePeriod(widget.endTime)}',
                    //   fontWeight: FontWeight.w600,
                    //   color: getGreyColor2(context),
                    // ),
                    TextWidget(
                      title:
                          '${get12HrFormat(widget.startTime).toStringAsFixed(2)} ${getTimePeriod(widget.startTime)} - ${widget.endTime == 0.0 ? '12.00' : get12HrFormat(widget.endTime).toStringAsFixed(2)} ${getTimePeriod(widget.endTime)}',
                      fontWeight: FontWeight.w600,
                      color: getGreyColor2(context),
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.035),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      title: widget.categoryName == 'Outdoor'
                          ? "Sports & Turf Size"
                          : "Sport",
                      fontWeight: FontWeight.w500,
                      color: getLightGreyColor1(context),
                    ),
                    SizedBox(height: dW * .02),
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          Image.network(widget.sport.image, height: 18),
                          SizedBox(width: dW * .02),
                          TextWidget(
                            title: widget.sport.sport,
                            color: getGreyColor2(context),
                            fontWeight: FontWeight.w500,
                          ),
                          if (widget.categoryName == 'Outdoor') ...[
                            SizedBox(width: dW * .02),
                            VerticalDivider(width: 1, color: Color(0xff505050)),
                            SizedBox(width: dW * .02),
                            TextWidget(
                              title:
                                  '${widget.sizeOrSport.replaceAll(':', 'x')}${widget.label != '' ? ' (${widget.label})' : ''}',
                              color: getGreyColor2(context),
                              fontWeight: FontWeight.w600,
                            ),
                          ]
                        ],
                      ),
                    )
                  ],
                ),
                SizedBox(height: dW * 0.035),
                Row(
                  children: [
                    buildColumnWidget(
                      title: 'Duration',
                      value: '${widget.durationCount} ${widget.duration}',
                    ),
                    SizedBox(width: dW * 0.12),
                    buildColumnWidget(
                      title: 'Total Booking',
                      value: widget.totalBooking.toString(),
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.035),
                TextWidget(
                  title: 'Selected Days',
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                  color: getLightGreyColor1(context),
                ),
                SizedBox(height: dW * 0.02),
                Wrap(
                  children: [
                    ...widget.selectedDays.map(
                      (day) => Container(
                        margin: EdgeInsets.only(
                            right: dW * 0.03, bottom: dW * 0.03),
                        constraints: BoxConstraints(minWidth: dW * 0.13),
                        padding: EdgeInsets.symmetric(
                          vertical: dW * 0.02,
                          horizontal: dW * 0.035,
                        ),
                        decoration: BoxDecoration(
                          color: getThemeColor(),
                          borderRadius: BorderRadius.circular(100),
                          border: Border.all(color: getThemeColor()),
                        ),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: TextWidget(
                            title: day,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.025),
          GestureDetector(
            onTap: () {
              push(
                BookedSlotsScreen(
                  bookingDates: widget.availableDates,
                  removeBookingDate: widget.removeBookingDate,
                  startTime: widget.startTime,
                  endTime: widget.endTime,
                ),
              );
            },
            child: CustomContainer(
              boxShadow: const [],
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                    title: 'Booking Dates',
                    fontWeight: FontWeight.w600,
                    color: getGreyColor2(context),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TextWidget(
                        title: 'View Slots',
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                        color: getGreyColor2(context),
                      ),
                      SizedBox(width: dW * .03),
                      AssetSvgIcon(
                          iconName: 'frwd_arrow', color: getThemeColor()),
                      SizedBox(width: dW * .02),
                    ],
                  )
                ],
              ),
            ),
          ),
          if (widget.notAvailableDates.isNotEmpty) ...[
            SizedBox(height: dW * 0.05),
            GestureDetector(
              onTap: () {
                dateBottomSheet(
                    'Not Available Dates', widget.notAvailableDates, context);
              },
              child: CustomContainer(
                boxShadow: const [],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextWidget(
                      title: 'Not Available Dates',
                      fontWeight: FontWeight.w600,
                      color: getGreyColor2(context),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        TextWidget(
                          title: 'View',
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                          color: getGreyColor2(context),
                        ),
                        SizedBox(width: dW * .03),
                        AssetSvgIcon(
                            iconName: 'frwd_arrow', color: getThemeColor()),
                        SizedBox(width: dW * .02),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ],
          SizedBox(height: dW * 0.05),
          CustomContainer(
            margin: EdgeInsets.only(bottom: dW * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(title: 'Type of GST Invoice you want to sent?'),
                SizedBox(height: dW * 0.025),
                Row(
                  children: [
                    ...['Inclusive', 'Exclusive'].map(
                      (value) => GestureDetector(
                        onTap: () => widget.selectInvoiceType(value),
                        child: Container(
                          margin: EdgeInsets.only(right: dW * 0.05),
                          child: RadioWidget(
                            active: widget.invoiceType == value,
                            activeColor: Theme.of(context).primaryColor,
                            inActiveBorderColor: Theme.of(context).primaryColor,
                            title: value,
                            radius: 6,
                            inactiveBorderRadius: 6,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(height: dW * 0.055),
                CustomTextFieldWithLabel(
                  label: 'Customer GST No.',
                  controller: widget.gstController,
                  optional: true,
                  hintText: 'Enter Gst  No. Here...',
                  inputAction: TextInputAction.done,
                ),
              ],
            ),
          ),
          CustomContainer(
            hPadding: 0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  child: TextWidget(
                    title: 'Payment Status:',
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: dW * 0.035),
                ...paymentStatus.map(
                  (payment) => GestureDetector(
                    onTap: () => setPaymentStatus(payment['index']),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: dW * 0.05,
                        vertical: dW * 0.02,
                      ),
                      width: double.infinity,
                      color: payment['isSelected']
                          ? Theme.of(context).primaryColor.withOpacity(0.15)
                          : Colors.transparent,
                      margin: EdgeInsets.only(bottom: dW * 0.02),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                color: Colors.transparent,
                                width: dW * 0.6,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    TextWidget(
                                      title: '${payment['title']}',
                                      fontSize:
                                          payment['isSelected'] ? tS * 14 : 14,
                                      color: Color(0xff242530),
                                      fontWeight: payment['isSelected']
                                          ? FontWeight.w600
                                          : FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                height: dW * 0.07,
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: payment['isSelected']
                                        ? Theme.of(context).primaryColor
                                        : Color(0xffB6B7BA),
                                  ),
                                ),
                                child: CircleAvatar(
                                  radius: 8,
                                  backgroundColor: payment['isSelected']
                                      ? Theme.of(context).primaryColor
                                      : Colors.white,
                                ),
                              ),
                            ],
                          ),
                          if ((payment['index'] == 2) &&
                              payment['isSelected'] == true)
                            Container(
                              width: dW * 0.7,
                              padding: EdgeInsets.only(top: dW * 0.025),
                              child: CustomTextFieldWithLabel(
                                label: '',
                                borderRadius: 5,
                                controller: widget.amountController,
                                hintText: 'Enter amount',
                                hintFS: 13,
                                textFS: 14,
                                inputFormatter: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                inputType: Platform.isIOS
                                    ? TextInputType.numberWithOptions(
                                        signed: true, decimal: false)
                                    : TextInputType.number,
                                maxLength: 10,
                                focusNode: amountNode,
                                onTap: () {
                                  amountNode.requestFocus();
                                  setState(() {});
                                },
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  child: DividerWidget(top: 0),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        child: CustomTextFieldWithLabel(
                          label: 'Discount Amount',
                          labelFW: FontWeight.w600,
                          controller: widget.discountController,
                          hintText: 'Enter discount amount',
                          optional: true,
                          inputFormatter: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          inputType: Platform.isIOS
                              ? TextInputType.numberWithOptions(
                                  signed: true, decimal: false)
                              : TextInputType.number,
                          maxLength: 10,
                          onChanged: (value) {
                            if (value.isEmpty) {
                              discountAmount = 0;
                            } else {
                              discountAmount = double.parse(value);
                            }
                            setState(() {});
                          },
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          showDiscription = !showDiscription;
                        });
                      },
                      child: Padding(
                        padding:
                            EdgeInsets.only(right: dW * 0.05, top: dW * 0.08),
                        child: AssetSvgIcon(
                          iconName: 'i',
                          height: dW * 0.07,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                if (showDiscription)
                  Padding(
                    padding: EdgeInsets.only(
                        left: dW * 0.05, right: dW * 0.05, top: dW * 0.03),
                    child: TextWidget(
                        title:
                            'For bulk bookings, discounts are evenly spread across all slots. For example, if one slot costs ₹1000 and a customer books 5 slots for ₹5000, a ₹500 discount will be divided equally. Each slot will receive a ₹100 discount, making the price per slot ₹900.'),
                  ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.05),
          CustomContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  title: 'Payment:',
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
                SizedBox(height: dW * 0.04),
                buildRow(
                  dW,
                  context,
                  tS,
                  'SubTotal',
                  widget.invoiceType == '' || widget.invoiceType != 'Inclusive'
                      ? '${widget.basicAmount.toStringAsFixed(2)}'
                      : '${(widget.basicAmount - widget.getTotalGST()).toStringAsFixed(2)}',
                  roboto: true,
                ),
                if (widget.invoiceType != '') ...[
                  SizedBox(height: dW * 0.02),
                  buildRow(
                    dW,
                    context,
                    tS,
                    'GST(18%)',
                    '${widget.getTotalGST().toStringAsFixed(2)}',
                    roboto: true,
                    addorSubtract: true,
                    operationIcon: '+',
                  ),
                ],
                Divider(color: Colors.black45, thickness: 0.2),
                SizedBox(height: dW * 0.01),
                buildRow(
                  dW,
                  context,
                  tS,
                  'Total',
                  '${(widget.totalAmount).toStringAsFixed(2)}',
                  roboto: true,
                ),
                Divider(
                  color: Colors.black45,
                  thickness: 0.2,
                ),
                SizedBox(height: dW * 0.01),
                buildRow(
                  dW,
                  context,
                  tS,
                  'Discount',
                  widget.discountController.text.trim().length == 0
                      ? '0'
                      : double.parse(widget.discountController.text.trim()) >
                              widget.totalAmount
                          ? '0'
                          : widget.discountController.text,
                  roboto: true,
                  addorSubtract: true,
                ),
                SizedBox(height: dW * 0.017),
                buildRow(
                  dW,
                  context,
                  tS,
                  'Paid',
                  widget.currentIndex == 2
                      ? widget.amountController.text.isEmpty ||
                              (double.parse(
                                      widget.amountController.text.trim()) >
                                  (widget.totalAmount - discountAmount))
                          ? '0.0'
                          : '${double.parse(widget.amountController.text)}'
                      : getAmountPaid().toString(),
                  roboto: true,
                  addorSubtract: true,
                ),
                Divider(color: Colors.black45, thickness: 0.2),
                buildRow(
                  dW,
                  context,
                  tS,
                  'Balance payment',
                  widget.currentIndex == 1
                      ? '0'
                      : widget.currentIndex == 2
                          ? widget.amountController.text.isEmpty
                              ? '${(widget.totalAmount - discountAmount).toStringAsFixed(2)}'
                              : double.parse(widget.amountController.text) >
                                      (widget.totalAmount - discountAmount)
                                  ? '0'
                                  : '${(widget.totalAmount - (double.parse(widget.amountController.text)) - discountAmount).toStringAsFixed(2)}'
                          : widget.currentIndex == 3
                              ? discountAmount > widget.totalAmount
                                  ? '0'
                                  : '${(widget.totalAmount - discountAmount).toStringAsFixed(2)}'
                              : '0',
                  roboto: true,
                ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.15),
        ],
      ),
    );
  }
}
