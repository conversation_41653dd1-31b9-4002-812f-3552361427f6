import 'package:flutter/material.dart';

import '../../fontSizes.dart';

class SelectDurationBottomSheet extends StatefulWidget {
  final String selectedDuration;
  bool addDays;
  SelectDurationBottomSheet({
    required this.selectedDuration,
    this.addDays = false,
  });

  @override
  SelectDurationBottomSheetState createState() =>
      SelectDurationBottomSheetState();
}

class SelectDurationBottomSheetState extends State<SelectDurationBottomSheet> {
  List<String> durations = ['Month', 'Year'];
  @override
  void initState() {
    super.initState();
    if (widget.addDays) {
      durations = ['Days', 'Month', 'Year'];
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: widget.addDays ? width * 0.65 : width * 0.5,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: width * 0.05),
          Container(
            margin: EdgeInsets.symmetric(horizontal: width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Select Duration',
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                        fontSize: textScaleFactor * 16,
                        color: Colors.black,
                      ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: CircleAvatar(
                    radius: 13,
                    backgroundColor: Colors.grey.shade400,
                    child: Icon(
                      Icons.clear,
                      size: 15,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: width * 0.05),
                  ...durations.map(
                    (duration) => GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop(duration);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(
                          bottom: width * 0.03,
                        ),
                        width: width,
                        padding: EdgeInsets.symmetric(
                          vertical: width * 0.035,
                          horizontal: width * 0.04,
                        ),
                        decoration: BoxDecoration(
                          color: duration == widget.selectedDuration
                              ? Theme.of(context).primaryColor.withOpacity(0.25)
                              : Colors.transparent,
                        ),
                        child: Text(
                          duration.toString(),
                          style:
                              Theme.of(context).textTheme.displaySmall!.copyWith(
                                    fontSize: textScaleFactor * displayMedium,
                                    color: Colors.black,
                                  ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
