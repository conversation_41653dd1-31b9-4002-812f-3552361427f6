import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../fontSizes.dart';

class DateBottomSheet extends StatelessWidget {
  final String title;
  final List dates;
  const DateBottomSheet({
    required this.title,
    required this.dates,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: width * 1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: width * 0.05),
          Container(
            margin: EdgeInsets.symmetric(horizontal: width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  title,
                  style: Theme.of(context).textTheme.displaySmall!.copyWith(
                        fontSize: textScaleFactor * 16,
                        color: Colors.black,
                      ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: CircleAvatar(
                    radius: 13,
                    backgroundColor: Colors.grey.shade400,
                    child: Icon(
                      Icons.clear,
                      size: 15,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: width * 0.03),
              child: Column(
                children: [
                  SizedBox(height: width * 0.05),
                  Wrap(
                    children: [
                      ...dates.map(
                        (date) => Container(
                          margin: EdgeInsets.only(
                            right: width * 0.02,
                            bottom: width * 0.03,
                          ),
                          constraints: BoxConstraints(minWidth: width * 0.13),
                          padding: EdgeInsets.symmetric(
                            vertical: width * 0.032,
                            horizontal: width * 0.04,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(100),
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          child: FittedBox(
                            child: Text(
                              DateFormat('dd MMM yyyy')
                                  .format(DateTime.parse(date)),
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(
                                    fontSize: textScaleFactor * 13,
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
