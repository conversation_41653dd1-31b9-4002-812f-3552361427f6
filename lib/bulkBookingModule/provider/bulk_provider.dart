import 'dart:convert';
import 'dart:io';

import '../../bulkBookingModule/model/bulk_model.dart';
import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../http_helper.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';

class BulkProvider with ChangeNotifier {
  List<BulkBookingModel> _bulkBookings = [];

  List<BulkBookingModel> get bulkBookings {
    return [..._bulkBookings];
  }

  int _limit = 10;
  int _skip = 0;

  resetBulkBooking() {
    _bulkBookings = [];
    _limit = 10;
    _skip = 0;
  }

  fetchBulkBookingByBusinessId({
    required String accessToken,
    required String business,
    required List venueIds,
    bool refresh = false,
  }) async {
    final url =
        '${webApi['domain']}${endPoint['fetchBulkBookingByBusinessId']}';

    if (_skip == 0 || refresh) _bulkBookings = [];

    var str = json.encode({
      "business": business,
      "venueIds": venueIds,
      'limit': _limit,
      'skip': _skip,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<BulkBookingModel> loadedBooking = [];

        responseData['result'].forEach((booking) {
          loadedBooking.add(
            BulkBookingModel(
              id: booking['_id'],
              venue: Venues(
                  id: booking['turfId']['_id'],
                  name: booking['turfId']['name']),
              otp: booking['otp'] == null ? 0 : booking['otp'].toInt(),
              userName:
                  '${booking['user']['firstName'] ?? ''} ${booking['user']['lastName'] ?? ''}',
              userAvatar: booking['user']['avatar'] ?? '',
              userPhone: booking['user']['phone'] ?? '',
              totalAmount: booking['totalAmount'].toDouble(),
              discountedAmount: booking['discountedAmount'] == null
                  ? 0
                  : booking['discountedAmount'].toDouble(),
              amountPaid: booking['amountPaid'].toDouble(),
              convenienceFee: booking['convenienceFee'] == null
                  ? 0
                  : booking['convenienceFee'],
              gstOnConvenienceFee: booking['gstOnConvenienceFee'] == null
                  ? 0
                  : booking['gstOnConvenienceFee'],
              basicAmount:
                  booking['basicAmount'] == null ? 0 : booking['basicAmount'],
              option: booking['option'] ?? '',
              sizeOrSport: booking['sizeOrSport'] ?? '',
              isNet: booking['isNet'] ?? false,
              label: booking['label'] ?? '',
              sport: SportType(
                id: booking['sport']['_id'],
                sport: booking['sport']['sport'],
                image: booking['sport']['image'],
              ),
              sportCategory: SportCategory(
                id: booking['sportCategory']['_id'],
                categoryName: booking['sportCategory']['categoryName'],
              ),
              startDate: DateTime.parse(booking['startDate']).toLocal(),
              endDate: DateTime.parse(booking['endDate']).toLocal(),
              days: booking['days'],
              startTime: booking['startTime'].toDouble(),
              endTime: booking['endTime'].toDouble(),
              notAvailableDates: booking['notAvailableDates'] ?? [],
              availableDates: booking['availableDates'] ?? [],
              tax: booking['tax'] ?? 0,
              invoiceType: booking['invoiceType'] ?? '',
              totalBookingCount: booking['totalBookingCount'].toInt(),
              duration: Duration(
                type: booking['duration']['type'],
                count: booking['duration']['count'].toInt(),
              ),
            ),
          );
        });

        if (_skip == 0) {
          _bulkBookings = List.from(loadedBooking);
        } else {
          _bulkBookings.addAll(loadedBooking);
        }
        if (loadedBooking.isNotEmpty) {
          _skip += loadedBooking.length;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  checkBulkBookingAvailability({
    required String accessToken,
    required String turfId,
    required String sizeOrSport,
    required String startDate,
    required String startTime,
    required String endTime,
    required Map duration,
    required List days,
    required bool isNet,
  }) async {
    final url =
        '${webApi['domain']}${endPoint['checkBulkBookingAvailability']}';

    var str = json.encode({
      "turfId": turfId,
      "sizeOrSport": sizeOrSport,
      "startDate": startDate,
      "startTime": startTime,
      "endTime": endTime,
      "duration": duration,
      "days": days,
      "isNet": isNet,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        return {
          "notAvailableDates": responseData['notAvailableDates'],
          "totalBookingCount": responseData['totalBookingCount'],
          "availableDates": responseData['availableDates'],
          "timeList": responseData['timeList'],
          "endDate": responseData['endDate'],
        };
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  createBulkBooking({
    required String accessToken,
    required String turfId,
    required String business,
    required double totalAmount,
    required double discountedAmount,
    required double amountPaid,
    required double totalAmountBulk,
    required double amountPaidBulk,
    required String option,
    required String sizeOrSport,
    required num discountAmountBooking,
    required bool isNet,
    required String paymentStatus,
    required String label,
    required String sport,
    required String sportCategory,
    required String startDate,
    required String endDate,
    required List days,
    required double startTime,
    required double endTime,
    required employeeId,
    required employeeMappingId,
    required String bookedBy,
    required List notAvailableDates,
    required List removedDates,
    required List availableDates,
    required int totalBookingCount,
    required Map duration,
    required List timeList,
    required turfQuantity,
    required bool isNine,
    required String phone,
    required String firstName,
    required String lastName,
    required num convenienceFee,
    required num basicAmount,
    required num gstOnConvenienceFee,
    required num perBookingConvenienceFee,
    required num perBookingGstOnConvenienceFee,
    required num perBookingBasicAmount,
    required String invoiceType,
    required String customerGSTNo,
    required num tax,
    required num taxPerBooking,
  }) async {
    final url = '${webApi['domain']}${endPoint['createBulkBooking']}';

    Map body = {
      "turfId": turfId,
      "business": business,
      "totalAmount": totalAmount,
      "discountedAmount": discountedAmount,
      "amountPaid": amountPaid,
      "totalAmountBulk": totalAmountBulk,
      "amountPaidBulk": amountPaidBulk,
      "discountAmountBooking": discountAmountBooking,
      "option": option,
      "sizeOrSport": sizeOrSport,
      "isNet": isNet,
      "label": label,
      "sport": sport,
      "sportCategory": sportCategory,
      "startDate": startDate,
      "endDate": endDate,
      "days": days,
      "startTime": startTime,
      "endTime": endTime,
      "employeeId": employeeId,
      "employeeMappingId": employeeMappingId,
      "bookedBy": bookedBy,
      "notAvailableDates": notAvailableDates,
      "availableDates": availableDates,
      "removedDates": removedDates,
      "totalBookingCount": totalBookingCount,
      "duration": duration,
      "timeList": timeList,
      "turfQuantity": turfQuantity,
      "isNine": isNine,
      "phone": phone,
      "paymentStatus": paymentStatus,
      "firstName": firstName,
      "lastName": lastName,
      "convenienceFee": convenienceFee,
      "basicAmount": basicAmount,
      "gstOnConvenienceFee": gstOnConvenienceFee,
      "perBookingConvenienceFee": perBookingConvenienceFee,
      "perBookingGstOnConvenienceFee": perBookingGstOnConvenienceFee,
      "perBookingBasicAmount": perBookingBasicAmount,
      'os': Platform.operatingSystem,
      'tax': tax,
      'taxPerBooking': taxPerBooking,
    };

    if (invoiceType != '') {
      body['invoiceType'] = invoiceType;
    }
    if (customerGSTNo != '') {
      body['customerGSTNo'] = customerGSTNo;
    }

    var str = json.encode(body);

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        var booking = responseData['result'];
        _bulkBookings.insert(
          0,
          BulkBookingModel(
            id: booking['_id'],
            venue: Venues(
              id: booking['turfId']['_id'],
              name: booking['turfId']['name'],
            ),
            userName:
                '${booking['user']['firstName'] ?? ''} ${booking['user']['lastName'] ?? ''}',
            userPhone: booking['user']['phone'] ?? '',
            userAvatar: booking['user']['avatar'] ?? '',
            totalAmount: booking['totalAmount'].toDouble(),
            discountedAmount: booking['discountedAmount'].toDouble(),
            amountPaid: booking['amountPaid'].toDouble(),
            convenienceFee: convenienceFee,
            gstOnConvenienceFee: gstOnConvenienceFee,
            basicAmount: basicAmount,
            option: booking['option'] ?? '',
            sizeOrSport: booking['sizeOrSport'] ?? '',
            isNet: booking['isNet'] ?? false,
            label: booking['label'] ?? '',
            sport: SportType(
              id: booking['sport']['_id'],
              sport: booking['sport']['sport'],
              image: booking['sport']['image'],
            ),
            otp: booking['otp'] == null ? 0 : booking['otp'].toInt(),
            sportCategory: SportCategory(
              id: booking['sportCategory']['_id'],
              categoryName: booking['sportCategory']['categoryName'],
            ),
            startDate: DateTime.parse(booking['startDate']).toLocal(),
            endDate: DateTime.parse(booking['endDate']).toLocal(),
            days: booking['days'],
            startTime: booking['startTime'].toDouble(),
            endTime: booking['endTime'].toDouble(),
            notAvailableDates: booking['notAvailableDates'] ?? [],
            availableDates: booking['availableDates'] ?? [],
            totalBookingCount: booking['totalBookingCount'].toInt(),
            tax: booking['tax'] ?? 0,
            invoiceType: booking['invoiceType'] ?? '',
            duration: Duration(
              type: booking['duration']['type'],
              count: booking['duration']['count'].toInt(),
            ),
          ),
        );
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  Future<BulkBookingModel?> fetchBulkBookingById({
    required String accessToken,
    required String bulkBookingId,
  }) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchBulkBookingById']}?bulkBookingId=$bulkBookingId';

      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        return BulkBookingModel.jsonToBulkBooking(response['result']);
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }
}
