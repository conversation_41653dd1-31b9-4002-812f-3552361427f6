import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/homeModule/models/bookingModel.dart';
import 'package:bys_business/homeModule/providers/homeProvider.dart';
import 'package:bys_business/homeModule/widgets/bookingWidget.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../common_function.dart';
import '../../employeeModule/screens/booking_screen.dart';
import '../../employeeModule/screens/employee_business_screen.dart';
import '../../fontSizes.dart';
import '../../moreModule.dart/provider/moreProvider.dart';
import '../../navigators.dart';

class ViewBulkBookingScreen extends StatefulWidget {
  final String bulkId;
  const ViewBulkBookingScreen({
    Key? key,
    required this.bulkId,
  }) : super(key: key);

  @override
  _ViewBulkBookingScreenState createState() => _ViewBulkBookingScreenState();
}

class _ViewBulkBookingScreenState extends State<ViewBulkBookingScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;

  List<BookingModel> listOfBookings = [];

  int selectedBooking = 0;

  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  fetchBulkBookingById({bool refresh = false}) async {
    try {
      if (!refresh) setState(() => isLoading = true);

      await Provider.of<HomeProvider>(context, listen: false)
          .fetchBookingByBulkIdNew(
        accessToken: user.accessToken,
        business: user.businessId,
        bulkId: widget.bulkId,
        refresh: true,
      );
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getBooking() async {}

  lazyLoad() async {
    setState(() {
      lazyLoading = true;
    });
    await Provider.of<HomeProvider>(context, listen: false)
        .fetchBookingByBulkIdNew(
      accessToken: user.accessToken,
      business: user.businessId,
      bulkId: widget.bulkId,
    );
    setState(() {
      lazyLoading = false;
    });
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        lazyLoad();
      }
    }
    return false;
  }

  cancelConfirmation() {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title: 'Are you sure you want to cancel selected bookings?',
            noText: 'Yes, Cancel',
            yesText: 'No',
            noFunction: () {
              pop();
              cancelBulkBooking();
            },
            yesFunction: () => pop(),
          )),
    );
  }

  refreshBooking() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);
      await Provider.of<MoreProvider>(context, listen: false)
          .fetchBusinessBookingV2(
        accessToken: user.accessToken,
        selectedTab: '',
        businessId: user.businessId,
        role: user.role,
        refresh: true,
      );
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  List bookings = [];

  cancelBulkBooking() async {
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .cancelBulkBooking(
      body: {
        'bookingIds': bookings.map((e) => e.id).toList(),
        'turfId': bookings.map((e) => e.turfId).toList(),
      },
      accessToken: user.accessToken,
    );

    if (response['success']) {
      if (user.role == 'Employee') {
        // await pushAndRemoveUntil(EmployeeBusinessScreen());
        push(BookingScreen(user: user));
      } else {
        bookings = [];
        selectedBooking = 0;
        pop();
        refreshBooking();
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    Provider.of<HomeProvider>(context, listen: false).resetBulkBooking();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchBulkBookingById();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    user = Provider.of<Auth>(context).user;
    listOfBookings = Provider.of<HomeProvider>(context).bulkBooking;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      width: dW,
      height: dH,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
            // SizedBox(height: dW * 0.05),
            NewAppBar(
              dW: dW,
              title: 'Bookings',
              suffixWidget: selectedBooking > 0
                  ? GestureDetector(
                      onTap: cancelConfirmation,
                      child: Row(
                        children: [
                          // SizedBox(width: dW * 0.02),
                          // Padding(
                          //   padding: EdgeInsets.only(top: dW * 0.02),
                          //   child: Text(
                          //     'Cancel Bookings : ${selectedBooking.toString()}',
                          //     style: TextStyle(
                          //       fontWeight: FontWeight.w500,
                          //       fontSize: 14,
                          //       color: Colors.transparent,
                          //       decoration: TextDecoration.underline,
                          //       decorationColor: redColor,
                          //       decorationStyle: TextDecorationStyle.solid,
                          //       decorationThickness: 2,
                          //       shadows: [
                          //         Shadow(
                          //           color: redColor,
                          //           offset: Offset(0, -5),
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                          Container(
                            padding: EdgeInsets.all(dW * 0.02),
                            decoration: BoxDecoration(
                              color: Color(0XFFD84848),
                              borderRadius: BorderRadius.circular(100),
                            ),
                            child: Row(
                              children: [
                                AssetSvgIcon(
                                  iconName: 'new_cancel_booking',
                                  height: dW * 0.06,
                                  color: Color(0XFFFFFFFF),
                                ),
                                SizedBox(width: dW * 0.02),
                                Text(
                                  'Cancel (${selectedBooking.toString()})',
                                  style: TextStyle(
                                    color: Color(0XFFFFFFFF),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                  : SizedBox.shrink(),
            ),
            SizedBox(height: dW * 0.03),
            Expanded(
              child: isLoading
                  ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                  : NotificationListener<ScrollNotification>(
                      onNotification: _handleScrollNotification,
                      child: RefreshIndicator(
                        onRefresh: () => fetchBulkBookingById(refresh: false),
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          physics: BouncingScrollPhysics(
                              parent: AlwaysScrollableScrollPhysics()),
                          child: listOfBookings.length == 0
                              ? Container(
                                  height: dH * 0.7,
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Bookings not found!',
                                    style: Theme.of(context)
                                        .textTheme
                                        .displayLarge!
                                        .copyWith(
                                          fontSize: tS * displayLarge,
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(height: dW * 0.02),
                                    ...listOfBookings.map(
                                      (booking) => BookingWidget(
                                        onSelectionChanged:
                                            (int count, bool isSelected) {
                                          setState(() {
                                            selectedBooking += count;
                                            if (isSelected) {
                                              bookings.add(booking);
                                            } else {
                                              bookings.remove(booking);
                                            }
                                          });
                                        },
                                        deviceWidth: dW,
                                        textScaleFactor: tS,
                                        booking: booking,
                                        user: user,
                                      ),
                                    ),
                                    SizedBox(height: dW * 0.04),
                                    SizedBox(height: dW * 0.04),
                                    if (lazyLoading) lazyLoader(dW),
                                    SizedBox(height: dW * 0.12),
                                  ],
                                ),
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
