import 'dart:io';

import 'package:bys_business/homeModule/models/bookingModel.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../bulkBookingModule/model/bulk_model.dart';
import '../../bulkBookingModule/provider/bulk_provider.dart';
import '../../bulkBookingModule/widgets/bulk_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../common_function.dart';
import '../../homeModule/providers/homeProvider.dart';
import '../../homeModule/widgets/select_venue_sheet.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class BulkBookingScreen extends StatefulWidget {
  final BookingModel? booking;

  const BulkBookingScreen({Key? key, this.booking}) : super(key: key);

  @override
  _BulkBookingScreenState createState() => _BulkBookingScreenState();
}

class _BulkBookingScreenState extends State<BulkBookingScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;

  List<BulkBookingModel> listOfBookings = [];

  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  fetchBulkBooking() async {
    try {
      setState(() => isLoading = true);
      await getBooking(true);
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  getBooking(bool refresh) async {
    List<String> venueIds = [];

    if (user.business != null) {
      user.business!.turfs.forEach((turf) => venueIds.add(turf.id));
    }
    await Provider.of<BulkProvider>(context, listen: false)
        .fetchBulkBookingByBusinessId(
      accessToken: user.accessToken,
      business: user.businessId,
      venueIds: venueIds,
    );
  }

  lazyLoad() async {
    setState(() {
      lazyLoading = true;
    });
    await getBooking(false);
    setState(() {
      lazyLoading = false;
    });
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        lazyLoad();
      }
    }
    return false;
  }

  openVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SelectVenueSheet(user: user, bookingType: 'Bulk Booking'),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    Provider.of<BulkProvider>(context, listen: false).resetBulkBooking();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchBulkBooking();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;
    listOfBookings = Provider.of<BulkProvider>(context).bulkBookings;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      floatingActionButton: isLoading
          ? null
          : FloatingActionButton(
              child: Icon(Icons.add),
              onPressed: openVenueBottomSheet,
              backgroundColor: getThemeColor(),
            ),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
         padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
            // SizedBox(height: dW * 0.05),
            NewAppBar(dW: dW, title: 'Bulk Bookings'),
            SizedBox(height: dW * 0.03),
            Expanded(
              child: isLoading
                  ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                  : listOfBookings.isEmpty
                      ? EmptyListWidget(
                          text: 'Bulk Booking not found!', topPadding: 0.8)
                      : NotificationListener<ScrollNotification>(
                          onNotification: _handleScrollNotification,
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                            physics: BouncingScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: dW * 0.02),
                                ...listOfBookings.map(
                                  (booking) => BulkWidget(
                                      dW: dW, tS: tS, booking: booking),
                                ),
                                SizedBox(height: dW * 0.04),
                                if (lazyLoading) lazyLoader(dW),
                                SizedBox(height: dW * 0.12),
                              ],
                            ),
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
