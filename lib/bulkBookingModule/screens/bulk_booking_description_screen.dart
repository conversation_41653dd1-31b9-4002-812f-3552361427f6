// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:bys_business/bulkBookingModule/provider/bulk_provider.dart';
import 'package:bys_business/bulkBookingModule/screens/view_bulk_booking_screen.dart';
import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/navigators.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../bulkBookingModule/model/bulk_model.dart';
import '../../bulkBookingModule/widgets/date_bottomsheet.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../homeModule/screens/bookingDescriptionScreen.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../new_colors.dart';

class BulkBookingDescriptionScreen extends StatefulWidget {
  BulkBookingModel? booking;
  final String bulkBookingId;

  BulkBookingDescriptionScreen({
    Key? key,
    this.booking,
    this.bulkBookingId = '',
  }) : super(key: key);

  @override
  State<BulkBookingDescriptionScreen> createState() =>
      _BulkBookingDescriptionScreenState();
}

class _BulkBookingDescriptionScreenState
    extends State<BulkBookingDescriptionScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool loadConvenienceCharges = false;
  bool isLoading = false;

  fetchData() async {
    if (widget.booking == null) {
      try {
        setState(() => isLoading = true);

        final result = await Provider.of<BulkProvider>(context, listen: false)
            .fetchBulkBookingById(
          accessToken:
              Provider.of<Auth>(context, listen: false).user.accessToken,
          bulkBookingId: widget.bulkBookingId,
        );

        if (result != null) {
          widget.booking = result;
        } else {
          pop();
          showSnackbar('Something went wrong');
        }
      } catch (e) {
        print(e);
      } finally {
        if (mounted) {
          setState(() => isLoading = false);
        }
      }
    }
  }

  String getPaymentStatus() {
    if (widget.booking!.amountPaid == 0) {
      return 'Unpaid';
    } else if (widget.booking!.amountPaid <
        widget.booking!.totalAmount - widget.booking!.discountedAmount) {
      return "Partially Paid";
    } else {
      return "Paid";
    }
  }

  Widget buildColumnWidget({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: title,
          fontWeight: FontWeight.w500,
          fontSize: 13,
          color: getLightGreyColor1(context),
        ),
        SizedBox(height: dW * .015),
        TextWidget(
          title: value,
          fontWeight: FontWeight.w600,
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
            // SizedBox(height: dW * 0.05),
            NewAppBar(dW: dW, title: 'Booking Details'),
            SizedBox(height: dW * 0.03),
            Expanded(
              child: isLoading || widget.booking == null
                  ? CircularLoader(android: dW * 0.002, iOS: dW * 0.035)
                  : SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      physics: BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: dW * 0.02),
                          CustomContainer(
                            margin: EdgeInsets.only(bottom: dW * 0.05),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomContainer(
                                  hPadding: 0.03,
                                  vPadding: 0.027,
                                  boxShadow: [],
                                  bgColor: Color(0xffE3FEDB),
                                  borderColor: Color(0xffE3FEDB),
                                  radius: 8,
                                  margin: EdgeInsets.only(bottom: dW * 0.035),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          TextWidget(
                                              title: 'Player name:',
                                              fontSize: 12),
                                          SizedBox(height: dW * 0.01),
                                          ConstrainedBox(
                                            constraints: BoxConstraints(
                                                maxWidth: dW * 0.6),
                                            child: TextWidget(
                                              title: widget.booking!.userName,
                                              fontWeight: FontWeight.w600,
                                              color: getThemeColor(),
                                            ),
                                          ),
                                          SizedBox(height: dW * 0.01),
                                        ],
                                      ),
                                      GestureDetector(
                                        onTap: () => customLaunch(
                                            'tel:${widget.booking!.userPhone}'),
                                        child: Container(
                                          padding: EdgeInsets.all(dW * 0.017),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const AssetSvgIcon(
                                              iconName: 'phone2'),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                buildColumnWidget(
                                    title: 'Venue Name',
                                    value: widget.booking!.venue.name),
                                SizedBox(height: dW * 0.035),
                                TextWidget(
                                  title: 'Date & Time',
                                  fontWeight: FontWeight.w500,
                                  fontSize: 13,
                                  color: getLightGreyColor1(context),
                                ),
                                SizedBox(height: dW * .03),
                                Row(
                                  children: [
                                    AssetSvgIcon(
                                        iconName: 'calendar1', height: dW * .05),
                                    SizedBox(width: dW * .02),
                                    TextWidget(
                                      title:
                                          '${DateFormat('dd MMM yyyy').format(widget.booking!.startDate)} - ${DateFormat('dd MMM yyyy').format(widget.booking!.endDate)}',
                                      fontWeight: FontWeight.w600,
                                      color: getGreyColor2(context),
                                    ),
                                  ],
                                ),
                                SizedBox(height: dW * .025),
                                Row(
                                  children: [
                                    AssetSvgIcon(
                                        iconName: 'clock2', height: dW * .05),
                                    SizedBox(width: dW * .02),
                                    TextWidget(
                                      title:
                                          '${get12HrFormat(widget.booking!.startTime).toStringAsFixed(2)} ${getTimePeriod(widget.booking!.startTime)} - ${get12HrFormat(widget.booking!.endTime).toStringAsFixed(2)} ${getTimePeriod(widget.booking!.endTime)}',
                                      fontWeight: FontWeight.w600,
                                      color: getGreyColor2(context),
                                    ),
                                  ],
                                ),
                                SizedBox(height: dW * 0.035),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextWidget(
                                      title: widget.booking!.sportCategory
                                                  .categoryName ==
                                              'Outdoor'
                                          ? "Sports & Turf Size"
                                          : "Sport",
                                      fontWeight: FontWeight.w500,
                                      color: getLightGreyColor1(context),
                                    ),
                                    SizedBox(height: dW * .02),
                                    IntrinsicHeight(
                                      child: Row(
                                        children: [
                                          Image.network(
                                              widget.booking!.sport.image,
                                              height: 18),
                                          SizedBox(width: dW * .02),
                                          TextWidget(
                                            title: widget.booking!.sport.sport,
                                            color: getGreyColor2(context),
                                            fontWeight: FontWeight.w500,
                                          ),
                                          if (widget.booking!.sportCategory
                                                  .categoryName ==
                                              'Outdoor') ...[
                                            SizedBox(width: dW * .02),
                                            VerticalDivider(
                                                width: 1,
                                                color: Color(0xff505050)),
                                            SizedBox(width: dW * .02),
                                            TextWidget(
                                              title:
                                                  '${widget.booking!.sizeOrSport.replaceAll(':', 'x')}${widget.booking!.label != '' ? ' (${widget.booking!.label})' : ''}',
                                              color: getGreyColor2(context),
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ]
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(height: dW * 0.035),
                                Row(
                                  children: [
                                    buildColumnWidget(
                                      title: 'Duration',
                                      value:
                                          '${widget.booking!.duration.count} ${widget.booking!.duration.type}',
                                    ),
                                    SizedBox(width: dW * 0.12),
                                    buildColumnWidget(
                                      title: 'Total Booking',
                                      value: widget.booking!.totalBookingCount
                                          .toString(),
                                    ),
                                  ],
                                ),
                                SizedBox(height: dW * 0.035),
                                TextWidget(
                                  title: 'Selected Days',
                                  fontWeight: FontWeight.w500,
                                  fontSize: 13,
                                  color: getLightGreyColor1(context),
                                ),
                                SizedBox(height: dW * 0.02),
                                Wrap(
                                  children: [
                                    ...widget.booking!.days.map(
                                      (day) => Container(
                                        margin: EdgeInsets.only(
                                            right: dW * 0.03, bottom: dW * 0.03),
                                        constraints:
                                            BoxConstraints(minWidth: dW * 0.13),
                                        padding: EdgeInsets.symmetric(
                                          vertical: dW * 0.02,
                                          horizontal: dW * 0.035,
                                        ),
                                        decoration: BoxDecoration(
                                          color: getThemeColor(),
                                          borderRadius:
                                              BorderRadius.circular(100),
                                          border:
                                              Border.all(color: getThemeColor()),
                                        ),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: TextWidget(
                                            title: day,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: dW * 0.035),
                                CustomContainer(
                                  boxShadow: [],
                                  borderColor: getThemeColor(),
                                  vPadding: 0.005,
                                  hPadding: 0.035,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      TextWidget(
                                        title: 'Booking Dates',
                                        fontWeight: FontWeight.w500,
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          dateBottomSheet(
                                            'Booking Dates',
                                            widget.booking!.availableDates,
                                            context,
                                          );
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: dW * 0.012,
                                              vertical: dW * 0.02),
                                          color: Colors.transparent,
                                          child: TextWidget(
                                            title: 'View',
                                            color: getThemeColor(),
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (widget
                                    .booking!.notAvailableDates.isNotEmpty) ...[
                                  SizedBox(height: dW * 0.05),
                                  CustomContainer(
                                    boxShadow: [],
                                    borderColor: getThemeColor(),
                                    vPadding: 0.005,
                                    hPadding: 0.035,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TextWidget(
                                          title: 'Not Available Dates',
                                          fontWeight: FontWeight.w500,
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            dateBottomSheet(
                                                'Not Available Dates',
                                                widget.booking!.notAvailableDates,
                                                context);
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: dW * 0.012,
                                                vertical: dW * 0.02),
                                            color: Colors.transparent,
                                            child: TextWidget(
                                              title: 'View',
                                              color: getThemeColor(),
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          SizedBox(height: dW * 0.01),
                          GestureDetector(
                            onTap: () => push(ViewBulkBookingScreen(
                                bulkId: widget.booking!.id)),
                            child: CustomContainer(
                              vPadding: 0.03,
                              hPadding: 0.035,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  TextWidget(
                                    title: 'View Bookings',
                                    fontWeight: FontWeight.w500,
                                  ),
                                  Icon(Icons.keyboard_arrow_right),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: dW * 0.06),
                          CustomContainer(
                            hPadding: 0,
                            vPadding: 0,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  alignment: Alignment.center,
                                  padding:
                                      EdgeInsets.symmetric(vertical: dW * .02),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(8),
                                        topRight: Radius.circular(8)),
                                    color: getPaymentStatus() == 'Paid'
                                        ? getGreenColor1(context)
                                        : getLightYellowColor1(context),
                                  ),
                                  child: TextWidget(
                                    title: getPaymentStatus(),
                                    fontSize: 16,
                                    textAlign: TextAlign.center,
                                    color: getPaymentStatus() == 'Paid'
                                        ? getThemeColor()
                                        : getYellowColor1(context),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: dW * 0.045,
                                    vertical: dW * 0.04,
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      buildHeading(
                                        dW,
                                        context,
                                        tS,
                                        'Payment',
                                      ),
                                      SizedBox(height: dW * 0.02),
                                      if (widget.booking!.invoiceType != '') ...[
                                        buildRow(
                                          dW,
                                          context,
                                          tS,
                                          'Subtotal',
                                          '${(widget.booking!.totalAmount - widget.booking!.tax).toStringAsFixed(2)}',
                                          roboto: true,
                                        ),
                                        SizedBox(height: dW * 0.02),
                                        buildRow(
                                          dW,
                                          context,
                                          tS,
                                          'GST(18%)',
                                          '${widget.booking!.tax.toStringAsFixed(2)}',
                                          roboto: true,
                                          addorSubtract: true,
                                          operationIcon: '+',
                                        ),
                                        DividerWidget(top: 5, bottom: 5),
                                        SizedBox(height: dW * 0.01),
                                      ],
                                      if (widget.booking!.convenienceFee !=
                                          0) ...[
                                        buildRow(
                                          dW,
                                          context,
                                          tS,
                                          'SubTotal',
                                          '${widget.booking!.basicAmount == 0 ? widget.booking!.totalAmount - widget.booking!.convenienceFee - widget.booking!.gstOnConvenienceFee : widget.booking!.basicAmount.toStringAsFixed(2)}',
                                          roboto: true,
                                        ),
                                        SizedBox(height: dW * 0.01),
                                        if (widget.booking!.convenienceFee != 0)
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                margin: EdgeInsets.only(
                                                    bottom: dW * 0.017),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    GestureDetector(
                                                      onTap: () {
                                                        setState(() {
                                                          loadConvenienceCharges =
                                                              !loadConvenienceCharges;
                                                        });
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Container(
                                                            constraints:
                                                                BoxConstraints(
                                                                    maxWidth:
                                                                        dW * 0.5),
                                                            child: Text(
                                                              'Convinience Fee',
                                                              style: Theme.of(
                                                                      context)
                                                                  .textTheme
                                                                  .headlineSmall!
                                                                  .copyWith(
                                                                    fontSize:
                                                                        tS * 13.5,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    color: Colors
                                                                        .black,
                                                                  ),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                              width: dW * 0.02),
                                                          loadConvenienceCharges
                                                              ? SvgPicture.asset(
                                                                  "assets/svgIcons/chevron-up.svg",
                                                                  width:
                                                                      dW * 0.04,
                                                                )
                                                              : SvgPicture.asset(
                                                                  "assets/svgIcons/down-arrow.svg",
                                                                  width:
                                                                      dW * 0.04,
                                                                ),
                                                        ],
                                                      ),
                                                    ),
                                                    RichText(
                                                      text: TextSpan(
                                                        children: [
                                                          TextSpan(
                                                            text: '\u20b9',
                                                            style: TextStyle(
                                                              fontSize: tS * 14.5,
                                                              fontWeight:
                                                                  FontWeight.bold,
                                                              color: Colors.black,
                                                            ),
                                                          ),
                                                          TextSpan(
                                                            text:
                                                                '${widget.booking!.convenienceFee + widget.booking!.gstOnConvenienceFee}',
                                                            style:
                                                                Theme.of(context)
                                                                    .textTheme
                                                                    .displayLarge!
                                                                    .copyWith(
                                                                      fontSize:
                                                                          tS *
                                                                              14.5,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      color: Colors
                                                                          .black,
                                                                    ),
                                                          ),
                                                        ],
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: dW * 0.01),
                                              if (loadConvenienceCharges)
                                                buildRow(
                                                  dW,
                                                  context,
                                                  tS * 0.85,
                                                  '       Base Amount',
                                                  '${widget.booking!.convenienceFee.toStringAsFixed(2)}',
                                                  roboto: true,
                                                  convenienceCharges: true,
                                                ),
                                              if (loadConvenienceCharges)
                                                SizedBox(height: dW * 0.012),
                                              if (loadConvenienceCharges)
                                                buildRow(
                                                  dW,
                                                  context,
                                                  tS * 0.85,
                                                  '       GST (18%)',
                                                  '${(widget.booking!.gstOnConvenienceFee).toStringAsFixed(2)}',
                                                  roboto: true,
                                                  convenienceCharges: true,
                                                ),
                                              if (loadConvenienceCharges)
                                                SizedBox(height: dW * 0.01),
                                              Divider(
                                                color: Colors.black45,
                                                thickness: 0.2,
                                              ),
                                              SizedBox(height: dW * 0.01),
                                            ],
                                          ),
                                      ],
                                      buildRow(
                                        dW,
                                        context,
                                        tS,
                                        'Total',
                                        '${widget.booking!.totalAmount.toStringAsFixed(2)}',
                                        roboto: true,
                                      ),
                                      Divider(
                                        color: Colors.black45,
                                        thickness: 0.2,
                                      ),
                                      SizedBox(height: dW * 0.017),
                                      buildRow(
                                        dW,
                                        context,
                                        tS,
                                        'Discount',
                                        '${widget.booking!.discountedAmount.toStringAsFixed(2)}',
                                        roboto: true,
                                        addorSubtract: true,
                                      ),
                                      SizedBox(height: dW * 0.017),
                                      buildRow(
                                        dW,
                                        context,
                                        tS,
                                        'Paid',
                                        '${widget.booking!.amountPaid.toStringAsFixed(2)}',
                                        roboto: true,
                                        addorSubtract: true,
                                      ),
                                      Divider(color: Colors.black45),
                                      buildRow(
                                        dW,
                                        context,
                                        tS,
                                        'Balance payment',
                                        '${((widget.booking!.totalAmount - widget.booking!.amountPaid - widget.booking!.discountedAmount).abs()).toStringAsFixed(2)}',
                                        roboto: true,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: dW * 0.15)
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

dateBottomSheet(String title, List dates, BuildContext context) {
  showModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
    ),
    context: (context),
    builder: (context) => GestureDetector(
      child: DateBottomSheet(
        title: title,
        dates: dates,
      ),
      onTap: () {},
      behavior: HitTestBehavior.opaque,
    ),
  );
}
