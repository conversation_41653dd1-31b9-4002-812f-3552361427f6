// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_container.dart';
import '../../common_function.dart';
import '../../new_colors.dart';

class BookedSlotsScreen extends StatefulWidget {
  final List bookingDates;
  final Function removeBookingDate;
  final num startTime;
  final num endTime;
  const BookedSlotsScreen({
    required this.bookingDates,
    required this.removeBookingDate,
    required this.startTime,
    required this.endTime,
  });

  @override
  State<BookedSlotsScreen> createState() => _BookedSlotsScreenState();
}

class _BookedSlotsScreenState extends State<BookedSlotsScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  TextTheme customTextTheme = const TextTheme();

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    customTextTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: CustomAppBar(title: 'Booked Slots', dW: dW),
      body: iOSCondition(dH)
          ? screenBody(context)
          : SafeArea(child: screenBody(context)),
    );
  }

  screenBody(BuildContext context) {
    return SizedBox(
      height: dH,
      width: dW,
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(height: dW * 0.05),
            Text(
              'Booked Slots',
              style: customTextTheme.titleLarge!.copyWith(
                  fontWeight: FontWeight.w500, color: getGreyColor2(context)),
            ),
            Text(
              'You can remove your slot from here',
              style: customTextTheme.titleMedium!.copyWith(
                  fontWeight: FontWeight.w500,
                  color: getLightGreyColor1(context)),
            ),
            SizedBox(height: dW * .05),
            ...widget.bookingDates
                .map(
                  (slot) => Stack(
                    children: [
                      CustomContainer(
                        boxShadow: const [],
                        borderColor: getThemeColor(),
                        margin: EdgeInsets.only(bottom: dW * .05),
                        child: Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                AssetSvgIcon(
                                    iconName: 'calendar', height: dW * .05),
                                SizedBox(width: dW * .02),
                                Text(
                                  DateFormat('dd MMMM yyyy')
                                      .format(DateTime.parse(slot)),
                                  style: customTextTheme.titleLarge!.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: getGreyColor2(context),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: dW * 0.03),
                            Row(
                              children: [
                                AssetSvgIcon(
                                  iconName: 'clock3',
                                  height: dW * .05,
                                ),
                                SizedBox(width: dW * .02),
                                Text(
                                  '${double.parse(get12HrFormat(widget.startTime.toDouble()).toString()).toStringAsFixed(2)} ${getTimePeriod(widget.startTime.toDouble())} - ${double.parse(get12HrFormat(widget.endTime.toDouble()).toString()).toStringAsFixed(2)} ${getTimePeriod(widget.endTime.toDouble())}',
                                  style: customTextTheme.titleMedium!.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color: getGreyColor2(context)),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        right: 10,
                        top: 12,
                        child: PopupMenuButton(
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                          padding: EdgeInsets.zero,
                          color: getWhiteColor(context),
                          itemBuilder: (BuildContext bc) => [
                            popupMenuItem(
                              position: 1,
                              title: 'Remove',
                              dW: dW,
                              icon: 'delete',
                              iconSize: 15,
                            ),
                          ],
                          onSelected: (value) {
                            if (value == 1) {
                              if (widget.bookingDates.length == 1) {
                                showSnackbar('You cannot remove all dates');
                              } else {
                                widget.removeBookingDate(slot);
                                setState(() {});
                              }
                            }
                          },
                          child: const Icon(Icons.more_vert, size: 21),
                        ),
                      ),
                    ],
                  ),
                )
                .toList()
          ],
        ),
      ),
    );
  }
}
