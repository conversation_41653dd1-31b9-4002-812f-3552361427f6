import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/navigators.dart';

import '../../bulkBookingModule/provider/bulk_provider.dart';
import '../../bulkBookingModule/widgets/select_count_bottomsheet.dart';
import '../../bulkBookingModule/widgets/select_duration_bottomsheet.dart';
import '../../bulkBookingModule/widgets/step3_bulk_widget.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/text_widget.dart';
import '../../employeeModule/models/business_model.dart';
import '../../employeeModule/screens/employee_business_screen.dart';
import '../../homeModule/models/bookingModel.dart';

import '../../authModule/modals/userModel.dart';
import '../../common_function.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../../homeModule/screens/homeScreen.dart';
import '../../venueModule/models/venue_model.dart';

class CreateBulkBookingScreen extends StatefulWidget {
  final UserModal user;
  final BookingModel? booking;

  final Venue selectedTurf;
  final String sizeOrSport;
  final SportType sport;
  final String time;
  final String hour;
  final double amount;
  final double taxes;
  final DateTime bookingDate;
  final List selectedTime;
  final int quantity;
  final String label;
  final String size;
  final String fullName;
  final String mobileNo;

  const CreateBulkBookingScreen({
    Key? key,
    required this.user,
    this.booking,
    required this.selectedTurf,
    required this.time,
    required this.hour,
    required this.sport,
    required this.amount,
    required this.taxes,
    required this.selectedTime,
    required this.bookingDate,
    required this.sizeOrSport,
    required this.label,
    required this.size,
    this.quantity = 0,
    required this.fullName,
    required this.mobileNo,
  }) : super(key: key);

  @override
  _CreateBulkBookingScreenState createState() =>
      _CreateBulkBookingScreenState();
}

class _CreateBulkBookingScreenState extends State<CreateBulkBookingScreen> {
  late DateTime selectedDate;

  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool turfSelected = false;
  bool isLoading = false;
  // String sportId = '';
  // String label = '';
  // Venue? selectedTurf;
  // List widget.selectedTime = [];
  // int quantity = 1;
  // double widget.amount = 0.0;
  double discountedAmount = 0.0;

  int step = 1; // change afterwards
  String selectedDuration = 'Month';
  int selectedCount = 1;
  List<String> selectedDays = [];
  late Slot selectedSlot;
  bool checkSlot = false;
  bool createBooking = false;
  var slotData;
  int currentIndex = 1;
  bool step1Completed = false;

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController amountController = TextEditingController();
  TextEditingController discountController = TextEditingController();
  TextEditingController gstController = TextEditingController();

  String invoiceType = '';

  num convenienceFee = 0;
  num gstOnConvenienceFee = 0;
  num basicAmount = 0;
  num perBookingBasicAmount = 0;
  num perBookingConvenienceFee = 0;
  num perBookingGstOnConvenienceFee = 0;

  List removedDates = [];
  List listOfDays = [];

  String durationType = '';

  selectInvoiceType(String type) {
    if (type == invoiceType) {
      invoiceType = '';
    } else {
      invoiceType = type;
    }
    setState(() {});
  }

  setCurrentIndex(index) {
    setState(() {
      currentIndex = index;
    });
    setConvenienceFee();
  }

  selectDays(day) {
    if (selectedDays.contains(day)) {
      selectedDays.removeWhere((value) => value == day);
    } else {
      selectedDays.add(day);
    }
    setState(() {
      slotData = null;
    });
  }

  incrementAndDecrementStep(bool increment) {
    if (increment) {
      setState(() {
        step += 1;
      });
    } else {
      setState(() {
        step -= 1;
        slotData = null;
      });
    }
  }

  int totalDuration(int selectedMonth, int selectedYear) {
    int duration = 0;
    if (selectedYear >= 1) {
      duration += selectedYear * 12;
    }
    if (selectedMonth >= 1) {
      duration += selectedMonth;
    }

    return duration;
  }

  getHintStyle(textScaleFactor) {
    return Theme.of(context).textTheme.headlineSmall!.copyWith(
          fontSize: textScaleFactor * 13,
          color: Color(0xff737373),
          fontWeight: FontWeight.w600,
        );
  }

  String selectedDaysString(List<String> selectedDays) {
    String selected = '';
    selectedDays.forEach((day) {
      selected += day + ", ";
    });
    if (selected.endsWith(', ')) {
      selected.replaceFirst(', ', '');
    }

    return selected;
  }

  getStyle(textScaleFactor) {
    return Theme.of(context).textTheme.headlineSmall!.copyWith(
          fontSize: textScaleFactor * displayMedium,
          color: Colors.black,
          fontWeight: FontWeight.w600,
        );
  }

  selectDurationOrCountDropDown(bool duration) {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: duration
            ? SelectDurationBottomSheet(selectedDuration: selectedDuration)
            : SelectCountBottomSheet(
                selectedDuration: selectedDuration,
                selectedCount: selectedCount,
              ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        setState(() {
          if (duration) {
            selectedDuration = value;
          } else {
            selectedCount = value;
          }
          slotData = null;
        });
      }
    });
  }

  selectDurationOrCountRow({
    required double deviceWidth,
    required double textScaleFactor,
    required String title,
    required String subTitle,
    required bool duration,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: deviceWidth * 0.02),
          child: TextWidget(
            title: title,
            letterSpacing: 0.4,
            color: Color(0xff5E5E5E),
            fontWeight: FontWeight.w500,
          ),
        ),
        GestureDetector(
          onTap: () {
            selectDurationOrCountDropDown(duration);
          },
          child: CustomContainer(
            boxShadow: [],
            vPadding: 0.03,
            hPadding: 0.03,
            borderColor: getThemeColor(),
            margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: subTitle,
                  fontSize: 16,
                  letterSpacing: 0.3,
                  fontWeight: FontWeight.w500,
                ),
                Icon(Icons.keyboard_arrow_down, color: Color(0xff5E5E5E)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  checkIsNine(List turfSizes) {
    if (turfSizes.length == 1 && turfSizes.contains('6:6')) {
      return false;
    } else if (turfSizes.length == 1 && turfSizes.contains('5:5')) {
      return false;
    } else if (turfSizes.contains('5:5') &&
        turfSizes.contains('6:6') &&
        !turfSizes.contains('9:9') &&
        !turfSizes.contains('7:7')) {
      return false;
    } else if ((turfSizes.contains('9:9') || turfSizes.contains('7:7')) &&
        !turfSizes.contains('8:8')) {
      return true;
    } else {
      return false;
    }
  }

  checkSlotAvailbility() async {
    try {
      if (selectedDays.isEmpty) {
        showSnackbar('Please Select Days');
        return;
      }
      setState(() {
        checkSlot = true;
      });
      slotData = await Provider.of<BulkProvider>(context, listen: false)
          .checkBulkBookingAvailability(
        accessToken: widget.user.accessToken,
        turfId: widget.selectedTurf.id,
        sizeOrSport: widget.sizeOrSport,
        startDate: widget.bookingDate.toString(),
        startTime: widget.selectedTime[0].toString().replaceAll(':', '.'),
        endTime: widget.selectedTime[widget.selectedTime.length - 1]
            .toString()
            .replaceAll(':', '.'),
        duration: {
          'type': selectedDuration,
          'count': selectedCount,
        },
        days: selectedDays,
        isNet: widget.selectedTurf.isNet!,
      );
      if (slotData != null) {
        print(slotData);
        if (slotData['endDate'] == null) {
          showSnackbar('Bookings not available');
        } else {
          setState(() {
            step = 2;
            setConvenienceFee();
          });
        }
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      setState(() {
        checkSlot = false;
      });
    }
  }

  setConvenienceFee() {
    convenienceFee =
        getPerBookingConvenienceFee() * slotData['totalBookingCount'];
    gstOnConvenienceFee =
        (getPerBookingConvenienceFee() * 0.18) * slotData['totalBookingCount'];
    basicAmount = (slotData['totalBookingCount'] * (widget.amount)).toDouble();
    perBookingBasicAmount = widget.amount;
    perBookingConvenienceFee = getPerBookingConvenienceFee();
    perBookingGstOnConvenienceFee = getPerBookingConvenienceFee() * 0.18;
  }

  exitFunction(textScaleFactor) async {
    if (step == 1) {
      return showDialog(
          context: context,
          builder: ((context) => CustomDialog(
                title:
                    'Are you sure you want to exit. All the entered information will be lost.',
                noText: 'Yes',
                yesText: 'No',
                noFunction: () {
                  pop(true);
                  pop(true);
                },
                yesFunction: () => pop(),
              )));
    } else {
      pop();
    }
  }

  getAmountPaid(double dsAmount) {
    if (currentIndex == 1) {
      return getTotalAmount() - dsAmount;
    } else if (currentIndex == 2) {
      return double.parse(amountController.text.trim());
    } else if (currentIndex == 3) {
      return 0.0;
    }
  }

  getPaymentStatus() {
    if (currentIndex == 1) {
      return 'SUCCESS';
    } else if (currentIndex == 2 || currentIndex == 3) {
      return 'OFFLINE';
    }
  }

  getPerBookingConvenienceFee() {
    // if (currentIndex == 1 || currentIndex == 3) {
    //   retur *
    //       widget.selectedTurf.convenienceFeeForFullPay;
    // } else {
    //   retur *
    //       widget.selectedTurf.convenienceFeeForAdvancePay;
    // }
    return 0;
  }

  removeBookingDate(String date) {
    slotData['totalBookingCount'] -= 1;
    (slotData['availableDates'] as List).remove(date);
    removedDates.add(date);
    setConvenienceFee();
    setState(() {});
  }

  createBulkBooking() async {
    try {
      if (createBooking) return;
      hideKeyBoard(context);

      setState(() {
        createBooking = true;
      });

      double dsAmount = discountController.text.isEmpty
          ? 0
          : double.parse(discountController.text);

      var totalAmountBulk =
          (slotData['totalBookingCount'] * (widget.amount)).toDouble();

      if ((currentIndex == 2) && amountController.text.trim().length == 0) {
        showSnackbar('Enter paid amount');
        return;
      }
      if (currentIndex == 2) {
        if (double.parse(amountController.text.trim()) == 0) {
          showSnackbar('Please enter amount greater than zero');
          return;
        } else if (double.parse(amountController.text.trim()) >
            (totalAmountBulk - dsAmount)) {
          showSnackbar('Please enter amount less than total amount');
          return;
        }
      }

      if (dsAmount > totalAmountBulk) {
        return showSnackbar('Discount cannot be greater than total amount');
      }

      Map turfQuanity = {};
      widget.selectedTurf.slots!.forEach((slot) {
        slot.priceAndQuantity.forEach((size) {
          if (!turfQuanity.containsKey(size.title)) {
            turfQuanity[size.title] = size.quantity;
          }
        });
      });

      bool net = widget.selectedTurf.sportCategory!.categoryName != 'Outdoor'
          ? false
          : widget.selectedTurf.isNet!;
      List turfSizes = [];

      if (net) {
        widget.selectedTurf.slots!.forEach((slot) {
          slot.priceAndQuantity.forEach((price) {
            if (!turfSizes.contains(price.title)) {
              turfSizes.add(price.title);
            }
          });
        });
      }

      String firstName = '';
      String lastName = '';

      if (widget.fullName.split(' ').length > 1) {
        firstName = widget.fullName.split(' ')[0];
        lastName = widget.fullName.split(' ')[1];
      } else {
        firstName = widget.fullName;
        lastName = '';
      }

      final result = await Provider.of<BulkProvider>(context, listen: false)
          .createBulkBooking(
        accessToken: widget.user.accessToken,
        turfId: widget.selectedTurf.id,
        business: widget.user.businessId,
        totalAmount: invoiceType == '' || invoiceType == 'Inclusive'
            ? widget.amount
            : widget.amount + (getTotalGST() / slotData['totalBookingCount']),
        taxPerBooking: (getTotalGST() / slotData['totalBookingCount']),
        discountedAmount: dsAmount,
        discountAmountBooking: discountController.text.isNotEmpty
            ? dsAmount / slotData['totalBookingCount']
            : 0.0,
        amountPaid: currentIndex == 1 || currentIndex == 2
            ? getAmountPaid(dsAmount) / slotData['totalBookingCount']
            : 0,
        amountPaidBulk: getAmountPaid(dsAmount),
        totalAmountBulk: getTotalAmount(),
        option: widget.selectedTurf.option!,
        sizeOrSport: widget.sizeOrSport,
        isNet: widget.selectedTurf.isNet!,
        label: widget.label,
        sport: widget.sport.id,
        sportCategory: widget.selectedTurf.sportCategory!.id,
        startDate: DateTime(selectedDate.year, selectedDate.month,
                selectedDate.day, 13, 0, 0)
            .toString(),
        startTime: double.parse(
            widget.selectedTime[0].toString().replaceAll(':', '.')),
        endTime: double.parse(widget
            .selectedTime[widget.selectedTime.length - 1]
            .toString()
            .replaceAll(':', '.')),
        endDate: slotData['endDate'],
        days: selectedDays,
        notAvailableDates: slotData['notAvailableDates'],
        availableDates: slotData['availableDates'],
        removedDates: removedDates,
        totalBookingCount: slotData['totalBookingCount'],
        duration: {
          'type': selectedDuration,
          'count': selectedCount,
        },
        timeList: slotData['timeList'],
        turfQuantity:
            widget.selectedTurf.sportCategory!.categoryName != 'Outdoor'
                ? widget.quantity
                : widget.selectedTurf.isNet!
                    ? turfQuanity
                    : widget.quantity,
        isNine: !widget.selectedTurf.isNet! ? false : checkIsNine(turfSizes),
        phone: widget.mobileNo,
        firstName: firstName,
        lastName: lastName,
        paymentStatus: getPaymentStatus(),
        employeeId: widget.user.business == null
            ? null
            : widget.user.business!.employee.id,
        employeeMappingId: widget.user.business == null
            ? null
            : widget.user.business!.mappingId,
        bookedBy: widget.user.role,
        convenienceFee: convenienceFee,
        gstOnConvenienceFee: gstOnConvenienceFee,
        basicAmount: basicAmount,
        perBookingBasicAmount: perBookingBasicAmount,
        perBookingConvenienceFee: perBookingConvenienceFee,
        perBookingGstOnConvenienceFee: perBookingGstOnConvenienceFee,
        invoiceType: invoiceType,
        tax: getTotalGST(),
        customerGSTNo: gstController.text.trim(),
      );
      if (result && widget.user.role == 'Employee') {
        showSnackbar('Booking will be created shortly', color: greenPrimary);

        Navigator.of(context).pop(true);
        Navigator.of(context).pop(true);
      } else if (result && widget.user.role == 'Business') {
        showSnackbar('Booking will be created shortly', color: greenPrimary);
        pushAndRemoveUntil(HomeScreen());
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      setState(() {
        createBooking = false;
      });
    }
  }

  num getTotalGST() {
    num totalGst = 0;

    double totalAmount = (slotData['totalBookingCount'] * (widget.amount)) -
        (discountController.text.isEmpty
            ? 0
            : double.parse(discountController.text));

    totalGst = (totalAmount * 18) / 100;
    return totalGst;
  }

  double getTotalAmount() {
    double totalAmount = slotData['totalBookingCount'] * (widget.amount);

    if (invoiceType == 'Exclusive') totalAmount += getTotalGST();

    return totalAmount;
  }

  textBorder() {
    return UnderlineInputBorder(
      borderSide: BorderSide(color: Colors.grey.shade500),
    );
  }

  @override
  void initState() {
    super.initState();

    // Create a set to identify duplicates
    Set<String> uniqueDaysSet = Set<String>();

    // Traverse the days in widget.args.venue.days
    for (var day in widget.selectedTurf.days!) {
      // If the day is not already in the set, add it to listOfDays
      if (!uniqueDaysSet.contains(day)) {
        listOfDays.add(day);
        uniqueDaysSet.add(day);
      }
    }

    // If listOfDays is empty, initialize it with widget.args.venue.weekends
    if (!widget.selectedTurf.weekends!
        .every((weekend) => listOfDays.contains(weekend))) {
      listOfDays.addAll(widget.selectedTurf.weekends!);
    }

    // Sort the list according to the desired order
    listOfDays.sort(_customSort);

    // Update widget.args.venue.days with the sorted list
    widget.selectedTurf.days!.clear();
    widget.selectedTurf.days!.addAll(listOfDays);
    selectedDate = widget.bookingDate;

    print(widget.selectedTurf.days!);
  }

  int _customSort(dynamic a, dynamic b) {
    // Update argument types to dynamic
    List<String> daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    return daysOfWeek.indexOf(a as String) -
        daysOfWeek.indexOf(b as String); // Cast arguments to String
  }

  // @override
  // void initState() {
  //   super.initState();
  //   listOfDays = widget.selectedTurf.days ?? [];
  //   listOfDays.addAll(widget.selectedTurf.weekends ?? []);
  //   selectedDate = widget.bookingDate;
  // }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return WillPopScope(
      onWillPop: () => step > 1
          ? incrementAndDecrementStep(false)
          : exitFunction(textScaleFactor),
      child: Scaffold(
        // appBar: androidAppBar,
        backgroundColor: Colors.white,
        body: iOSCondition(deviceHeight)
            ? screenBody()
            : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () {
        hideKeyBoard(context);
      },
      child: Container(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
        height: deviceHeight,
        width: deviceWidth,
        child: Column(
          children: [
            // SizedBox(height: deviceWidth * 0.05),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NewAppBar(
                  dW: deviceWidth,
                  title: 'Bulk Booking',
                  onTap: () => step > 1
                      ? incrementAndDecrementStep(false)
                      : exitFunction(textScaleFactor),
                ),
                Container(
                  alignment: Alignment.bottomRight,
                  margin: EdgeInsets.only(right: deviceWidth * 0.03),
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                  decoration: BoxDecoration(
                    border: Border.all(color: getThemeColor()),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: TextWidget(
                    title: '$step/2',
                    fontSize: 12,
                    color: Color(0xff0F402C),
                  ),
                ),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(
                    horizontal: step == 2 ? 0 : deviceWidth * 0.05),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: deviceWidth * 0.05),
                      if (step == 1)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            selectDurationOrCountRow(
                              deviceWidth: deviceWidth,
                              textScaleFactor: textScaleFactor,
                              title: 'Select Duration',
                              subTitle: selectedDuration,
                              duration: true,
                            ),
                            selectDurationOrCountRow(
                              deviceWidth: deviceWidth,
                              textScaleFactor: textScaleFactor,
                              title: 'Select $selectedDuration',
                              subTitle: selectedCount.toString(),
                              duration: false,
                            ),
                            TextWidget(
                              title: 'Select Days',
                              letterSpacing: 0.4,
                              color: Color(0xff5E5E5E),
                              fontWeight: FontWeight.w500,
                            ),
                            SizedBox(height: deviceWidth * 0.025),
                            Wrap(
                              children: [
                                ...listOfDays.map(
                                  (day) => GestureDetector(
                                    onTap: () => selectDays(day),
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        right: deviceWidth * 0.03,
                                        bottom: deviceWidth * 0.03,
                                      ),
                                      constraints: BoxConstraints(
                                          minWidth: deviceWidth * 0.13),
                                      padding: EdgeInsets.symmetric(
                                        vertical: deviceWidth * 0.022,
                                        horizontal: deviceWidth * 0.04,
                                      ),
                                      decoration: BoxDecoration(
                                        color: selectedDays.contains(day)
                                            ? Theme.of(context).primaryColor
                                            : Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(100),
                                        border: Border.all(
                                          color: selectedDays.contains(day)
                                              ? Theme.of(context).primaryColor
                                              : Color(0xff707070),
                                        ),
                                      ),
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: TextWidget(
                                          title: day,
                                          color: selectedDays.contains(day)
                                              ? Colors.white
                                              : Color(0xff5E5E5E),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      if (step == 2)
                        Step3BulkBooking(
                          deviceWidth: deviceWidth,
                          textScaleFactor: textScaleFactor,
                          name: widget.fullName,
                          phone: widget.mobileNo,
                          venue: widget.selectedTurf.name,
                          sizeOrSport: widget.sizeOrSport,
                          sport: widget.sport,
                          option: widget.selectedTurf.option!,
                          isNet: widget.selectedTurf.isNet!,
                          label: widget.label,
                          availableDates: slotData['availableDates'],
                          notAvailableDates: slotData['notAvailableDates'],
                          categoryName:
                              widget.selectedTurf.sportCategory!.categoryName,
                          duration: selectedDuration,
                          durationCount: selectedCount,
                          endDate: DateTime.parse(slotData['endDate']),
                          startDate: selectedDate,
                          selectedDays: selectedDays,
                          totalBooking:
                              slotData['totalBookingCount'].toString(),
                          startTime: double.parse(widget.selectedTime[0]
                              .toString()
                              .replaceAll(':', '.')),
                          endTime: double.parse(widget
                              .selectedTime[widget.selectedTime.length - 1]
                              .toString()
                              .replaceAll(':', '.')),
                          totalAmount: getTotalAmount(),
                          amountController: amountController,
                          currentIndex: currentIndex,
                          setCurrentIndex: setCurrentIndex,
                          convenienceFee: convenienceFee,
                          gstOnConvenienceFee: gstOnConvenienceFee,
                          basicAmount: basicAmount,
                          discountController: discountController,
                          gstController: gstController,
                          invoiceType: invoiceType,
                          selectInvoiceType: selectInvoiceType,
                          getTotalGST: getTotalGST,
                          removeBookingDate: removeBookingDate,
                        )
                    ],
                  ),
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: deviceWidth,
              dH: deviceHeight,
              child: CustomButton(
                width: deviceWidth,
                height: deviceWidth * 0.12,
                buttonText:
                    step == 1 ? 'Check Slot Availability' : 'Create Booking',
                fontSize: 16,
                radius: 7,
                isLoading: checkSlot || createBooking,
                onPressed: checkSlot || createBooking
                    ? () {}
                    : () {
                        if (!_formKey.currentState!.validate()) {
                          return;
                        }
                        if (step == 1) {
                          checkSlotAvailbility();
                        } else if (step == 2) {
                          createBulkBooking();
                        }
                      },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
