import '../../employeeManagement/model/employeeManagemenrModel.dart';
import '../../venueModule/models/venue_model.dart';

class BulkBookingModel {
  final String id;
  final Venues venue;
  final String userName;
  final String userPhone;
  final String userAvatar;
  final double totalAmount;
  final double discountedAmount;
  final double amountPaid;
  final String option;
  final String sizeOrSport;
  final bool isNet;
  final String label;
  final SportType sport;
  final SportCategory sportCategory;
  final DateTime startDate;
  final DateTime endDate;
  final List days;
  final double startTime;
  final double endTime;
  final List notAvailableDates;
  final List availableDates;
  final int totalBookingCount;
  final Duration duration;
  final int otp;
  final num convenienceFee;
  final num gstOnConvenienceFee;
  final num basicAmount;
  final num tax;
  final String invoiceType;

  BulkBookingModel({
    required this.id,
    required this.venue,
    required this.userName,
    required this.userPhone,
    required this.userAvatar,
    required this.totalAmount,
    required this.discountedAmount,
    required this.amountPaid,
    required this.option,
    required this.sizeOrSport,
    required this.isNet,
    required this.label,
    required this.sport,
    required this.sportCategory,
    required this.startDate,
    required this.endDate,
    required this.days,
    required this.startTime,
    required this.endTime,
    required this.notAvailableDates,
    required this.availableDates,
    required this.totalBookingCount,
    required this.duration,
    required this.otp,
    required this.convenienceFee,
    required this.gstOnConvenienceFee,
    required this.basicAmount,
    required this.tax,
    required this.invoiceType,
  });

  static BulkBookingModel jsonToBulkBooking(Map booking) {
    return BulkBookingModel(
      id: booking['_id'],
      venue:
          Venues(id: booking['turfId']['_id'], name: booking['turfId']['name']),
      otp: booking['otp'] == null ? 0 : booking['otp'].toInt(),
      userName:
          '${booking['user']['firstName'] ?? ''} ${booking['user']['lastName'] ?? ''}',
      userAvatar: '${booking['user']['avatar'] ?? ''}',
      userPhone: booking['user']['phone'] ?? '',
      totalAmount: booking['totalAmount'].toDouble(),
      discountedAmount: booking['discountedAmount'] == null
          ? 0
          : booking['discountedAmount'].toDouble(),
      amountPaid: booking['amountPaid'].toDouble(),
      convenienceFee:
          booking['convenienceFee'] == null ? 0 : booking['convenienceFee'],
      gstOnConvenienceFee: booking['gstOnConvenienceFee'] == null
          ? 0
          : booking['gstOnConvenienceFee'],
      basicAmount: booking['basicAmount'] == null ? 0 : booking['basicAmount'],
      option: booking['option'] ?? '',
      sizeOrSport: booking['sizeOrSport'] ?? '',
      isNet: booking['isNet'] ?? false,
      label: booking['label'] ?? '',
      sport: SportType(
        id: booking['sport']['_id'],
        sport: booking['sport']['sport'],
        image: booking['sport']['image'],
      ),
      sportCategory: SportCategory(
        id: booking['sportCategory']['_id'],
        categoryName: booking['sportCategory']['categoryName'],
      ),
      startDate: DateTime.parse(booking['startDate']).toLocal(),
      endDate: DateTime.parse(booking['endDate']).toLocal(),
      days: booking['days'],
      startTime: booking['startTime'].toDouble(),
      endTime: booking['endTime'].toDouble(),
      notAvailableDates: booking['notAvailableDates'] ?? [],
      availableDates: booking['availableDates'] ?? [],
      totalBookingCount: booking['totalBookingCount'].toInt(),
      tax: booking['tax'] ?? 0,
      invoiceType: booking['invoiceType'] ?? '',
      duration: Duration(
        type: booking['duration']['type'],
        count: booking['duration']['count'].toInt(),
      ),
    );
  }
}

class Duration {
  final String type;
  final int count;

  Duration({
    required this.type,
    required this.count,
  });
}
