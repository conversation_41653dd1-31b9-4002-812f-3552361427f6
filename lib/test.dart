// import 'package:bys_business/authModule/modals/userModel.dart';
// import 'package:bys_business/authModule/providers/auth.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:provider/provider.dart';
// import 'colors.dart';
// import 'commonWidgets/bottom_aligned_widget.dart';
// import 'commonWidgets/custom_button.dart';
// import 'commonWidgets/custom_checkBox2.dart';
// import 'commonWidgets/custom_checkbox_widget.dart';
// import 'commonWidgets/custom_container.dart';
// import 'commonWidgets/custom_text_field.dart';
// import 'commonWidgets/new_appbar.dart';
// import 'commonWidgets/text_widget.dart';
// import 'common_function.dart';
// import 'venueModule/models/venue_model.dart';
// import 'venueModule/newVenueFlow/widgets/step1/search_location_bottomsheet.dart';
// import 'venueModule/widgets/venueDetailsWidget/multi_select_bottom_sheet.dart';

// class AddEditVenueScreen extends StatefulWidget {
//   const AddEditVenueScreen({
//     Key? key,
//   }) : super(key: key);

//   @override
//   AddEditVenueScreenState createState() => AddEditVenueScreenState();
// }

// class AddEditVenueScreenState extends State<AddEditVenueScreen> {
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;

//   //Variables
//   bool isLoading = false;
//   late UserModal user;
//   TextEditingController venueNameController = TextEditingController();
//   TextEditingController venuePhoneController = TextEditingController();
//   TextEditingController venueDescriptionController = TextEditingController();
//   TextEditingController landmarkController = TextEditingController();
//   Address? venueAddress;
//   List listOfSports = [];
//   List listofSportCategory = [];
//   List filteredSports = [];
//   List selectedWeekDays = [];
//   List selectedWeekEnds = [];

//   List<String> getDays(bool isWeekdays) {
//     if (isWeekdays) {
//       if (selectedWeekEnds.contains('Fri')) {
//         return ['Mon', 'Tue', 'Wed', 'Thu'];
//       } else {
//         return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
//       }
//     } else {
//       if (selectedWeekDays.contains('Fri')) {
//         return ['Sat', 'Sun'];
//       } else {
//         return [
//           'Fri',
//           'Sat',
//           'Sun',
//         ];
//       }
//     }
//   }

//   List listOfSlots = [
//     {
//       'title': 'Morning',
//       'isSelected': false,
//       'id': 'Slot1',
//       'startTime': TimeOfDay(hour: 5, minute: 00),
//       'endTime': TimeOfDay(hour: 12, minute: 0),
//       'priceAndQuantity': [],
//     },
//     {
//       'title': 'Afternoon',
//       'isSelected': false,
//       'id': 'Slot2',
//       'startTime': TimeOfDay(hour: 12, minute: 00),
//       'endTime': TimeOfDay(hour: 16, minute: 00),
//       'priceAndQuantity': [],
//     },
//     {
//       'title': 'Evening',
//       'isSelected': false,
//       'id': 'Slot3',
//       'startTime': TimeOfDay(hour: 16, minute: 00),
//       'endTime': TimeOfDay(hour: 20, minute: 00),
//       'priceAndQuantity': [],
//     },
//     {
//       'title': 'Night',
//       'isSelected': false,
//       'id': 'Slot4',
//       'startTime': TimeOfDay(hour: 20, minute: 00),
//       'endTime': TimeOfDay(hour: 24, minute: 00),
//       'priceAndQuantity': [],
//     },
//     {
//       'title': 'Late Night',
//       'isSelected': false,
//       'id': 'Slot5',
//       'startTime': TimeOfDay(hour: 00, minute: 00),
//       'endTime': TimeOfDay(hour: 05, minute: 0),
//       'priceAndQuantity': [],
//     },
//   ];

//   bool isSameForAllSports = false;
//   bool isSingleChecked = false;
//   bool isDoubleChecked = false;

//   bool isIndoorSelected = true;

//   List selectedSports = [];

//   List listOfCourtsAndTables = [
//     {
//       'sport': 'Badminton',
//       'title': 'Default Court',
//       'isSelected': false,
//       'id': 'C1',
//     },
//     {
//       'sport': 'Snooker',
//       'title': 'Snooker Table',
//       'isSelected': false,
//       'id': 'T1',
//     },
//     {
//       'sport': 'Snooker',
//       'title': 'Pool Table',
//       'isSelected': false,
//       'id': 'T2',
//     },
//   ];

//   //Functions
//   setVenueAddress(Address address) {
//     venueAddress = address;
//     setState(() {});
//   }

//   openSearchLocationBottomSheet() {
//     showModalBottomSheet(
//       isScrollControlled: true,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(24),
//           topRight: Radius.circular(24),
//         ),
//       ),
//       context: (context),
//       builder: (context) => GestureDetector(
//         child: SearchLocationBottomSheet(
//             fullAddress: venueAddress != null ? venueAddress!.fullAddress : ''),
//         onTap: () {},
//         behavior: HitTestBehavior.opaque,
//       ),
//     ).then((value) {
//       if (value != null) {
//         setVenueAddress(value);
//       }
//     });
//   }

//   String getImage(String title) {
//     if (title == 'Indoor') {
//       return 'indoor';
//     } else if (title == 'Outdoor') {
//       return 'outdoor';
//     } else {
//       return 'esport';
//     }
//   }

//   String getSportsName(String categoryId) {
//     List sport =
//         listOfSports.where((data) => data['categoryId'] == categoryId).toList();
//     String sportsName = '';
//     sport.forEach((data) {
//       sportsName += '${data['title']}, ';
//     });

//     List splitList = sportsName.split(',');
//     splitList.remove(" ");
//     sportsName = splitList.join(',');
//     return sportsName;
//   }

//   filterSportsByCategory(String categoryId) {
//     setState(() {
//       // Unselect all previously selected sports
//       for (var sport in listOfSports) {
//         sport['isSelected'] = false;
//       }

//       // Filter sports based on the selected category
//       filteredSports = listOfSports
//           .where((sport) => sport['categoryId'] == categoryId)
//           .toList();
//     });
//   }

//   selectDaysBottomSheet({
//     required BuildContext context,
//     required bool isWeekdays,
//   }) {
//     showModalBottomSheet(
//       context: context,
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(15.0),
//           topRight: Radius.circular(15.0),
//         ),
//       ),
//       builder: (BuildContext context) => MultiSelectBottomSheet(
//         listOfFields: getDays(isWeekdays),
//         title: 'Select ${isWeekdays ? 'Weekdays' : 'Weekends'}',
//         selectedFields: isWeekdays
//             ? List<String>.from(selectedWeekDays)
//             : List<String>.from(selectedWeekEnds),
//       ),
//     ).then((value) {
//       if (value != null) {
//         try {
//           setState(() {
//             if (isWeekdays) {
//               selectedWeekDays = List<String>.from(value);
//             } else {
//               selectedWeekEnds = List<String>.from(value);
//             }
//           });
//         } catch (e) {
//           print(e);
//         }
//       }
//     });
//   }

//   toggleSameForAllSports() {
//     setState(() {
//       isSameForAllSports = !isSameForAllSports;
//       filteredSports.where((sport) => sport['isSelected']).forEach((sport) {
//         if (isSingleChecked) {
//           sport['single'] = isSameForAllSports;
//         }
//         if (isDoubleChecked) {
//           sport['double'] = isSameForAllSports;
//         }
//       });
//     });
//   }

//   //Widgets
//   Widget buildDaysSelectionWidget({
//     required BuildContext context,
//     required bool isWeekdays,
//     required List listOfIterate,
//   }) {
//     return GestureDetector(
//       onTap: () =>
//           selectDaysBottomSheet(context: context, isWeekdays: isWeekdays),
//       child: CustomContainer(
//         boxShadow: [],
//         radius: 8,
//         vPadding: 0.03,
//         hPadding: 0,
//         borderColor: getThemeColor(),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             SizedBox(
//               width: dW * 0.7,
//               child: (selectedWeekDays.isEmpty && isWeekdays) ||
//                       (selectedWeekEnds.isEmpty && !isWeekdays)
//                   ? Padding(
//                       padding: EdgeInsets.only(left: dW * 0.03),
//                       child: TextWidget(
//                         title: 'Select ${isWeekdays ? 'weekdays' : 'weekends'}',
//                         textAlign: TextAlign.left,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     )
//                   : SingleChildScrollView(
//                       scrollDirection: Axis.horizontal,
//                       physics: BouncingScrollPhysics(),
//                       child: Row(
//                         children: [
//                           SizedBox(width: dW * 0.03),
//                           ...listOfIterate.map(
//                             (day) => Container(
//                               margin: EdgeInsets.only(right: dW * 0.02),
//                               constraints: BoxConstraints(minWidth: dW * 0.13),
//                               padding: EdgeInsets.symmetric(
//                                 vertical: dW * 0.015,
//                                 horizontal: dW * 0.02,
//                               ),
//                               decoration: BoxDecoration(
//                                 color: getThemeColor(),
//                                 borderRadius: BorderRadius.circular(50),
//                                 border: Border.all(color: getThemeColor()),
//                               ),
//                               child: FittedBox(
//                                 fit: BoxFit.scaleDown,
//                                 child: TextWidget(
//                                   title: day,
//                                   fontSize: 13,
//                                   color: Colors.white,
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                           SizedBox(width: dW * 0.03),
//                         ],
//                       ),
//                     ),
//             ),
//             Padding(
//               padding: EdgeInsets.only(right: dW * 0.02),
//               child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   //Api Functions
//   fetchSports() async {
//     setState(() {
//       isLoading = true;
//     });

//     listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
//     print(listOfSports);
//     listofSportCategory =
//         Provider.of<Auth>(context, listen: false).listOfSportCategory;
//     print(listofSportCategory);

//     setState(() {
//       isLoading = false;
//     });
//   }

//   //Init Function
//   myInit() {
//     fetchSports();

//     //Initially Selected Sports Category
//     if (listofSportCategory.isNotEmpty) {
//       listofSportCategory[0]['isSelected'] = true;
//       filterSportsByCategory(listofSportCategory[0]['id']);
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     user = Provider.of<Auth>(context, listen: false).user;
//     myInit();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     venueNameController.dispose();
//     venuePhoneController.dispose();
//     venueDescriptionController.dispose();
//     landmarkController.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     dH = MediaQuery.of(context).size.height;
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;

//     return Scaffold(
//       body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
//     );
//   }

//   screenBody() {
//     return SizedBox(
//       height: dH,
//       width: dW,
//       child: Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.only(top: dW * 0.05, bottom: dW * 0.05),
//             child: NewAppBar(dW: dW, title: ''),
//           ),
//           Expanded(
//             child: SingleChildScrollView(
//               physics: const BouncingScrollPhysics(),
//               padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: [
//                   TextWidget(
//                     title: 'Lets give venue title & small description',
//                     fontSize: 18,
//                     fontWeight: FontWeight.w500,
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03),
//                     child: TextWidget(
//                       title: 'Now give your venue title and small description',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.08),
//                     child: CustomTextFieldWithLabel(
//                       label: 'Name',
//                       controller: venueNameController,
//                       hintText: 'Enter venue name',
//                       borderRadius: 5,
//                       labelFS: 14,
//                       labelColor: Color(0xff9798A3),
//                       labelFW: FontWeight.w500,
//                       hintColor: Colors.black,
//                       borderColor: Color(0xffACACB4),
//                       fillColor: Color(0xffF8F9FD),
//                       onChanged: (value) => setState(() {}),
//                       inputAction: TextInputAction.next,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.08),
//                     child: CustomTextFieldWithLabel(
//                       label: 'Phone',
//                       controller: venuePhoneController,
//                       hintText: 'Enter venue phone no.',
//                       borderRadius: 5,
//                       labelFS: 14,
//                       maxLength: 10,
//                       labelColor: Color(0xff9798A3),
//                       labelFW: FontWeight.w500,
//                       hintColor: Colors.black,
//                       borderColor: Color(0xffACACB4),
//                       fillColor: Color(0xffF8F9FD),
//                       onChanged: (value) => setState(() {}),
//                       inputFormatter: [FilteringTextInputFormatter.digitsOnly],
//                       inputType: TextInputType.number,
//                       inputAction: TextInputAction.next,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.08),
//                     child: CustomTextFieldWithLabel(
//                       label: 'Description',
//                       controller: venueDescriptionController,
//                       hintText: 'Enter description here ',
//                       borderRadius: 5,
//                       labelFS: 14,
//                       labelColor: Color(0xff9798A3),
//                       labelFW: FontWeight.w500,
//                       hintColor: Colors.black,
//                       borderColor: Color(0xffACACB4),
//                       fillColor: Color(0xffF8F9FD),
//                       counterText:
//                           '${venueDescriptionController.text.trim().length}/300',
//                       maxLines: 6,
//                       maxLength: 300,
//                       textFS: 14,
//                       textCapitalization: TextCapitalization.sentences,
//                       inputType: TextInputType.streetAddress,
//                       inputAction: TextInputAction.newline,
//                       onChanged: (value) => setState(() {}),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.05),
//                     child: TextWidget(
//                       title: 'Where is your venue located?',
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03),
//                     child: TextWidget(
//                       title:
//                           'Specify your venue\'s location with a map pinpoint and nearby landmark details.',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.08),
//                     child: Row(
//                       children: [
//                         TextWidget(
//                           title: 'Venue Address',
//                           color: Color(0xff9798A3),
//                           fontWeight: FontWeight.w500,
//                         ),
//                         TextWidget(
//                           title: '*',
//                           color: redColor,
//                         ),
//                       ],
//                     ),
//                   ),
//                   GestureDetector(
//                     onTap: openSearchLocationBottomSheet,
//                     child: CustomContainer(
//                       margin: EdgeInsets.only(top: dW * 0.025),
//                       width: dW,
//                       boxShadow: [],
//                       borderColor: Color(0xffACACB4),
//                       bgColor: Color(0xffF8F9FD),
//                       radius: 5,
//                       hPadding: .03,
//                       vPadding: .03,
//                       child: TextWidget(
//                         title: venueAddress == null
//                             ? 'Select Address'
//                             : venueAddress!.fullAddress.trim().isEmpty
//                                 ? '${venueAddress!.area.isEmpty ? venueAddress!.streetName : venueAddress!.area}, ${venueAddress!.landmark}, ${venueAddress!.city}, ${venueAddress!.state}, ${venueAddress!.pincode}.'
//                                 : '${venueAddress!.fullAddress}.',
//                         fontWeight: venueAddress == null
//                             ? FontWeight.normal
//                             : FontWeight.w600,
//                         fontSize: 15,
//                         color: blackColor3,
//                       ),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.05),
//                     child: CustomTextFieldWithLabel(
//                       label: 'Landmark',
//                       controller: landmarkController,
//                       hintText: 'Enter landmark here',
//                       borderRadius: 5,
//                       labelFS: 14,
//                       labelColor: Color(0xff9798A3),
//                       labelFW: FontWeight.w500,
//                       hintColor: Colors.black,
//                       borderColor: Color(0xffACACB4),
//                       fillColor: Color(0xffF8F9FD),
//                       onChanged: (value) => setState(() {}),
//                       inputAction: TextInputAction.next,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.08),
//                     child: TextWidget(
//                       title: 'Describe your venue type',
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.12),
//                     child: TextWidget(
//                       title:
//                           'Select the category that best describes your venue, whether it\'s indoor, outdoor, or an esports arena.',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   ...listofSportCategory.map(
//                     (category) => GestureDetector(
//                       onTap: () {
//                         setState(() {
//                           for (var cat in listofSportCategory) {
//                             cat['isSelected'] = false;
//                           }
//                           category['isSelected'] = true;
//                            filterSportsByCategory(category['id']);
//                           isIndoorSelected = category['title'] == "Indoor";
//                           selectedSports = [];
//                         });
//                       },
//                       child: CustomContainer(
//                         margin: EdgeInsets.only(bottom: dW * 0.05),
//                         boxShadow: [],
//                         borderColor: category['isSelected']
//                             ? getThemeColor()
//                             : Color(0xffABABAB),
//                         bgColor: category['isSelected']
//                             ? getThemeColor()
//                             : Colors.transparent,
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 TextWidget(
//                                   title: category['title'],
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.w500,
//                                   color: category['isSelected']
//                                       ? Colors.white
//                                       : Colors.black,
//                                 ),
//                                 SizedBox(height: dW * 0.02),
//                                 ConstrainedBox(
//                                   constraints:
//                                       BoxConstraints(maxWidth: dW * 0.6),
//                                   child: TextWidget(
//                                     title: getSportsName(category['id']),
//                                     fontSize: 11,
//                                     color: category['isSelected']
//                                         ? Color(0xffD9D9D9)
//                                         : Colors.black,
//                                   ),
//                                 )
//                               ],
//                             ),
//                             Image.asset(
//                               'assets/images/${getImage(category['title'])}.png',
//                               height: 30,
//                             )
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.05),
//                     child: TextWidget(
//                       title: 'Now select which sports played at your venue',
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03),
//                     child: TextWidget(
//                       title: 'Select the sports that you offered at your venue',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.1),
//                     child: Wrap(
//                       children: [
//                         ...filteredSports.map(
//                           (sport) => GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 sport['isSelected'] = !sport['isSelected'];
//                                 if (sport['isSelected']) {
//                                   if (!selectedSports
//                                       .contains(sport['title'])) {
//                                     selectedSports.add(sport['title']);
//                                   }
//                                 } else {
//                                   selectedSports.remove(sport['title']);
//                                 }
//                               });
//                             },
//                             child: Container(
//                               margin: EdgeInsets.only(
//                                   right: dW * 0.03, bottom: dW * 0.03),
//                               width: sport['title'] == 'Badminton'
//                                   ? dW * 0.44
//                                   : sport['title'] == 'Table Tennis'
//                                       ? dW * 0.5
//                                       : dW * 0.4,
//                               padding: EdgeInsets.symmetric(
//                                   vertical: dW * 0.025, horizontal: dW * 0.03),
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                 color: sport['isSelected']
//                                     ? getThemeColor()
//                                     : Colors.transparent,
//                                 borderRadius: BorderRadius.circular(100),
//                                 border: Border.all(color: getThemeColor()),
//                               ),
//                               child: Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   ConstrainedBox(
//                                     constraints:
//                                         BoxConstraints(maxWidth: dW * 0.3),
//                                     child: FittedBox(
//                                       fit: BoxFit.scaleDown,
//                                       child: TextWidget(
//                                         title: sport['title'],
//                                         fontSize: sport['title'] == 'Badminton'
//                                             ? 16
//                                             : 18,
//                                         fontWeight: FontWeight.w500,
//                                         color: !sport['isSelected']
//                                             ? Colors.black
//                                             : Colors.white,
//                                       ),
//                                     ),
//                                   ),
//                                   SizedBox(width: dW * .03),
//                                   if (sport['image'] != '') ...[
//                                     !sport['image'].contains('https')
//                                         ? Image.asset(sport['image'],
//                                             height: 35)
//                                         : CachedNetworkImage(
//                                             imageUrl: sport['image'],
//                                             height: 30,
//                                             fit: BoxFit.cover,
//                                             placeholder: (_, __) => Image.asset(
//                                               'assets/images/user.png',
//                                               fit: BoxFit.cover,
//                                               height: 35,
//                                             ),
//                                           ),
//                                   ],
//                                 ],
//                               ),
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),

//                   //MS
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.05),
//                     child: TextWidget(
//                       title:
//                           'Let’s describe weekdays and weekends for you venue',
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03),
//                     child: TextWidget(
//                       title:
//                           'Select the venue weekends and weekdays, whether it\'s outdoor, indoor or an esports arena.',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.1, bottom: dW * 0.03),
//                     child: TextWidget(
//                       title: 'Weekdays',
//                       fontWeight: FontWeight.w500,
//                       color: Color(0xff9798A3),
//                     ),
//                   ),
//                   buildDaysSelectionWidget(
//                     context: context,
//                     isWeekdays: true,
//                     listOfIterate: selectedWeekDays,
//                   ),
//                   Padding(
//                     padding:
//                         EdgeInsets.only(top: dW * 0.075, bottom: dW * 0.03),
//                     child: TextWidget(
//                       title: 'Weekend Days',
//                       fontWeight: FontWeight.w500,
//                       color: Color(0xff9798A3),
//                     ),
//                   ),
//                   buildDaysSelectionWidget(
//                     context: context,
//                     isWeekdays: false,
//                     listOfIterate: selectedWeekEnds,
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.05),
//                     child: TextWidget(
//                       title: 'Now select time slots',
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.03),
//                     child: TextWidget(
//                       title:
//                           'Select the time slots in which your venues are open for the users',
//                       fontSize: 12,
//                       color: Color(0xff21272A),
//                     ),
//                   ),
//                   SizedBox(height: dW * 0.1),
//                   Wrap(
//                     children: [
//                       ...listOfSlots.map(
//                         (slot) => GestureDetector(
//                           onTap: () {
//                             setState(() {
//                               slot['isSelected'] = !slot['isSelected'];
//                             });
//                           },
//                           child: IntrinsicWidth(
//                             child: Container(
//                               margin: EdgeInsets.only(
//                                   right: dW * 0.03, bottom: dW * 0.03),
//                               padding: EdgeInsets.symmetric(
//                                   vertical: dW * 0.025, horizontal: dW * 0.03),
//                               alignment: Alignment.center,
//                               decoration: BoxDecoration(
//                                 color: slot['isSelected']
//                                     ? getThemeColor()
//                                     : Colors.transparent,
//                                 borderRadius: BorderRadius.circular(100),
//                                 border: Border.all(color: getThemeColor()),
//                               ),
//                               child: Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   TextWidget(
//                                     title: slot['title'],
//                                     fontSize:
//                                         slot['title'] == 'Badminton' ? 16 : 18,
//                                     fontWeight: FontWeight.w500,
//                                     color: !slot['isSelected']
//                                         ? Colors.black
//                                         : Colors.white,
//                                   ),
//                                   SizedBox(width: dW * .03),
//                                   slot['title'] == 'Late Night'
//                                       ? Image.asset(
//                                           'assets/images/late_night.png',
//                                           height: dW * 0.06,
//                                         )
//                                       : Image.asset(
//                                           'assets/images/${slot['title'].toLowerCase()}.png',
//                                           height: dW * 0.06,
//                                         ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       )
//                     ],
//                   ),
//                   if ((selectedSports.contains('Snooker') ||
//                           selectedSports.contains('Table Tennis') ||
//                           selectedSports.contains('Badminton')) &&
//                       (!selectedSports.contains('Cricket') ||
//                           !selectedSports.contains('Football')))
//                     Padding(
//                       padding: EdgeInsets.only(top: dW * 0.05),
//                       child: Text(
//                         'Specify the game offerings at your venue',
//                         style: TextStyle(
//                           fontSize: 20,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     ),

//                   //Salman
//                   if ((selectedSports.contains('Snooker') ||
//                           selectedSports.contains('Table Tennis') ||
//                           selectedSports.contains('Badminton')) &&
//                       (!selectedSports.contains('Cricket') ||
//                           !selectedSports.contains('Football')))
//                     Padding(
//                       padding: EdgeInsets.only(top: dW * 0.1),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           ...filteredSports
//                               .where((sport) => sport['isSelected'])
//                               .toList()
//                               .asMap()
//                               .entries
//                               .map((entry) {
//                             final index = entry.key;
//                             final sport = entry.value;
//                             bool isSingle = sport['single'] ?? false;
//                             bool isDouble = sport['double'] ?? false;

//                             return Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Row(
//                                   children: [
//                                     if (sport['image'] != '')
//                                       sport['image'].contains('https')
//                                           ? Container(
//                                               padding:
//                                                   EdgeInsets.all(dW * 0.01),
//                                               decoration: BoxDecoration(
//                                                 shape: BoxShape.circle,
//                                                 border: Border.all(
//                                                   color: getThemeColor(),
//                                                   width: 1,
//                                                 ),
//                                               ),
//                                               child: CachedNetworkImage(
//                                                 imageUrl: sport['image'],
//                                                 height: 16,
//                                                 fit: BoxFit.cover,
//                                               ),
//                                             )
//                                           : Container(
//                                               padding:
//                                                   EdgeInsets.all(dW * 0.01),
//                                               decoration: BoxDecoration(
//                                                 shape: BoxShape.circle,
//                                                 border: Border.all(
//                                                   color: getThemeColor(),
//                                                   width: 1,
//                                                 ),
//                                               ),
//                                               child: Image.asset(sport['image'],
//                                                   height: 30),
//                                             ),
//                                     SizedBox(width: dW * 0.03),
//                                     Text(
//                                       sport['title'],
//                                       style: TextStyle(
//                                         fontWeight: FontWeight.w600,
//                                         color: Color(0xff3E3E3E),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 GestureDetector(
//                                   onTap: () {
//                                     setState(() {
//                                       sport['single'] = !isSingle;
//                                       isSingleChecked = sport['single'];
//                                     });
//                                   },
//                                   child: Container(
//                                     margin: EdgeInsets.only(
//                                         bottom: dW * 0.03, top: dW * 0.03),
//                                     padding: EdgeInsets.symmetric(
//                                       horizontal: dW * 0.04,
//                                       vertical: dW * 0.06,
//                                     ),
//                                     decoration: BoxDecoration(
//                                       borderRadius: BorderRadius.circular(8),
//                                       border: Border.all(
//                                         width: 1,
//                                         color: isSingle
//                                             ? Colors.green
//                                             : Colors.grey,
//                                       ),
//                                     ),
//                                     width: dW,
//                                     child: Row(
//                                       children: [
//                                         SizedBox(width: dW * 0.04),
//                                         Expanded(
//                                           child: CustomCheckbox(
//                                             title: 'Singles',
//                                             fontWeight: FontWeight.w500,
//                                             fontSize: 16,
//                                             textColor: isSingle
//                                                 ? getThemeColor()
//                                                 : Colors.black,
//                                             onChanged: () {
//                                               setState(() {
//                                                 sport['single'] = !isSingle;
//                                                 isSingleChecked =
//                                                     sport['single'];
//                                               });
//                                             },
//                                             value: isSingle,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                                 GestureDetector(
//                                   onTap: () {
//                                     setState(() {
//                                       sport['double'] = !isDouble;
//                                       isDoubleChecked = sport['double'];
//                                     });
//                                   },
//                                   child: Container(
//                                     margin: EdgeInsets.only(bottom: dW * 0.06),
//                                     padding: EdgeInsets.symmetric(
//                                       horizontal: dW * 0.04,
//                                       vertical: dW * 0.06,
//                                     ),
//                                     decoration: BoxDecoration(
//                                       borderRadius: BorderRadius.circular(8),
//                                       border: Border.all(
//                                         width: 1,
//                                         color: isDouble
//                                             ? Colors.green
//                                             : Colors.grey,
//                                       ),
//                                     ),
//                                     width: dW,
//                                     child: Row(
//                                       children: [
//                                         SizedBox(width: dW * 0.04),
//                                         Expanded(
//                                           child: CustomCheckbox(
//                                             title: 'Doubles',
//                                             fontWeight: FontWeight.w500,
//                                             fontSize: 16,
//                                             textColor: isDouble
//                                                 ? getThemeColor()
//                                                 : Colors.black,
//                                             onChanged: () {
//                                               setState(() {
//                                                 sport['double'] = !isDouble;
//                                                 isDoubleChecked =
//                                                     sport['double'];
//                                               });
//                                             },
//                                             value: isDouble,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                                 if (index == 0 && selectedSports.length > 1)
//                                   CustomCheckbox2(
//                                     value: isSameForAllSports,
//                                     spaceBetween: dW * 0.04,
//                                     size: 15,
//                                     reverseCheckBox: true,
//                                     onChanged: toggleSameForAllSports,
//                                     activeColor: getThemeColor(),
//                                     title: 'Same for all sports',
//                                   ),
//                                 if (index !=
//                                     filteredSports
//                                             .where(
//                                                 (sport) => sport['isSelected'])
//                                             .toList()
//                                             .length -
//                                         1)
//                                   Container(
//                                     margin: EdgeInsets.symmetric(
//                                         vertical: dW * 0.07),
//                                     child: Divider(
//                                       color: Color(0xffD9D9D9),
//                                       thickness: 1,
//                                     ),
//                                   ),
//                               ],
//                             );
//                           }).toList(),
//                         ],
//                       ),
//                     ),
//                   if ((selectedSports.contains('Snooker') ||
//                           selectedSports.contains('Table Tennis') ||
//                           selectedSports.contains('Badminton')) &&
//                       (!selectedSports.contains('Cricket') ||
//                           !selectedSports.contains('Football')))
//                     Padding(
//                       padding: EdgeInsets.only(top: dW * 0.05),
//                       child: TextWidget(
//                         title:
//                             'Now add type of courts and tables available at your venue',
//                         fontSize: 20,
//                         fontWeight: FontWeight.w600,
//                       ),
//                     ),
//                   if ((selectedSports.contains('Snooker') ||
//                           selectedSports.contains('Table Tennis') ||
//                           selectedSports.contains('Badminton')) &&
//                       (!selectedSports.contains('Cricket') ||
//                           !selectedSports.contains('Football')))
//                     Padding(
//                       padding: EdgeInsets.only(top: dW * 0.1),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: listOfCourtsAndTables
//                             .where((sport) => sport['isSelected'])
//                             .toList()
//                             .asMap()
//                             .entries
//                             .map((entry) {
//                           final isLastSport = listOfCourtsAndTables.last;
//                           return Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Row(
//                                 children: [
//                                   Image.asset(
//                                     'assets/images/${entry.value['sport']}.png',
//                                     height: 24,
//                                   ),
//                                   SizedBox(
//                                     width: dW * 0.02,
//                                   ),
//                                   TextWidget(
//                                     title: '${entry.value['sport']}',
//                                     fontWeight: FontWeight.w600,
//                                     color: Color(0xff3E3E3E),
//                                   ),
//                                 ],
//                               ),
//                               Container(
//                                 margin: EdgeInsets.only(
//                                     bottom: dW * 0.04, top: dW * 0.04),
//                                 padding: EdgeInsets.only(
//                                   left: dW * 0.04,
//                                   top: dW * 0.035,
//                                   right: dW * 0.045,
//                                   bottom: dW * 0.035,
//                                 ),
//                                 decoration: BoxDecoration(
//                                   color: entry.value['isSelected']
//                                       ? getThemeColor()
//                                       : whiteColor,
//                                   border: Border.all(
//                                     color: getThemeColor(),
//                                   ),
//                                   borderRadius: BorderRadius.circular(55),
//                                 ),
//                                 child: IntrinsicWidth(
//                                   child: Row(
//                                     children: [
//                                       Text(
//                                         entry.value['title'],
//                                         style: TextStyle(
//                                           fontWeight: FontWeight.w500,
//                                           fontSize: 20,
//                                           color: entry.value['isSelected']
//                                               ? whiteColor
//                                               : Color(0xff5E5E5E),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                               if (!isLastSport)
//                                 Container(
//                                   margin:
//                                       EdgeInsets.symmetric(vertical: dW * 0.07),
//                                   child: Divider(
//                                     color: Color(0xffD9D9D9),
//                                     thickness: 1,
//                                   ),
//                                 ),
//                             ],
//                           );
//                         }).toList(),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ),
//           BottomAlignedWidget(
//             dW: dW,
//             dH: dH,
//             child: Column(
//               children: [
//                 CustomButton(
//                   width: dW * 0.9,
//                   height: dW * 0.12,
//                   fontSize: 16,
//                   buttonText: 'Preview',
//                   onPressed: () {},
//                 ),
//                 Padding(
//                   padding: EdgeInsets.only(top: dW * 0.04),
//                   child: TextWidget(
//                     title:
//                         'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
//                     fontSize: 12,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 )
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'colors.dart';
import 'commonWidgets/asset_svg_icon.dart';
import 'commonWidgets/bottom_aligned_widget.dart';
import 'commonWidgets/custom_button.dart';
import 'commonWidgets/custom_checkBox2.dart';
import 'commonWidgets/custom_checkbox_widget.dart';
import 'commonWidgets/custom_container.dart';
import 'commonWidgets/custom_text_field.dart';
import 'commonWidgets/divider_widget.dart';
import 'commonWidgets/new_appbar.dart';
import 'commonWidgets/raisedButton.dart';
import 'commonWidgets/text_widget.dart';
import 'common_function.dart';
import 'venueModule/models/venue_model.dart';
import 'venueModule/newVenueFlow/widgets/step1/search_location_bottomsheet.dart';
import 'venueModule/widgets/venueDetailsWidget/multi_select_bottom_sheet.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';

class AddEditVenueScreen extends StatefulWidget {
  const AddEditVenueScreen({
    Key? key,
  }) : super(key: key);

  @override
  AddEditVenueScreenState createState() => AddEditVenueScreenState();
}

class AddEditVenueScreenState extends State<AddEditVenueScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  //Variables
  bool isLoading = false;
  late UserModal user;
  TextEditingController venueNameController = TextEditingController();
  TextEditingController venuePhoneController = TextEditingController();
  TextEditingController venueDescriptionController = TextEditingController();
  TextEditingController landmarkController = TextEditingController();
  Address? venueAddress;
  List listOfSports = [];
  List listofSportCategory = [];
  bool isIndoorSelected = true;
  List selectedWeekDays = [];
  List selectedWeekEnds = [];
  List listOfSlots = [
    {
      'title': 'Morning',
      'isSelected': false,
      'id': 'Slot1',
      'startTime': TimeOfDay(hour: 5, minute: 00),
      'endTime': TimeOfDay(hour: 12, minute: 0),
      'priceAndQuantity': [],
    },
    {
      'title': 'Afternoon',
      'isSelected': false,
      'id': 'Slot2',
      'startTime': TimeOfDay(hour: 12, minute: 00),
      'endTime': TimeOfDay(hour: 16, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Evening',
      'isSelected': false,
      'id': 'Slot3',
      'startTime': TimeOfDay(hour: 16, minute: 00),
      'endTime': TimeOfDay(hour: 20, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Night',
      'isSelected': false,
      'id': 'Slot4',
      'startTime': TimeOfDay(hour: 20, minute: 00),
      'endTime': TimeOfDay(hour: 24, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Late Night',
      'isSelected': false,
      'id': 'Slot5',
      'startTime': TimeOfDay(hour: 00, minute: 00),
      'endTime': TimeOfDay(hour: 05, minute: 0),
      'priceAndQuantity': [],
    },
  ];

  List selectedSports = [];
  bool isSameForAllSports = false;
  bool isSingleChecked = false;
  bool isDoubleChecked = false;

  List listOfCourtsAndTables = [
    {
      'sport': 'Badminton',
      'title': 'Default Court',
      'isSelected': false,
      'id': 'C1',
    },
    {
      'sport': 'Snooker',
      'title': 'Snooker Table',
      'isSelected': false,
      'id': 'T1',
    },
    {
      'sport': 'Snooker',
      'title': 'Pool Table',
      'isSelected': false,
      'id': 'T2',
    },
  ];

  List listOfTurfOption = [
    {
      'title': 'Netting',
      'isSelected': false,
      'id': '1',
      'isNet': true,
      'label': "Turf with netting system",
    },
    {
      'title': 'Non Netting',
      'isSelected': true,
      'id': '2',
      'isNet': false,
      'label': "Turf without Net",
    },
    {
      'title': 'One On',
      'isSelected': false,
      'id': '3',
      'isNet': true,
      'label': "Turf with Cricket Net",
    },
  ];

  List listOfTurfSizeAndPrice = [
    {
      'title': '11:11',
      'price': 0,
      'isSelected': false,
      'id': 'S11',
    },
    {
      'title': '9:9',
      'price': 0,
      'isSelected': false,
      'id': 'S4',
    },
    {
      'title': '8:8',
      'price': 0,
      'isSelected': false,
      'id': 'S3',
    },
    {
      'title': '7:7',
      'price': 0,
      'isSelected': false,
      'id': 'S6',
    },
    {
      'title': '6:6',
      'price': 0,
      'isSelected': false,
      'id': 'S2',
    },
    {
      'title': '5:5',
      'price': 0,
      'isSelected': false,
      'id': 'S1',
    },
    {
      'title': '1:1',
      'price': 0,
      'isSelected': false,
      'id': 'S12',
    },
  ];

  String option = 'Non Netting';
  bool isNet = false;
  List selectedTurfSize = [];
  bool usePreviousValueForPrice = false;

  List listofSlotTime = [
    {
      'slot': 30,
      'isSelected': false,
      'id': 'ST1',
    },
    {
      'slot': 60,
      'isSelected': false,
      'id': 'ST2',
    },
  ];

  int selectedSlotDuration = 30;
  List selectedSlots = [];
  bool isAutoFillPrice = false;
  List sportsName = [];

  //Functions
  setVenueAddress(Address address) {
    venueAddress = address;
    setState(() {});
  }

  openSearchLocationBottomSheet() {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SearchLocationBottomSheet(
            fullAddress: venueAddress != null ? venueAddress!.fullAddress : ''),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        setVenueAddress(value);
      }
    });
  }

  String getImage(String title) {
    if (title == 'Indoor') {
      return 'indoor';
    } else if (title == 'Outdoor') {
      return 'outdoor';
    } else {
      return 'esport';
    }
  }

  String getSportsName(String categoryId) {
    List sport =
        listOfSports.where((data) => data['categoryId'] == categoryId).toList();
    String sportsName = '';
    sport.forEach((data) {
      sportsName += '${data['title']}, ';
    });

    List splitList = sportsName.split(',');
    splitList.remove(" ");
    sportsName = splitList.join(',');
    return sportsName;
  }

  List<String> getDays(bool isWeekdays) {
    if (isWeekdays) {
      if (selectedWeekEnds.contains('Fri')) {
        return ['Mon', 'Tue', 'Wed', 'Thu'];
      } else {
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
      }
    } else {
      if (selectedWeekDays.contains('Fri')) {
        return ['Sat', 'Sun'];
      } else {
        return [
          'Fri',
          'Sat',
          'Sun',
        ];
      }
    }
  }

  toggleSameForAllSports() {
    setState(() {
      isSameForAllSports = !isSameForAllSports;
      listOfSports.where((sport) => sport['isSelected']).forEach((sport) {
        if (isSingleChecked) {
          sport['single'] = isSameForAllSports;
        }
        if (isDoubleChecked) {
          sport['double'] = isSameForAllSports;
        }
      });
    });
  }

  String getTitle(String title) {
    if (title == 'Netting') {
      return 'Turf with division';
    } else if (title == 'Non Netting') {
      return 'Turf without division';
    } else if (title == 'One On') {
      return 'Turf with cricket net ';
    } else {
      return '';
    }
  }

  String getSubTitle(String title) {
    if (title == 'Netting') {
      return 'Safe and contained sports experience.';
    } else if (title == 'Non Netting') {
      return 'Open space for free play';
    } else if (title == 'One On') {
      return 'Specialised cricket practice facility';
    } else {
      return '';
    }
  }

  checkTurfSize(size) {
    if (option == 'One On') {
      if (selectedTurfSize.length == 1) {
        return false;
      } else {
        if (selectedTurfSize.contains(size)) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      if (isNet) {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else if (size == '8:8') {
          if (selectedTurfSize.contains('9:9') ||
              selectedTurfSize.contains('7:7')) {
            return true;
          } else {
            return false;
          }
        } else if (size == '9:9' || size == '7:7') {
          if (selectedTurfSize.contains('8:8')) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      } else {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  setTurfOption(String title) {
    usePreviousValueForPrice = false;
    selectedTurfSize = [];
    listOfTurfSizeAndPrice.forEach((element) {
      element['isSelected'] = false;
    });
    if (title == 'One On') {
      selectedTurfSize.add('1:1');
      listOfTurfSizeAndPrice.forEach((size) {
        if (size['title'] == '1:1') {
          size['isSelected'] = true;
        }
      });
    }
    listOfTurfOption.forEach((data) {
      if (data['title'] == title) {
        setState(() {
          data['isSelected'] = true;
          option = data['title'];
          isNet = data['isNet'];
        });
      } else {
        setState(() {
          data['isSelected'] = false;
        });
      }
    });
  }

  selectAndUnSelectType(
    String id,
    bool isSelected,
    List listToIterate,
    List listToSet,
    bool turfSize,
  ) {
    print('www');
    listToIterate.forEach((data) {
      if (data['id'] == id) {
        if (isSelected) {
          setState(() {
            data['isSelected'] = false;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.removeWhere((value) => value['title'] == data['title']);
          } else {
            if (isIndoorSelected) {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.removeWhere((value) => value == data['title']);
          }
          return;
        } else {
          setState(() {
            data['isSelected'] = true;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.add(data);
          } else {
            if (isIndoorSelected) {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.add(data['title']);
          }
          return;
        }
      }
    });
  }

  setUsePreviousValueForPrice() {
    setState(() {
      usePreviousValueForPrice = false;
    });
  }

  selectSlotDuration(id) {
    listofSlotTime.forEach((slot) {
      if (slot['id'] == id) {
        setState(() {
          slot['isSelected'] = true;
          selectedSlotDuration = slot['slot'];
        });
      } else {
        setState(() {
          slot['isSelected'] = false;
        });
      }
    });
  }

  selectTime(bool isStartTime, String slotId, startTime) async {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onChanged: (date) {
        updateTime(date, isStartTime, slotId);
      },
      onConfirm: (date) {
        updateTime(date, isStartTime, slotId);
      },
      currentTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        startTime.hour,
        startTime.minute,
      ),
      showSecondsColumn: false,
    );
  }

  updateTime(value, isStartTime, slotId) {
    if (value != null) {
      selectedSlots.forEach((slot) {
        if (slot['id'] == slotId) {
          setState(() {
            if (isStartTime) {
              slot['startTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            } else {
              slot['endTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            }
          });
        }
      });
    }
  }

  String addAndGetSportName(
      Map<String, dynamic> sport, List<dynamic> sportsName) {
    String sportName = sport['sport'] == null ? sport['title'] : sport['sport'];
    if (!sportsName.contains(sportName)) {
      sportsName.add(sportName);
    }
    return sportName;
  }

  toggleAutoFillPrice(bool newValue) {
    setState(() {
      isAutoFillPrice = newValue;

      if (isAutoFillPrice) {
        for (var i = 0; i < selectedSlots.length; i++) {
          for (var j = 0;
              j < selectedSlots[i]['priceAndQuantity'].length;
              j++) {
            if (i != 0) {
              if (selectedSlots[0]['priceAndQuantity'][j]['controller'].text !=
                  '') {
                selectedSlots[i]['priceAndQuantity'][j]['controller'].text =
                    selectedSlots[0]['priceAndQuantity'][j]['controller'].text;
                selectedSlots[i]['priceAndQuantity'][j]['price'] = int.parse(
                    selectedSlots[0]['priceAndQuantity'][j]['controller'].text);
                selectedSlots[0]['priceAndQuantity'][j]['price'] = int.parse(
                    selectedSlots[0]['priceAndQuantity'][j]['controller'].text);
              }
              if (selectedSlots[0]['priceAndQuantity'][j]['weekendController']
                      .text !=
                  '') {
                selectedSlots[i]['priceAndQuantity'][j]['weekendController']
                    .text = selectedSlots[0]['priceAndQuantity'][j]
                        ['weekendController']
                    .text;
                selectedSlots[i]['priceAndQuantity'][j]['weekendPrice'] =
                    int.parse(selectedSlots[0]['priceAndQuantity'][j]
                            ['weekendController']
                        .text);
                selectedSlots[0]['priceAndQuantity'][j]['weekendPrice'] =
                    int.parse(selectedSlots[0]['priceAndQuantity'][j]
                            ['weekendController']
                        .text);
              }
              if (selectedSlots[0]['priceAndQuantity'][j]['controller'].text ==
                  '') {
                selectedSlots[i]['priceAndQuantity'][j]['controller'].text = '';
                selectedSlots[i]['priceAndQuantity'][j]['price'] = 0;
                selectedSlots[0]['priceAndQuantity'][j]['price'] = 0;
              }
              if (selectedSlots[0]['priceAndQuantity'][j]['weekendController']
                      .text ==
                  '') {
                selectedSlots[i]['priceAndQuantity'][j]['weekendController']
                    .text = '';
                selectedSlots[i]['priceAndQuantity'][j]['weekendPrice'] = 0;
                selectedSlots[0]['priceAndQuantity'][j]['weekendPrice'] = 0;
              }
            }
          }
        }
      }
    });
  }

  validateSelection(sport) {
    if (!sport['isSelected']) {
      callToastMessage(
        isIndoorSelected
            ? "Please select the sport"
            : 'Please select the turf size',
      );
    }
  }

  //Widgets

  selectDaysBottomSheet({
    required BuildContext context,
    required bool isWeekdays,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => MultiSelectBottomSheet(
        listOfFields: getDays(isWeekdays),
        title: 'Select ${isWeekdays ? 'Weekdays' : 'Weekends'}',
        selectedFields: isWeekdays
            ? List<String>.from(selectedWeekDays)
            : List<String>.from(selectedWeekEnds),
      ),
    ).then((value) {
      if (value != null) {
        try {
          setState(() {
            if (isWeekdays) {
              selectedWeekDays = List<String>.from(value);
            } else {
              selectedWeekEnds = List<String>.from(value);
            }
          });
        } catch (e) {
          print(e);
        }
      }
    });
  }

  Widget buildDaysSelectionWidget({
    required BuildContext context,
    required bool isWeekdays,
    required List listOfIterate,
  }) {
    return GestureDetector(
      onTap: () =>
          selectDaysBottomSheet(context: context, isWeekdays: isWeekdays),
      child: CustomContainer(
        boxShadow: [],
        radius: 8,
        vPadding: 0.03,
        hPadding: 0,
        borderColor: getThemeColor(),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: dW * 0.7,
              child: (selectedWeekDays.isEmpty && isWeekdays) ||
                      (selectedWeekEnds.isEmpty && !isWeekdays)
                  ? Padding(
                      padding: EdgeInsets.only(left: dW * 0.03),
                      child: TextWidget(
                        title: 'Select ${isWeekdays ? 'weekdays' : 'weekends'}',
                        textAlign: TextAlign.left,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: BouncingScrollPhysics(),
                      child: Row(
                        children: [
                          SizedBox(width: dW * 0.03),
                          ...listOfIterate.map(
                            (day) => Container(
                              margin: EdgeInsets.only(right: dW * 0.02),
                              constraints: BoxConstraints(minWidth: dW * 0.13),
                              padding: EdgeInsets.symmetric(
                                vertical: dW * 0.015,
                                horizontal: dW * 0.02,
                              ),
                              decoration: BoxDecoration(
                                color: getThemeColor(),
                                borderRadius: BorderRadius.circular(50),
                                border: Border.all(color: getThemeColor()),
                              ),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: day,
                                  fontSize: 13,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: dW * 0.03),
                        ],
                      ),
                    ),
            ),
            Padding(
              padding: EdgeInsets.only(right: dW * 0.02),
              child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTimeSelectionWidget(
      {required String title, required String value, required Function onTap}) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: title,
            color: Color(0xff9798A3),
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.02),
          CustomContainer(
            boxShadow: [],
            width: dW * 0.4,
            hPadding: .03,
            borderColor: Color(0xffACACB4),
            radius: 7,
            bgColor: Color(0xffF8F9FD),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: value,
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                AssetSvgIcon(iconName: 'clock2', height: 22)
              ],
            ),
          )
        ],
      ),
    );
  }

  //Api Functions
  fetchSports() async {
    setState(() {
      isLoading = true;
    });

    listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
    print(listOfSports);
    listofSportCategory =
        Provider.of<Auth>(context, listen: false).listOfSportCategory;
    print(listofSportCategory);

    setState(() {
      isLoading = false;
    });
  }

  //Init Function
  myInit() {
    fetchSports();

    //Initially Selected Sports Category
    if (listofSportCategory.isNotEmpty) {
      listofSportCategory[0]['isSelected'] = true;
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    myInit();
  }

  @override
  void dispose() {
    super.dispose();
    venueNameController.dispose();
    venuePhoneController.dispose();
    venueDescriptionController.dispose();
    landmarkController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        children: [
          Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
            child: NewAppBar(dW: dW, title: ''),
          ),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextWidget(
                    title: 'Lets give venue title & small description',
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title: 'Now give your venue title and small description',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.08),
                    child: CustomTextFieldWithLabel(
                      label: 'Name',
                      controller: venueNameController,
                      hintText: 'Enter venue name',
                      borderRadius: 5,
                      labelFS: 14,
                      labelColor: Color(0xff9798A3),
                      labelFW: FontWeight.w500,
                      hintColor: Colors.black,
                      borderColor: Color(0xffACACB4),
                      fillColor: Color(0xffF8F9FD),
                      onChanged: (value) => setState(() {}),
                      inputAction: TextInputAction.next,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.08),
                    child: CustomTextFieldWithLabel(
                      label: 'Phone',
                      controller: venuePhoneController,
                      hintText: 'Enter venue phone no.',
                      borderRadius: 5,
                      labelFS: 14,
                      maxLength: 10,
                      labelColor: Color(0xff9798A3),
                      labelFW: FontWeight.w500,
                      hintColor: Colors.black,
                      borderColor: Color(0xffACACB4),
                      fillColor: Color(0xffF8F9FD),
                      onChanged: (value) => setState(() {}),
                      inputFormatter: [FilteringTextInputFormatter.digitsOnly],
                      inputType: TextInputType.number,
                      inputAction: TextInputAction.next,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.08),
                    child: CustomTextFieldWithLabel(
                      label: 'Description',
                      controller: venueDescriptionController,
                      hintText: 'Enter description here ',
                      borderRadius: 5,
                      labelFS: 14,
                      labelColor: Color(0xff9798A3),
                      labelFW: FontWeight.w500,
                      hintColor: Colors.black,
                      borderColor: Color(0xffACACB4),
                      fillColor: Color(0xffF8F9FD),
                      counterText:
                          '${venueDescriptionController.text.trim().length}/300',
                      maxLines: 6,
                      maxLength: 300,
                      textFS: 14,
                      textCapitalization: TextCapitalization.sentences,
                      inputType: TextInputType.streetAddress,
                      inputAction: TextInputAction.newline,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: TextWidget(
                      title: 'Where is your venue located?',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title:
                          'Specify your venue\'s location with a map pinpoint and nearby landmark details.',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.08),
                    child: Row(
                      children: [
                        TextWidget(
                          title: 'Venue Address',
                          color: Color(0xff9798A3),
                          fontWeight: FontWeight.w500,
                        ),
                        TextWidget(
                          title: '*',
                          color: redColor,
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: openSearchLocationBottomSheet,
                    child: CustomContainer(
                      margin: EdgeInsets.only(top: dW * 0.025),
                      width: dW,
                      boxShadow: [],
                      borderColor: Color(0xffACACB4),
                      bgColor: Color(0xffF8F9FD),
                      radius: 5,
                      hPadding: .03,
                      vPadding: .03,
                      child: TextWidget(
                        title: venueAddress == null
                            ? 'Select Address'
                            : venueAddress!.fullAddress.trim().isEmpty
                                ? '${venueAddress!.area.isEmpty ? venueAddress!.streetName : venueAddress!.area}, ${venueAddress!.landmark}, ${venueAddress!.city}, ${venueAddress!.state}, ${venueAddress!.pincode}.'
                                : '${venueAddress!.fullAddress}.',
                        fontWeight: venueAddress == null
                            ? FontWeight.normal
                            : FontWeight.w600,
                        fontSize: 15,
                        color: blackColor3,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: CustomTextFieldWithLabel(
                      label: 'Landmark',
                      controller: landmarkController,
                      hintText: 'Enter landmark here',
                      borderRadius: 5,
                      labelFS: 14,
                      labelColor: Color(0xff9798A3),
                      labelFW: FontWeight.w500,
                      hintColor: Colors.black,
                      borderColor: Color(0xffACACB4),
                      fillColor: Color(0xffF8F9FD),
                      onChanged: (value) => setState(() {}),
                      inputAction: TextInputAction.next,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.08),
                    child: TextWidget(
                      title: 'Describe your venue type',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.12),
                    child: TextWidget(
                      title:
                          'Select the category that best describes your venue, whether it\'s indoor, outdoor, or an esports arena.',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  ...listofSportCategory.map(
                    (e) => GestureDetector(
                      onTap: () {
                        setState(() {
                          for (var category in listofSportCategory) {
                            category['isSelected'] = false;
                          }
                          e['isSelected'] = true;
                          isIndoorSelected = e['title'] == "Indoor";
                          selectedSports = [];
                          for (var sport in listOfSports) {
                            sport['isSelected'] = false;
                          }
                          print(selectedSports);
                        });
                      },
                      child: CustomContainer(
                        margin: EdgeInsets.only(bottom: dW * 0.05),
                        boxShadow: [],
                        borderColor: e['isSelected']
                            ? getThemeColor()
                            : Color(0xffABABAB),
                        bgColor: e['isSelected']
                            ? getThemeColor()
                            : Colors.transparent,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  title: e['title'],
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: e['isSelected']
                                      ? Colors.white
                                      : Colors.black,
                                ),
                                SizedBox(height: dW * 0.02),
                                ConstrainedBox(
                                  constraints:
                                      BoxConstraints(maxWidth: dW * 0.6),
                                  child: TextWidget(
                                    title: getSportsName(e['id']),
                                    fontSize: 11,
                                    color: e['isSelected']
                                        ? Color(0xffD9D9D9)
                                        : Colors.black,
                                  ),
                                )
                              ],
                            ),
                            Image.asset(
                              'assets/images/${getImage(e['title'])}.png',
                              height: 30,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: TextWidget(
                      title: 'Now select which sports played at your venue',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title: 'Select the sports that you offered at your venue',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.1),
                    child: Wrap(
                      children: listOfSports
                          .where((e) {
                            if (isIndoorSelected) {
                              return e['title'] == 'Table Tennis' ||
                                  e['title'] == 'Badminton' ||
                                  e['title'] == 'Snooker';
                            }
                            return e['title'] == 'Cricket' ||
                                e['title'] == 'Football';
                          })
                          .map((e) => GestureDetector(
                                onTap: () {
                                  setState(() {
                                    e['isSelected'] = !e['isSelected'];
                                    if (e['isSelected']) {
                                      if (!selectedSports
                                          .contains(e['title'])) {
                                        selectedSports.add(e['title']);
                                        print(selectedSports);
                                      }
                                    } else {
                                      selectedSports.remove(e['title']);
                                      print(selectedSports);
                                    }
                                  });
                                },
                                child: Container(
                                  margin: EdgeInsets.only(
                                      right: dW * 0.03, bottom: dW * 0.03),
                                  width: e['title'] == 'Badminton'
                                      ? dW * 0.44
                                      : e['title'] == 'Table Tennis'
                                          ? dW * 0.5
                                          : dW * 0.4,
                                  padding: EdgeInsets.symmetric(
                                      vertical: dW * 0.025,
                                      horizontal: dW * 0.03),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: e['isSelected']
                                        ? getThemeColor()
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(color: getThemeColor()),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      ConstrainedBox(
                                        constraints:
                                            BoxConstraints(maxWidth: dW * 0.3),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: TextWidget(
                                            title: e['title'],
                                            fontSize: e['title'] == 'Badminton'
                                                ? 16
                                                : 18,
                                            fontWeight: FontWeight.w500,
                                            color: !e['isSelected']
                                                ? Colors.black
                                                : Colors.white,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: dW * .03),
                                      if (e['image'] != '') ...[
                                        !e['image'].contains('https')
                                            ? Image.asset(e['image'],
                                                height: 35)
                                            : CachedNetworkImage(
                                                imageUrl: e['image'],
                                                height: 30,
                                                fit: BoxFit.cover,
                                                placeholder: (_, __) =>
                                                    Image.asset(
                                                  'assets/images/user.png',
                                                  fit: BoxFit.cover,
                                                  height: 35,
                                                ),
                                              ),
                                      ],
                                    ],
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: TextWidget(
                      title:
                          'Let’s describe weekdays and weekends for you venue',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title:
                          'Select the venue weekends and weekdays, whether it\'s outdoor, indoor or an esports arena.',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.1, bottom: dW * 0.03),
                    child: TextWidget(
                      title: 'Weekdays',
                      fontWeight: FontWeight.w500,
                      color: Color(0xff9798A3),
                    ),
                  ),
                  buildDaysSelectionWidget(
                    context: context,
                    isWeekdays: true,
                    listOfIterate: selectedWeekDays,
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(top: dW * 0.075, bottom: dW * 0.03),
                    child: TextWidget(
                      title: 'Weekend Days',
                      fontWeight: FontWeight.w500,
                      color: Color(0xff9798A3),
                    ),
                  ),
                  buildDaysSelectionWidget(
                    context: context,
                    isWeekdays: false,
                    listOfIterate: selectedWeekEnds,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: TextWidget(
                      title: 'Now select time slots',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title:
                          'Select the time slots in which your venues are open for the users',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.1),
                    child: Wrap(
                      children: [
                        ...listOfSlots.map(
                          (slot) => GestureDetector(
                            onTap: () {
                              setState(() {
                                slot['isSelected'] = !slot['isSelected'];
                                if (slot['isSelected']) {
                                  selectedSlots.add(slot);
                                  print(selectedSlots);
                                } else {
                                  selectedSlots.remove(slot);
                                }
                              });
                            },
                            child: IntrinsicWidth(
                              child: Container(
                                margin: EdgeInsets.only(
                                    right: dW * 0.03, bottom: dW * 0.03),
                                padding: EdgeInsets.symmetric(
                                    vertical: dW * 0.025,
                                    horizontal: dW * 0.03),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: slot['isSelected']
                                      ? getThemeColor()
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(100),
                                  border: Border.all(color: getThemeColor()),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    TextWidget(
                                      title: slot['title'],
                                      fontSize: slot['title'] == 'Badminton'
                                          ? 16
                                          : 18,
                                      fontWeight: FontWeight.w500,
                                      color: !slot['isSelected']
                                          ? Colors.black
                                          : Colors.white,
                                    ),
                                    SizedBox(width: dW * .03),
                                    slot['title'] == 'Late Night'
                                        ? Image.asset(
                                            'assets/images/late_night.png',
                                            height: dW * 0.06,
                                          )
                                        : Image.asset(
                                            'assets/images/${slot['title'].toLowerCase()}.png',
                                            height: dW * 0.06,
                                          ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  if (isIndoorSelected &&
                      (selectedSports.contains('Snooker') ||
                          selectedSports.contains('Table Tennis') ||
                          selectedSports.contains('Badminton')))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: Text(
                        'Specify the game offerings at your venue',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  if (isIndoorSelected &&
                      (selectedSports.contains('Snooker') ||
                          selectedSports.contains('Table Tennis') ||
                          selectedSports.contains('Badminton')))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...listOfSports
                              .where((sport) => sport['isSelected'])
                              .toList()
                              .asMap()
                              .entries
                              .map((entry) {
                            final index = entry.key;
                            final sport = entry.value;
                            bool isSingle = sport['single'] ?? false;
                            bool isDouble = sport['double'] ?? false;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    if (sport['image'] != '')
                                      sport['image'].contains('https')
                                          ? Container(
                                              padding:
                                                  EdgeInsets.all(dW * 0.01),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: getThemeColor(),
                                                  width: 1,
                                                ),
                                              ),
                                              child: CachedNetworkImage(
                                                imageUrl: sport['image'],
                                                height: 16,
                                                fit: BoxFit.cover,
                                              ),
                                            )
                                          : Container(
                                              padding:
                                                  EdgeInsets.all(dW * 0.01),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: getThemeColor(),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Image.asset(sport['image'],
                                                  height: 30),
                                            ),
                                    SizedBox(width: dW * 0.03),
                                    Text(
                                      sport['title'],
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xff3E3E3E),
                                      ),
                                    ),
                                  ],
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      sport['single'] = !isSingle;
                                      isSingleChecked = sport['single'];
                                    });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        bottom: dW * 0.03, top: dW * 0.03),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04,
                                      vertical: dW * 0.06,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        width: 1,
                                        color: isSingle
                                            ? Colors.green
                                            : Colors.grey,
                                      ),
                                    ),
                                    width: dW,
                                    child: Row(
                                      children: [
                                        SizedBox(width: dW * 0.04),
                                        Expanded(
                                          child: CustomCheckbox(
                                            title: 'Singles',
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                            textColor: isSingle
                                                ? getThemeColor()
                                                : Colors.black,
                                            onChanged: () {
                                              setState(() {
                                                sport['single'] = !isSingle;
                                                isSingleChecked =
                                                    sport['single'];
                                              });
                                            },
                                            value: isSingle,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      sport['double'] = !isDouble;
                                      isDoubleChecked = sport['double'];
                                    });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(bottom: dW * 0.06),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04,
                                      vertical: dW * 0.06,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        width: 1,
                                        color: isDouble
                                            ? Colors.green
                                            : Colors.grey,
                                      ),
                                    ),
                                    width: dW,
                                    child: Row(
                                      children: [
                                        SizedBox(width: dW * 0.04),
                                        Expanded(
                                          child: CustomCheckbox(
                                            title: 'Doubles',
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                            textColor: isDouble
                                                ? getThemeColor()
                                                : Colors.black,
                                            onChanged: () {
                                              setState(() {
                                                sport['double'] = !isDouble;
                                                isDoubleChecked =
                                                    sport['double'];
                                              });
                                            },
                                            value: isDouble,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (index == 0 && selectedSports.length > 1)
                                  CustomCheckbox2(
                                    value: isSameForAllSports,
                                    spaceBetween: dW * 0.04,
                                    size: 15,
                                    reverseCheckBox: true,
                                    onChanged: toggleSameForAllSports,
                                    activeColor: getThemeColor(),
                                    title: 'Same for all sports',
                                  ),
                                if (index !=
                                    listOfSports
                                            .where(
                                                (sport) => sport['isSelected'])
                                            .toList()
                                            .length -
                                        1)
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: dW * 0.07),
                                    child: Divider(
                                      color: Color(0xffD9D9D9),
                                      thickness: 1,
                                    ),
                                  ),
                              ],
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  if (isIndoorSelected &&
                      (selectedSports.contains('Snooker') ||
                          selectedSports.contains('Badminton')))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'Now add type of courts and tables available at your venue',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  if (isIndoorSelected &&
                      (selectedSports.contains('Snooker') ||
                          selectedSports.contains('Badminton')))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...listOfCourtsAndTables
                              .where((e) => selectedSports.contains(e['sport']))
                              .toList()
                              .asMap()
                              .entries
                              .map((entry) {
                            final index = entry.key;
                            final sportData = entry.value;
                            final isLastItem = index ==
                                listOfCourtsAndTables
                                        .where((e) =>
                                            selectedSports.contains(e['sport']))
                                        .length -
                                    1;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      'assets/images/${sportData['sport']}.png',
                                      height: 24,
                                    ),
                                    SizedBox(
                                      width: dW * 0.02,
                                    ),
                                    TextWidget(
                                      title: '${sportData['sport']}',
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff3E3E3E),
                                    ),
                                  ],
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      sportData['isSelected'] =
                                          !sportData['isSelected'];
                                    });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        bottom: dW * 0.04, top: dW * 0.04),
                                    padding: EdgeInsets.only(
                                      left: dW * 0.04,
                                      top: dW * 0.035,
                                      right: dW * 0.045,
                                      bottom: dW * 0.035,
                                    ),
                                    decoration: BoxDecoration(
                                      color: sportData['isSelected']
                                          ? getThemeColor()
                                          : whiteColor,
                                      border: Border.all(
                                        color: getThemeColor(),
                                      ),
                                      borderRadius: BorderRadius.circular(55),
                                    ),
                                    child: IntrinsicWidth(
                                      child: Row(
                                        children: [
                                          Text(
                                            sportData['title'],
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 20,
                                              color: sportData['isSelected']
                                                  ? whiteColor
                                                  : Color(0xff5E5E5E),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                if (!isLastItem)
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: dW * 0.07),
                                    child: Divider(
                                      color: Color(0xffD9D9D9),
                                      thickness: 1,
                                    ),
                                  ),
                              ],
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  if (!isIndoorSelected && selectedSports.contains('Cricket') ||
                      selectedSports.contains('Football'))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: TextWidget(
                        title:
                            'Which type of turf option is available at your venue?',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  if (!isIndoorSelected && selectedSports.contains('Cricket') ||
                      selectedSports.contains('Football'))
                    Padding(
                      padding:
                          EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.1),
                      child: TextWidget(
                        title: 'Add type of turf available at your venue',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                  ...listOfTurfOption
                      .where((option) =>
                          !isIndoorSelected &&
                              selectedSports.contains('Cricket') ||
                          selectedSports.contains('Football'))
                      .map(
                        (option) => GestureDetector(
                          onTap: () {
                            setState(() {
                              for (var turfOption in listOfTurfOption) {
                                turfOption['isSelected'] = false;
                              }
                              option['isSelected'] = true;
                            });
                            setTurfOption(option['title']);
                          },
                          child: CustomContainer(
                            margin: EdgeInsets.only(bottom: dW * 0.05),
                            boxShadow: [],
                            borderColor: option['isSelected']
                                ? getThemeColor()
                                : Color(0xffABABAB),
                            bgColor: option['isSelected']
                                ? getThemeColor()
                                : Colors.transparent,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextWidget(
                                      title: getTitle(option['title']),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: option['isSelected']
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                    SizedBox(height: dW * 0.02),
                                    ConstrainedBox(
                                      constraints:
                                          BoxConstraints(maxWidth: dW * 0.6),
                                      child: TextWidget(
                                        title: getSubTitle(option['title']),
                                        fontSize: 11,
                                        color: option['isSelected']
                                            ? Color(0xffD9D9D9)
                                            : Colors.black,
                                      ),
                                    )
                                  ],
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                        color: getThemeColor(), width: 2),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.asset(
                                      'assets/images/${(option['title'] as String).replaceAll(' ', '').trim().toLowerCase()}.png',
                                      scale: 1.7,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                  if (!isIndoorSelected && selectedSports.contains('Cricket') ||
                      selectedSports.contains('Football'))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Now add turf sizes available at your venue',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  if (!isIndoorSelected && selectedSports.contains('Cricket') ||
                      selectedSports.contains('Football'))
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: Wrap(
                        children: [
                          ...listOfTurfSizeAndPrice.map(
                            (size) => GestureDetector(
                              onTap: size['title'] == '1:1'
                                  ? null
                                  : () {
                                      if (!checkTurfSize(size['title'])) {
                                        selectAndUnSelectType(
                                          size['id'],
                                          size['isSelected'],
                                          listOfTurfSizeAndPrice,
                                          selectedTurfSize,
                                          false,
                                        );
                                        setUsePreviousValueForPrice();
                                      }
                                    },
                              child: Container(
                                margin: EdgeInsets.only(
                                    right: dW * 0.03, bottom: dW * 0.03),
                                constraints: BoxConstraints(
                                  minWidth: size['title'] == '1:1'
                                      ? dW * 0.12
                                      : dW * 0.15,
                                ),
                                padding: EdgeInsets.symmetric(
                                    vertical: dW * 0.02, horizontal: dW * 0.05),
                                decoration: BoxDecoration(
                                  color: checkTurfSize(size['title'])
                                      ? Colors.grey.shade300
                                      : size['isSelected']
                                          ? Theme.of(context).primaryColor
                                          : Colors.white,
                                  borderRadius: BorderRadius.circular(100),
                                  border: Border.all(
                                    color: checkTurfSize(size['title'])
                                        ? Colors.black12
                                        : size['isSelected']
                                            ? Theme.of(context).primaryColor
                                            : Color(0xff5E5E5E),
                                  ),
                                ),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title: size['title']
                                        .toString()
                                        .replaceAll(':', ' x '),
                                    color: checkTurfSize(size['title'])
                                        ? Colors.grey.shade500
                                        : size['isSelected']
                                            ? Colors.white
                                            : Colors.black,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (selectedSlots.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Let’s set timings for your venue',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  if (selectedSlots.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: TextWidget(
                        title: 'Time Slot Duration',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  if (selectedSlots.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.035),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ...listofSlotTime.map(
                            (slot) => GestureDetector(
                              onTap: () => selectSlotDuration(slot['id']),
                              child: CustomContainer(
                                boxShadow: [],
                                vPadding: .03,
                                radius: 7,
                                borderColor:
                                    slot['slot'] == selectedSlotDuration
                                        ? getThemeColor()
                                        : Color.fromRGBO(172, 172, 180, 1),
                                bgColor: slot['slot'] == selectedSlotDuration
                                    ? getThemeColor()
                                    : Colors.transparent,
                                width: dW * 0.38,
                                child: TextWidget(
                                  title: '${slot['slot']} mins',
                                  color: slot['slot'] == selectedSlotDuration
                                      ? Colors.white
                                      : Color(0xff242530),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 17,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  DividerWidget(top: 20, bottom: 0, color: Color(0xffD9D9D9)),
                  ...selectedSlots
                      .asMap()
                      .map(
                        (i, slot) => MapEntry(
                          i,
                          Container(
                            padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                            decoration: i == selectedSlots.length - 1
                                ? null
                                : BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: Color(0xffD9D9D9)))),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  title: '${slot['title']} Slot',
                                  color: getThemeColor(),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                SizedBox(height: dW * 0.03),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    buildTimeSelectionWidget(
                                      title: 'Start Time',
                                      value:
                                          '${slot['startTime'].format(context) == '00:00' ? '12:00' : slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                      onTap: () => selectTime(
                                          true, slot['id'], slot['startTime']),
                                    ),
                                    SizedBox(height: dW * 0.05),
                                    buildTimeSelectionWidget(
                                      title: 'End Time',
                                      value:
                                          '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                      onTap: () => selectTime(
                                          false, slot['id'], slot['endTime']),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                      .values
                      .toList(),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.05),
                    child: TextWidget(
                      title: 'Now add sports pricing',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.03),
                    child: TextWidget(
                      title:
                          'Include the taxes while adding price for $selectedSlotDuration mins, you can change this later.',
                      fontSize: 12,
                      color: Color(0xff21272A),
                    ),
                  ),

                  //salman
                  ...selectedSlots
                      .asMap()
                      .map((i, slot) {
                        return MapEntry(
                          i,
                          Container(
                            padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                            decoration: i == selectedSlots.length - 1
                                ? null
                                : BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: Color(0xffD9D9D9)))),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      'assets/images/${slot['title'].toLowerCase()}.png',
                                      height: 18,
                                    ),
                                    SizedBox(
                                      width: dW * 0.02,
                                    ),
                                    TextWidget(
                                      title: '${slot['title']} Slot',
                                      color: Color(0xff3E3E3E),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ],
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                      top: dW * 0.06, bottom: dW * 0.02),
                                  padding: EdgeInsets.all(dW * 0.04),
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Color(0xffD9D9D9),
                                      ),
                                      borderRadius: BorderRadius.circular(8)),
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          ...[
                                            isIndoorSelected
                                                ? 'Sport'
                                                : 'Turf Size',
                                            'Weekdays',
                                            'Weekends'
                                          ].map(
                                            (type) => Container(
                                              alignment: Alignment.topLeft,
                                              width: type == 'Weekends' ||
                                                      type == 'Weekdays'
                                                  ? dW * 0.21
                                                  : dW * .24,
                                              child: FittedBox(
                                                fit: BoxFit.scaleDown,
                                                child: TextWidget(
                                                  title: type,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: dW * 0.03),
                                      ...slot['priceAndQuantity'].map(
                                        (sport) {
                                          sport['isSelected'] = true;

                                          return Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              if (!sportsName
                                                  .contains(sport['sport']))
                                                Container(
                                                  margin: EdgeInsets.only(
                                                      bottom: dW * 0.02,
                                                      top: dW * 0.02),
                                                  child: TextWidget(
                                                    title: addAndGetSportName(
                                                        sport, selectedSports),
                                                    color: getThemeColor(),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  GestureDetector(
                                                    onTap: () {},
                                                    child: Container(
                                                      margin: EdgeInsets.only(
                                                        // right: dW * 0.03,
                                                        bottom: dW * 0.03,
                                                        top: sport['sport'] ==
                                                                'Snooker'
                                                            ? dW * 0.05
                                                            : dW * 0.03,
                                                      ),
                                                      width: dW * 0.22,
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical:
                                                                  dW * 0.03,
                                                              horizontal:
                                                                  dW * 0.02),
                                                      decoration: BoxDecoration(
                                                        color: sport[
                                                                'isSelected']
                                                            ? getThemeColor()
                                                            : Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(7),
                                                        border: Border.all(
                                                            color:
                                                                getThemeColor()),
                                                      ),
                                                      child: FittedBox(
                                                        fit: BoxFit.scaleDown,
                                                        child: TextWidget(
                                                          title: sport['title'],
                                                          fontSize:
                                                              isIndoorSelected
                                                                  ? tS * 14
                                                                  : tS * 12,
                                                          color: sport[
                                                                  'isSelected']
                                                              ? Colors.white
                                                              : getThemeColor(),
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  GestureDetector(
                                                    onTap: () {
                                                      validateSelection(sport);
                                                    },
                                                    child: Container(
                                                      margin: EdgeInsets.only(
                                                          top: sport['sport'] ==
                                                                  'Snooker'
                                                              ? dW * 0.04
                                                              : dW * 0.02),
                                                      width: dW * 0.23,
                                                      child:
                                                          CustomTextFieldWithLabel(
                                                        label: '',
                                                        controller:
                                                            sport['controller'],
                                                        textAlign:
                                                            TextAlign.left,
                                                        contentPadding:
                                                            EdgeInsets.symmetric(
                                                                vertical:
                                                                    dW * 0.04),
                                                        prefixIcon: Icon(
                                                          Icons
                                                              .currency_rupee_sharp,
                                                          size: 16,
                                                          color: blackColor3,
                                                        ),
                                                        // hintText: 'Price',
                                                        hintText: '',
                                                        hintFS: 12,
                                                        borderRadius: 7,
                                                        textFS: 13.5,
                                                        inputFormatter: [
                                                          FilteringTextInputFormatter
                                                              .digitsOnly
                                                        ],
                                                        enabled:
                                                            sport['isSelected']
                                                                ? true
                                                                : false,
                                                        maxLength: 6,
                                                        inputType: TextInputType
                                                            .number,
                                                        onChanged: (value) {
                                                          toggleAutoFillPrice(
                                                              isAutoFillPrice);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  GestureDetector(
                                                    onTap: () {
                                                      validateSelection(sport);
                                                    },
                                                    child: Container(
                                                      margin: EdgeInsets.only(
                                                          top: sport['sport'] ==
                                                                  'Snooker'
                                                              ? dW * 0.04
                                                              : dW * 0.02),
                                                      width: dW * 0.23,
                                                      child:
                                                          CustomTextFieldWithLabel(
                                                        label: '',
                                                        controller: sport[
                                                            'weekendController'],
                                                        contentPadding:
                                                            EdgeInsets.symmetric(
                                                                vertical:
                                                                    dW * 0.04),
                                                        prefixIcon: Icon(
                                                          Icons
                                                              .currency_rupee_sharp,
                                                          size: 16,
                                                          color: blackColor3,
                                                        ),
                                                        // hintText: 'Price',
                                                        hintText: '',
                                                        borderRadius: 7,
                                                        hintFS: 12,
                                                        textFS: 13.5,
                                                        inputFormatter: [
                                                          FilteringTextInputFormatter
                                                              .digitsOnly
                                                        ],
                                                        maxLength: 6,
                                                        inputType: TextInputType
                                                            .number,
                                                        onChanged: (value) {
                                                          toggleAutoFillPrice(
                                                              isAutoFillPrice);
                                                        },
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                              // Container(
                                              //     margin:
                                              //         EdgeInsets.only(top: dW * 0.02),
                                              //     child: Divider(color: blackColor3)),
                                            ],
                                          );
                                        },
                                      ),

                                      // if (i == 0)
                                      if (selectedSlots.length != 1)
                                        SizedBox(
                                          height: dW * 0.04,
                                        ),
                                      // if (i == 0)
                                      if (selectedSlots.length != 1)
                                        CustomCheckbox2(
                                          value: isAutoFillPrice,
                                          spaceBetween: dW * 0.02,
                                          textColor: Color(0xff3E3E3E),
                                          size: 15,
                                          reverseCheckBox: true,
                                          borderColor: Color(0xff3E3E3E)
                                              .withOpacity(0.7),
                                          onChanged: () => toggleAutoFillPrice(
                                              !isAutoFillPrice),
                                          activeColor: getThemeColor(),
                                          title: isIndoorSelected
                                              ? 'Same for all turf sizes'
                                              : 'Same for all',
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      })
                      .values
                      .toList(),
                ],
              ),
            ),
          ),
          BottomAlignedWidget(
            dW: dW,
            dH: dH,
            child: Column(
              children: [
                CustomButton(
                  width: dW * 0.9,
                  height: dW * 0.12,
                  fontSize: 16,
                  buttonText: 'Preview',
                  onPressed: () {},
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.04),
                  child: TextWidget(
                    title:
                        'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
