import 'dart:io';

import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class HomePageSkeletonLoader extends StatelessWidget {
  HomePageSkeletonLoader({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;
  final List<double> noOfBoxes = [1, 2, 3, 4];

  @override
  Widget build(BuildContext context) {
    // return SingleChildScrollView(
    //   child: Container(
    //     margin: EdgeInsets.symmetric(
    //       vertical: deviceWidth * 0.05,
    //       horizontal: deviceWidth * 0.05,
    //     ),
    //     child: Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         // Shimmer.fromColors(
    //         //   baseColor: Colors.black12.withOpacity(0.2),
    //         //   highlightColor: Colors.white.withOpacity(0.5),
    //         //   child: Row(
    //         //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //         //     children: [
    //         //       Column(
    //         //         crossAxisAlignment: CrossAxisAlignment.start,
    //         //         children: [
    //         //           Container(
    //         //             margin:
    //         //                 EdgeInsets.symmetric(vertical: deviceWidth * 0.01),
    //         //             height: deviceWidth * 0.025,
    //         //             width: deviceWidth * 0.2,
    //         //             decoration: BoxDecoration(
    //         //                 color: Colors.red.withOpacity(0.4),
    //         //                 borderRadius: BorderRadius.circular(10)),
    //         //           ),
    //         //           Container(
    //         //             margin:
    //         //                 EdgeInsets.symmetric(vertical: deviceWidth * 0.01),
    //         //             height: deviceWidth * 0.025,
    //         //             width: deviceWidth * 0.45,
    //         //             decoration: BoxDecoration(
    //         //                 color: Colors.red.withOpacity(0.4),
    //         //                 borderRadius: BorderRadius.circular(10)),
    //         //           ),
    //         //         ],
    //         //       ),
    //         //       Container(
    //         //         height: deviceWidth * 0.1,
    //         //         width: deviceWidth * 0.1,
    //         //         decoration: BoxDecoration(
    //         //             color: Colors.red.withOpacity(0.4),
    //         //             shape: BoxShape.circle),
    //         //       ),
    //         //     ],
    //         //   ),
    //         // ),
    //         Shimmer.fromColors(
    //           baseColor: Colors.black12.withOpacity(0.2),
    //           highlightColor: Colors.white.withOpacity(0.5),
    //           child: Container(
    //             margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.03),
    //             height: deviceWidth * 0.04,
    //             width: deviceWidth * 0.35,
    //             decoration: BoxDecoration(
    //                 color: Colors.red.withOpacity(0.4),
    //                 borderRadius: BorderRadius.circular(10)),
    //           ),
    //         ),
    //         SingleChildScrollView(
    //           scrollDirection: Axis.horizontal,
    //           child: Row(
    //             children: [
    //               ...noOfBoxes
    //                   .map((e) => RectangularBoxes(deviceWidth: deviceWidth)),
    //             ],
    //           ),
    //         ),
    //         SizedBox(height: deviceWidth * 0.05),
    //         ...[1, 2, 3].map(
    //           (e) => Row(
    //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //             children: [
    //               Row(
    //                 children: [
    //                   Container(
    //                     margin:
    //                         EdgeInsets.symmetric(vertical: deviceWidth * 0.04),
    //                     height: deviceWidth * 0.13,
    //                     width: deviceWidth * 0.13,
    //                     child: Shimmer.fromColors(
    //                         child: Container(
    //                           alignment: Alignment.topLeft,
    //                           decoration: BoxDecoration(
    //                             borderRadius: BorderRadius.circular(8),
    //                             color: Colors.red.withOpacity(0.4),
    //                             // shape: BoxShape.circle,
    //                           ),
    //                         ),
    //                         baseColor: Colors.black12.withOpacity(0.2),
    //                         highlightColor: Colors.white.withOpacity(0.5)),
    //                   ),
    //                   SizedBox(width: deviceWidth * 0.04),
    //                   Column(
    //                     crossAxisAlignment: CrossAxisAlignment.start,
    //                     children: [
    //                       Container(
    //                         margin: EdgeInsets.symmetric(
    //                             vertical: deviceWidth * 0.01),
    //                         height: deviceWidth * 0.03,
    //                         width: deviceWidth * 0.25,
    //                         child: Shimmer.fromColors(
    //                             child: Container(
    //                               alignment: Alignment.topLeft,
    //                               decoration: BoxDecoration(
    //                                 borderRadius: BorderRadius.circular(8),
    //                                 color: Colors.red.withOpacity(0.4),
    //                                 // shape: BoxShape.circle,
    //                               ),
    //                             ),
    //                             baseColor: Colors.black12.withOpacity(0.2),
    //                             highlightColor: Colors.white.withOpacity(0.5)),
    //                       ),
    //                       Container(
    //                         margin: EdgeInsets.symmetric(
    //                             vertical: deviceWidth * 0.01),
    //                         height: deviceWidth * 0.03,
    //                         width: deviceWidth * 0.22,
    //                         child: Shimmer.fromColors(
    //                             child: Container(
    //                               alignment: Alignment.topLeft,
    //                               decoration: BoxDecoration(
    //                                 borderRadius: BorderRadius.circular(8),
    //                                 color: Colors.red.withOpacity(0.4),
    //                                 // shape: BoxShape.circle,
    //                               ),
    //                             ),
    //                             baseColor: Colors.black12.withOpacity(0.2),
    //                             highlightColor: Colors.white.withOpacity(0.5)),
    //                       ),
    //                     ],
    //                   )
    //                 ],
    //               ),
    //               Icon(
    //                 Icons.arrow_forward_ios,
    //                 size: 20,
    //                 color: Colors.grey.shade300,
    //               ),
    //             ],
    //           ),
    //         )
    //       ],
    //     ),
    //   ),
    // );

    return Center(
      child: Platform.isAndroid
          ? MaterialCircularLoader(deviceWidth * 0.07)
          : CupertinoCircularLoader(15.0),
    );
  }
}

class RectangularBoxes extends StatelessWidget {
  const RectangularBoxes({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.black12.withOpacity(0.2),
      highlightColor: Colors.white.withOpacity(0.5),
      child: Container(
        margin: EdgeInsets.only(right: deviceWidth * 0.04),
        height: deviceWidth * 0.27,
        width: deviceWidth * 0.27,
        decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.4),
            borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
