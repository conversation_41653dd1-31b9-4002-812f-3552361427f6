import 'dart:io';

import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BookTurfOfflineSkeletonLoader extends StatelessWidget {
  BookTurfOfflineSkeletonLoader({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    // return SingleChildScrollView(
    //   child: Container(
    //     margin: EdgeInsets.symmetric(
    //       vertical: deviceWidth * 0.06,
    //       horizontal: deviceWidth * 0.06,
    //     ),
    //     child: Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         ...[1, 2, 3, 4, 5, 6, 7].map(
    //           (e) => Column(
    //             crossAxisAlignment: CrossAxisAlignment.start,
    //             children: [
    //               Container(
    //                 height: deviceWidth * 0.025,
    //                 width: deviceWidth * 0.15,
    //                 child: Shimmer.fromColors(
    //                     child: Container(
    //                       alignment: Alignment.topLeft,
    //                       decoration: BoxDecoration(
    //                         borderRadius: BorderRadius.circular(2),
    //                         color: Colors.red.withOpacity(0.4),
    //                         // shape: BoxShape.circle,
    //                       ),
    //                     ),
    //                     baseColor: Colors.black12.withOpacity(0.2),
    //                     highlightColor: Colors.white.withOpacity(0.5)),
    //               ),
    //               Container(
    //                 margin: EdgeInsets.only(
    //                     top: deviceWidth * 0.02, bottom: deviceWidth * 0.06),
    //                 height: deviceWidth * 0.12,
    //                 width: double.infinity,
    //                 child: Shimmer.fromColors(
    //                     child: Container(
    //                       alignment: Alignment.topLeft,
    //                       decoration: BoxDecoration(
    //                         borderRadius: BorderRadius.circular(35),
    //                         color: Colors.red.withOpacity(0.4),
    //                         // shape: BoxShape.circle,
    //                       ),
    //                     ),
    //                     baseColor: Colors.black12.withOpacity(0.2),
    //                     highlightColor: Colors.white.withOpacity(0.5)),
    //               ),
    //             ],
    //           ),
    //         )
    //       ],
    //     ),
    //   ),
    // );
    return Center(
      child: Platform.isAndroid
          ? MaterialCircularLoader(deviceWidth * 0.07)
          : CupertinoCircularLoader(15.0),
    );
  }
}
