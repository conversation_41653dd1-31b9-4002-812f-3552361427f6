import 'dart:io';

import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class TurfScreenSkeletonLoader extends StatelessWidget {
  TurfScreenSkeletonLoader({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;
  final List<double> noOfTurfs = [1, 2];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: deviceWidth * 0.02,
          horizontal: deviceWidth * 0.05,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...noOfTurfs.map(
              (e) => TurfWidgetSkeleton(deviceWidth: deviceWidth),
            ),
          ],
        ),
      ),
    );
  }
}

class TurfWidgetSkeleton extends StatelessWidget {
  const TurfWidgetSkeleton({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    // return Row(
    //   children: [
    //     Container(
    //       margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.02),
    //       height: deviceWidth * 0.25,
    //       width: deviceWidth * 0.3,
    //       child: Shimmer.fromColors(
    //           child: Container(
    //             alignment: Alignment.topLeft,
    //             decoration: BoxDecoration(
    //               borderRadius: BorderRadius.circular(8),
    //               color: Colors.red.withOpacity(0.4),
    //               // shape: BoxShape.circle,
    //             ),
    //           ),
    //           baseColor: Colors.black12.withOpacity(0.2),
    //           highlightColor: Colors.white.withOpacity(0.5)),
    //     ),
    //     SizedBox(width: deviceWidth * 0.04),
    //     Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         Container(
    //           margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.01),
    //           height: deviceWidth * 0.03,
    //           width: deviceWidth * 0.3,
    //           child: Shimmer.fromColors(
    //               child: Container(
    //                 alignment: Alignment.topLeft,
    //                 decoration: BoxDecoration(
    //                   borderRadius: BorderRadius.circular(8),
    //                   color: Colors.red.withOpacity(0.4),
    //                   // shape: BoxShape.circle,
    //                 ),
    //               ),
    //               baseColor: Colors.black12.withOpacity(0.2),
    //               highlightColor: Colors.white.withOpacity(0.5)),
    //         ),
    //         Container(
    //           margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.01),
    //           height: deviceWidth * 0.03,
    //           width: deviceWidth * 0.36,
    //           child: Shimmer.fromColors(
    //               child: Container(
    //                 alignment: Alignment.topLeft,
    //                 decoration: BoxDecoration(
    //                   borderRadius: BorderRadius.circular(8),
    //                   color: Colors.red.withOpacity(0.4),
    //                   // shape: BoxShape.circle,
    //                 ),
    //               ),
    //               baseColor: Colors.black12.withOpacity(0.2),
    //               highlightColor: Colors.white.withOpacity(0.5)),
    //         ),
    //         Container(
    //           margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.01),
    //           height: deviceWidth * 0.03,
    //           width: deviceWidth * 0.17,
    //           child: Shimmer.fromColors(
    //               child: Container(
    //                 alignment: Alignment.topLeft,
    //                 decoration: BoxDecoration(
    //                   borderRadius: BorderRadius.circular(8),
    //                   color: Colors.red.withOpacity(0.4),
    //                   // shape: BoxShape.circle,
    //                 ),
    //               ),
    //               baseColor: Colors.black12.withOpacity(0.2),
    //               highlightColor: Colors.white.withOpacity(0.5)),
    //         ),
    //         SingleChildScrollView(
    //           scrollDirection: Axis.horizontal,
    //           child: Row(
    //             mainAxisAlignment: MainAxisAlignment.start,
    //             children: [
    //               ...[1, 2, 3, 4]
    //                   .map((e) => Circles(deviceWidth: deviceWidth / 2.5)),
    //             ],
    //           ),
    //         ),
    //       ],
    //     )
    //   ],
    // );
    return Center(
      child: Platform.isAndroid
          ? MaterialCircularLoader(deviceWidth * 0.07)
          : CupertinoCircularLoader(15.0),
    );
  }
}

class Circles extends StatelessWidget {
  const Circles({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      child: Container(
        margin: EdgeInsets.only(
            top: deviceWidth * 0.04,
            bottom: deviceWidth * 0.04,
            right: deviceWidth * 0.04),
        height: deviceWidth * 0.12,
        width: deviceWidth * 0.12,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
      ),
      baseColor: Colors.black12.withOpacity(0.2),
      highlightColor: Colors.white.withOpacity(0.5),
    );
  }
}
