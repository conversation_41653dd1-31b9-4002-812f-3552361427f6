import 'dart:io';

import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BookTurfOfflineSummarySkeletonLoader extends StatelessWidget {
  BookTurfOfflineSummarySkeletonLoader({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;
  final List<double> noOfList = [
    1,
    2,
    3,
    4,
    5,
    6,
    7,
  ];

  @override
  Widget build(BuildContext context) {
    // return SingleChildScrollView(
    //   child: Container(
    //     margin: EdgeInsets.symmetric(
    //       vertical: deviceWidth * 0.08,
    //       horizontal: deviceWidth * 0.05,
    //     ),
    //     child: Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         Container(
    //           // height: deviceWidth * 0.08,
    //           // width: deviceWidth * 0.55,
    //           child: Shimmer.fromColors(
    //             child: Container(
    //               padding: EdgeInsets.all(10),
    //               alignment: Alignment.topLeft,
    //               decoration: BoxDecoration(
    //                 borderRadius: BorderRadius.circular(8),
    //                 color: Colors.red.withOpacity(0.4),
    //                 // shape: BoxShape.circle,
    //               ),
    //               child: Column(
    //                 mainAxisSize: MainAxisSize.min,
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //                   Container(
    //                     height: deviceWidth * 0.05,
    //                     width: deviceWidth * 0.35,
    //                     decoration:
    //                         BoxDecoration(color: Colors.red.withOpacity(0.4)),
    //                   ),
    //                   ...noOfList.map(
    //                       (e) => SummaryListTile(deviceWidth: deviceWidth)),
    //                 ],
    //               ),
    //             ),
    //             baseColor: Colors.black12.withOpacity(0.2),
    //             highlightColor: Colors.white.withOpacity(0.5),
    //           ),
    //         ),
    //         Shimmer.fromColors(
    //           baseColor: Colors.black12.withOpacity(0.2),
    //           highlightColor: Colors.white.withOpacity(0.5),
    //           child: Container(
    //             height: deviceWidth * 0.12,
    //             margin: EdgeInsets.symmetric(vertical: deviceWidth*0.1,horizontal: deviceWidth*0.05),
    //             decoration: BoxDecoration(borderRadius: BorderRadius.circular(24),
    //             color: Colors.red.withOpacity(0.4)
    //             ),
    //           ),
    //         )
    //       ],
    //     ),
    //   ),
    // );
    return Center(
      child: Platform.isAndroid
          ? MaterialCircularLoader(deviceWidth * 0.07)
          : CupertinoCircularLoader(15.0),
    );
  }
}

class SummaryListTile extends StatelessWidget {
  const SummaryListTile({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.035),
          height: deviceWidth * 0.025,
          width: deviceWidth * 0.2,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: Colors.red.withOpacity(0.4),
          ),
        ),
        Container(
          height: deviceWidth * 0.025,
          width: deviceWidth * 0.15,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: Colors.red.withOpacity(0.4),
          ),
        ),
      ],
    );
  }
}

class Circles extends StatelessWidget {
  const Circles({
    Key? key,
    required this.deviceWidth,
  }) : super(key: key);

  final double deviceWidth;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      child: Container(
        margin: EdgeInsets.only(
            top: deviceWidth * 0.04,
            bottom: deviceWidth * 0.04,
            right: deviceWidth * 0.04),
        height: deviceWidth * 0.12,
        width: deviceWidth * 0.12,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
      ),
      baseColor: Colors.black12.withOpacity(0.2),
      highlightColor: Colors.white.withOpacity(0.5),
    );
  }
}
