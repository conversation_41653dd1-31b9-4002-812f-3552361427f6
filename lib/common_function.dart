import 'dart:io';

import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../commonWidgets/raisedButton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import 'authModule/providers/auth.dart';
import 'colors.dart';
import 'commonWidgets/asset_svg_icon.dart';
import 'commonWidgets/location_disabled_widget.dart';
import 'commonWidgets/text_widget.dart';
import 'main.dart';
import 'venueModule/models/venue_model.dart';
import 'dart:ui' as ui;

BuildContext get bContext => navigatorKey.currentContext!;

getTimePeriod(double time) {
  if (time >= 12) {
    return 'pm';
  } else {
    return 'am';
  }
}

callLaunch(mobileNumber) async {
  // if (await canLaunch(command)) {
  await launch('tel:$mobileNumber');
  // } else {
  //   print('could not launch $command');
  // }
}

getDuration(int minutes) {
  if (minutes != 0) {
    int remainder = 0;
    int quotient = 0;
    String min = '';
    String sec = '';
    quotient = minutes ~/ 60;
    remainder = minutes % 60;
    min = '$quotient';
    sec = "$remainder";

    if (quotient < 9) {
      min = '0$quotient';
    } else {
      min = '$quotient';
    }

    if (remainder < 9) {
      sec = '0$remainder';
    } else {
      sec = '$remainder';
    }
    return '$min:$sec';
  } else {
    return '00:00';
  }
}

getWeekDays(int index) {
  switch (index) {
    case 1:
      return 'Mon';
    case 2:
      return 'Tue';
    case 3:
      return 'Wed';
    case 4:
      return 'Thu';
    case 5:
      return 'Fri';
    case 6:
      return 'Sat';
    case 7:
      return 'Sun';
  }
}

getFullWeekDays(String day) {
  switch (day) {
    case 'Mon':
      return 'Monday';
    case 'Tue':
      return 'Tuesday';
    case 'Wed':
      return 'Wednesday';
    case 'Thu':
      return 'Thursday';
    case 'Fri':
      return 'Friday';
    case 'Sat':
      return 'Saturday';
    case 'Sun':
      return 'Sunday';
  }
}

getWeekNumber(String day) {
  switch (day) {
    case 'Mon':
      return 1;
    case 'Tue':
      return 2;
    case 'Wed':
      return 3;
    case 'Thu':
      return 4;
    case 'Fri':
      return 5;
    case 'Sat':
      return 6;
    case 'Sun':
      return 7;
  }
}

Future<void> checkVersion(BuildContext context) async {
  try {
    Map update = Provider.of<Auth>(context, listen: false).update;

    if (update.isNotEmpty && update['showButton']) {
      final info = await PackageInfo.fromPlatform();

      if (update['latestVersion'] != info.version) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) => Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.2,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(7),
              ),
              title: Text(
                'Update Available!',
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              content: Text(
                update['message'],
                style: TextStyle(
                  fontSize: 13.5,
                  fontWeight: FontWeight.w500,
                ),
              ),
              actions: [
                // if (!update['compulsory'])
                TextButton(
                  child: Text(
                    'Later',
                    style: TextStyle(
                      fontSize: 14.5,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text(
                    'Update',
                    style: TextStyle(
                      fontSize: 14.5,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    customLaunch(update['link']);
                  },
                ),
              ],
            ),
          ),
        );
      }
    }
  } catch (e) {
    print(e);
  }
}

get12HrClockFormat(String time) {
  if (time == '00:00' || time == '00:0') {
    return "12:00";
  } else if (double.parse(time.replaceAll(":", '.')) >= 13 &&
      double.parse(time.replaceAll(":", '.')) <= 24) {
    if (time == '13:00' || time == '13:0') {
      return "01:00";
    } else if (time == '13:30' || time == '13:3') {
      return "01:30";
    } else if (time == '14:00' || time == '14:0') {
      return "02:00";
    } else if (time == '14:30' || time == '14:3') {
      return "02:30";
    } else if (time == '15:00' || time == '15:0') {
      return "03:00";
    } else if (time == '15:30' || time == '15:3') {
      return "03:30";
    } else if (time == '16:00' || time == '16:0') {
      return "04:00";
    } else if (time == '16:30' || time == '16:3') {
      return "04:30";
    } else if (time == '17:00' || time == '17:0') {
      return "05:00";
    } else if (time == '17:30' || time == '17:3') {
      return "05:30";
    } else if (time == '18:00' || time == '18:0') {
      return "06:00";
    } else if (time == '18:30' || time == '18:3') {
      return "06:30";
    } else if (time == '19:00' || time == '19:0') {
      return "07:00";
    } else if (time == '19:30' || time == '19:3') {
      return "07:30";
    } else if (time == '20:00' || time == '20:0') {
      return "08:00";
    } else if (time == '20:30' || time == '20:3') {
      return "08:30";
    } else if (time == '21:00' || time == '21:0') {
      return "09:00";
    } else if (time == '21:30' || time == '21:3') {
      return "09:30";
    } else if (time == '22:00' || time == '22:0') {
      return "10:00";
    } else if (time == '22:30' || time == '22:3') {
      return "10:30";
    } else if (time == '23:00' || time == '23:0') {
      return "11:00";
    } else if (time == '23:30' || time == '23:3') {
      return "11:30";
    } else if (time == '24:00' || time == '24:0') {
      return "00:00";
    } else if (time == '24:30' || time == '24:3') {
      return "00:30";
    }
  } else {
    return time;
  }
}

get12HrFormat(double time) {
  if (time >= 13 && time <= 24) {
    if (time == 13.00) {
      return 1.0;
    } else if (time == 13.30) {
      return 1.3;
    } else if (time == 14.00) {
      return 2.0;
    } else if (time == 14.30) {
      return 2.3;
    } else if (time == 15.00) {
      return 3.0;
    } else if (time == 15.30) {
      return 3.3;
    } else if (time == 16.00) {
      return 4.0;
    } else if (time == 16.30) {
      return 4.3;
    } else if (time == 17.00) {
      return 5.0;
    } else if (time == 17.30) {
      return 5.3;
    } else if (time == 18.00) {
      return 6.0;
    } else if (time == 18.30) {
      return 6.3;
    } else if (time == 19.00) {
      return 7.0;
    } else if (time == 19.30) {
      return 7.3;
    } else if (time == 20.00) {
      return 8.0;
    } else if (time == 20.30) {
      return 8.3;
    } else if (time == 21.00) {
      return 9.0;
    } else if (time == 21.30) {
      return 9.3;
    } else if (time == 22.00) {
      return 10.0;
    } else if (time == 22.30) {
      return 10.3;
    } else if (time == 23.00) {
      return 11.0;
    } else if (time == 23.30) {
      return 11.3;
    } else if (time == 24.00) {
      return 0.0;
    } else if (time == 24.30) {
      return 0.3;
    }
  } else {
    return time;
  }
}

List defaultSlots = [
  {
    'title': 'Morning',
    'isSelected': false,
    'id': 'Slot1',
    'startTime': 5,
    'endTime': 12,
    'priceAndQuantity': [],
  },
  {
    'title': 'Afternoon',
    'isSelected': false,
    'id': 'Slot2',
    'startTime': 12,
    'endTime': 16,
    'priceAndQuantity': [],
  },
  {
    'title': 'Evening',
    'isSelected': false,
    'id': 'Slot3',
    'startTime': 16,
    'endTime': 20,
    'priceAndQuantity': [],
  },
  {
    'title': 'Night',
    'isSelected': false,
    'id': 'Slot4',
    'startTime': 20,
    'endTime': 5,
    'priceAndQuantity': [],
  },
];

Iterable<TimeOfDay> getTimes(
    TimeOfDay startTime, TimeOfDay endTime, Duration step) sync* {
  var hour = startTime.hour;
  var minute = startTime.minute;

  do {
    // hour: hour, minute: minute
    yield TimeOfDay.fromDateTime(
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day,
          hour, minute),
    );
    minute += step.inMinutes;
    while (minute >= 60) {
      minute -= 60;
      hour++;
    }
  } while (hour < endTime.hour ||
      (hour == endTime.hour && minute <= endTime.minute));
}

Widget buildTitleAndSubtitle({
  required double deviceWidth,
  required double textScaleFactor,
  required BuildContext context,
  required String title,
  required String subtitle,
}) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: deviceWidth * 0.025),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.displaySmall!.copyWith(
                fontSize: textScaleFactor * 13,
                fontWeight: FontWeight.w500,
                color: const Color(0xffA3A2A2),
              ),
        ),
        SizedBox(height: deviceWidth * 0.015),
        title == 'Mobile Number'
            ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  buildText(
                    textScaleFactor: textScaleFactor,
                    context: context,
                    subtitle: subtitle,
                  ),
                  SvgPicture.asset(
                    'assets/svgIcons/verified.svg',
                    height: 20,
                  ),
                ],
              )
            : buildText(
                textScaleFactor: textScaleFactor,
                context: context,
                subtitle: subtitle,
              )
      ],
    ),
  );
}

Widget buildText({
  required double textScaleFactor,
  required BuildContext context,
  required String subtitle,
}) {
  return Text(
    subtitle,
    style: Theme.of(context).textTheme.displaySmall!.copyWith(
          fontSize: textScaleFactor * 14.5,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
  );
}

bool iOSCondition(double dH) => Platform.isIOS && dH > 850;

Color getThemeColor() {
  return Theme.of(navigatorKey.currentState!.context).primaryColor;
}

List<BoxShadow> get shadow => [
      BoxShadow(
        color: Colors.black.withOpacity(0.08),
        offset: const Offset(0, 4),
        spreadRadius: 4,
        blurRadius: 8,
      )
    ];

BoxDecoration commonBoxDecoration(double radius, {Color? borderColor}) =>
    BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(radius),
      border: Border.all(
          width: 1, color: borderColor ?? getThemeColor().withOpacity(.13)),
      boxShadow: shadow,
    );

String amountText(double amount) {
  String amountString = amount.toStringAsFixed(2);

  if (amountString.split('.')[1][1] == '0') {
    amountString =
        '${amountString.split('.')[0]}.${amountString.split('.')[1][0]}';
    if (amountString.split('.')[1][0] == '0') {
      amountString = amountString.split('.')[0];
    }
  }
  return amountString;
}

String convertAmountString(double amount) {
  var strToReturn = '';
  String aS = amount.round().toStringAsFixed(0);
  final list = aS.split('.');
  aS = list[0];
  final length = aS.length;
  if (length < 6) {
    strToReturn = amountText(amount);
  } else if (length == 6) {
    String trail = aS.substring(length - 5, length);
    String lead = aS.substring(0, length - 5);
    if (trail[0] != '0') lead = '$lead.${trail[0]}';
    strToReturn = '${lead}L';
  } else if (length == 7) {
    String trail = aS.substring(length - 6, length);
    String lead = '${aS.substring(0, length - 6)}0';
    if (trail[0] != '0') lead = '$lead.${trail[0]}';
    strToReturn = '${lead}L';
  } else if (length > 7) {
    String trail = aS.substring(length - 7, length);
    String lead = aS.substring(0, length - 7);
    if (trail[0] != '0') lead = '$lead.${trail[0]}';
    strToReturn = '${lead}Cr';
  }
  return strToReturn;
}

showSnackbar(
  String msg, {
  Color color = Colors.red,
  int duration = 2,
  FontWeight fontWeight = FontWeight.w500,
}) {
  ScaffoldMessenger.of(navigatorKey.currentContext!).hideCurrentSnackBar();
  ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(SnackBar(
    content: Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Text(
        msg,
        softWrap: true,
        style: TextStyle(fontWeight: fontWeight, fontFamily: 'Poppins'),
      ),
    ),
    behavior: SnackBarBehavior.floating,
    backgroundColor: color,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    duration: Duration(seconds: duration),
    margin: const EdgeInsets.only(
      bottom: 40,
      right: 20,
      left: 20,
    ),
  ));
}

hideKeyBoard(BuildContext context) =>
    FocusScope.of(context).requestFocus(FocusNode());

PopupMenuEntry popupMenuItem({
  required int position,
  required String title,
  String icon = '',
  required double dW,
  double? iconSize,
  bool removeColor = false,
  bool isActive = true,
}) {
  return PopupMenuItem(
    value: position,
    height: dW * 0.07,
    child: Container(
      margin: EdgeInsets.only(bottom: dW * 0.02, top: dW * 0.02),
      child: Row(
        children: [
          if (icon != '') ...[
            AssetSvgIcon(
              iconName: icon,
              color: removeColor
                  ? null
                  : !isActive
                      ? Colors.grey.shade400
                      : greenPrimary,
              height: iconSize,
            ),
            SizedBox(width: dW * 0.035),
          ],
          TextWidget(
            title: title,
            fontWeight: FontWeight.w500,
            color: !isActive ? Colors.grey.shade400 : Colors.black,
          ),
        ],
      ),
    ),
  );
}

String getInitials(String text) {
  if (text.trim().isEmpty) {
    return '';
  }
  var textList = text.trim().split(' ');
  textList.remove('');
  if (textList.length == 1 && textList[0] == '') {
    return '';
  } else if (textList.length >= 2) {
    return '${textList[0].substring(0, 1)}${textList[1].substring(0, 1)}';
  } else {
    return textList[0].length == 1 ? textList[0] : textList[0].substring(0, 2);
  }
}

pickImage(ImageSource source) async {
  try {
    ImagePicker picker = ImagePicker();
    final image = await picker.pickImage(source: source, imageQuality: 50);

    return image;
  } catch (e, stackTrace) {
    return null;
  }
}

Future<XFile?> pickCropImage({
  // required CropAspectRatio crossAspectRatio,
  required ImageSource imageSource,
}) async {
  try {
    XFile? pickImage =
        await ImagePicker().pickImage(source: imageSource, imageQuality: 50);

    if (pickImage == null) return null;

    //Crop
    // CroppedFile? croppedFile = await ImageCropper().cropImage(
    //   sourcePath: pickImage.path,
    //   maxHeight: 1080,
    //   maxWidth: 1080,
    //   aspectRatioPresets: [
    //     CropAspectRatioPreset.square,
    //     CropAspectRatioPreset.ratio3x2,
    //     CropAspectRatioPreset.original,
    //     CropAspectRatioPreset.ratio4x3,
    //     CropAspectRatioPreset.ratio16x9
    //   ],
    // );
    // if (croppedFile == null) {
    return pickImage;
    // } else {
    //   return XFile(croppedFile.path);
    // }
  } catch (e) {
    print(e);
    return null;
  }
}

Future<File> convertToJpeg(File image, String name) async {
  final tempDir = await getTemporaryDirectory();
  final targetPath =
      '${tempDir.path}/${name.split('.').isNotEmpty ? name.split('.')[0] : DateTime.now().toString()}.jpeg';

  // Compress and convert the image to JPEG format˳
  await FlutterImageCompress.compressAndGetFile(
    image.absolute.path,
    targetPath,
    quality: 80,
    format: CompressFormat.jpeg,
  );

  return File(targetPath);
}

double getImageSize(File image) {
  final imageSize = image.lengthSync();
  final kilobytes = imageSize / 1024; // Convert to kilobytes
  final megabytes = kilobytes / 1024; // Convert to megabytes

  return megabytes;
}

handlePermissionsFunction() async {
  try {
    Map<Permission, PermissionStatus> statuses = {};
    if (Platform.isIOS) {
      statuses =
          await [Permission.location, Permission.locationAlways].request();

      if (statuses.containsValue(PermissionStatus.permanentlyDenied) ||
          statuses.containsValue(PermissionStatus.denied)) {
        // showSnackbar('Please enable location', Colors.red);
        return false;
      } else {
        return true;
      }
    } else {
      statuses = await [Permission.location].request();

      if ((statuses[Permission.location] == PermissionStatus.denied) ||
          (statuses[Permission.location] ==
              PermissionStatus.permanentlyDenied)) {
        // showSnackbar('Please enable location', Colors.red);
        return false;
      } else {
        return true;
      }
    }
  } catch (e, stackTrace) {
    return false;
  }
}

String addrPiece(String? piece) {
  if (piece == null || piece == '') {
    return '';
    //
  } else {
    return '$piece, ';
  }
}

checkIfValid(String? text) => (text != '' && text != null);

showDisabledLocationWidget(String messageContent,
        {bool showManualOption = false}) =>
    showDialog(
      context: bContext,
      builder: (ctx) => AlertDialog(
        titlePadding: const EdgeInsets.all(0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        contentPadding: const EdgeInsets.all(0),
        content: LocationDisabledWidget(
            messageContent: messageContent, showManualOption: showManualOption),
      ),
    );

Map<String, String> areaCode = {
  "AN": "Andaman and Nicobar Islands",
  "AP": "Andhra Pradesh",
  "AR": "Arunachal Pradesh",
  "AS": "Assam",
  "BR": "Bihar",
  "CG": "Chandigarh",
  "CH": "Chhattisgarh",
  "DN": "Dadra and Nagar Haveli",
  "DD": "Daman and Diu",
  "DL": "Delhi",
  "GA": "Goa",
  "GJ": "Gujarat",
  "HR": "Haryana",
  "HP": "Himachal Pradesh",
  "JK": "Jammu and Kashmir",
  "JH": "Jharkhand",
  "KA": "Karnataka",
  "KL": "Kerala",
  "LA": "Ladakh",
  "LD": "Lakshadweep",
  "MP": "Madhya Pradesh",
  "MH": "Maharashtra",
  "MN": "Manipur",
  "ML": "Meghalaya",
  "MZ": "Mizoram",
  "NL": "Nagaland",
  "OR": "Odisha",
  "PY": "Puducherry",
  "PB": "Punjab",
  "RJ": "Rajasthan",
  "SK": "Sikkim",
  "TN": "Tamil Nadu",
  "TS": "Telangana",
  "TR": "Tripura",
  "UP": "Uttar Pradesh",
  "UK": "Uttarakhand",
  "WB": "West Bengal"
};

List<Slot> getVenueSlot(Venue venue) {
  if (venue.sportCategory!.categoryName == 'Outdoor') {
    if (venue.cricket != null) {
      return venue.cricket!.slots;
    } else if (venue.football != null) {
      return venue.football!.slots;
    } else {
      return venue.slots ?? [];
    }
  } else if (venue.sportCategory!.categoryName == 'Indoor') {
    if (venue.badminton != null) {
      return venue.badminton!.slots;
    } else if (venue.snooker != null) {
      return venue.snooker!.slots;
    } else if (venue.pool != null) {
      return venue.pool!.slots;
    } else if (venue.pickleball != null) {
      return venue.pickleball!.slots;
    } else if (venue.tableTennis != null) {
      return venue.tableTennis!.slots;
    } else if (venue.tableTennis != null) {
      return venue.tennis!.slots;
    } else {
      return venue.slots ?? [];
    }
  } else if (venue.sportCategory!.categoryName == 'Esport') {
    if (venue.ps3 != null) {
      return venue.ps3!.slots;
    } else if (venue.ps4 != null) {
      return venue.ps4!.slots;
    } else if (venue.ps5 != null) {
      return venue.ps5!.slots;
    } else if (venue.xbox1 != null) {
      return venue.xbox1!.slots;
    } else {
      return venue.slots ?? [];
    }
  } else {
    return venue.slots ?? [];
  }
}

bool isSameDay(DateTime date1, DateTime date2) =>
    date1.day == date2.day &&
    date1.month == date2.month &&
    date1.year == date2.year;

Future<Uint8List> getBytesFromAsset(String path, int width) async {
  ByteData data = await rootBundle.load(path);
  ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
      targetWidth: width);
  ui.FrameInfo fi = await codec.getNextFrame();
  return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
      .buffer
      .asUint8List();
}

launchCall(mobileNumber) async {
  try {
    var url = Uri.parse('tel:$mobileNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      print('could not launch ');
    }
  } catch (e) {
    print(e);
  }
}

// // Add this to clean up temporary files
// Future<void> cleanupTempFiles() async {
//   try {
//     final tempDir = await getTemporaryDirectory();
//     final dir = Directory(tempDir.path);
//     if (await dir.exists()) {
//       // Delete files older than 24 hours
//       final now = DateTime.now();
//       await for (final file in dir.list()) {
//         if (file is File) {
//           final stat = await file.stat();
//           final fileAge = now.difference(stat.modified);
//           if (fileAge.inHours > 24) {
//             await file.delete();
//           }
//         }
//       }
//     }
//   } catch (e) {
//     print('Error cleaning temp files: $e');
//   }
// }

// Add this helper function to your common_function.dart
Future<bool> isFileValid(String path) async {
  if (path.isEmpty) return false;
  final file = File(path);
  return await file.exists();
}

// Add this helper function for safe file loading
Widget safeLoadImage(String path, {BoxFit fit = BoxFit.cover, Color errorColor = Colors.red}) {
  return FutureBuilder<bool>(
    future: isFileValid(path),
    builder: (context, snapshot) {
      if (snapshot.connectionState == ConnectionState.done) {
        if (snapshot.data == true) {
          return Image.file(File(path), fit: fit);
        } else {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: errorColor),
                SizedBox(height: 8),
                Text("File not found", style: TextStyle(color: Colors.white)),
              ],
            ),
          );
        }
      }
      return CircularLoader(android: 24, iOS: 12, color: Colors.white);
    },
  );
}

// Add this helper function for safely loading network images
Widget safeNetworkImage({
  required String imageUrl,
  BoxFit fit = BoxFit.cover,
  double? height,
  double? width,
  Widget? placeholder,
  Widget? errorWidget,
}) {
  return CachedNetworkImage(
    imageUrl: imageUrl,
    fit: fit,
    height: height,
    width: width,
    placeholder: (context, url) => placeholder ?? CircularLoader(android: 24, iOS: 12),
    errorWidget: (context, url, error) => errorWidget ?? Container(
      color: Colors.grey[300],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, color: Colors.red),
          SizedBox(height: 4),
          Text(
            "Image failed to load",
            style: TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
    // Add retry options
    maxHeightDiskCache: 1500,
    memCacheHeight: 1500,
    httpHeaders: const {"Connection": "keep-alive"},
    fadeOutDuration: const Duration(milliseconds: 300),
    fadeInDuration: const Duration(milliseconds: 300),
  );
}
