import 'dart:io';

import 'package:bys_business/common_function.dart';
import 'package:bys_business/theme_manager.dart';
import 'package:bys_business/withdrawalModule/providers/withdrawal_provider.dart';

import '../../bulkBookingModule/provider/bulk_provider.dart';
import '../../customerModule/provider/customer_provider.dart';
import '../../employeeManagement/provider/employeeManagementProvider.dart';
import '../../employeeModule/provider/employee_provider.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../authModule/screens/loginScreen.dart';
import '../../authModule/screens/splashScreen.dart';
import '../../moreModule.dart/provider/moreProvider.dart';
import '../../homeModule/providers/homeProvider.dart';
import 'venueModule/providers/turfProvider.dart';
import '../../notificationModule.dart/provider/notificationProvider.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

Future<void> backgroundHandler(RemoteMessage message) async {
  print('Background Call ${message.notification!.body.toString()}');
  print('Background Call ${message.notification!.title}');
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // await cleanupTempFiles();
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(backgroundHandler);
  if (Platform.isAndroid) {
    // var androidInfo = await DeviceInfoPlugin().androidInfo;
    // if (double.parse(androidInfo.version.release!.split('.')[0]) <= 8.0) {
    HttpOverrides.global = new MyHttpOverrides();
    // }
  }
  return runApp(ChangeNotifierProvider<ThemeNotifier>(
    create: (_) => new ThemeNotifier(),
    child: MyApp(),
  ));
}

final GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  // static FirebaseAnalytics analytics = FirebaseAnalytics();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => Auth()),
        ChangeNotifierProvider(create: (_) => HomeProvider()),
        ChangeNotifierProvider(create: (_) => TurfProvider()),
        ChangeNotifierProvider(create: (_) => EmployeeProvider()),
        ChangeNotifierProvider(create: (_) => EmployeeManagementProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => MoreProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => BulkProvider()),
        ChangeNotifierProvider(create: (_) => WithdrawalProvider()),
      ],
      child: Consumer<ThemeNotifier>(
        builder: (context, theme, _) => MaterialApp(
            navigatorKey: navigatorKey,
            builder: (context, child) {
              // final mediaQueryData = MediaQuery.of(context);
              // final scale = mediaQueryData.textScaleFactor.clamp(1.0, 1.1);
              return MediaQuery(
                child: child!,
                data: MediaQuery.of(context).copyWith(
                  textScaleFactor: 1.0,
                  alwaysUse24HourFormat: true,
                 
                ),

                // data: MediaQuery.of(context).copyWith(textScaleFactor: scale),
              );
            },
            title: 'BYS Business',
            theme: theme.getTheme,
            debugShowCheckedModeBanner: false,
            home: SplashScreen(),
            navigatorObservers: [
              // FirebaseAnalyticsObserver(analytics: analytics),
            ],
            routes: {
              '/SplashScreen': (BuildContext context) => SplashScreen(),
              '/LoginScreen': (BuildContext context) => LoginScreen(),
            }),
      ),
    );
  }
}
