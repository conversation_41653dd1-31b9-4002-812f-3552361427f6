import '../venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:share_plus/share_plus.dart';

// class DynamicLinksApi {
final dynamicLink = FirebaseDynamicLinks.instance;

// handleDynamicLink() async {
//   dynamicLink.onLink(onSuccess: (PendingDynamicLinkData? data) async {
//     handleSuccessLinking(data!);
//   }, onError: (OnLinkErrorException error) async {
//     print(error.message.toString());
//   });
// }

createReferralLink(
  BuildContext context,
  String referralCode,
  accessToken,
  String mobileNo,
) async {
  final RenderBox? box = context.findRenderObject() as RenderBox?;

  final DynamicLinkParameters dynamicLinkParameters = DynamicLinkParameters(
    uriPrefix: 'https://bookyourslotuser.page.link',
    link: Uri.parse(
        'https://bookyourslotuser.page.link/referOwner?referralCode=$referralCode&referredByUserId=$mobileNo'),
    androidParameters: AndroidParameters(
      packageName: 'com.BookYourSlotBusinessNew.app',
    ),
    iosParameters: IOSParameters(
      bundleId: 'com.BookYourSlotBusinessNew.app',
      appStoreId: '1599722438',
    ),
    socialMetaTagParameters: SocialMetaTagParameters(
      title: 'Refer a friend',
      description: '',
      imageUrl: Uri.parse(
        'https://book-your-slot.s3.ap-south-1.amazonaws.com/bys_Business_logo.png',
      ),
    ),
  );

  final ShortDynamicLink shortLink =
      await FirebaseDynamicLinks.instance.buildShortLink(
    dynamicLinkParameters,
    shortLinkType: ShortDynamicLinkType.unguessable,
  );

  await SharePlus.instance.share(
ShareParams(
  text: 'Hey! Join the BYS Business app using my referral link. ${shortLink.shortUrl} \n\nInstructions:\nDownload & register the application through link.',
  subject: 'Create Venue for varieties of sports.',
  sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
)
    // 'Hey! Join the BYS Business app using my referral link. ${shortLink.shortUrl} \n\nInstructions:\nDownload & register the application through link.',
    // subject: 'Create Venue for varieties of sports.',
    // sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
  );
}

shareTurf(
  BuildContext context,
  Venue turf,
) async {
  final RenderBox? box = context.findRenderObject() as RenderBox?;

  final DynamicLinkParameters dynamicLinkParameters = DynamicLinkParameters(
    uriPrefix: 'https://bookyourslotuser.page.link',
    link:
        Uri.parse('https://bookyourslotuser.page.link/turf?turfId=${turf.id}'),
    androidParameters: AndroidParameters(
      packageName: 'com.bookYourSlotUser.app',
    ),
    socialMetaTagParameters: SocialMetaTagParameters(
      title: turf.name,
      description: '',
      imageUrl: Uri.parse(turf.images![0]),
    ),
  );
  final ShortDynamicLink shortLink =
      await FirebaseDynamicLinks.instance.buildShortLink(
    dynamicLinkParameters,
    shortLinkType: ShortDynamicLinkType.unguessable,
  );
  // final Uri dynamicUrl = shortLink.shortUrl;
  // return dynamicUrl.toString();

  await Share.share(
    '${turf.description}\n\n ${shortLink.shortUrl}',
    subject: turf.name,
    sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
  );
}

void handleSuccessLinking(PendingDynamicLinkData data) {
  final Uri deeplink = data.link;

  if (deeplink != null) {
    var isRefer = deeplink.pathSegments.contains('refer');
    if (isRefer) {
      var code = deeplink.queryParameters['code'];
      if (code != null) {
        //navigate
      }
    }
  }
}
// }
