// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/custom_container.dart';
import '../../../../common_function.dart';

class ChildStep2ThirdWidget extends StatelessWidget {
  final List listOfTurfOption;
  final Function setTurfOption;

  ChildStep2ThirdWidget({
    required this.listOfTurfOption,
    required this.setTurfOption,
  });

  double dW = 0.0;
  double tS = 0.0;

  String getTitle(String title) {
    if (title == 'Netting') {
      return 'Turf with division';
    } else if (title == 'Non Netting') {
      return 'Turf without division';
    } else if (title == 'One On') {
      return 'Turf with cricket net ';
    } else {
      return '';
    }
  }

  String getSubTitle(String title) {
    if (title == 'Netting') {
      return 'Safe and contained sports experience.';
    } else if (title == 'Non Netting') {
      return 'Open space for free play';
    } else if (title == 'One On') {
      return 'Specialised cricket practice facility';
    } else {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Which type of turf option is available at your venue?',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title: 'Add type of turf available at your venue',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.1),
          ...listOfTurfOption.map(
            (option) => GestureDetector(
              onTap: () => setTurfOption(option['title']),
              child: CustomContainer(
                margin: EdgeInsets.only(bottom: dW * 0.05),
                boxShadow: [],
                borderColor:
                    option['isSelected'] ? getThemeColor() : Color(0xffABABAB),
                bgColor:
                    option['isSelected'] ? getThemeColor() : Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          title: getTitle(option['title']),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: option['isSelected']
                              ? Colors.white
                              : Colors.black,
                        ),
                        SizedBox(height: dW * 0.02),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: dW * 0.6),
                          child: TextWidget(
                            title: getSubTitle(option['title']),
                            fontSize: 11,
                            color: option['isSelected']
                                ? Color(0xffD9D9D9)
                                : Colors.black,
                          ),
                        )
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: getThemeColor(), width: 2),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          'assets/images/${(option['title'] as String).replaceAll(' ', '').trim().toLowerCase()}.png',
                          scale: 1.7,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
