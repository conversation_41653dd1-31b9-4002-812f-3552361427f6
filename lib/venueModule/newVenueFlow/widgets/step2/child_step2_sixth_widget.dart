// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_checkbox_widget.dart';
import 'package:bys_business/commonWidgets/raisedButton.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../../commonWidgets/custom_checkBox2.dart';
import '../../../../commonWidgets/custom_text_field.dart';
import '../../../../new_colors.dart';
import '../../../providers/turfProvider.dart';

class ChildStep2SixthWidget extends StatefulWidget {
  final List selectedSports;
  final List selectedSlots;
  final Map selectedSportCategory;
  final int selectedSlotDuration;
  final Function addOrRemovePrice;
  // final Function selectSlotDuration;
  bool? isAutoFillPrice;
  final VoidCallback? toggleAutoFillPrice;

  ChildStep2SixthWidget({
    required this.selectedSports,
    required this.selectedSlots,
    required this.selectedSlotDuration,
    required this.addOrRemovePrice,
    // required this.selectSlotDuration,
    required this.selectedSportCategory,
    this.isAutoFillPrice,
    this.toggleAutoFillPrice,
  });

  @override
  State<ChildStep2SixthWidget> createState() => _ChildStep2SixthWidgetState();

  String? typedWeekdayValue;
  String? typedWeekendValue;
}

class _ChildStep2SixthWidgetState extends State<ChildStep2SixthWidget> {
  double dW = 0.0;

  double tS = 0.0;

  FocusNode weekdayFocusNode = FocusNode();
  FocusNode weekendFocusNode = FocusNode();
  TextEditingController? lastTappedWeekdayController;
  FocusNode? lastTappedWeekdayFocusNode;
  bool isLastTappedWeekdayFocused = false;
  bool isAutoFillPrice = false;
  Set<String> seenSports = Set();
  List sportsName = [];

  validateSelection(sport) {
    if (!sport['isSelected']) {
      callToastMessage(
        widget.selectedSportCategory['categoryName'] != 'Outdoor'
            ? "Please select the sport"
            : 'Please select the turf size',
      );
    }
  }

  void toggleAutoFillPrice(bool newValue) {
    setState(() {
      isAutoFillPrice = newValue;

      if (isAutoFillPrice) {
        for (var i = 0; i < widget.selectedSlots.length; i++) {
          for (var j = 0;
              j < widget.selectedSlots[i]['priceAndQuantity'].length;
              j++) {
            if (i != 0) {
              if (widget.selectedSlots[0]['priceAndQuantity'][j]['controller']
                      .text !=
                  '') {
                widget.selectedSlots[i]['priceAndQuantity'][j]['controller']
                        .text =
                    widget.selectedSlots[0]['priceAndQuantity'][j]['controller']
                        .text;
                widget.selectedSlots[i]['priceAndQuantity'][j]['price'] =
                    int.parse(widget
                        .selectedSlots[0]['priceAndQuantity'][j]['controller']
                        .text);
                widget.selectedSlots[0]['priceAndQuantity'][j]['price'] =
                    int.parse(widget
                        .selectedSlots[0]['priceAndQuantity'][j]['controller']
                        .text);
              }
              if (widget
                      .selectedSlots[0]['priceAndQuantity'][j]
                          ['weekendController']
                      .text !=
                  '') {
                widget
                        .selectedSlots[i]['priceAndQuantity'][j]
                            ['weekendController']
                        .text =
                    widget
                        .selectedSlots[0]['priceAndQuantity'][j]
                            ['weekendController']
                        .text;
                widget.selectedSlots[i]['priceAndQuantity'][j]['weekendPrice'] =
                    int.parse(widget
                        .selectedSlots[0]['priceAndQuantity'][j]
                            ['weekendController']
                        .text);
                widget.selectedSlots[0]['priceAndQuantity'][j]['weekendPrice'] =
                    int.parse(widget
                        .selectedSlots[0]['priceAndQuantity'][j]
                            ['weekendController']
                        .text);
              }
              if (widget.selectedSlots[0]['priceAndQuantity'][j]['controller']
                      .text ==
                  '') {
                widget.selectedSlots[i]['priceAndQuantity'][j]['controller']
                    .text = '';
                widget.selectedSlots[i]['priceAndQuantity'][j]['price'] = 0;
                widget.selectedSlots[0]['priceAndQuantity'][j]['price'] = 0;
              }
              if (widget
                      .selectedSlots[0]['priceAndQuantity'][j]
                          ['weekendController']
                      .text ==
                  '') {
                widget
                    .selectedSlots[i]['priceAndQuantity'][j]
                        ['weekendController']
                    .text = '';
                widget.selectedSlots[i]['priceAndQuantity'][j]['weekendPrice'] =
                    0;
                widget.selectedSlots[0]['priceAndQuantity'][j]['weekendPrice'] =
                    0;
              }
            }
          }
        }
      }
    });
  }

  String addAndGetSportName(
      Map<String, dynamic> sport, List<dynamic> sportsName) {
    String sportName = sport['sport'] == null ? sport['title'] : sport['sport'];
    if (!sportsName.contains(sportName)) {
      sportsName.add(sportName);
    }
    return sportName;
  }

  @override
  void initState() {
    super.initState();

    // Call addOrRemovePrice function when the screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Perform the addOrRemovePrice operation for each slot's priceAndQuantity
      widget.selectedSlots.forEach((slot) {
        slot['priceAndQuantity'].forEach((sport) {
          widget.addOrRemovePrice(slot['priceAndQuantity'], sport['id'], true);
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Now add sports pricing',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'Include the taxes while adding price for ${widget.selectedSlotDuration} mins, you can change this later.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.05),
          ...widget.selectedSlots
              .asMap()
              .map((i, slot) {
                sportsName.clear();
                return MapEntry(
                  i,
                  Container(
                    padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                    decoration: i == widget.selectedSlots.length - 1
                        ? null
                        : BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: Color(0xffD9D9D9)))),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Image.asset(
                              'assets/images/${slot['title'].toLowerCase()}.png',
                              height: 18,
                            ),
                            SizedBox(
                              width: dW * 0.02,
                            ),
                            TextWidget(
                              title: '${slot['title']} Slot',
                              color: Color(0xff3E3E3E),
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ],
                        ),
                        Container(
                          margin: EdgeInsets.only(
                              top: dW * 0.06, bottom: dW * 0.02),
                          padding: EdgeInsets.all(dW * 0.04),
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Color(0xffD9D9D9),
                              ),
                              borderRadius: BorderRadius.circular(8)),
                          child: Column(
                            children: [
                              // SizedBox(height: dW * 0.05),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  ...[
                                    widget.selectedSportCategory[
                                                'categoryName'] !=
                                            'Outdoor'
                                        ? 'Sport'
                                        : 'Turf Size',
                                    'Weekdays',
                                    'Weekends'
                                  ].map(
                                    (type) => Container(
                                      alignment: Alignment.topLeft,
                                      width: type == 'Weekends' ||
                                              type == 'Weekdays'
                                          ? dW * 0.21
                                          : dW * .24,
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: TextWidget(
                                          title: type,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: dW * 0.03),
                              ...slot['priceAndQuantity'].map(
                                (sport) {
                                  sport['isSelected'] = true;

                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (!sportsName.contains(sport['sport']))
                                        Container(
                                          margin: EdgeInsets.only(
                                              bottom: dW * 0.02,
                                              top: dW * 0.02),
                                          child: TextWidget(
                                            title: addAndGetSportName(
                                                sport, sportsName),
                                            color: getThemeColor(),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          GestureDetector(
                                            onTap: () {},
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                // right: dW * 0.03,
                                                bottom: dW * 0.03,
                                                top: sport['sport'] == 'Snooker'
                                                    ? dW * 0.05
                                                    : dW * 0.03,
                                              ),
                                              width: dW * 0.22,
                                              padding: EdgeInsets.symmetric(
                                                  vertical: dW * 0.03,
                                                  horizontal: dW * 0.02),
                                              decoration: BoxDecoration(
                                                color: sport['isSelected']
                                                    ? getThemeColor()
                                                    : Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(7),
                                                border: Border.all(
                                                    color: getThemeColor()),
                                              ),
                                              child: FittedBox(
                                                fit: BoxFit.scaleDown,
                                                child: TextWidget(
                                                  title: sport['title'],
                                                  fontSize:
                                                      widget.selectedSportCategory[
                                                                  'categoryName'] ==
                                                              'Outdoor'
                                                          ? tS * 14
                                                          : tS * 12,
                                                  color: sport['isSelected']
                                                      ? Colors.white
                                                      : getThemeColor(),
                                                  fontWeight: FontWeight.w600,
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              validateSelection(sport);
                                            },
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                  top: sport['sport'] ==
                                                          'Snooker'
                                                      ? dW * 0.04
                                                      : dW * 0.02),
                                              width: dW * 0.23,
                                              child: CustomTextFieldWithLabel(
                                                label: '',
                                                controller: sport['controller'],
                                                textAlign: TextAlign.left,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        vertical: dW * 0.04),
                                                prefixIcon: Icon(
                                                  Icons.currency_rupee_sharp,
                                                  size: 16,
                                                  color: blackColor3,
                                                ),
                                                // hintText: 'Price',
                                                hintText: '',
                                                hintFS: 12,
                                                borderRadius: 7,
                                                textFS: 13.5,
                                                inputFormatter: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                enabled: sport['isSelected']
                                                    ? true
                                                    : false,
                                                maxLength: 6,
                                                inputType: TextInputType.number,
                                                onChanged: (value) {
                                                  toggleAutoFillPrice(
                                                      isAutoFillPrice);
                                                },
                                              ),
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              validateSelection(sport);
                                            },
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                  top: sport['sport'] ==
                                                          'Snooker'
                                                      ? dW * 0.04
                                                      : dW * 0.02),
                                              width: dW * 0.23,
                                              child: CustomTextFieldWithLabel(
                                                label: '',
                                                controller:
                                                    sport['weekendController'],
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        vertical: dW * 0.04),
                                                prefixIcon: Icon(
                                                  Icons.currency_rupee_sharp,
                                                  size: 16,
                                                  color: blackColor3,
                                                ),
                                                // hintText: 'Price',
                                                hintText: '',
                                                borderRadius: 7,
                                                hintFS: 12,
                                                textFS: 13.5,
                                                inputFormatter: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                maxLength: 6,
                                                inputType: TextInputType.number,
                                                onChanged: (value) {
                                                  toggleAutoFillPrice(
                                                      isAutoFillPrice);
                                                },
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                      // Container(
                                      //     margin:
                                      //         EdgeInsets.only(top: dW * 0.02),
                                      //     child: Divider(color: blackColor3)),
                                    ],
                                  );
                                },
                              ),

                              // if (i == 0)
                              if (widget.selectedSlots.length != 1)
                                SizedBox(
                                  height: dW * 0.04,
                                ),
                              // if (i == 0)
                              if (widget.selectedSlots.length != 1)
                                CustomCheckbox2(
                                  value: isAutoFillPrice,
                                  spaceBetween: dW * 0.02,
                                  textColor: Color(0xff3E3E3E),
                                  size: 15,
                                  reverseCheckBox: true,
                                  borderColor:
                                      Color(0xff3E3E3E).withOpacity(0.7),
                                  onChanged: () =>
                                      toggleAutoFillPrice(!isAutoFillPrice),
                                  activeColor: getThemeColor(),
                                  title: widget.selectedSportCategory[
                                              'categoryName'] ==
                                          'Outdoorr'
                                      ? 'Same for all turf sizes'
                                      : 'Same for all',
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              })
              .values
              .toList(),
        ],
      ),
    );
  }
}
