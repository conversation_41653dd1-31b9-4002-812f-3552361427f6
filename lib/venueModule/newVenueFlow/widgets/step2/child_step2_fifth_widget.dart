// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

class ChildStep2FifthWidget extends StatelessWidget {
  final List selectedSlots;
  final List listofSlotTime;
  final int selectedSlotDuration;
  final Function selectTime;
  final Function selectSlotDuration;

  ChildStep2FifthWidget({
    required this.selectedSlots,
    required this.listofSlotTime,
    required this.selectedSlotDuration,
    required this.selectTime,
    required this.selectSlotDuration,
  });

  double dW = 0.0;
  double tS = 0.0;

  Widget buildTimeSelectionWidget(
      {required String title, required String value, required Function onTap}) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: title,
            color: Color(0xff9798A3),
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.02),
          CustomContainer(
            boxShadow: [],
            width: dW * 0.4,
            hPadding: .03,
            borderColor: Color(0xffACACB4),
            radius: 7,
            bgColor: Color(0xffF8F9FD),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: value,
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                AssetSvgIcon(iconName: 'clock2', height: 22)
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          Align(
            alignment: Alignment.topLeft,
            child: TextWidget(
              title: 'Let’s set timings for your venue',
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: dW * 0.1),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextWidget(
                title: 'Time Slot Duration',
                fontWeight: FontWeight.w500,
              ),
              SizedBox(height: dW * 0.035),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ...listofSlotTime.map(
                    (slot) => GestureDetector(
                      onTap: () => selectSlotDuration(slot['id']),
                      child: CustomContainer(
                        boxShadow: [],
                        vPadding: .03,
                        radius: 7,
                        borderColor: slot['slot'] == selectedSlotDuration
                            ? getThemeColor()
                            : Color.fromRGBO(172, 172, 180, 1),
                        bgColor: slot['slot'] == selectedSlotDuration
                            ? getThemeColor()
                            : Colors.transparent,
                        width: dW * 0.38,
                        child: TextWidget(
                          title: '${slot['slot']} mins',
                          color: slot['slot'] == selectedSlotDuration
                              ? Colors.white
                              : Color(0xff242530),
                          fontWeight: FontWeight.w500,
                          fontSize: 17,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          DividerWidget(top: 20, bottom: 0, color: Color(0xffD9D9D9)),
          ...selectedSlots
              .asMap()
              .map(
                (i, slot) => MapEntry(
                  i,
                  Container(
                    padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                    decoration: i == selectedSlots.length - 1
                        ? null
                        : BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: Color(0xffD9D9D9)))),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          title: '${slot['title']} Slot',
                          color: getThemeColor(),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        SizedBox(height: dW * 0.03),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            buildTimeSelectionWidget(
                              title: 'Start Time',
                              value:
                                  '${slot['startTime'].format(context) == '00:00' ? '12:00' : slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                              onTap: () => selectTime(
                                  true, slot['id'], slot['startTime']),
                            ),
                            SizedBox(height: dW * 0.05),
                            buildTimeSelectionWidget(
                              title: 'End Time',
                              value:
                                  '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                              onTap: () => selectTime(
                                  false, slot['id'], slot['endTime']),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              )
              .values
              .toList(),
        ],
      ),
    );
  }
}
