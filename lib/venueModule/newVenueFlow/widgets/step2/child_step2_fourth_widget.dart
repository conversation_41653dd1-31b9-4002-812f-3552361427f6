// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

class ChildStep2FourthWidget extends StatelessWidget {
  String option;
  bool isNet;
  List selectedTurfSize;
  List listOfTurfSizeAndPrice;
  Function setUsePreviousValueForPrice;
  Function selectAndUnSelectType;

  ChildStep2FourthWidget({
    required this.option,
    required this.isNet,
    required this.selectedTurfSize,
    required this.listOfTurfSizeAndPrice,
    required this.setUsePreviousValueForPrice,
    required this.selectAndUnSelectType,
  });

  double dW = 0.0;
  double tS = 0.0;

  checkTurfSize(size) {
    if (option == 'One On') {
      if (selectedTurfSize.length == 1) {
        return false;
      } else {
        if (selectedTurfSize.contains(size)) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      if (isNet) {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else if (size == '8:8') {
          if (selectedTurfSize.contains('9:9') ||
              selectedTurfSize.contains('7:7')) {
            return true;
          } else {
            return false;
          }
        } else if (size == '9:9' || size == '7:7') {
          if (selectedTurfSize.contains('8:8')) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }

        //Blocking 6 and 5
        // if (size == '8:8') {
        //   if (selectedTurfSize.contains('9:9') ||
        //       selectedTurfSize.contains('7:7') ||
        //       selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '9:9' || size == '7:7') {
        //   if (selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '6:6') {
        //   if (selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '5:5') {
        //   if (selectedTurfSize.contains('6:6') ||
        //       selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else {
        //   return false;
        // }
      } else {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          Align(
            alignment: Alignment.topLeft,
            child: TextWidget(
              title: 'Now add turf sizes available at your venue',
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: dW * 0.1),
          Wrap(
            children: [
              ...listOfTurfSizeAndPrice.map(
                (size) => GestureDetector(
                  onTap: size['title'] == '1:1'
                      ? null
                      : () {
                          if (!checkTurfSize(size['title'])) {
                            selectAndUnSelectType(
                              size['id'],
                              size['isSelected'],
                              listOfTurfSizeAndPrice,
                              selectedTurfSize,
                              false,
                            );
                            setUsePreviousValueForPrice();
                          }
                        },
                  child: Container(
                    margin:
                        EdgeInsets.only(right: dW * 0.03, bottom: dW * 0.03),
                    constraints: BoxConstraints(
                      minWidth: size['title'] == '1:1' ? dW * 0.12 : dW * 0.15,
                    ),
                    padding: EdgeInsets.symmetric(
                        vertical: dW * 0.02, horizontal: dW * 0.05),
                    decoration: BoxDecoration(
                      color: checkTurfSize(size['title'])
                          ? Colors.grey.shade300
                          : size['isSelected']
                              ? Theme.of(context).primaryColor
                              : Colors.white,
                      borderRadius: BorderRadius.circular(100),
                      border: Border.all(
                        color: checkTurfSize(size['title'])
                            ? Colors.black12
                            : size['isSelected']
                                ? Theme.of(context).primaryColor
                                : Color(0xff5E5E5E),
                      ),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: TextWidget(
                        title: size['title'].toString().replaceAll(':', ' x '),
                        color: checkTurfSize(size['title'])
                            ? Colors.grey.shade500
                            : size['isSelected']
                                ? Colors.white
                                : Colors.black,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
