// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/custom_container.dart';
import '../../../../common_function.dart';
import '../../../widgets/venueDetailsWidget/multi_select_bottom_sheet.dart';

class ChildStep2FirstWidget extends StatelessWidget {
  final List listOfDays;
  final List selectedWeekDays;
  final List selectedWeekEnds;
  final Function selectAndUnselectDays;

  ChildStep2FirstWidget({
    required this.listOfDays,
    required this.selectedWeekDays,
    required this.selectedWeekEnds,
    required this.selectAndUnselectDays,
  });

  double dW = 0.0;
  double tS = 0.0;

  List<String> getDays(bool isWeekdays) {
    if (isWeekdays) {
      if (selectedWeekEnds.contains('Fri')) {
        return ['Mon', 'Tue', 'Wed', 'Thu'];
      } else {
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
      }
    } else {
      if (selectedWeekDays.contains('Fri')) {
        return ['Sat', 'Sun'];
      } else {
        return [
          'Fri',
          'Sat',
          'Sun',
        ];
      }
    }
  }

  selectDaysBottomSheet({
    required BuildContext context,
    required bool isWeekdays,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => MultiSelectBottomSheet(
        listOfFields: getDays(isWeekdays),
        title: 'Select ${isWeekdays ? 'Weekdays' : 'Weekends'}',
        selectedFields: isWeekdays
            ? List<String>.from(selectedWeekDays)
            : List<String>.from(selectedWeekEnds),
      ),
    ).then((value) {
      if (value != null) {
        try {
          selectAndUnselectDays(isWeekdays: isWeekdays, days: value);
        } catch (e) {
          print(e);
        }
      }
    });
  }

  Widget buildDaysSelectionWidget({
    required BuildContext context,
    required bool isWeekdays,
    required List listOfIterate,
  }) {
    return GestureDetector(
      onTap: () =>
          selectDaysBottomSheet(context: context, isWeekdays: isWeekdays),
      child: CustomContainer(
        boxShadow: [],
        radius: 8,
        vPadding: 0.03,
        hPadding: 0,
        borderColor: getThemeColor(),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: dW * 0.7,
              child: (selectedWeekDays.isEmpty && isWeekdays) ||
                      (selectedWeekEnds.isEmpty && !isWeekdays)
                  ? Padding(
                      padding: EdgeInsets.only(left: dW * 0.03),
                      child: TextWidget(
                        title: 'Select ${isWeekdays ? 'weekdays' : 'weekends'}',
                        textAlign: TextAlign.left,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: BouncingScrollPhysics(),
                      child: Row(
                        children: [
                          SizedBox(width: dW * 0.03),
                          ...listOfIterate.map(
                            (day) => Container(
                              margin: EdgeInsets.only(right: dW * 0.02),
                              constraints: BoxConstraints(minWidth: dW * 0.13),
                              padding: EdgeInsets.symmetric(
                                vertical: dW * 0.015,
                                horizontal: dW * 0.02,
                              ),
                              decoration: BoxDecoration(
                                color: getThemeColor(),
                                borderRadius: BorderRadius.circular(50),
                                border: Border.all(color: getThemeColor()),
                              ),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: day,
                                  fontSize: 13,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: dW * 0.03),
                        ],
                      ),
                    ),
            ),
            Padding(
              padding: EdgeInsets.only(right: dW * 0.02),
              child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Let’s describe weekdays and weekends for you venue',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'Select the venue weekends and weekdays, whether it\'s outdoor, indoor or an esports arena.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.1),
          TextWidget(
            title: 'Weekdays',
            fontWeight: FontWeight.w500,
            color: Color(0xff9798A3),
          ),
          SizedBox(height: dW * 0.03),
          buildDaysSelectionWidget(
            context: context,
            isWeekdays: true,
            listOfIterate: selectedWeekDays,
          ),
          SizedBox(height: dW * 0.075),

          //Select Weekends

          TextWidget(
            title: 'Weekend Days',
            fontWeight: FontWeight.w500,
            color: Color(0xff9798A3),
          ),
          SizedBox(height: dW * 0.03),
          buildDaysSelectionWidget(
            context: context,
            isWeekdays: false,
            listOfIterate: selectedWeekEnds,
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
