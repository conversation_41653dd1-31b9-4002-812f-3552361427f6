// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';

import '../../../../common_function.dart';

class ChildStep2SecondWidget extends StatelessWidget {
  final List listOfSlots;
  final List selectedSlots;
  final Function selectAndUnSelectType;

  ChildStep2SecondWidget({
    required this.listOfSlots,
    required this.selectedSlots,
    required this.selectAndUnSelectType,
  });

  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Now select time slots',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: dW * 0.03),
          TextWidget(
            title:
                'Select the time slots in which your venues are open for the users',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.1),
          Wrap(
            children: [
              ...listOfSlots.map(
                (slot) => GestureDetector(
                  onTap: () {
                    selectAndUnSelectType(slot['id'], slot['isSelected'],
                        listOfSlots, selectedSlots, true);
                  },
                  child: IntrinsicWidth(
                    child: Container(
                      margin:
                          EdgeInsets.only(right: dW * 0.03, bottom: dW * 0.03),
                      // width: dW * 0.4,
                      padding: EdgeInsets.symmetric(
                          vertical: dW * 0.025, horizontal: dW * 0.03),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: slot['isSelected']
                            ? getThemeColor()
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(100),
                        border: Border.all(color: getThemeColor()),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextWidget(
                            title: slot['title'],
                            fontSize: slot['title'] == 'Badminton' ? 16 : 18,
                            fontWeight: FontWeight.w500,
                            color: !slot['isSelected']
                                ? Colors.black
                                : Colors.white,
                          ),
                          SizedBox(width: dW * .03),
                          slot['title'] == 'Late Night'
                              ? Image.asset(
                                  'assets/images/late_night.png',
                                  height: dW * 0.06,
                                )
                              : Image.asset(
                                  'assets/images/${slot['title'].toLowerCase()}.png',
                                  height: dW * 0.06,
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
