import 'package:flutter/material.dart';

import '../../../../colors.dart';
import '../../../../commonWidgets/custom_checkBox2.dart';
import '../../../../commonWidgets/custom_checkbox_widget.dart';
import '../../../../common_function.dart';

class ChildStep2ThirdIndoorWidget extends StatefulWidget {
  final List selectedSports;
  final List listOfSports;

  final List<Map<String, dynamic>> listOfSportOfferings;
  final Function setSportOfferingTypes;

  ChildStep2ThirdIndoorWidget({
    required this.selectedSports,
    required this.listOfSports,
    required this.listOfSportOfferings,
    required this.setSportOfferingTypes,
  });

  @override
  State<ChildStep2ThirdIndoorWidget> createState() =>
      _ChildStep2ThirdIndoorWidgetState();
}

class _ChildStep2ThirdIndoorWidgetState
    extends State<ChildStep2ThirdIndoorWidget> {
  double dW = 0.0;

  double tS = 0.0;
  bool isAutoFillPrice = false;

  void toggleAutoFillPrice(bool newValue) {
    setState(() {
      isAutoFillPrice = newValue;

      if (isAutoFillPrice) {
        // Find the first sport in commonSports
        final firstSportTitle = widget.selectedSports.firstWhere((sport) =>
            widget.listOfSportOfferings
                .any((offering) => offering['title'] == sport));

        // Find the offering of the first sport
        final firstSportOffering = widget.listOfSportOfferings.firstWhere(
          (offering) => offering['title'] == firstSportTitle,
          orElse: () => {},
        );

        if (firstSportOffering != null) {
          // Get the states of "Singles" and "Doubles" settings for the first sport
          final singlesState = firstSportOffering['type']
              .firstWhere((type) => type.keys.first == 'Singles')['Singles'];
          final doublesState = firstSportOffering['type']
              .firstWhere((type) => type.keys.first == 'Doubles')['Doubles'];

          // Apply the states of "Singles" and "Doubles" settings for the first sport to all other sports
          for (var i = 0; i < widget.listOfSportOfferings.length; i++) {
            final sportOffering = widget.listOfSportOfferings[i];
            for (var j = 0; j < sportOffering['type'].length; j++) {
              final typeName = sportOffering['type'][j].keys.first;
              if (typeName == 'Singles') {
                sportOffering['type'][j][typeName] = singlesState;
              } else if (typeName == 'Doubles') {
                sportOffering['type'][j][typeName] = doublesState;
              }
            }
          }
        }
      }
    });
  }

  void toggleAutoFillPrices(bool newValue) {
    setState(() {
      isAutoFillPrice = newValue;
      List commonSports = widget.selectedSports
          .where((sport) => widget.listOfSportOfferings
              .any((offering) => offering['title'] == sport))
          .toList();

      if (isAutoFillPrice) {
        // Get the first sport in commonSports and find its offerings
        final firstCommonSport =
            commonSports.isNotEmpty ? commonSports.first : null;
        final firstSportOffering = firstCommonSport != null
            ? widget.listOfSportOfferings.firstWhere(
                (offering) => offering['title'] == firstCommonSport,
                orElse: () => {},
              )
            : null;

        if (firstSportOffering != null) {
          // Apply the settings of the first sport to all other selected sports
          for (var i = 0; i < widget.listOfSportOfferings.length; i++) {
            final sportOffering = widget.listOfSportOfferings[i];
            for (var j = 0; j < sportOffering['type'].length; j++) {
              final typeName = sportOffering['type'][j].keys.first;
              // Apply the same checked value as the first sport
              sportOffering['type'][j][typeName] =
                  firstSportOffering['type'][j].values.first;
            }
          }
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    List commonSports = widget.selectedSports
        .where((sport) => widget.listOfSportOfferings
            .any((offering) => offering['title'] == sport))
        .toList();

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          Text(
            'Specify the game offerings at your venue',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: dW * 0.1),
          ...commonSports.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final sportOffering = widget.listOfSportOfferings.firstWhere(
              (offering) => offering['title'] == option,
              orElse: () => {'type': []},
            );

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                      'assets/images/$option.png',
                      height: 24,
                    ),
                    SizedBox(
                      width: dW * 0.02,
                    ),
                    Text(
                      option,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3E3E3E),
                      ),
                    ),
                  ],
                ),
                // Image.network(),

                SizedBox(height: dW * 0.04),
                ...sportOffering['type'].map<Widget>((type) {
                  final typeName = type.keys.first;
                  bool isChecked = type[typeName];

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        // type[typeName] = !isChecked;
                        widget.setSportOfferingTypes(
                            option, typeName, !isChecked);
                        if (index == 0) {
                          toggleAutoFillPrice(isAutoFillPrice);
                        }
                      });
                    },
                    child: Container(
                      margin: EdgeInsets.only(bottom: dW * 0.06),
                      padding: EdgeInsets.symmetric(
                        horizontal: dW * 0.04,
                        vertical: dW * 0.06,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          width: 1,
                          color: isChecked ? Colors.green : Colors.grey,
                        ),
                      ),
                      width: dW,
                      child: Row(
                        children: [
                          // Image.asset(
                          //   'assets/images/$typeName.png',
                          //   height: 24,
                          // ),
                          SizedBox(width: dW * 0.04),
                          Expanded(
                            child: CustomCheckbox(
                              title: typeName,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              textColor:
                                  isChecked ? getThemeColor() : blackColor1,
                              onChanged: () {
                                setState(() {
                                  // type[typeName] = !isChecked;

                                  widget.setSportOfferingTypes(
                                      option, typeName, !isChecked);
                                  if (index == 0) {
                                    toggleAutoFillPrice(isAutoFillPrice);
                                  }
                                });
                              },
                              value: isChecked,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                if (index == 0 && commonSports.length > 1)
                  CustomCheckbox2(
                    value: isAutoFillPrice,
                    spaceBetween: dW * 0.04,
                    size: 15,
                    reverseCheckBox: true,
                    onChanged: () => toggleAutoFillPrice(!isAutoFillPrice),
                    // setSameForAll,
                    // onChanged: widget.toggleAutoFillPrice!,
                    activeColor: getThemeColor(),
                    title: 'Same for all sports',
                  ),
                if (index != commonSports.length - 1)
                  Container(
                    margin: EdgeInsets.symmetric(vertical: dW * 0.07),
                    child: Divider(
                      color: Color(0xffD9D9D9),
                      thickness: 1,
                    ),
                  ),
              ],
            );
          }),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
