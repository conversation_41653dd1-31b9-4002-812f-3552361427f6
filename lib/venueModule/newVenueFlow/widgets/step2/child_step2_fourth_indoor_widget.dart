// ignore_for_file: must_be_immutable

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

class ChildStep2FourthIndoorWidget extends StatefulWidget {
  final List listOfCourtsAndTables;
  final List listOfSports;
  final Function setCourtsAndTableTypes;

  ChildStep2FourthIndoorWidget({
    required this.listOfCourtsAndTables,
    required this.listOfSports,
    required this.setCourtsAndTableTypes,
  });

  @override
  State<ChildStep2FourthIndoorWidget> createState() =>
      _ChildStep2FourthIndoorWidgetState();
}

class _ChildStep2FourthIndoorWidgetState
    extends State<ChildStep2FourthIndoorWidget> {
  double dW = 0.0;

  double tS = 0.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Now add type of courts and tables available at your venue',
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.1),
          ...widget.listOfSports
              .where(
                  (element) => element == 'Badminton' || element == 'Snooker')
              .map((sports) {
            final isLastSport = sports == widget.listOfSports.last;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                      'assets/images/$sports.png',
                      height: 24,
                    ),
                    SizedBox(
                      width: dW * 0.02,
                    ),
                    TextWidget(
                      title: sports,
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3E3E3E),
                    ),
                  ],
                ),
                SizedBox(
                  height: dW * 0.04,
                ),
                ...widget.listOfCourtsAndTables
                    .where((courtOrTable) => courtOrTable['sport'] == sports)
                    .map((courtOrTable) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        widget.setCourtsAndTableTypes(
                            sports, courtOrTable['title']);
                      });
                    },
                    child: Container(
                        margin: EdgeInsets.only(bottom: dW * 0.04),
                        // width: dW * 0.6,
                        padding: EdgeInsets.only(
                          left: dW * 0.04,
                          top: dW * 0.035,
                          right: dW * 0.045,
                          bottom: dW * 0.035,
                        ),
                        decoration: BoxDecoration(
                            color: courtOrTable['isSelected']
                                ? getThemeColor()
                                : whiteColor,
                            border: Border.all(
                              color: getThemeColor(),
                            ),
                            borderRadius: BorderRadius.circular(55)),
                        child: IntrinsicWidth(
                          child: Row(
                            children: [
                              TextWidget(
                                title: courtOrTable['title'],
                                fontWeight: FontWeight.w500,
                                fontSize: 20,
                                color: courtOrTable['isSelected']
                                    ? whiteColor
                                    : Color(0xff5E5E5E),
                              ),
                              SizedBox(
                                width: dW * 0.04,
                              ),
                              // Image.asset(
                              //   'assets/images/${courtOrTable['title']}.png',
                              //   height: 24,
                              //   width: 24,
                              // )
                            ],
                          ),
                        )),
                  );
                }).toList(),
                if (!isLastSport)
                  Container(
                    margin: EdgeInsets.symmetric(vertical: dW * 0.07),
                    child: Divider(
                      color: Color(0xffD9D9D9),
                      thickness: 1,
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }
}
