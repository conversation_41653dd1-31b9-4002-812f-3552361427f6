// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../commonWidgets/custom_text_field.dart';

class ChildStep1FirstWidget extends StatefulWidget {
  final TextEditingController venueNameController;
  final TextEditingController venuePhoneController;
  final TextEditingController aboutTurfController;

  ChildStep1FirstWidget({
    required this.venueNameController,
    required this.venuePhoneController,
    required this.aboutTurfController,
  });

  @override
  State<ChildStep1FirstWidget> createState() => _ChildStep1FirstWidgetState();
}

class _ChildStep1FirstWidgetState extends State<ChildStep1FirstWidget> {
  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Lets give venue title & small description',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title: 'Now give your venue title and small description',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.08),
          CustomTextFieldWithLabel(
            label: 'Name',
            controller: widget.venueNameController,
            hintText: 'Enter venue name',
            borderRadius: 5,
            labelFS: 14,
            labelColor: Color(0xff9798A3),
            labelFW: FontWeight.w500,
            hintColor: Colors.black,
            borderColor: Color(0xffACACB4),
            fillColor: Color(0xffF8F9FD),
            onChanged: (value) => setState(() {}),
            inputAction: TextInputAction.next,
          ),
          SizedBox(height: dW * 0.05),
          CustomTextFieldWithLabel(
            label: 'Phone',
            controller: widget.venuePhoneController,
            hintText: 'Enter venue phone no.',
            borderRadius: 5,
            labelFS: 14,
            maxLength: 10,
            labelColor: Color(0xff9798A3),
            labelFW: FontWeight.w500,
            hintColor: Colors.black,
            borderColor: Color(0xffACACB4),
            fillColor: Color(0xffF8F9FD),
            onChanged: (value) => setState(() {}),
            inputFormatter: [FilteringTextInputFormatter.digitsOnly],
            inputType: TextInputType.number,
            inputAction: TextInputAction.next,
          ),
          SizedBox(height: dW * 0.05),
          CustomTextFieldWithLabel(
            label: 'Description',
            controller: widget.aboutTurfController,
            hintText: 'Enter description here ',
            borderRadius: 5,
            labelFS: 14,
            labelColor: Color(0xff9798A3),
            labelFW: FontWeight.w500,
            hintColor: Colors.black,
            borderColor: Color(0xffACACB4),
            fillColor: Color(0xffF8F9FD),
            counterText: '${widget.aboutTurfController.text.trim().length}/300',
            maxLines: 6,
            maxLength: 300,
            textFS: 14,
            textCapitalization: TextCapitalization.sentences,
            inputType: TextInputType.streetAddress,
            inputAction: TextInputAction.newline,
            onChanged: (value) => setState(() {}),
          ),
        ],
      ),
    );
  }
}
