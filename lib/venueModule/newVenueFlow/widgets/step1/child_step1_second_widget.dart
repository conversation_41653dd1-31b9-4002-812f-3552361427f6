// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/search_box_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/new_colors.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step1/embedded_map_widget.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/custom_text_field.dart';
import '../../../models/venue_model.dart';
import 'search_location_bottomsheet.dart';

class ChildStep1SecondWidget extends StatefulWidget {
  final Address? venueAddress;
  final Function setVenueAddress;
  final TextEditingController landmarkController;

  ChildStep1SecondWidget({
    required this.venueAddress,
    required this.setVenueAddress,
    required this.landmarkController,
  });

  @override
  State<ChildStep1SecondWidget> createState() => _ChildStep1FirstWidgetState();
}

class _ChildStep1FirstWidgetState extends State<ChildStep1SecondWidget> {
  double dW = 0.0;
  double tS = 0.0;

  openSearchLocationBottomSheet() {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SearchLocationBottomSheet(
            fullAddress: widget.venueAddress != null
                ? widget.venueAddress!.fullAddress
                : ''),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        widget.setVenueAddress(value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Where is your venue located?',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'Specify your venue\'s location with a map pinpoint and nearby landmark details.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.08),
          // SearchBoxWidget(
          //   searchBarText: 'Search location here',
          //   onTap: openSearchLocationBottomSheet,
          // ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  TextWidget(
                    title: 'Venue Address',
                    color: Color(0xff9798A3),
                    fontWeight: FontWeight.w500,
                  ),
                  TextWidget(
                    title: '*',
                    color: redColor,
                  ),
                ],
              ),
              SizedBox(height: dW * 0.025),
              GestureDetector(
                onTap: openSearchLocationBottomSheet,
                child: CustomContainer(
                  width: dW,
                  boxShadow: [],
                  borderColor: Color(0xffACACB4),
                  bgColor: Color(0xffF8F9FD),
                  radius: 5,
                  hPadding: .03,
                  vPadding: .03,
                  child: TextWidget(
                    title: widget.venueAddress == null
                        ? 'Select Address'
                        : widget.venueAddress!.fullAddress.trim().isEmpty
                            ? '${widget.venueAddress!.area.isEmpty ? widget.venueAddress!.streetName : widget.venueAddress!.area}, ${widget.venueAddress!.landmark}, ${widget.venueAddress!.city}, ${widget.venueAddress!.state}, ${widget.venueAddress!.pincode}.'
                            : '${widget.venueAddress!.fullAddress}.',
                    fontWeight: widget.venueAddress == null
                        ? FontWeight.normal
                        : FontWeight.w600,
                    fontSize: 15,
                    color: blackColor3,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: dW * 0.05),
          CustomTextFieldWithLabel(
            label: 'Landmark',
            controller: widget.landmarkController,
            hintText: 'Enter landmark here',
            borderRadius: 5,
            labelFS: 14,
            labelColor: Color(0xff9798A3),
            labelFW: FontWeight.w500,
            hintColor: Colors.black,
            borderColor: Color(0xffACACB4),
            fillColor: Color(0xffF8F9FD),
            onChanged: (value) => setState(() {}),
            inputAction: TextInputAction.next,
          ),
        ],
      ),
    );
  }
}
