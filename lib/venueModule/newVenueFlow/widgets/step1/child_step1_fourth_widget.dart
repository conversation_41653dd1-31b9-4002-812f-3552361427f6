// // ignore_for_file: must_be_immutable

// import 'package:bys_business/commonWidgets/text_widget.dart';
// import 'package:bys_business/common_function.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';

// class ChildStep1FourthWidget extends StatelessWidget {
//   final List listOfSports;
//   final List selectedSports;
//   // final String selectedSports;
//   final List listofSportCategory;
//   final Map selectedSportCategory;
//   final Function selectAndUnSelectType;

//   ChildStep1FourthWidget({
//     required this.listOfSports,
//     required this.selectedSports,
//     required this.listofSportCategory,
//     required this.selectedSportCategory,
//     required this.selectAndUnSelectType,
//   });

//   double dW = 0.0;
//   double tS = 0.0;

//   String getImage(String title) {
//     if (title == 'Indoor') {
//       return 'indoor';
//     } else if (title == 'Outdoor') {
//       return 'outdoor';
//     } else {
//       return 'esport';
//     }
//   }

//   String getSportsName(String categoryId) {
//     List sport =
//         listOfSports.where((data) => data['categoryId'] == categoryId).toList();
//     String sportsName = '';
//     sport.forEach((data) {
//       sportsName += '${data['title']}, ';
//     });

//     List splitList = sportsName.split(',');
//     splitList.remove(" ");
//     sportsName = splitList.join(',');
//     return sportsName;
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;

//     return Container(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SizedBox(height: dW * 0.05),
//           TextWidget(
//             title: 'Now select which sports played at your venue',
//             fontSize: 18,
//             fontWeight: FontWeight.w500,
//           ),
//           SizedBox(height: dW * 0.03),
//           TextWidget(
//             title: 'Select the sports that you offered at your venue',
//             fontSize: 12,
//             color: Color(0xff21272A),
//           ),
//           SizedBox(height: dW * 0.1),
//           Wrap(
//             children: [
//               ...listOfSports
//                   .where((sport) =>
//                       sport['categoryId'] ==
//                       selectedSportCategory['categoryId'])
//                   .toList()
//                   .map(
//                     (sport) => GestureDetector(
//                       onTap: () {
//                         // listOfSports.forEach((element) {
//                         //   if (element['categoryId'] ==
//                         //       selectedSportCategory['categoryId']) {
//                         //     if (element['id'] == sport['id']) {
//                         //       // Select the tapped sport

//                         //       selectAndUnSelectType(element['id'], true,
//                         //           listOfSports, selectedSports, false);
//                         //     } else {
//                         //       // Deselect other sports
//                         //       selectAndUnSelectType(element['id'], false,
//                         //           listOfSports, selectedSports, false);
//                         //     }
//                         //   }
//                         // });
//                         // when listOfSports is string

//                         // listOfSports.forEach((element) {
//                         //   if (element['categoryId'] ==
//                         //       selectedSportCategory['categoryId']) {
//                         //     selectAndUnSelectType(element['id'], true,
//                         //         listOfSports, selectedSports, false);
//                         //   }
//                         // });
//                         // // Select the tapped sport
//                         // selectAndUnSelectType(sport['id'], sport['isSelected'],
//                         //     listOfSports, selectedSports, false);

//                         // when listOfSports is list

//                         selectAndUnSelectType(sport['id'], sport['isSelected'],
//                             listOfSports, selectedSports, false);
//                       },
//                       child: Container(
//                         margin: EdgeInsets.only(
//                             right: dW * 0.03, bottom: dW * 0.03),
//                         width: sport['title'] == 'Badminton'
//                             ? dW * 0.44
//                             : sport['title'] == 'Table Tennis'
//                                 ? dW * 0.5
//                                 : dW * 0.4,
//                         padding: EdgeInsets.symmetric(
//                             vertical: dW * 0.025, horizontal: dW * 0.03),
//                         alignment: Alignment.center,
//                         decoration: BoxDecoration(
//                           color: sport['isSelected']
//                               ? getThemeColor()
//                               : Colors.transparent,
//                           borderRadius: BorderRadius.circular(100),
//                           border: Border.all(color: getThemeColor()),
//                         ),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             ConstrainedBox(
//                               constraints: BoxConstraints(maxWidth: dW * 0.3),
//                               child: FittedBox(
//                                 fit: BoxFit.scaleDown,
//                                 child: TextWidget(
//                                   title: sport['title'],
//                                   fontSize:
//                                       sport['title'] == 'Badminton' ? 16 : 18,
//                                   fontWeight: FontWeight.w500,
//                                   color: !sport['isSelected']
//                                       ? Colors.black
//                                       : Colors.white,
//                                 ),
//                               ),
//                             ),
//                             SizedBox(width: dW * .03),
//                             if (sport['image'] != '') ...[
//                               !sport['image'].contains('https')
//                                   ? Image.asset(sport['image'], height: 35)
//                                   : CachedNetworkImage(
//                                       imageUrl: sport['image'],
//                                       height: 30,
//                                       fit: BoxFit.cover,
//                                       placeholder: (_, __) => Image.asset(
//                                         'assets/images/user.png',
//                                         fit: BoxFit.cover,
//                                         height: 35,
//                                       ),
//                                     ),
//                               // Image.network(sport['image'], height: 35),
//                             ],
//                           ],
//                         ),
//                       ),
//                     ),
//                   )
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }

// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ChildStep1FourthWidget extends StatelessWidget {
  final List listOfSports;
  final List selectedSports;
  // final String selectedSports;
  final List listofSportCategory;
  final Map selectedSportCategory;
  final Function selectAndUnSelectType;

  ChildStep1FourthWidget({
    required this.listOfSports,
    required this.selectedSports,
    required this.listofSportCategory,
    required this.selectedSportCategory,
    required this.selectAndUnSelectType,
  });

  double dW = 0.0;
  double tS = 0.0;

  String getImage(String title) {
    if (title == 'Indoor') {
      return 'indoor';
    } else if (title == 'Outdoor') {
      return 'outdoor';
    } else {
      return 'esport';
    }
  }

  String getSportsName(String categoryId) {
    List sport =
        listOfSports.where((data) => data['categoryId'] == categoryId).toList();
    String sportsName = '';
    sport.forEach((data) {
      sportsName += '${data['title']}, ';
    });

    List splitList = sportsName.split(',');
    splitList.remove(" ");
    sportsName = splitList.join(',');
    return sportsName;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Now select which sports played at your venue',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title: 'Select the sports that you offered at your venue',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.1),
          Wrap(
            children: [
              ...listOfSports
                  .where((sport) =>
                      sport['categoryId'] ==
                      selectedSportCategory['categoryId'])
                  .toList()
                  .where((sport) => sport['title'] != 'Pool') // Temp line added
                  .map(
                    (sport) => GestureDetector(
                      onTap: () {
                        // listOfSports.forEach((element) {
                        //   if (element['categoryId'] ==
                        //       selectedSportCategory['categoryId']) {
                        //     if (element['id'] == sport['id']) {
                        //       // Select the tapped sport

                        //       selectAndUnSelectType(element['id'], true,
                        //           listOfSports, selectedSports, false);
                        //     } else {
                        //       // Deselect other sports
                        //       selectAndUnSelectType(element['id'], false,
                        //           listOfSports, selectedSports, false);
                        //     }
                        //   }
                        // });
                        // when listOfSports is string

                        // listOfSports.forEach((element) {
                        //   if (element['categoryId'] ==
                        //       selectedSportCategory['categoryId']) {
                        //     selectAndUnSelectType(element['id'], true,
                        //         listOfSports, selectedSports, false);
                        //   }
                        // });
                        // // Select the tapped sport
                        // selectAndUnSelectType(sport['id'], sport['isSelected'],
                        //     listOfSports, selectedSports, false);

                        // when listOfSports is list

                        selectAndUnSelectType(sport['id'], sport['isSelected'],
                            listOfSports, selectedSports, false);
                      },
                      child: Container(
                        margin: EdgeInsets.only(
                            right: dW * 0.03, bottom: dW * 0.03),
                        width: sport['title'] == 'Badminton'
                            ? dW * 0.44
                            : sport['title'] == 'Table Tennis'
                                ? dW * 0.5
                                : dW * 0.4,
                        padding: EdgeInsets.symmetric(
                            vertical: dW * 0.025, horizontal: dW * 0.03),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: sport['isSelected']
                              ? getThemeColor()
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(100),
                          border: Border.all(color: getThemeColor()),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: dW * 0.3),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: sport['title'],
                                  fontSize:
                                      sport['title'] == 'Badminton' ? 16 : 18,
                                  fontWeight: FontWeight.w500,
                                  color: !sport['isSelected']
                                      ? Colors.black
                                      : Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: dW * .03),
                            if (sport['image'] != '') ...[
                              !sport['image'].contains('https')
                                  ? Image.asset(sport['image'], height: 35)
                                  : CachedNetworkImage(
                                      imageUrl: sport['image'],
                                      height: 30,
                                      fit: BoxFit.cover,
                                      placeholder: (_, __) => Image.asset(
                                        'assets/images/user.png',
                                        fit: BoxFit.cover,
                                        height: 35,
                                      ),
                                    ),
                              // Image.network(sport['image'], height: 35),
                            ],
                          ],
                        ),
                      ),
                    ),
                  )
            ],
          ),
        ],
      ),
    );
  }
}
