import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../colors.dart';
import '../../../../common_function.dart';
import '../../../models/venue_model.dart';

class EmbeddedMap extends StatefulWidget {
  final Address address;
  final bool setPolyline;

  const EmbeddedMap(
      {Key? key, required this.address, this.setPolyline = false});

  @override
  State<EmbeddedMap> createState() => _EmbeddedMapState();
}

class _EmbeddedMapState extends State<EmbeddedMap> with WidgetsBindingObserver {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;

  List<LatLng> polylineCoordinates = [];

  late LatLng sourceLocation;
  late LatLng destinationLocation;
  LatLng? currentLocation;

  BitmapDescriptor? sourceIcon;
  BitmapDescriptor? destinationIcon;

  bool locationEnabled = false;

  generateMarkerIcon() async {
    sourceIcon = BitmapDescriptor.fromBytes(
        await getBytesFromAsset('assets/images/source_pin.png', 80));

    // destinationIcon = await BitmapDescriptor.fromAssetImage(
    //   const ImageConfiguration(size: Size(0, 0)),
    //   'assets/images/destination_pin.png',
    // );
    setState(() {});
  }

  Future<void> getPolyPoints() async {
    PolylinePoints polylinePoints = PolylinePoints();

    PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
     googleApiKey:  'AIzaSyD5tfRulNpZlDVGAgjoYqp5uXwjO53v6vA',
      
       request: PolylineRequest(destination: PointLatLng(destinationLocation.latitude, destinationLocation.longitude), origin: PointLatLng(destinationLocation.latitude, destinationLocation.longitude), mode: TravelMode.driving, ),
    );

    if (result.points.isNotEmpty) {
      for (PointLatLng point in result.points) {
        polylineCoordinates.add(LatLng(point.latitude, point.longitude));
      }

      setState(() {});
    }
  }

  init() async {
    locationEnabled = await Permission.location.isGranted;

    sourceLocation = widget.address.coordinates;
    destinationLocation = widget.setPolyline
        ? widget.address.coordinates
        : widget.address.coordinates;

    generateMarkerIcon();
    // if (widget.setPolyline && locationEnabled) {
    //   generateMarkerIcon();
    //   getPolyPoints();
    // }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      width: dW,
      height: dW * 0.55,
      margin: EdgeInsets.only(bottom: dW * 0.02),
      decoration: commonBoxDecoration(10)
          .copyWith(border: Border.all(width: 1.5, color: primaryFadeBorder)),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child:
              // locationEnabled
              //     ?
              GoogleMap(
            mapType: MapType.normal,
            myLocationButtonEnabled: false,
            myLocationEnabled: true,
            polylines: {
              if (widget.setPolyline)
                Polyline(
                  polylineId: const PolylineId('route'),
                  points: polylineCoordinates,
                  color: Theme.of(context).primaryColor,
                  width: 6,
                )
            },
            markers: {
              if (sourceIcon != null)
                Marker(
                  markerId: const MarkerId('source'),
                  icon: sourceIcon!,
                  position: widget.address.coordinates,
                ),
              // if (destinationIcon != null)
              //   Marker(
              //       markerId: const MarkerId('destination'),
              //       icon: destinationIcon!,
              //       position: widget.setPolyline
              //           ? widget.address.coordinates
              //           : widget.address.coordinates),
            },
            zoomGesturesEnabled: false,
            scrollGesturesEnabled: false,
            rotateGesturesEnabled: false,
            zoomControlsEnabled: false,
            initialCameraPosition: CameraPosition(
              target: widget.address.coordinates,
              zoom: widget.setPolyline ? 11.5 : 20,
            ),
            tiltGesturesEnabled: false,
          )
          // : Image.asset(
          //     'assets/images/map_holder.png',
          //     fit: BoxFit.cover,
          //   ),
          ),
    );
  }
}
