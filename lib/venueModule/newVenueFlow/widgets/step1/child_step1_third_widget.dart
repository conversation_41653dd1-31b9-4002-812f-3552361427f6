// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

class ChildStep1ThirdWidget extends StatelessWidget {
  final List listOfSports;
  final List listofSportCategory;
  final Map selectedSportCategory;
  final Function selectSportCategory;

  ChildStep1ThirdWidget({
    required this.listOfSports,
    required this.listofSportCategory,
    required this.selectedSportCategory,
    required this.selectSportCategory,
  });

  double dW = 0.0;
  double tS = 0.0;

  String getImage(String title) {
    if (title == 'Indoor') {
      return 'indoor';
    } else if (title == 'Outdoor') {
      return 'outdoor';
    } else {
      return 'esport';
    }
  }

  String getSportsName(String categoryId) {
    List sport =
        listOfSports.where((data) => data['categoryId'] == categoryId).toList();
    String sportsName = '';
    sport.forEach((data) {
      sportsName += '${data['title']}, ';
    });

    List splitList = sportsName.split(',');
    splitList.remove(" ");
    sportsName = splitList.join(',');
    return sportsName;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Describe your venue type',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'Select the category that best describes your venue, whether it\'s indoor, outdoor, or an esports arena.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.12),
          ...listofSportCategory.map(
            (category) => GestureDetector(
              onTap: () {
                selectSportCategory(category['id']);
              },
              child: CustomContainer(
                margin: EdgeInsets.only(bottom: dW * 0.05),
                boxShadow: [],
                borderColor: category['isSelected']
                    ? getThemeColor()
                    : Color(0xffABABAB),
                bgColor: category['isSelected']
                    ? getThemeColor()
                    : Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          title: category['title'],
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: category['isSelected']
                              ? Colors.white
                              : Colors.black,
                        ),
                        SizedBox(height: dW * 0.02),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: dW * 0.6),
                          child: TextWidget(
                            title: getSportsName(category['id']),
                            fontSize: 11,
                            color: category['isSelected']
                                ? Color(0xffD9D9D9)
                                : Colors.black,
                          ),
                        )
                      ],
                    ),
                    Image.asset(
                      'assets/images/${getImage(category['title'])}.png',
                      height: 30,
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
