import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/materialCircularLoader.dart';
import 'package:bys_business/commonWidgets/raisedButton.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/pick_location_on_map_screen.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

import '../../../../authModule/providers/auth.dart';
import '../../../../commonWidgets/asset_svg_icon.dart';
import '../../../../commonWidgets/custom_text_field.dart';
import '../../../../commonWidgets/search_box_widget.dart';
import '../../../../common_function.dart';
import '../../../models/venue_model.dart';

class SearchLocationBottomSheet extends StatefulWidget {
  final String fullAddress;
  const SearchLocationBottomSheet({Key? key, required this.fullAddress})
      : super(key: key);

  @override
  State<SearchLocationBottomSheet> createState() =>
      _SearchLocationBottomSheetState();
}

class _SearchLocationBottomSheetState extends State<SearchLocationBottomSheet> {
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  bool isManual = false;
  bool isFetchingAddress = false;
  Address? selectedAddress;

  TextEditingController addressController = TextEditingController();

  checkAndGetLocationPermission() async {
    try {
      if (isLoading) return;

      setState(() => isLoading = true);
      await handlePermissionsFunction();

      if (await Permission.location.isGranted) {
        final authProvider = Provider.of<Auth>(context, listen: false);

        await authProvider.fetchMyLocation();

        final coord = authProvider.myCordinates;
        final address =
            await placemarkFromCoordinates(coord.latitude, coord.longitude);
        if (address.isNotEmpty) {
          Address currentAddress = Address(
            id: '',
            coordinates: coord,
            pincode: address[0].postalCode == null
                ? 0
                : int.parse(address[0].postalCode ?? '0'),
            fullAddress: addrPiece(address[0].street) +
                addrPiece(address[0].locality) +
                addrPiece(
                  areaCode[address[0].administrativeArea] ??
                      address[0].administrativeArea ??
                      '',
                ),
            city: address[0].locality ?? '',
            area: '',
            streetName: '',
            landmark: '',
            state: areaCode[address[0].administrativeArea] != null
                ? areaCode[address[0].administrativeArea] ??
                    address[0].administrativeArea ??
                    ''
                : '',
          );
          selectedAddress = currentAddress;
          pop(currentAddress);
        }
      } else {
        showDisabledLocationWidget(
            'Please enable location permissions for a better experience');
        return;
      }
    } catch (e) {
      showDisabledLocationWidget(
          'Please enable location permissions for a better experience');

      showSnackbar('Please enable location access');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  fetchAddressDetails() async {
    try {
      if (isFetchingAddress) return;
      setState(() => isFetchingAddress = true);
      final address = await Provider.of<Auth>(context, listen: false)
          .fetchAddressDetails(queryString: addressController.text.trim());
      if (address == null) {
        callToastMessage('Invalid address');
      } else {
        pop(address);
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isFetchingAddress = false);
    }
  }

  searchLocationAndSet(Address address) async {
    selectedAddress = address;
    pop(address);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    addressController.text = widget.fullAddress;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: Container(
        height: MediaQuery.of(context).viewInsets.bottom == 0
            ? dW * 1.2
            : dW * 1.55,
        child: Column(
          children: [
            SizedBox(height: dW * 0.05),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                      title: 'Search Location', fontWeight: FontWeight.w600),
                  GestureDetector(
                    onTap: pop,
                    child: Container(
                      padding: EdgeInsets.all(3),
                      decoration: BoxDecoration(
                          shape: BoxShape.circle, border: Border.all()),
                      child: Icon(Icons.clear, size: 18),
                    ),
                  )
                ],
              ),
            ),
            DividerWidget(color: Color(0xffD9D9D9), bottom: 0),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        children: [
                          if (!isManual)
                            SearchBoxWidget(
                              searchBarText: 'Search location here',
                              onTap: () {
                                push(
                                  PickLocationOnMapScreen(coordinates: null),
                                ).then((value) {
                                  if (value != null) {
                                    final Address address = value as Address;
                                    searchLocationAndSet(address);
                                  }
                                });
                              },
                            ),
                          if (isManual)
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.03),
                              child: CustomTextFieldWithLabel(
                                label: '',
                                controller: addressController,
                                hintText: 'Enter address here',
                                borderRadius: 5,
                                labelFS: 14,
                                labelColor: Color(0xff9798A3),
                                labelFW: FontWeight.w500,
                                hintColor: Colors.black,
                                borderColor: Color(0xffACACB4),
                                fillColor: Color(0xffF8F9FD),
                                maxLines: 3,
                                maxLength: 200,
                                textFS: 14,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                inputType: TextInputType.streetAddress,
                                inputAction: TextInputAction.newline,
                                onChanged: (value) => setState(() {}),
                              ),
                            ),
                          SizedBox(height: dW * 0.05),
                          isLoading
                              ? circularForButton(dW, color: getThemeColor())
                              : GestureDetector(
                                  onTap: checkAndGetLocationPermission,
                                  child: Container(
                                    margin: EdgeInsets.only(top: dW * 0.03),
                                    padding: EdgeInsets.symmetric(
                                        vertical: dW * 0.00),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const AssetSvgIcon(
                                            iconName: 'navigator'),
                                        SizedBox(width: dW * 0.02),
                                        Text(
                                          'Use My Current Location',
                                          style: Theme.of(context)
                                              .textTheme
                                              .displayMedium!
                                              .copyWith(
                                                fontSize: tS * 14.5,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                          SizedBox(height: dW * 0.15),
                          GestureDetector(
                            onTap: () {
                              isManual = !isManual;
                              setState(() {});
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.symmetric(vertical: dW * 0.025),
                              color: Colors.transparent,
                              child: TextWidget(
                                title: isManual
                                    ? 'Search Location'
                                    : 'Add Address Manually',
                                color: getThemeColor(),
                                letterSpacing: .4,
                                textDecoration: TextDecoration.underline,
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                bottom: dW * 0.05,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomButton(
                    width: dW * .4,
                    height: dW * 0.12,
                    buttonText: 'Close',
                    radius: 7,
                    borderColor: getThemeColor(),
                    textColor: getThemeColor(),
                    buttonColor: Colors.white,
                    fontSize: 15,
                    onPressed: pop,
                  ),
                  CustomButton(
                    width: dW * .4,
                    height: dW * 0.12,
                    buttonText: 'Proceed',
                    radius: 7,
                    fontSize: 15,
                    isLoading: isFetchingAddress,
                    onPressed: (!isManual && selectedAddress == null) ||
                            (isManual && addressController.text.trim().isEmpty)
                        ? null
                        : fetchAddressDetails,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
