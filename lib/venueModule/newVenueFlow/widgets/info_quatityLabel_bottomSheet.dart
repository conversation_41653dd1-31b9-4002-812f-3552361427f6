import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/custom_app_bar.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/navigators.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../commonWidgets/custom_button.dart';
import '../../../new_colors.dart';

class InfoQuantityLabelBottomSheet extends StatefulWidget {
  const InfoQuantityLabelBottomSheet({Key? key}) : super(key: key);

  @override
  InfoQuantityLabelBottomSheetState createState() =>
      InfoQuantityLabelBottomSheetState();
}

class InfoQuantityLabelBottomSheetState
    extends State<InfoQuantityLabelBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Map language = {};

  bool isLoading = false;
  late UserModal user;

  fetchData() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return Container(
        padding:
            EdgeInsets.symmetric(horizontal: dW * 0.06, vertical: dW * 0.06),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              title: 'Guide to add Quantity & Label ',
              color: getGreyColor8(context),
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            SizedBox(height: dW * 0.06),
            Expanded(
              child: isLoading
                  ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                  : SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      // padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                children: [
                                  TextWidget(title: '1.'),
                                ],
                              ),
                              SizedBox(
                                width: dW * 0.01,
                              ),
                              Expanded(
                                child: Column(
                                  children: [
                                    TextWidget(
                                        color: getGreyColor8(context),
                                        title:
                                            'First you should enter the quantity of each turf size or no. of pc’s you have available. For example,  '),
                                  ],
                                ),
                              )
                            ],
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: dW * 0.04, top: dW * 0.005),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  children: [
                                    TextWidget(title: 'a.'),
                                  ],
                                ),
                                SizedBox(
                                  width: dW * 0.01,
                                ),
                                Expanded(
                                  child: Column(
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: 'if you have ',
                                              style: TextStyle(
                                                color: getGreyColor8(context),
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                            TextSpan(
                                              text: '"3"',
                                              style: TextStyle(
                                                color: getGreyColor8(context),
                                                fontWeight: FontWeight.bold,
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' Pc’s, you should input ',
                                              style: TextStyle(
                                                color: getGreyColor8(context),
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                            TextSpan(
                                              text: '"3"',
                                              style: TextStyle(
                                                color: getGreyColor8(context),
                                                fontWeight: FontWeight.bold,
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' in the quantity field.',
                                              style: TextStyle(
                                                color: getGreyColor8(context),
                                                fontFamily: 'Poppins',
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: dW * 0.04,
                                right: dW * 0.04,
                                bottom: dW * 0.08),
                            child: Image.asset('assets/images/quantity.jpg'),
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                children: [
                                  TextWidget(
                                    title: '2.',
                                    color: getGreyColor8(context),
                                  ),
                                ],
                              ),
                              SizedBox(
                                width: dW * 0.01,
                              ),
                              Expanded(
                                child: Column(
                                  children: [
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                'Now how much quantity you entered that much label will appear, Now assign label for each quantity.',
                                            style: TextStyle(
                                              color: getGreyColor8(context),
                                              fontFamily: 'Poppins',
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' *',
                                            style: TextStyle(
                                              color: redColor,
                                              fontWeight: FontWeight.bold,
                                              fontFamily: 'Poppins',
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                                'you can’t add label for quantity exceeding 5',
                                            style: TextStyle(
                                              color: getGreyColor8(context),
                                              fontFamily: 'Poppins',
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: dW * 0.04,
                                right: dW * 0.04,
                                bottom: dW * 0.08),
                            child: Image.asset('assets/images/label.jpg'),
                          ),
                        ],
                      ),
                    ),
            ),
            CustomButton(
              width: dW,
              height: dW * 0.125,
              fontSize: 16,
              radius: 8,
              buttonText: 'Close',
              onPressed: () {
                pop();
              },
            )
          ],
        ));
  }
}
