// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/new_check_box_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/new_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../commonWidgets/custom_text_field.dart';

class ChildStep3ThirdWidget extends StatefulWidget {
  final Map selectedSportCategory;
  final List sportQuantity;
  final List<Map<String, dynamic>> listOfSportOfferings;
  List<Map<String, dynamic>> setCourtsAndTableTypes;

  ChildStep3ThirdWidget({
    required this.selectedSportCategory,
    required this.sportQuantity,
    required this.listOfSportOfferings,
    required this.setCourtsAndTableTypes,
  });

  @override
  State<ChildStep3ThirdWidget> createState() => _ChildStep3ThirdWidgetState();
}

class _ChildStep3ThirdWidgetState extends State<ChildStep3ThirdWidget> {
  double dW = 0.0;
  double tS = 0.0;

  bool sameForAll = false;

  setSameForAll() {
    sameForAll = !sameForAll;
    setAmount();
    setState(() {});
  }

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(10),
    );
  }

  setAmount() {
    if (sameForAll) {
      var amount = '';
      for (var i = 0; i < widget.sportQuantity.length; i++) {
        if (i == 0) {
          amount = widget.sportQuantity[i]['advanceAmount'].text;
        } else {
          widget.sportQuantity[i]['advanceAmount'].text = amount;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: widget.selectedSportCategory['categoryName'] == 'Outdoor'
                ? 'Enter advance amount for each sports or each turf size.'
                : 'Enter advance amount for each sports',
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.06),
          if (widget.selectedSportCategory['categoryName'] == 'Outdoor')
            ...widget.sportQuantity
                .asMap()
                .map(
                  (i, sport) => MapEntry(
                    i,
                    Container(
                      padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                      decoration: i == widget.sportQuantity.length - 1
                          ? null
                          : BoxDecoration(
                              border: Border(
                                  bottom:
                                      BorderSide(color: Color(0xffD9D9D9)))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextWidget(
                            title: sport['title'],
                            color: getThemeColor(),
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          SizedBox(height: dW * 0.03),
                          CustomTextFieldWithLabel(
                            label: 'Advance Amount',
                            controller:
                                // sport['title'] == 'Badminton'
                                //     ? sport['advanceAmount'][0]['Singles']
                                //     :
                                sport['advanceAmount'],
                            hintText: 'Enter amount',
                            inputFormatter: [
                              FilteringTextInputFormatter.digitsOnly
                            ],
                            borderRadius: 5,
                            labelFS: 14,
                            labelColor: Color(0xff9798A3),
                            labelFW: FontWeight.w500,
                            hintColor: Colors.black,
                            borderColor: Color(0xffACACB4),
                            fillColor: Color(0xffF8F9FD),
                            inputType: TextInputType.number,
                            inputAction: TextInputAction.next,
                            maxLength: 10,
                            onChanged: (value) => setAmount(),
                            prefixIcon: Icon(Icons.currency_rupee_rounded,
                                color: Colors.black, size: 20),
                          ),
                          if (i == 0 && widget.sportQuantity.length > 1) ...[
                            SizedBox(height: dW * 0.03),
                            CheckBoxWidget(
                              activeBorderColor: getThemeColor(),
                              activeColor: getThemeColor(),
                              active: sameForAll,
                              borderRadius: 3,
                              height: dW * 0.04,
                              iconSize: 14,
                              title: 'Same for all',
                              constraintWidth: null,
                              onTap: setSameForAll,
                              fontSize: 14,
                              textColor: getGreyColor1(context),
                              textFontWeight: FontWeight.w500,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                )
                .values
                .toList(),
          if (widget.selectedSportCategory['categoryName'] == 'Indoor')
            Container(
              padding: EdgeInsets.all(dW * 0.04),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(0xffD9D9D9),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextWidget(
                        title: 'Sport',
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3E3E3E),
                      ),
                      TextWidget(
                        title: 'Advance Amount',
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3E3E3E),
                      ),
                    ],
                  ),
                  ListView.builder(
                    itemCount: widget.sportQuantity.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final item = widget.sportQuantity[index];
                      return Container(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: dW * 0.06),
                            Row(
                              children: [
                                TextWidget(
                                  title: item['title'],
                                  fontWeight: FontWeight.w500,
                                  color: getThemeColor(),
                                ),
                                // if (item['title'] == 'Badminton')
                                //   TextWidget(
                                //     title: ' (${item['type']})',
                                //     fontWeight: FontWeight.w500,
                                //     color: blackColor3,
                                //     fontSize: 10,
                                //   ),
                                if (item['title'] == 'Badminton')
                                  TextWidget(
                                    title: ' (Default Court)',
                                    fontWeight: FontWeight.w500,
                                    color: blackColor3,
                                    fontSize: 10,
                                  ),
                              ],
                            ),
                            // SizedBox(height: dW * 0.06),
                            if (item['advanceAmount'] is List &&
                                item['advanceAmount'].isNotEmpty) ...[
                              // Check if "Singles" exists in the advanceAmount list
                              if (item['advanceAmount'].any((element) =>
                                  element is Map &&
                                  element.containsKey('Singles'))) ...[
                                Container(
                                  margin: EdgeInsets.only(top: dW * 0.04),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextWidget(
                                          title: 'Singles:',
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xff3E3E3E),
                                        ),
                                      ),
                                      Expanded(
                                        child: TextFormField(
                                          style: TextStyle(
                                              fontSize: tS * 14,
                                              letterSpacing: .30,
                                              fontWeight: FontWeight.w600),
                                          textAlign: TextAlign.center,
                                          decoration: InputDecoration(
                                            // iconColor: blackColor3,
                                            prefixIcon: Icon(
                                              Icons.currency_rupee_sharp,
                                              size: 18,
                                              color: blackColor3,
                                            ),
                                            hintStyle: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade400),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: dW * 0.02,
                                                    vertical: 0),
                                            hintText: '',
                                            counterText: "",
                                            fillColor: Colors.transparent,
                                            border: textFormBorder(context),
                                            focusedErrorBorder:
                                                textFormBorder(context),
                                            focusedBorder:
                                                textFormBorder(context),
                                            enabledBorder:
                                                textFormBorder(context),
                                            filled: true,
                                          ),
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          maxLength: 5,
                                          textInputAction: TextInputAction.next,
                                          cursorColor: Colors.black,
                                          autovalidateMode: AutovalidateMode
                                              .onUserInteraction,
                                          keyboardType: TextInputType.number,
                                          controller: item['advanceAmount']
                                              .firstWhere((element) =>
                                                  element is Map &&
                                                  element.containsKey(
                                                      'Singles'))['Singles'],
                                          onChanged: (value) {
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              // Check if "Doubles" exists in the advanceAmount list
                              if (item['advanceAmount'].any((element) =>
                                  element is Map &&
                                  element.containsKey('Doubles'))) ...[
                                Container(
                                  margin: EdgeInsets.only(top: dW * 0.04),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextWidget(
                                          title: 'Doubles:',
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xff3E3E3E),
                                        ),
                                      ),
                                      Expanded(
                                        child: TextFormField(
                                          style: TextStyle(
                                              fontSize: tS * 14,
                                              letterSpacing: .30,
                                              fontWeight: FontWeight.w600),
                                          textAlign: TextAlign.center,
                                          decoration: InputDecoration(
                                            prefixIcon: Icon(
                                              Icons.currency_rupee_sharp,
                                              size: 18,
                                              color: blackColor3,
                                            ),
                                            hintStyle: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade400),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: dW * 0.02,
                                                    vertical: 0),
                                            hintText: '',
                                            counterText: "",
                                            fillColor: Colors.transparent,
                                            border: textFormBorder(context),
                                            focusedErrorBorder:
                                                textFormBorder(context),
                                            focusedBorder:
                                                textFormBorder(context),
                                            enabledBorder:
                                                textFormBorder(context),
                                            filled: true,
                                          ),
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          maxLength: 5,
                                          textInputAction: TextInputAction.next,
                                          cursorColor: Colors.black,
                                          autovalidateMode: AutovalidateMode
                                              .onUserInteraction,
                                          keyboardType: TextInputType.number,
                                          controller: item['advanceAmount']
                                              .firstWhere((element) =>
                                                  element is Map &&
                                                  element.containsKey(
                                                      'Doubles'))['Doubles'],
                                          onChanged: (value) {
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                            if (!(item['advanceAmount'] is List) &&
                                item['advanceAmount'] != null) ...[
                              Row(
                                children: [
                                  Expanded(
                                    child: TextWidget(
                                      title: item['type'],
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3E3E3E),
                                    ),
                                  ),
                                  Expanded(
                                    child: TextFormField(
                                      style: TextStyle(
                                          fontSize: tS * 14,
                                          letterSpacing: .30,
                                          fontWeight: FontWeight.w600),
                                      textAlign: TextAlign.center,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      maxLength: 5,
                                      decoration: InputDecoration(
                                        prefixIcon: Icon(
                                          Icons.currency_rupee_sharp,
                                          size: 18,
                                          color: blackColor3,
                                        ),
                                        hintStyle: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade400),
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: dW * 0.02, vertical: 0),
                                        hintText: '',
                                        counterText: "",
                                        fillColor: Colors.transparent,
                                        border: textFormBorder(context),
                                        focusedErrorBorder:
                                            textFormBorder(context),
                                        focusedBorder: textFormBorder(context),
                                        enabledBorder: textFormBorder(context),
                                        filled: true,
                                      ),
                                      textInputAction: TextInputAction.next,
                                      cursorColor: Colors.black,
                                      autovalidateMode:
                                          AutovalidateMode.onUserInteraction,
                                      keyboardType: TextInputType.number,
                                      controller: item['advanceAmount'],
                                      onChanged: (value) {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            if (index != widget.sportQuantity.length - 1)
                              Container(
                                margin: EdgeInsets.only(
                                  top: dW * 0.06,
                                ),
                                child: Divider(
                                  color: Color(0xffD9D9D9),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),

                  // ListView.builder(
                  //   itemCount: widget.sportQuantity.length,
                  //   shrinkWrap: true,
                  //   itemBuilder: (context, index) {
                  //     final item = widget.sportQuantity[index];
                  //     return Container(
                  //       child: Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           SizedBox(
                  //             height: dW * 0.06,
                  //           ),
                  //           TextWidget(
                  //             title: item['title'],
                  //             fontWeight: FontWeight.w500,
                  //             color: getThemeColor(),
                  //           ),
                  //           SizedBox(
                  //             height: dW * 0.06,
                  //           ),
                  //           if (item['advanceAmount'] is List) ...[
                  //             // Check if "Singles" exists in the first element of advanceAmount list
                  //             if (item['advanceAmount'].length > 0 &&
                  //                 item['advanceAmount'][0]
                  //                     .containsKey('Singles'))
                  //               Row(
                  //                 children: [
                  //                   Expanded(
                  //                     child: TextWidget(
                  //                       title:
                  //                           'Singles: ${item['advanceAmount'][0]['Singles'].text}',
                  //                       fontWeight: FontWeight.w500,
                  //                       color: Color(0xff3E3E3E),
                  //                     ),
                  //                   ),
                  //                   Expanded(
                  //                     child: TextFormField(
                  //                       // initialValue: '',
                  //                       style: TextStyle(
                  //                         fontSize: tS * 14,
                  //                         letterSpacing: .30,
                  //                         fontWeight: FontWeight.w600,
                  //                       ),
                  //                       textAlign: TextAlign.center,
                  //                       decoration: InputDecoration(
                  //                         // prefixIcon: ,
                  //                         hintStyle: TextStyle(
                  //                             fontSize: 12,
                  //                             color: Colors.grey.shade400),

                  //                         contentPadding: EdgeInsets.symmetric(
                  //                             horizontal: dW * 0.02,
                  //                             vertical: 0),
                  //                         hintText: '',
                  //                         counterText: "",
                  //                         fillColor: Colors.transparent,
                  //                         border: textFormBorder(context),
                  //                         focusedErrorBorder:
                  //                             textFormBorder(context),
                  //                         focusedBorder:
                  //                             textFormBorder(context),
                  //                         enabledBorder:
                  //                             textFormBorder(context),
                  //                         filled: true,
                  //                       ),
                  //                       inputFormatters: [
                  //                         FilteringTextInputFormatter.digitsOnly
                  //                       ],
                  //                       maxLength: 5,
                  //                       textInputAction: TextInputAction.next,
                  //                       cursorColor: Colors.black,
                  //                       autovalidateMode:
                  //                           AutovalidateMode.onUserInteraction,
                  //                       keyboardType: TextInputType.number,
                  //                       controller: item['advanceAmount'][0]
                  //                           ['Singles'],
                  //                       onChanged: (value) {
                  //                         setState(() {});
                  //                       },
                  //                     ),
                  //                   ),
                  //                 ],
                  //               ),
                  //             // Check if "Doubles" exists in the second element of advanceAmount list
                  //             if (item['advanceAmount'].length > 1 &&
                  //                 item['advanceAmount'][1]
                  //                     .containsKey('Doubles'))
                  //               Row(
                  //                 children: [
                  //                   Expanded(
                  //                     child: TextWidget(
                  //                       title:
                  //                           'Doubles: ${item['advanceAmount'][1]['Doubles'].text}',
                  //                       fontWeight: FontWeight.w500,
                  //                       color: Color(0xff3E3E3E),
                  //                     ),
                  //                   ),
                  //                   Expanded(
                  //                     child: TextFormField(
                  //                       // initialValue: '',
                  //                       style: TextStyle(
                  //                         fontSize: tS * 14,
                  //                         letterSpacing: .30,
                  //                         fontWeight: FontWeight.w600,
                  //                       ),
                  //                       textAlign: TextAlign.center,
                  //                       decoration: InputDecoration(
                  //                         // prefixIcon: ,
                  //                         hintStyle: TextStyle(
                  //                             fontSize: 12,
                  //                             color: Colors.grey.shade400),

                  //                         contentPadding: EdgeInsets.symmetric(
                  //                             horizontal: dW * 0.02,
                  //                             vertical: 0),
                  //                         hintText: '',
                  //                         counterText: "",
                  //                         fillColor: Colors.transparent,
                  //                         border: textFormBorder(context),
                  //                         focusedErrorBorder:
                  //                             textFormBorder(context),
                  //                         focusedBorder:
                  //                             textFormBorder(context),
                  //                         enabledBorder:
                  //                             textFormBorder(context),
                  //                         filled: true,
                  //                       ),
                  //                       inputFormatters: [
                  //                         FilteringTextInputFormatter.digitsOnly
                  //                       ],
                  //                       maxLength: 5,
                  //                       textInputAction: TextInputAction.next,
                  //                       cursorColor: Colors.black,
                  //                       autovalidateMode:
                  //                           AutovalidateMode.onUserInteraction,
                  //                       keyboardType: TextInputType.number,
                  //                       controller: item['advanceAmount'][1]
                  //                           ['Doubles'],
                  //                       onChanged: (value) {
                  //                         setState(() {});
                  //                       },
                  //                     ),
                  //                   ),
                  //                 ],
                  //               ),
                  //           ],
                  //           if (!(item['advanceAmount'] is List))
                  //             Row(
                  //               children: [
                  //                 Expanded(
                  //                   child: TextWidget(
                  //                     title: item['type'],
                  //                     fontWeight: FontWeight.w500,
                  //                     color: Color(0xff3E3E3E),
                  //                   ),
                  //                 ),
                  //                 Expanded(
                  //                   child: TextFormField(
                  //                     // initialValue: '',
                  //                     style: TextStyle(
                  //                       fontSize: tS * 14,
                  //                       letterSpacing: .30,
                  //                       fontWeight: FontWeight.w600,
                  //                     ),
                  //                     textAlign: TextAlign.center,
                  //                     inputFormatters: [
                  //                       FilteringTextInputFormatter.digitsOnly
                  //                     ],
                  //                     maxLength: 5,
                  //                     decoration: InputDecoration(
                  //                       // prefixIcon: ,
                  //                       hintStyle: TextStyle(
                  //                           fontSize: 12,
                  //                           color: Colors.grey.shade400),

                  //                       contentPadding: EdgeInsets.symmetric(
                  //                           horizontal: dW * 0.02, vertical: 0),

                  //                       hintText: '',
                  //                       counterText: "",
                  //                       fillColor: Colors.transparent,
                  //                       border: textFormBorder(context),
                  //                       focusedErrorBorder:
                  //                           textFormBorder(context),
                  //                       focusedBorder: textFormBorder(context),
                  //                       enabledBorder: textFormBorder(context),
                  //                       filled: true,
                  //                     ),
                  //                     textInputAction: TextInputAction.next,
                  //                     cursorColor: Colors.black,
                  //                     autovalidateMode:
                  //                         AutovalidateMode.onUserInteraction,
                  //                     keyboardType: TextInputType.number,
                  //                     controller: item['advanceAmount'],
                  //                     onChanged: (value) {
                  //                       setState(() {});
                  //                     },
                  //                   ),
                  //                 ),
                  //               ],
                  //             ),
                  //         ],
                  //       ),
                  //     );
                  //   },
                  // ),
                ],
              ),
            )
        ],
      ),
    );
  }
}
