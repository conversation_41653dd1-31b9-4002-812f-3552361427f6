// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/asset_svg_icon.dart';
import '../../../../commonWidgets/materialCircularLoader.dart';
import '../../../../commonWidgets/open_media_full_screen.dart';
import '../../../../common_function.dart';
import '../../../../navigators.dart';
import '../../../../new_colors.dart';

class ChildStep3FirstWidget extends StatelessWidget {
  final Map selectedSportCategory;
  final List<Map<String, dynamic>> setCourtsAndTableTypes;
  final List<String> selectedImagePaths;
  final String videoPath;
  final String videoThumbnail;
  final Function imagePickerIndoorWithKey;
  final Function removeSelectedOrVideoIndoor;
  final Function imagePickerBottomSheet;
  final Function videoPickerBottomSheet;
  final Function removeSelectedOrVideo;
  final bool isCompressingVideo;

  ChildStep3FirstWidget({
    Key? key,
    required this.selectedSportCategory,
    required this.setCourtsAndTableTypes,
    required this.selectedImagePaths,
    required this.videoPath,
    required this.videoThumbnail,
    required this.imagePickerBottomSheet,
    required this.videoPickerBottomSheet,
    required this.removeSelectedOrVideo,
    required this.imagePickerIndoorWithKey,
    required this.removeSelectedOrVideoIndoor,
    required this.isCompressingVideo,
  }) : super(key: key);
  double dW = 0.0;

  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    List<String> selectedTitles = [];
    for (var sport in setCourtsAndTableTypes) {
      selectedTitles.addAll(List<String>.from(sport['title']));
    }

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Add some photos or video of your venue',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'You’ll need to add at least 1 photo and while adding video max duration should be 30 Sec.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.08),
          Row(
            children: [
              const TextWidget(
                  title: 'Upload Images', fontWeight: FontWeight.w500),
              TextWidget(title: '*', color: getRedColor1(context)),
            ],
          ),
          SizedBox(height: dW * 0.06),
          Wrap(
            spacing: 15,
            runSpacing: 15,
            children: [
              ...selectedImagePaths
                  .asMap()
                  .map(
                    (index, imagePath) => MapEntry(
                      index,
                      GestureDetector(
                        onTap: () => imagePath.isEmpty
                            ? imagePickerBottomSheet(index)
                            : null,
                        child: SizedBox(
                          height: dW * 0.48,
                          width: dW * 0.38,
                          child: imagePath.isEmpty
                              ? DottedBorder(
                                  color: getLightGreyColor1(context),
                                  dashPattern: const [10, 10],
                                  radius: const Radius.circular(8),
                                  borderType: BorderType.RRect,
                                  child: Container(
                                    width: dW,
                                    padding: EdgeInsets.symmetric(
                                        vertical: dW * .12),
                                    decoration: BoxDecoration(
                                        color: getLightGreyColor2(context)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const AssetSvgIcon(
                                            iconName: 'camera', height: 30),
                                        SizedBox(height: dW * 0.02),
                                        TextWidget(
                                          title: 'Add Photo',
                                          fontWeight: FontWeight.w400,
                                          color: getGreyColor2(context),
                                        ),
                                        TextWidget(
                                          title: '360 X 261 PX, 5 MB',
                                          fontWeight: FontWeight.w400,
                                          fontSize: 11,
                                          color: getGreyColor10(context),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Positioned(
                                      top: 0,
                                      bottom: 0,
                                      right: 0,
                                      left: 0,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: imagePath.contains('https')
                                            ? Image.network(
                                                imagePath,
                                                fit: BoxFit.cover,
                                              )
                                            : Image.file(
                                                File(imagePath),
                                                fit: BoxFit.cover,
                                              ),
                                      ),
                                    ),
                                    Positioned(
                                      right: -8,
                                      top: -8,
                                      child: GestureDetector(
                                        onTap: () =>
                                            removeSelectedOrVideo(index),
                                        child: CircleAvatar(
                                          radius: 12,
                                          backgroundColor: getThemeColor(),
                                          child: Icon(Icons.clear,
                                              size: 18, color: Colors.white),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                        ),
                      ),
                    ),
                  )
                  .values
                  .toList()
            ],
          ),
          if (selectedImagePaths.length >= 2 &&
              selectedImagePaths.length < 5 &&
              !selectedImagePaths.contains(''))
            GestureDetector(
              onTap: () => imagePickerBottomSheet(selectedImagePaths.length),
              child: Container(
                margin: EdgeInsets.only(top: dW * 0.04),
                width: dW,
                height: dW * 0.12,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: getThemeColor())),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TextWidget(
                      title: 'Add more photos',
                      fontWeight: FontWeight.w600,
                      color: getThemeColor(),
                    ),
                  ],
                ),
              ),
            ),
          SizedBox(height: dW * 0.06),
          // Row(
          //   children: [
          const TextWidget(title: 'Upload Videos'),
          // TextWidget(title: '*', color: getRedColor1(context)),
          //   ],
          // ),
          SizedBox(height: dW * 0.05),
          videoPath.isEmpty
              ? GestureDetector(
                  onTap: isCompressingVideo
                      ? null
                      : () => videoPickerBottomSheet(),
                  child: DottedBorder(
                    color: getLightGreyColor1(context),
                    dashPattern: const [10, 10],
                    radius: const Radius.circular(8),
                    borderType: BorderType.RRect,
                    child: Container(
                      width: dW,
                      padding: EdgeInsets.symmetric(vertical: dW * .14),
                      decoration:
                          BoxDecoration(color: getLightGreyColor2(context)),
                      child: isCompressingVideo
                          ? Padding(
                              padding:
                                  EdgeInsets.symmetric(vertical: dW * 0.08),
                              child:
                                  circularForButton(dW, color: getThemeColor()),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const AssetSvgIcon(iconName: 'camera'),
                                SizedBox(height: dW * 0.02),
                                TextWidget(
                                  title: 'Add Video',
                                  fontWeight: FontWeight.w400,
                                  color: getGreyColor2(context),
                                ),
                                TextWidget(
                                  title: '360 X 261 PX, 10 MB',
                                  fontWeight: FontWeight.w400,
                                  fontSize: 11,
                                  color: getGreyColor10(context),
                                ),
                              ],
                            ),
                    ),
                  ),
                )
              : GestureDetector(
                  onTap: () {
                    push(OpenMediaFullScreen(
                        type: 'Video', isLocal: true, url: videoPath));
                  },
                  child: Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: videoThumbnail.contains('https')
                            ? Image.network(
                                videoThumbnail,
                                width: dW,
                                height: dW * 0.5,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(videoThumbnail),
                                width: dW,
                                height: dW * 0.5,
                                fit: BoxFit.cover,
                              ),
                      ),
                      Icon(
                        Icons.play_circle_outline_rounded,
                        color: Colors.white,
                        size: dW * 0.1,
                      ),
                      Positioned(
                        right: -8,
                        top: -8,
                        child: GestureDetector(
                          onTap: () => removeSelectedOrVideo(0, isImage: false),
                          child: CircleAvatar(
                            radius: 12,
                            backgroundColor: getThemeColor(),
                            child: Icon(Icons.clear,
                                size: 18, color: Colors.white),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
          if (selectedSportCategory['categoryName'] == 'Indoor')
            ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: setCourtsAndTableTypes.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    for (int i = 0;
                        i < setCourtsAndTableTypes[index]['title'].length;
                        i++)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: dW * 0.04,
                          ),
                          Row(
                            children: [
                              TextWidget(
                                  title: setCourtsAndTableTypes[index]['title']
                                      [i]),
                            ],
                          ),
                          Wrap(
                            spacing: 15,
                            runSpacing: 15,
                            // mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              ...setCourtsAndTableTypes[index][
                                      '${setCourtsAndTableTypes[index]['title'][i]} image']
                                  .asMap()
                                  .map((index2, e) {
                                return MapEntry(
                                  index2,
                                  GestureDetector(
                                    onTap: () => e.isEmpty
                                        ? imagePickerIndoorWithKey(
                                            index2,
                                            setCourtsAndTableTypes[index]
                                                ['sport'],
                                            '${setCourtsAndTableTypes[index]['title'][i]} image',
                                          )
                                        : null,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: dW * 0.04,
                                      ),
                                      height: dW * 0.48,
                                      width: dW * 0.38,
                                      child: e.isEmpty
                                          ? DottedBorder(
                                              color:
                                                  getLightGreyColor1(context),
                                              dashPattern: const [10, 10],
                                              radius: const Radius.circular(8),
                                              borderType: BorderType.RRect,
                                              child: Container(
                                                width: dW,
                                                padding: EdgeInsets.symmetric(
                                                    vertical: dW * .14),
                                                decoration: BoxDecoration(
                                                    color: getLightGreyColor2(
                                                        context)),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const AssetSvgIcon(
                                                        iconName: 'camera',
                                                        height: 30),
                                                    SizedBox(height: dW * 0.02),
                                                    TextWidget(
                                                      title: 'Add Photo',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: getGreyColor2(
                                                          context),
                                                    ),
                                                    TextWidget(
                                                      title:
                                                          '360 X 261 PX, 5 MB',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: 11,
                                                      color: getGreyColor10(
                                                          context),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                          : Stack(
                                              clipBehavior: Clip.none,
                                              children: [
                                                Positioned(
                                                  top: 0,
                                                  bottom: 0,
                                                  right: 0,
                                                  left: 0,
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    child: e.contains('https')
                                                        ? Image.network(
                                                            e,
                                                            fit: BoxFit.cover,
                                                          )
                                                        : Image.file(
                                                            File(e),
                                                            fit: BoxFit.cover,
                                                          ),
                                                  ),
                                                ),
                                                Positioned(
                                                  right: -8,
                                                  top: -8,
                                                  child: GestureDetector(
                                                    onTap: () =>
                                                        removeSelectedOrVideoIndoor(
                                                            index2,
                                                            sportName:
                                                                setCourtsAndTableTypes[
                                                                        index]
                                                                    ['sport'],
                                                            imageValue:
                                                                '${setCourtsAndTableTypes[index]['title'][i]} image'),
                                                    child: CircleAvatar(
                                                      radius: 12,
                                                      backgroundColor:
                                                          getThemeColor(),
                                                      child: Icon(Icons.clear,
                                                          size: 18,
                                                          color: Colors.white),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                    ),
                                  ),
                                );
                              }).values,
                            ],
                          )
                          // for (int j = 0;
                          //     j <
                          //         setCourtsAndTableTypes[index][
                          //                     '${setCourtsAndTableTypes[index]['title'][i]} image']
                          //                 [j]
                          //             .length;
                          //     j++)
                          //   TextWidget(
                          //       title:
                          //           'text ${setCourtsAndTableTypes[index]['${setCourtsAndTableTypes[index]['title'][i]} image'][j]}')
                        ],
                      )
                  ],
                );
              },
            )
//             ...setCourtsAndTableTypes
//                 .asMap()
//                 .map((index, entry) {
//                   final titleList = entry['title']
//                       as List<String>; // Accessing the title list
//                   final imageLists = entry.entries
//                       .where((e) => e.key.endsWith('image'))
//                       .toList();
//                   return MapEntry(
//                     index,
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Container(
//                           margin: EdgeInsets.only(top: dW * 0.08),
//                           child: Column(
//                             children: [
//                               for (var i = 0; i < titleList.length; i++)
//                                 Column(
//                                   children: [
//                                     TextWidget(
//                                       title: '${titleList[i]} Photos',
//                                       fontWeight: FontWeight.w600,
//                                       fontSize: 16,
//                                       color: Color(0xff21272A),
//                                     ),
//                                     SizedBox(height: dW * 0.03),
//                                     // ...imageLists.map((e) => Column(
//                                     //       children: [
//                                     //         TextWidget(title: e.key),
//                                     //         Row(
//                                     //           children: [
//                                     //             TextWidget(title: e.value[i]),
//                                     //           ],
//                                     //         )
//                                     //       ],
//                                     //     )),
//                                     // for (var imageEntry in imageLists)
//                                     // Your previous code...

// // In the section where you iterate over imageLists, replace it with the following:

          // ...imageLists
          //     .asMap()
          //     .map((index2, entry) {
          //       final imageKey = entry.key;
          //       final imageValueList =
          //           entry.value as List<String>;
          //       final sportName = entry.key;
          //       return MapEntry(
          //         index2,
          //         Row(
          //           children: [
          //             for (int i = 0;
          //                 i < imageValueList.length;
          //                 i++)
          //               // if (imageValueList[i]
          //               //     .isNotEmpty) // Check if the image value is not empty
          //               // If image value is empty, display the "Add Photo" field
          //               GestureDetector(
          //                 onTap: () => imageValueList[
          //                             i]
          //                         .isEmpty
          //                     ? imagePickerIndoorWithKey(
          //                         i,
          //                         sportName, // Using the image key as the sport name
          //                         imageKey,
          //                       )
          //                     : null,
          //                 child: SizedBox(
          //                   height: dW * 0.48,
          //                   width: dW * 0.38,
          //                   child: DottedBorder(
          //                     color:
          //                         getLightGreyColor1(
          //                             context),
          //                     dashPattern: const [
          //                       10,
          //                       10
          //                     ],
          //                     radius: const Radius
          //                         .circular(8),
          //                     borderType:
          //                         BorderType.RRect,
          //                     child: Container(
          //                       width: dW,
          //                       padding: EdgeInsets
          //                           .symmetric(
          //                               vertical:
          //                                   dW * .14),
          //                       decoration:
          //                           BoxDecoration(
          //                         color:
          //                             getLightGreyColor2(
          //                                 context),
          //                       ),
          //                       child: Column(
          //                         crossAxisAlignment:
          //                             CrossAxisAlignment
          //                                 .center,
          //                         mainAxisAlignment:
          //                             MainAxisAlignment
          //                                 .center,
          //                         children: [
          //                           const AssetSvgIcon(
          //                             iconName:
          //                                 'camera',
          //                             height: 30,
          //                           ),
          //                           SizedBox(
          //                               height: dW *
          //                                   0.02),
          //                           TextWidget(
          //                             title:
          //                                 'Add Photo',
          //                             fontWeight:
          //                                 FontWeight
          //                                     .w400,
          //                             color:
          //                                 getGreyColor2(
          //                                     context),
          //                           ),
          //                           TextWidget(
          //                             title:
          //                                 '360 X 261 PX, 5 MB',
          //                             fontWeight:
          //                                 FontWeight
          //                                     .w400,
          //                             fontSize: 11,
          //                             color:
          //                                 getGreyColor10(
          //                                     context),
          //                           ),
          //                         ],
          //                       ),
          //                     ),
          //                   ),
          //                 ),
          //               ),
          //           ],
          //         ),
          //       );
          //     })
          //     .values
          //     .toList(),

// // Your remaining code...
//                                   ],
//                                 ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   );
//                 })
//                 .values
//                 .toList(),
        ],
      ),
    );
  }
}
