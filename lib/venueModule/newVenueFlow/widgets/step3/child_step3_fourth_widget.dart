// ignore_for_file: must_be_immutable

import 'package:bys_business/venueModule/providers/turfProvider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../../commonWidgets/text_widget.dart';
import '../../../../common_function.dart';

class ChildStep3FourthWidget extends StatefulWidget {
  final Map selectedSportCategory;
  final List sportQuantity;
  final List<Map<String, dynamic>> listOfSportOfferings;
  List<Map<String, dynamic>> setCourtsAndTableTypes;

  ChildStep3FourthWidget({
    required this.selectedSportCategory,
    required this.sportQuantity,
    required this.listOfSportOfferings,
    required this.setCourtsAndTableTypes,
  });

  @override
  State<ChildStep3FourthWidget> createState() => _ChildStep3FourthWidgetState();
}

class _ChildStep3FourthWidgetState extends State<ChildStep3FourthWidget> {
  double dW = 0.0;

  double tS = 0.0;

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(10),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'What is the maximum number of players allowed per booking?',
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          TextWidget(
            title: "*You must fill in all of the fields",
            fontSize: 12,
            color: Color(0xffD84848),
          ),
          Container(
            margin: EdgeInsets.only(top: dW * 0.04),
            padding: EdgeInsets.all(dW * 0.04),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Color(0xffD9D9D9),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TextWidget(
                        title: 'Sport',
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3E3E3E),
                      ),
                    ),
                    // Expanded(
                    //   child: TextWidget(
                    //     title: 'No. of players ',
                    //     fontWeight: FontWeight.w600,
                    //     color: Color(0xff3E3E3E),
                    //   ),
                    // ),
                  ],
                ),

                ListView.builder(
                  itemCount: widget.sportQuantity.length,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final item = widget.sportQuantity[index];
                    return Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: dW * 0.06),
                          Row(
                            children: [
                              if (widget
                                      .selectedSportCategory['categoryName'] ==
                                  'Indoor')
                                TextWidget(
                                  title: item['type'],
                                  fontWeight: FontWeight.w500,
                                  color: getThemeColor(),
                                ),
                              if (widget
                                      .selectedSportCategory['categoryName'] ==
                                  'Outdoor')
                                TextWidget(
                                  title: item['title'],
                                  fontWeight: FontWeight.w500,
                                  color: getThemeColor(),
                                ),

                              SizedBox(width: dW * 0.02),
                              Expanded(
                                child: TextWidget(
                                  title: '(No. of players)',
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xff3E3E3E),
                                ),
                              ),
                              // if (item['title'] == 'Badminton')
                              //   TextWidget(
                              //     title: ' (${item['type']})',
                              //     fontWeight: FontWeight.w500,
                              //     color: blackColor3,
                              //     fontSize: 10,
                              //   ),
                            ],
                          ),
                          // SizedBox(height: dW * 0.06),
                          // if (widget.selectedSportCategory['categoryName'] ==
                          //     'Outdoor')
                          //   Container(
                          //     margin: EdgeInsets.only(top: dW * 0.04),
                          //     child: Row(
                          //       children: [
                          //         Expanded(
                          //           child: TextFormField(
                          //             style: TextStyle(
                          //                 fontSize: tS * 14,
                          //                 letterSpacing: .30,
                          //                 fontWeight: FontWeight.w600),
                          //             textAlign: TextAlign.center,
                          //             inputFormatters: [
                          //               FilteringTextInputFormatter.digitsOnly
                          //             ],
                          //             maxLength: 5,
                          //             decoration: InputDecoration(
                          //               hintStyle: TextStyle(
                          //                   fontSize: 12,
                          //                   color: Colors.grey.shade400),
                          //               contentPadding: EdgeInsets.symmetric(
                          //                   horizontal: dW * 0.02, vertical: 0),
                          //               hintText: '',
                          //               counterText: "",
                          //               fillColor: Colors.transparent,
                          //               border: textFormBorder(context),
                          //               focusedErrorBorder:
                          //                   textFormBorder(context),
                          //               focusedBorder: textFormBorder(context),
                          //               enabledBorder: textFormBorder(context),
                          //               filled: true,
                          //             ),
                          //             textInputAction: TextInputAction.next,
                          //             cursorColor: Colors.black,
                          //             autovalidateMode:
                          //                 AutovalidateMode.onUserInteraction,
                          //             keyboardType: TextInputType.number,
                          //             controller: item['totalPlayersAllowed'],
                          //             onChanged: (value) {
                          //               setState(() {});
                          //             },
                          //           ),
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                          if (item['totalPlayersAllowed'] is List &&
                              item['totalPlayersAllowed'].isNotEmpty) ...[
                            // Check if "Singles" exists in the totalPlayersAllowed list
                            if (item['totalPlayersAllowed'].any((element) =>
                                element is Map &&
                                element.containsKey('Singles'))) ...[
                              Container(
                                margin: EdgeInsets.only(top: dW * 0.04),
                                child: Row(
                                  children: [
                                    // Expanded(
                                    //   child: TextWidget(
                                    //     title: 'Singles:',
                                    //     fontWeight: FontWeight.w500,
                                    //     color: Color(0xff3E3E3E),
                                    //   ),
                                    // ),
                                    Expanded(
                                      child: TextFormField(
                                        style: TextStyle(
                                            fontSize: tS * 14,
                                            letterSpacing: .30,
                                            fontWeight: FontWeight.w600),
                                        textAlign: TextAlign.center,
                                        decoration: InputDecoration(
                                          hintStyle: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade400),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: dW * 0.02,
                                              vertical: 0),
                                          hintText: '',
                                          counterText: "",
                                          fillColor: Colors.transparent,
                                          border: textFormBorder(context),
                                          focusedErrorBorder:
                                              textFormBorder(context),
                                          focusedBorder:
                                              textFormBorder(context),
                                          enabledBorder:
                                              textFormBorder(context),
                                          filled: true,
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly
                                        ],
                                        maxLength: 5,
                                        textInputAction: TextInputAction.next,
                                        cursorColor: Colors.black,
                                        autovalidateMode:
                                            AutovalidateMode.onUserInteraction,
                                        keyboardType: TextInputType.number,
                                        controller: item['totalPlayersAllowed']
                                            .firstWhere((element) =>
                                                element is Map &&
                                                element.containsKey(
                                                    'Singles'))['Singles'],
                                        onChanged: (value) {
                                          setState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],

                            // Check if "Doubles" exists in the totalPlayersAllowed list
                            // if (item['totalPlayersAllowed'].any((element) =>
                            //     element is Map &&
                            //     element.containsKey('Doubles'))) ...[
                            //   Container(
                            //     margin: EdgeInsets.only(top: dW * 0.04),
                            //     child: Row(
                            //       children: [
                            //         Expanded(
                            //           child: TextWidget(
                            //             title: 'Doubles:',
                            //             fontWeight: FontWeight.w500,
                            //             color: Color(0xff3E3E3E),
                            //           ),
                            //         ),
                            //         Expanded(
                            //           child: TextFormField(
                            //             style: TextStyle(
                            //                 fontSize: tS * 14,
                            //                 letterSpacing: .30,
                            //                 fontWeight: FontWeight.w600),
                            //             textAlign: TextAlign.center,
                            //             decoration: InputDecoration(
                            //               hintStyle: TextStyle(
                            //                   fontSize: 12,
                            //                   color: Colors.grey.shade400),
                            //               contentPadding: EdgeInsets.symmetric(
                            //                   horizontal: dW * 0.02,
                            //                   vertical: 0),
                            //               hintText: '',
                            //               counterText: "",
                            //               fillColor: Colors.transparent,
                            //               border: textFormBorder(context),
                            //               focusedErrorBorder:
                            //                   textFormBorder(context),
                            //               focusedBorder:
                            //                   textFormBorder(context),
                            //               enabledBorder:
                            //                   textFormBorder(context),
                            //               filled: true,
                            //             ),
                            //             inputFormatters: [
                            //               FilteringTextInputFormatter.digitsOnly
                            //             ],
                            //             maxLength: 5,
                            //             textInputAction: TextInputAction.next,
                            //             cursorColor: Colors.black,
                            //             autovalidateMode:
                            //                 AutovalidateMode.onUserInteraction,
                            //             keyboardType: TextInputType.number,
                            //             controller: item['totalPlayersAllowed']
                            //                 .firstWhere((element) =>
                            //                     element is Map &&
                            //                     element.containsKey(
                            //                         'Doubles'))['Doubles'],
                            //             onChanged: (value) {
                            //               setState(() {});
                            //             },
                            //           ),
                            //         ),
                            //       ],
                            //     ),
                            //   ),
                            // ],
                          ],
                          if (!(item['totalPlayersAllowed'] is List) &&
                              item['totalPlayersAllowed'] != null) ...[
                            Container(
                              margin: EdgeInsets.only(top: dW * 0.04),
                              child: Row(
                                children: [
                                  // Expanded(
                                  //   child: TextWidget(
                                  //     title: widget.selectedSportCategory[
                                  //                 'categoryName'] ==
                                  //             'Indoor'
                                  //         ? item['type']
                                  //         : item['title'],
                                  //     fontWeight: FontWeight.w500,
                                  //     color: Color(0xff3E3E3E),
                                  //   ),
                                  // ),
                                  Expanded(
                                    child: TextFormField(
                                      style: TextStyle(
                                          fontSize: tS * 14,
                                          letterSpacing: .30,
                                          fontWeight: FontWeight.w600),
                                      textAlign: TextAlign.center,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      maxLength: 5,
                                      decoration: InputDecoration(
                                        hintStyle: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade400),
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: dW * 0.02, vertical: 0),
                                        hintText: '',
                                        counterText: "",
                                        fillColor: Colors.transparent,
                                        border: textFormBorder(context),
                                        focusedErrorBorder:
                                            textFormBorder(context),
                                        focusedBorder: textFormBorder(context),
                                        enabledBorder: textFormBorder(context),
                                        filled: true,
                                      ),
                                      textInputAction: TextInputAction.next,
                                      cursorColor: Colors.black,
                                      autovalidateMode:
                                          AutovalidateMode.onUserInteraction,
                                      keyboardType: TextInputType.number,
                                      controller: item['totalPlayersAllowed'],
                                      onChanged: (value) {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],

                          if (index != widget.sportQuantity.length - 1)
                            Container(
                              margin: EdgeInsets.only(
                                top: dW * 0.06,
                              ),
                              child: Divider(
                                color: Color(0xffD9D9D9),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),

                // ListView.builder(
                //   itemCount: widget.sportQuantity.length,
                //   shrinkWrap: true,
                //   itemBuilder: (context, index) {
                //     final item = widget.sportQuantity[index];
                //     return Container(
                //       child: Column(
                //         crossAxisAlignment: CrossAxisAlignment.start,
                //         children: [
                //           SizedBox(
                //             height: dW * 0.06,
                //           ),
                //           TextWidget(
                //             title: item['title'],
                //             fontWeight: FontWeight.w500,
                //             color: getThemeColor(),
                //           ),
                //           SizedBox(
                //             height: dW * 0.06,
                //           ),
                //           if (item['totalPlayersAllowed'] is List) ...[
                //             // Check if "Singles" exists in the first element of totalPlayersAllowed list
                //             if (item['totalPlayersAllowed'].length > 0 &&
                //                 item['totalPlayersAllowed'][0]
                //                     .containsKey('Singles'))
                //               Row(
                //                 children: [
                //                   Expanded(
                //                     child: TextWidget(
                //                       title:
                //                           'Singles: ${item['totalPlayersAllowed'][0]['Singles'].text}',
                //                       fontWeight: FontWeight.w500,
                //                       color: Color(0xff3E3E3E),
                //                     ),
                //                   ),
                //                   Expanded(
                //                     child: TextFormField(
                //                       // initialValue: '',
                //                       style: TextStyle(
                //                         fontSize: tS * 14,
                //                         letterSpacing: .30,
                //                         fontWeight: FontWeight.w600,
                //                       ),
                //                       textAlign: TextAlign.center,
                //                       decoration: InputDecoration(
                //                         // prefixIcon: ,
                //                         hintStyle: TextStyle(
                //                             fontSize: 12,
                //                             color: Colors.grey.shade400),

                //                         contentPadding: EdgeInsets.symmetric(
                //                             horizontal: dW * 0.02,
                //                             vertical: 0),
                //                         hintText: '',
                //                         counterText: "",
                //                         fillColor: Colors.transparent,
                //                         border: textFormBorder(context),
                //                         focusedErrorBorder:
                //                             textFormBorder(context),
                //                         focusedBorder:
                //                             textFormBorder(context),
                //                         enabledBorder:
                //                             textFormBorder(context),
                //                         filled: true,
                //                       ),
                //                       inputFormatters: [
                //                         FilteringTextInputFormatter.digitsOnly
                //                       ],
                //                       maxLength: 5,
                //                       textInputAction: TextInputAction.next,
                //                       cursorColor: Colors.black,
                //                       autovalidateMode:
                //                           AutovalidateMode.onUserInteraction,
                //                       keyboardType: TextInputType.number,
                //                       controller: item['totalPlayersAllowed'][0]
                //                           ['Singles'],
                //                       onChanged: (value) {
                //                         setState(() {});
                //                       },
                //                     ),
                //                   ),
                //                 ],
                //               ),
                //             // Check if "Doubles" exists in the second element of totalPlayersAllowed list
                //             if (item['totalPlayersAllowed'].length > 1 &&
                //                 item['totalPlayersAllowed'][1]
                //                     .containsKey('Doubles'))
                //               Row(
                //                 children: [
                //                   Expanded(
                //                     child: TextWidget(
                //                       title:
                //                           'Doubles: ${item['totalPlayersAllowed'][1]['Doubles'].text}',
                //                       fontWeight: FontWeight.w500,
                //                       color: Color(0xff3E3E3E),
                //                     ),
                //                   ),
                //                   Expanded(
                //                     child: TextFormField(
                //                       // initialValue: '',
                //                       style: TextStyle(
                //                         fontSize: tS * 14,
                //                         letterSpacing: .30,
                //                         fontWeight: FontWeight.w600,
                //                       ),
                //                       textAlign: TextAlign.center,
                //                       decoration: InputDecoration(
                //                         // prefixIcon: ,
                //                         hintStyle: TextStyle(
                //                             fontSize: 12,
                //                             color: Colors.grey.shade400),

                //                         contentPadding: EdgeInsets.symmetric(
                //                             horizontal: dW * 0.02,
                //                             vertical: 0),
                //                         hintText: '',
                //                         counterText: "",
                //                         fillColor: Colors.transparent,
                //                         border: textFormBorder(context),
                //                         focusedErrorBorder:
                //                             textFormBorder(context),
                //                         focusedBorder:
                //                             textFormBorder(context),
                //                         enabledBorder:
                //                             textFormBorder(context),
                //                         filled: true,
                //                       ),
                //                       inputFormatters: [
                //                         FilteringTextInputFormatter.digitsOnly
                //                       ],
                //                       maxLength: 5,
                //                       textInputAction: TextInputAction.next,
                //                       cursorColor: Colors.black,
                //                       autovalidateMode:
                //                           AutovalidateMode.onUserInteraction,
                //                       keyboardType: TextInputType.number,
                //                       controller: item['totalPlayersAllowed'][1]
                //                           ['Doubles'],
                //                       onChanged: (value) {
                //                         setState(() {});
                //                       },
                //                     ),
                //                   ),
                //                 ],
                //               ),
                //           ],
                //           if (!(item['totalPlayersAllowed'] is List))
                //             Row(
                //               children: [
                //                 Expanded(
                //                   child: TextWidget(
                //                     title: item['type'],
                //                     fontWeight: FontWeight.w500,
                //                     color: Color(0xff3E3E3E),
                //                   ),
                //                 ),
                //                 Expanded(
                //                   child: TextFormField(
                //                     // initialValue: '',
                //                     style: TextStyle(
                //                       fontSize: tS * 14,
                //                       letterSpacing: .30,
                //                       fontWeight: FontWeight.w600,
                //                     ),
                //                     textAlign: TextAlign.center,
                //                     inputFormatters: [
                //                       FilteringTextInputFormatter.digitsOnly
                //                     ],
                //                     maxLength: 5,
                //                     decoration: InputDecoration(
                //                       // prefixIcon: ,
                //                       hintStyle: TextStyle(
                //                           fontSize: 12,
                //                           color: Colors.grey.shade400),

                //                       contentPadding: EdgeInsets.symmetric(
                //                           horizontal: dW * 0.02, vertical: 0),

                //                       hintText: '',
                //                       counterText: "",
                //                       fillColor: Colors.transparent,
                //                       border: textFormBorder(context),
                //                       focusedErrorBorder:
                //                           textFormBorder(context),
                //                       focusedBorder: textFormBorder(context),
                //                       enabledBorder: textFormBorder(context),
                //                       filled: true,
                //                     ),
                //                     textInputAction: TextInputAction.next,
                //                     cursorColor: Colors.black,
                //                     autovalidateMode:
                //                         AutovalidateMode.onUserInteraction,
                //                     keyboardType: TextInputType.number,
                //                     controller: item['totalPlayersAllowed'],
                //                     onChanged: (value) {
                //                       setState(() {});
                //                     },
                //                   ),
                //                 ),
                //               ],
                //             ),
                //         ],
                //       ),
                //     );
                //   },
                // ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
