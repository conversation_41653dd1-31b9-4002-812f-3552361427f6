// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';

import '../../../../commonWidgets/checkBoxWidget.dart';
import '../../../../commonWidgets/text_widget.dart';

class ChildStep3FifthWidget extends StatelessWidget {
  final List listOfFacilities;
  final List selectedFacilities;
  final Function selectAndUnSelectType;
  final Map selectedSportCategory;
  ChildStep3FifthWidget({
    required this.listOfFacilities,
    required this.selectAndUnSelectType,
    required this.selectedFacilities,
    required this.selectedSportCategory,
  });

  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Tell users what facilities available at your venue',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.1),
          // ...listOfFacilities.map(
          //   (facility) => GestureDetector(
          //     onTap: () => selectAndUnSelectType(
          //       facility['id'],
          //       facility['isSelected'],
          //       listOfFacilities,
          //       selectedFacilities,
          //       false,
          //     ),
          //     child: Container(
          //       margin: EdgeInsets.only(bottom: dW * 0.05),
          //       width: dW,
          //       color: Colors.transparent,
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //         children: [
          //           Container(
          //             width: dW * 0.7,
          //             alignment: Alignment.topLeft,
          //             child: FittedBox(
          //               fit: BoxFit.scaleDown,
          //               child: TextWidget(
          //                 title: facility['title'],
          //                 letterSpacing: .30,
          //                 color: Color(0xff636363),
          //                 fontSize: 16,
          //                 fontWeight: FontWeight.w500,
          //               ),
          //             ),
          //           ),
          //           OldCheckBoxWidget(dW, facility['isSelected']),
          //         ],
          //       ),
          //     ),
          //   ),
          // ),
          ...listOfFacilities
              .where((facility) =>
                  !(selectedSportCategory['categoryName'] == 'Indoor' &&
                      facility['title'] == 'Covered'))
              .map(
                (facility) => GestureDetector(
                  onTap: () => selectAndUnSelectType(
                    facility['id'],
                    facility['isSelected'],
                    listOfFacilities,
                    selectedFacilities,
                    false,
                  ),
                  child: Container(
                    margin: EdgeInsets.only(bottom: dW * 0.05),
                    width: dW,
                    color: Colors.transparent,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: dW * 0.7,
                          alignment: Alignment.topLeft,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: TextWidget(
                              title: facility['title'],
                              letterSpacing: .30,
                              color: Color(0xff636363),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        OldCheckBoxWidget(dW, facility['isSelected']),
                      ],
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }
}
