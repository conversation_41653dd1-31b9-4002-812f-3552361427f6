// // ignore_for_file: must_be_immutable

// import 'dart:io';

// import 'package:bys_business/commonWidgets/custom_button.dart';
// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// import '../../../../commonWidgets/asset_svg_icon.dart';
// import '../../../../commonWidgets/custom_container.dart';
// import '../../../../commonWidgets/text_widget.dart';
// import '../../../../common_function.dart';
// import '../../../../new_colors.dart';
// import '../../../widgets/venueDetailsWidget/multi_select_bottom_sheet.dart';

// class ChildStep3SixthWidget extends StatefulWidget {
//   final List<Map<String, dynamic>> equipments;
//   final List listOfSports;
//   final List selectedSports;
//   final Function selectAndUnselectSports;
//   final List selectedSportsForEquip;
//   final Function imagePickerBottomSheetForEquipment;
//   final Function removeSelectedOrVideoForEquipment;

//   ChildStep3SixthWidget({
//     required this.equipments,
//     required this.selectedSports,
//     required this.listOfSports,
//     required this.selectAndUnselectSports,
//     required this.selectedSportsForEquip,
//     required this.imagePickerBottomSheetForEquipment,
//     required this.removeSelectedOrVideoForEquipment,
//   });

//   @override
//   State<ChildStep3SixthWidget> createState() => _ChildStep3SixthWidgetState();
// }

// class _ChildStep3SixthWidgetState extends State<ChildStep3SixthWidget> {
//   double dW = 0.0;
//   double dH = 0.0;

//   double tS = 0.0;
//   int nameCursorPosition = 0;
//   int priceCursorPosition = 0;

//   textFormBorder(context) {
//     return OutlineInputBorder(
//       borderSide: BorderSide(color: getThemeColor()),
//       borderRadius: BorderRadius.circular(10),
//     );
//   }

//   bool areAllFieldsFilled() {
//     for (final equipment in widget.equipments) {
//       if (equipment['name'] == '' ||
//           equipment['price'] == '' ||
//           equipment['image'] == '') {
//         return false;
//       }
//     }
//     return true;
//   }

//   selectDaysBottomSheet({
//     required BuildContext context,
//   }) {
//     showModalBottomSheet(
//       context: context,
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(15.0),
//           topRight: Radius.circular(15.0),
//         ),
//       ),
//       builder: (BuildContext context) => MultiSelectBottomSheet(
//         listOfFields: widget.selectedSports,
//         title: 'Select Sports',
//         selectedFields: List<String>.from(widget.selectedSportsForEquip),
//       ),
//     ).then((value) {
//       if (value != null) {
//         try {
//           widget.selectAndUnselectSports(sports: value);
//         } catch (e) {
//           print(e);
//         }
//       }
//     });
//   }

//   Widget buildDaysSelectionWidget({
//     required BuildContext context,
//     required List listOfIterate,
//   }) {
//     return GestureDetector(
//       onTap: () => selectDaysBottomSheet(
//         context: context,
//       ),
//       child: CustomContainer(
//         boxShadow: [],
//         radius: 8,
//         vPadding: 0.03,
//         hPadding: 0,
//         borderColor: getThemeColor(),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             SizedBox(
//               width: dW * 0.7,
//               child: widget.equipments.isEmpty
//                   ? Padding(
//                       padding: EdgeInsets.only(left: dW * 0.03),
//                       child: TextWidget(
//                         title: 'Select Sports',
//                         textAlign: TextAlign.left,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     )
//                   : SingleChildScrollView(
//                       scrollDirection: Axis.horizontal,
//                       physics: BouncingScrollPhysics(),
//                       child: Row(
//                         children: [
//                           SizedBox(width: dW * 0.03),
//                           ...listOfIterate.map(
//                             (day) => Container(
//                               margin: EdgeInsets.only(right: dW * 0.02),
//                               constraints: BoxConstraints(minWidth: dW * 0.13),
//                               padding: EdgeInsets.symmetric(
//                                 vertical: dW * 0.015,
//                                 horizontal: dW * 0.02,
//                               ),
//                               decoration: BoxDecoration(
//                                 color: getThemeColor(),
//                                 borderRadius: BorderRadius.circular(50),
//                                 border: Border.all(color: getThemeColor()),
//                               ),
//                               child: FittedBox(
//                                 fit: BoxFit.scaleDown,
//                                 child: TextWidget(
//                                   title: day,
//                                   fontSize: 13,
//                                   color: Colors.white,
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                           SizedBox(width: dW * 0.03),
//                         ],
//                       ),
//                     ),
//             ),
//             Padding(
//               padding: EdgeInsets.only(right: dW * 0.02),
//               child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   @override
//   void initState() {
//     super.initState();
//     if (widget.equipments.isEmpty) {
//       widget.equipments.add({
//         'name': '',
//         'price': '',
//         // 'selectedSport': [],
//         'image': '',
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     dH = MediaQuery.of(context).size.height;

//     tS = MediaQuery.of(context).textScaleFactor;

//     return Container(
//       alignment: Alignment.topLeft,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SizedBox(height: dW * 0.05),
//           TextWidget(
//             title: 'Is any Equipments available at your venue?',
//             fontSize: 20,
//             fontWeight: FontWeight.w600,
//           ),
//           SizedBox(height: dW * 0.025),
//           TextWidget(
//             title:
//                 'Rent sports equipment and gear to enhance the player experience at your venue.',
//             fontSize: 12,
//           ),
//           SizedBox(
//             height: dW * 0.1,
//           ),
//           SizedBox(
//             height: dW * 0.06,
//           ),
//           ListView.builder(
//             physics: NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: widget.equipments.length,
//             itemBuilder: (context, index) {
//               final e = widget.equipments[index];
//               return Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       TextWidget(
//                         title: 'Equipment ${index + 1}',
//                         fontWeight: FontWeight.w600,
//                         color: getThemeColor(),
//                       ),
//                       if (widget.equipments.length > 1)
//                         GestureDetector(
//                           onTap: () {
//                             setState(() {
//                               widget.equipments.remove(e);
//                             });
//                           },
//                           child: TextWidget(
//                             title: 'Remove',
//                             fontWeight: FontWeight.w600,
//                             color: Color(0xffD84848),
//                           ),
//                         ),
//                     ],
//                   ),
//                   SizedBox(
//                     height: dW * 0.03,
//                   ),
//                   TextWidget(
//                     title: 'Name',
//                     fontWeight: FontWeight.w500,
//                     color: Color(0xff9798A3),
//                   ),
//                   SizedBox(
//                     height: dW * 0.02,
//                   ),
//                   TextFormField(
//                     style: TextStyle(
//                         fontSize: tS * 14,
//                         letterSpacing: .30,
//                         fontWeight: FontWeight.w600),
//                     // textAlign: TextAlign.center,
//                     // inputFormatters: [FilteringTextInputFormatter.digitsOnly],
//                     // maxLength: 5,
//                     decoration: InputDecoration(
//                       hintStyle:
//                           TextStyle(fontSize: 12, color: Colors.grey.shade400),
//                       contentPadding: EdgeInsets.symmetric(
//                           horizontal: dW * 0.03, vertical: 0),
//                       hintText: 'Enter Name',
//                       counterText: "",
//                       fillColor: Colors.transparent,
//                       border: textFormBorder(context),
//                       focusedErrorBorder: textFormBorder(context),
//                       focusedBorder: textFormBorder(context),
//                       enabledBorder: textFormBorder(context),
//                       filled: true,
//                     ),
//                     textInputAction: TextInputAction.next,
//                     cursorColor: Colors.black,
//                     autovalidateMode: AutovalidateMode.onUserInteraction,
//                     keyboardType: TextInputType.text,
//                     controller: TextEditingController.fromValue(
//                       TextEditingValue(
//                         text: e['name'] ?? '',
//                         selection: TextSelection.collapsed(
//                             offset:
//                                 nameCursorPosition), // Set cursor position for Name field
//                       ),
//                     ),
//                     onChanged: (value) {
//                       setState(() {
//                         e['name'] = value;
//                         nameCursorPosition = e['name'].length ?? 0;
//                       });
//                     },
//                   ),
//                   SizedBox(
//                     height: dW * 0.06,
//                   ),
//                   TextWidget(
//                     title: 'Price',
//                     fontWeight: FontWeight.w500,
//                     color: Color(0xff9798A3),
//                   ),
//                   SizedBox(
//                     height: dW * 0.02,
//                   ),
//                   TextFormField(
//                     style: TextStyle(
//                         fontSize: tS * 14,
//                         letterSpacing: .30,
//                         fontWeight: FontWeight.w600),
//                     // textAlign: TextAlign.center,
//                     inputFormatters: [FilteringTextInputFormatter.digitsOnly],
//                     maxLength: 5,
//                     decoration: InputDecoration(
//                       hintStyle:
//                           TextStyle(fontSize: 12, color: Colors.grey.shade400),
//                       contentPadding: EdgeInsets.symmetric(
//                           horizontal: dW * 0.03, vertical: 0),
//                       hintText: 'Enter Price',
//                       counterText: "",
//                       fillColor: Colors.transparent,
//                       border: textFormBorder(context),
//                       focusedErrorBorder: textFormBorder(context),
//                       focusedBorder: textFormBorder(context),
//                       enabledBorder: textFormBorder(context),
//                       filled: true,
//                     ),
//                     textInputAction: TextInputAction.next,
//                     cursorColor: Colors.black,
//                     autovalidateMode: AutovalidateMode.onUserInteraction,
//                     keyboardType: TextInputType.number,
//                     controller: TextEditingController.fromValue(
//                       TextEditingValue(
//                         text: e['price'] ?? '',
//                         selection: TextSelection.collapsed(
//                             offset:
//                                 priceCursorPosition), // Set cursor position for Name field
//                       ),
//                     ),
//                     onChanged: (value) {
//                       setState(() {
//                         e['price'] = value;
//                         priceCursorPosition = e['price'].length ?? 0;
//                       });
//                     },
//                   ),
//                   // SizedBox(
//                   //   height: dW * 0.06,
//                   // ),
//                   // TextWidget(
//                   //   title: 'Select Sports',
//                   //   fontWeight: FontWeight.w500,
//                   //   color: Color(0xff9798A3),
//                   // ),
//                   // SizedBox(
//                   //   height: dW * 0.02,
//                   // ),
//                   // buildDaysSelectionWidget(
//                   //   context: context,
//                   //   listOfIterate: widget.selectedSportsForEquip,
//                   // ),
//                   SizedBox(
//                     height: dW * 0.06,
//                   ),
//                   TextWidget(
//                     title: 'Equipment Image',
//                     fontWeight: FontWeight.w500,
//                     color: Color(0xff9798A3),
//                   ),
//                   GestureDetector(
//                     onTap: () => e['image'].isEmpty
//                         ? widget.imagePickerBottomSheetForEquipment(index)
//                         : null,
//                     child: Container(
//                       margin:
//                           EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.06),
//                       height: dW * 0.48,
//                       width: dW,
//                       child: e['image'].isEmpty
//                           ? DottedBorder(
//                               color: getLightGreyColor1(context),
//                               dashPattern: const [10, 10],
//                               radius: const Radius.circular(8),
//                               borderType: BorderType.RRect,
//                               child: Container(
//                                 width: dW,
//                                 padding: EdgeInsets.symmetric(
//                                   vertical: dW * .12,
//                                 ),
//                                 decoration: BoxDecoration(
//                                     color: getLightGreyColor2(context)),
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.center,
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     const AssetSvgIcon(
//                                         iconName: 'camera', height: 30),
//                                     SizedBox(height: dW * 0.02),
//                                     TextWidget(
//                                       title: 'Add Photo',
//                                       fontWeight: FontWeight.w400,
//                                       color: getGreyColor2(context),
//                                     ),
//                                     TextWidget(
//                                       title: '360 X 261 PX, 5 MB',
//                                       fontWeight: FontWeight.w400,
//                                       fontSize: 11,
//                                       color: getGreyColor10(context),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             )
//                           : Stack(
//                               clipBehavior: Clip.none,
//                               children: [
//                                 Positioned(
//                                   top: 0,
//                                   bottom: 0,
//                                   right: 0,
//                                   left: 0,
//                                   child: ClipRRect(
//                                     borderRadius: BorderRadius.circular(10),
//                                     child: e['image'].contains('https')
//                                         ? Image.network(
//                                             e['image'],
//                                             fit: BoxFit.cover,
//                                           )
//                                         : Image.file(
//                                             File(e['image']),
//                                             fit: BoxFit.cover,
//                                           ),
//                                   ),
//                                 ),
//                                 Positioned(
//                                   right: -8,
//                                   top: -8,
//                                   child: GestureDetector(
//                                     onTap: () {
//                                       widget.removeSelectedOrVideoForEquipment(
//                                           index);
//                                     },
//                                     child: CircleAvatar(
//                                       radius: 12,
//                                       backgroundColor: getThemeColor(),
//                                       child: Icon(Icons.clear,
//                                           size: 18, color: Colors.white),
//                                     ),
//                                   ),
//                                 )
//                               ],
//                             ),
//                     ),
//                   ),
//                   Divider(
//                     color: Color(
//                       0xffD9D9D9,
//                     ),
//                     thickness: 1,
//                   ),
//                   SizedBox(
//                     height: dW * 0.06,
//                   ),
//                 ],
//               );
//             },
//           ),
//           CustomButton(
//             width: dW,
//             height: dH * 0.055,
//             buttonText: 'Add more items',
//             onPressed: areAllFieldsFilled()
//                 ? () {
//                     setState(() {
//                       widget.equipments.add({
//                         'name': '',
//                         'price': '',
//                         // 'selectedSport': [],
//                         'image': '',
//                       });
//                     });
//                   }
//                 : null,
//           ),
//         ],
//       ),
//     );
//   }
// }

// ignore_for_file: must_be_immutable

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import '../../../../common_function.dart';
import '../../../../homeModule/models/bookingModel.dart';

class ChildStep3SixthWidget extends StatefulWidget {
  final List<RentalItem> rentalItems;
  final Function(List<Map<String, dynamic>>) onItemsSelected;

  ChildStep3SixthWidget({
    required this.rentalItems,
    required this.onItemsSelected,
  });

  @override
  State<ChildStep3SixthWidget> createState() => _ChildStep3SixthWidgetState();
}

class _ChildStep3SixthWidgetState extends State<ChildStep3SixthWidget> {
  double dW = 0.0;
  double dH = 0.0;

  double tS = 0.0;
  Set<int> selectedIndex = {};

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Is any Equipment available at your venue? (Optional)',
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.025),
          TextWidget(
            title:
                'Rent sports equipment and gear to enhance the player experience at your venue.',
            fontSize: 12,
          ),
          ListView.builder(
            shrinkWrap: true,
            itemCount: widget.rentalItems.length,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, i) {
              final item = widget.rentalItems[i];
              final isSelected = selectedIndex.contains(i);

              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      selectedIndex.remove(i);
                    } else {
                      selectedIndex.add(i);
                    }
                  });

                  List<Map<String, dynamic>> selectedItems = selectedIndex
                      .map((index) => {
                            'name': widget.rentalItems[index].productName,
                            'price': widget.rentalItems[index].price,
                            'image': widget.rentalItems[index].productImage,
                          })
                      .toList();

                  widget.onItemsSelected(selectedItems);
                },
                child: Container(
                  margin: EdgeInsets.only(top: dW * 0.1),
                  padding: EdgeInsets.symmetric(
                      vertical: dW * 0.05, horizontal: dW * 0.05),
                  decoration: BoxDecoration(
                    color: isSelected ? getThemeColor() : Colors.transparent,
                    borderRadius: BorderRadius.circular(5),
                    border: Border.all(color: getThemeColor()),
                  ),
                  child: Row(
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.productImage,
                        height: 30,
                        fit: BoxFit.cover,
                        placeholder: (_, __) => Image.asset(
                          'assets/images/user.png',
                          fit: BoxFit.cover,
                          height: 35,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: dW * 0.03),
                        child: SizedBox(
                          width: dW * 0.4,
                          child: Text(
                            item.productName,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 18,
                              color: isSelected ? Colors.white : Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '\u20b9 ${item.price.toStringAsFixed(1).replaceAll(RegExp(r'\.0$'), '')}/${item.duration} min',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: isSelected ? Colors.white : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
