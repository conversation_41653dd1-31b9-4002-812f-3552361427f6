// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ChildStep3SecondWidget extends StatefulWidget {
  final Map selectedSportCategory;
  final List selectedSports;
  List<Map<String, dynamic>> setCourtsAndTableTypes;
  final List<Map<String, dynamic>> listOfSportOfferings;

  final List turfSize;
  final Function setQuantity;
  final bool isNet;
  final String option;
  List sportQuantity;
  ChildStep3SecondWidget({
    required this.selectedSportCategory,
    required this.selectedSports,
    required this.setCourtsAndTableTypes,
    required this.listOfSportOfferings,
    required this.sportQuantity,
    required this.turfSize,
    required this.setQuantity,
    required this.isNet,
    required this.option,
  });

  @override
  State<ChildStep3SecondWidget> createState() => _ChildStep3SecondWidgetState();
}

class _ChildStep3SecondWidgetState extends State<ChildStep3SecondWidget> {
  double dW = 0.0;
  double tS = 0.0;

  List sportQuantity = [];
  // List<Map<String, dynamic>> sportQuantity = [];

  List<Widget> labelFields = [];
  List<List<Widget>> labelFieldsList = [];

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(10),
    );
  }

  Widget _generateLabelFields(int index) {
    int quantity = widget.sportQuantity[index]['quantity'];
    // List<dynamic> labels = widget.sportQuantity[index]['labels'];
    List<Widget> labelFields = [];

    quantity = quantity > 5 ? 0 : quantity;

    if (quantity > 0) {
      int rows = (quantity / 2).ceil();

      for (int i = 0; i < rows; i++) {
        List<Widget> rowFields = [];

        for (int j = 0; j < 2 && (i * 2 + j) < quantity; j++) {
          rowFields.add(
            Expanded(
              child: Container(
                width: dW * 0.23,
                margin: EdgeInsets.only(right: dW * 0.03),
                child: TextFormField(
                  initialValue:
                      // labels.isNotEmpty ? labels[i * 2 + j] : '',
                      widget.sportQuantity[index]['labels'] != null &&
                              widget.sportQuantity[index]['labels'].length >
                                  (i * 2 + j)
                          ? widget.sportQuantity[index]['labels'][i * 2 + j]
                          : '',
                  style: TextStyle(
                    fontSize: tS * 14,
                    letterSpacing: .30,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    hintStyle:
                        TextStyle(fontSize: 12, color: Colors.grey.shade400),
                    // hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    //     fontSize: tS * 12, color: Colors.grey.shade400),
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: dW * 0.02, vertical: 0),
                    hintText: '${i * 2 + j + 1}',
                    counterText: "",
                    fillColor: Colors.transparent,
                    border: textFormBorder(context),
                    focusedErrorBorder: textFormBorder(context),
                    focusedBorder: textFormBorder(context),
                    enabledBorder: textFormBorder(context),
                    filled: true,
                  ),
                  textInputAction: TextInputAction.next,
                  cursorColor: Colors.black,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  keyboardType: TextInputType.text,
                  onChanged: (value) {
                    setState(() {
                      if (widget.sportQuantity[index]['labels'] == null) {
                        widget.sportQuantity[index]['labels'] = List.generate(
                            quantity, (index) => (index + 1).toString());
                      } else {
                        int currentLength =
                            widget.sportQuantity[index]['labels'].length;
                        int expectedLength = quantity;
                        if (currentLength < expectedLength) {
                          // Add empty labels for the new quantity
                          for (int k = currentLength; k < expectedLength; k++) {
                            widget.sportQuantity[index]['labels'].add('');
                          }
                        } else if (currentLength > expectedLength) {
                          // Remove extra labels if the quantity decreases
                          widget.sportQuantity[index]['labels']
                              .removeRange(expectedLength, currentLength);
                        }
                        widget.sportQuantity[index]['labels'][i * 2 + j] =
                            value.trim();
                      }
                      // Update the label for the corresponding sport quantity
                    });

                    // setState(() {
                    //   // Ensure labels list is initialized
                    //   if (widget.sportQuantity[index]['labels'] == null) {
                    //     widget.sportQuantity[index]['labels'] = List.generate(
                    //         quantity, (index) => (index + 1).toString());
                    //     // widget.sportQuantity[index]['labels'] = [];
                    //   }

                    //   // Update the label for the corresponding sport quantity
                    //   if (widget.sportQuantity[index]['labels'].length <=
                    //       (i * 2 + j)) {
                    //     widget.sportQuantity[index]['labels'].add(value.trim());
                    //   } else {
                    //     while (widget.sportQuantity[index]['labels'].length >
                    //         (i * 2 + j)) {
                    //       widget.sportQuantity[index]['labels'].removeLast();
                    //     }
                    //     widget.sportQuantity[index]['labels'][i * 2 + j] =
                    //         value.trim();
                    //   }

                    // });
                  },
                ),
              ),
            ),
          );
        }
        labelFields.add(
          Container(
            margin: EdgeInsets.only(top: dW * 0.04),
            child: Row(
              children: rowFields,
            ),
          ),
        );
      }
    }

    return Column(children: labelFields);
  }

  // Widget _buildLabelTextField(
  //   int index,
  //   List<String> labels,
  //   String labelText,
  // ) {
  //   // List<String> labels = widget.sportQuantity[index]['labels'];
  //   return Container(
  //     width: dW * 0.23,
  //     margin: EdgeInsets.only(right: dW * 0.03),
  //     child: TextFormField(
  //       initialValue:
  //           // labels.isNotEmpty ? labels[index] :
  //           '',
  //       style: TextStyle(
  //         fontSize: tS * 14,
  //         letterSpacing: .30,
  //         fontWeight: FontWeight.w600,
  //       ),
  //       textAlign: TextAlign.center,
  //       decoration: InputDecoration(
  //         hintStyle: Theme.of(context)
  //             .textTheme
  //             .headlineSmall!
  //             .copyWith(fontSize: tS * 12, color: Colors.grey.shade400),
  //         contentPadding:
  //             EdgeInsets.symmetric(horizontal: dW * 0.02, vertical: 0),
  //         hintText: labelText,
  //         counterText: "",
  //         fillColor: Colors.transparent,
  //         border: textFormBorder(context),
  //         focusedErrorBorder: textFormBorder(context),
  //         focusedBorder: textFormBorder(context),
  //         enabledBorder: textFormBorder(context),
  //         filled: true,
  //       ),
  //       textInputAction: TextInputAction.next,
  //       cursorColor: Colors.black,
  //       autovalidateMode: AutovalidateMode.onUserInteraction,
  //       keyboardType: TextInputType.text,
  //       onChanged: (value) {
  //         if (value.length != 0) {
  //           setState(() {
  //             widget.sportQuantity[index]['labels'].add(value.trim());

  //             //  = value.trim();
  //             // Update the label for the corresponding sport quantity
  //           });
  //         }
  //       },
  //     ),
  //   );
  // }

  @override
  void initState() {
    super.initState();

    // for (int i = 0; i < widget.sportQuantity.length; i++) {
    //   if (widget.sportQuantity[i]['labels'] == null) {
    //     widget.sportQuantity[i]['labels'] = [];
    //   }
    // }
    // for (int i = 0; i < sportQuantity.length; i++) {
    if (widget.sportQuantity.isEmpty) {
      if (widget.selectedSportCategory['categoryName'] != 'Outdoor') {
        widget.selectedSports.forEach((sport) {
          if (sport != 'Snooker') {
            List<dynamic> types = widget.listOfSportOfferings
                .firstWhere((item) => item['title'] == sport)['type'];
            List advanceAmounts = [];
            List totalPlayersAllowed = [];

            bool hasSingles = types.any(
                (type) => type.keys.contains('Singles') && type['Singles']);
            bool hasDoubles = types.any(
                (type) => type.keys.contains('Doubles') && type['Doubles']);
            if (hasSingles) {
              advanceAmounts.add({'Singles': TextEditingController()});
              totalPlayersAllowed.add({'Singles': TextEditingController()});
            }
            if (hasDoubles) {
              advanceAmounts.add({'Doubles': TextEditingController()});
              totalPlayersAllowed.add({'Doubles': TextEditingController()});
            }
            if (sport == 'Table Tennis') {
              sportQuantity.add({
                'title': 'Table Tennis',
                'type': 'Table Tennis Tables',
                'quantity': 0,
                'label': '',
                'labels': [],
                'advanceAmount':
                    advanceAmounts.isNotEmpty ? advanceAmounts : null,
                'totalPlayersAllowed':
                    totalPlayersAllowed.isNotEmpty ? totalPlayersAllowed : null,

                // 'advanceAmount': TextEditingController()
              });
            } else if (sport == 'Squash') {
              sportQuantity.add({
                'title': 'Squash',
                'type': 'Squash Courts',
                'quantity': 0,
                'label': '',
                'labels': [],
                'advanceAmount':
                    advanceAmounts.isNotEmpty ? advanceAmounts : null,
                'totalPlayersAllowed':
                    totalPlayersAllowed.isNotEmpty ? totalPlayersAllowed : null,

                // 'advanceAmount': TextEditingController()
              });
            }
          }
          List advanceAmountList = [];
          widget.setCourtsAndTableTypes.forEach((element) {
            if (sport == element['sport']) {
              element['title'].forEach((type) {
                if (sport != 'Snooker') {
                  List<dynamic> types = widget.listOfSportOfferings
                      .firstWhere((item) => item['title'] == sport)['type'];
                  List advanceAmounts = [];
                  List totalPlayersAllowed = [];

                  bool hasSingles = types.any((type) =>
                      type.keys.contains('Singles') && type['Singles']);
                  bool hasDoubles = types.any((type) =>
                      type.keys.contains('Doubles') && type['Doubles']);
                  if (hasSingles) {
                    advanceAmounts.add({'Singles': TextEditingController()});
                    totalPlayersAllowed
                        .add({'Singles': TextEditingController()});
                  }
                  if (hasDoubles) {
                    advanceAmounts.add({'Doubles': TextEditingController()});
                    totalPlayersAllowed
                        .add({'Doubles': TextEditingController()});
                  }
                  //  sport == 'Badminton'
                  //   ?
                  if (sport == 'Badminton') {
                    sportQuantity.add({
                      'title': sport,
                      'type': type,
                      'quantity': 0,
                      'label': '',
                      'labels': [],
                      // 'advanceAmount': TextEditingController()
                      'advanceAmount':
                          advanceAmounts.isNotEmpty ? advanceAmounts : null,

                      'totalPlayersAllowed': totalPlayersAllowed.isNotEmpty
                          ? totalPlayersAllowed
                          : null,
                    });
                  }
                  // if (sport == 'Table Tennis') {
                  //   sportQuantity.add({
                  //     'title': 'Table Tennis',
                  //     'type': 'Table Tennis Tables',
                  //     'quantity': 0,
                  //     'label': '',
                  //     'labels': [],
                  //     'advanceAmount':
                  //         advanceAmounts.isNotEmpty ? advanceAmounts : null,
                  //   });
                  // }
                  // if (sport == 'Squash') {
                  //   sportQuantity.add({
                  //     'title': 'Squash',
                  //     'type': 'Squash Courts',
                  //     'quantity': 0,
                  //     'label': '',
                  //     'labels': [],
                  //     'advanceAmount':
                  //         advanceAmounts.isNotEmpty ? advanceAmounts : null,
                  //   });
                  // }
                }
                if (sport == 'Snooker') {
                  sportQuantity.add({
                    'title': sport,
                    'type': type,
                    'quantity': 0,
                    'label': '',
                    'labels': [],
                    'advanceAmount': TextEditingController(),
                    'totalPlayersAllowed': TextEditingController(),
                  });
                }
              });
            }
          });
        });
      } else {
        if (widget.turfSize.contains('11:11')) {
          sportQuantity.add({
            'title': '11:11',
            'quantity': 0,
            'label': '',
            'labels': [],
            'advanceAmount': TextEditingController(),
            'totalPlayersAllowed': TextEditingController(),
          });
        }
        if (widget.turfSize.contains('9:9')) {
          sportQuantity.add({
            'title': '9:9',
            'quantity': 0,
            'label': '',
            'labels': [],
            'advanceAmount': TextEditingController(),
            'totalPlayersAllowed': TextEditingController(),
          });
          if (widget.turfSize.contains('7:7')) {
            sportQuantity.add({
              'title': '7:7',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
          if (widget.turfSize.contains('5:5')) {
            sportQuantity.add({
              'title': '5:5',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
          if (widget.turfSize.contains('6:6')) {
            sportQuantity.add({
              'title': '6:6',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
        } else if (widget.turfSize.contains('7:7')) {
          sportQuantity.add({
            'title': '7:7',
            'quantity': 0,
            'label': '',
            'labels': [],
            'advanceAmount': TextEditingController(),
            'totalPlayersAllowed': TextEditingController(),
          });
          if (widget.turfSize.contains('5:5')) {
            sportQuantity.add({
              'title': '5:5',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
          if (widget.turfSize.contains('6:6')) {
            sportQuantity.add({
              'title': '6:6',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
        } else if (widget.turfSize.contains('5:5') &&
            !widget.turfSize.contains('6:6') &&
            !widget.turfSize.contains('8:8')) {
          sportQuantity = [
            {
              'title': '5:5',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            }
          ];
        } else if (widget.turfSize.contains('6:6') &&
            !widget.turfSize.contains('8:8') &&
            !widget.turfSize.contains('5:5')) {
          sportQuantity = [
            {
              'title': '6:6',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            }
          ];
        } else if (widget.turfSize.contains('5:5') &&
            widget.turfSize.contains('6:6') &&
            !widget.turfSize.contains('8:8')) {
          sportQuantity = [
            {
              'title': '6:6',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            },
            {
              'title': '5:5',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            }
          ];
        } else if (widget.turfSize.contains('8:8') ||
            widget.turfSize.contains('6:6') ||
            widget.turfSize.contains('5:5')) {
          sportQuantity = [
            {
              'title': '8:8',
              'quantity': 0,
              'label': '',
              'labels': [],
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            }
          ];
          if (widget.turfSize.contains('6:6')) {
            sportQuantity.add({
              'title': '6:6',
              'quantity': 0,
              'labels': [],
              'label': '',
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
          if (widget.turfSize.contains('5:5')) {
            sportQuantity.add({
              'title': '5:5',
              'quantity': 0,
              'labels': [],
              'label': '',
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            });
          }
        } else if (widget.turfSize.contains('6:6')) {
          sportQuantity = [
            {
              'title': '6:6',
              'quantity': 0,
              'labels': [],
              'label': '',
              'advanceAmount': TextEditingController(),
              'totalPlayersAllowed': TextEditingController(),
            }
          ];
        }
        if (!widget.isNet) {
          if (widget.turfSize.contains('6:6')) {
            var index = sportQuantity
                .indexWhere((element) => element['title'] == '6:6');
            if (index == -1) {
              sportQuantity.add({
                'title': '6:6',
                'quantity': 0,
                'labels': [],
                'label': '',
                'advanceAmount': TextEditingController(),
                'totalPlayersAllowed': TextEditingController(),
              });
            }
          }
          if (widget.turfSize.contains('8:8')) {
            var index = sportQuantity
                .indexWhere((element) => element['title'] == '8:8');
            if (index == -1) {
              sportQuantity.add({
                'title': '8:8',
                'quantity': 0,
                'labels': [],
                'label': '',
                'advanceAmount': TextEditingController(),
                'totalPlayersAllowed': TextEditingController(),
              });
            }
          }
        }
      }
      if (widget.option == 'One On' &&
          widget.selectedSportCategory['categoryName'] == 'Outdoor') {
        sportQuantity.add({
          'title': '1:1',
          'quantity': 0,
          'labels': [],
          'label': '',
          'advanceAmount': TextEditingController(),
          'totalPlayersAllowed': TextEditingController(),
        });
      }
    }
    // }

    widget.turfSize.toSet();
    for (int i = 0; i < sportQuantity.length; i++) {
      if (sportQuantity[i]['labels'].isEmpty) {
        sportQuantity.toSet();

        widget.setQuantity(sportQuantity);
      }
    }

    // for (int i = 0; i < widget.sportQuantity.length; i++) {
    //   _generateLabelFields(
    //     i,
    //   );
    // }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title:
                // 'Now lets enter ${widget.isNet ? 'relation' : 'quantity'} of venue',
                widget.selectedSportCategory['categoryName'] == 'Outdoor'
                    ? 'Now lets enter quantity of venue'
                    : 'Now lets enter quantity of courts & tables',
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title: widget.selectedSportCategory['categoryName'] == 'Outdoor'
                ? 'Specify the number of sports available at your turf'
                : 'Specify the number of courts & tables available at your venue.',
            fontSize: 12,
            color: Color(0xff21272A),
            fontWeight: FontWeight.w400,
          ),
          if (widget.selectedSportCategory['categoryName'] == 'Indoor')
            TextWidget(
              title: "*Note- You can't add labels for quantities exceeding 5",
              fontSize: 12,
              color: Color(0xffD84848),
            ),
          SizedBox(height: dW * 0.1),
          // ...widget.sportQuantity.map(
          //   (sport) =>
          for (int i = 0; i < widget.sportQuantity.length; i++)
            Column(
              children: [
                CustomContainer(
                  boxShadow: [],
                  borderColor: getThemeColor(),
                  vPadding: .032,
                  hPadding: .035,
                  margin: EdgeInsets.only(bottom: dW * 0.05),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // TextWidget(
                      //   title: widget.sportQuantity[i]['title'] ==
                      //               'Table Tennis' ||
                      //           widget.sportQuantity[i]['title'] == 'Squash'
                      //       ? widget.sportQuantity[i]['type']
                      //       : widget.selectedSportCategory['categoryName'] ==
                      //               'Indoor'
                      //           ? '${widget.sportQuantity[i]['title']} (${widget.sportQuantity[i]['type']})'
                      //           : '${widget.sportQuantity[i]['title']}',
                      //   color: getThemeColor(),
                      //   fontSize: 14,
                      //   fontWeight: FontWeight.w600,
                      // ),
                      TextWidget(
                        title: widget.sportQuantity[i]['title'] ==
                                    'Table Tennis' ||
                                widget.sportQuantity[i]['title'] == 'Squash'
                            ? widget.sportQuantity[i]['type']
                            : widget.selectedSportCategory['categoryName'] ==
                                    'Indoor'
                                ? '${widget.sportQuantity[i]['title']} (${widget.sportQuantity[i]['type'] == 'Wooden Court' ? 'Default Court' : widget.sportQuantity[i]['type']})'
                                : '${widget.sportQuantity[i]['title']}',
                        color: getThemeColor(),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      SizedBox(height: dW * 0.03),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              TextWidget(
                                title: widget.isNet
                                    ? 'Enter Quantity'
                                    : 'Enter Quantity',
                                // sport['title'],
                                color: Color(0xff636363),
                                fontWeight: FontWeight.w500,
                                fontSize: widget.selectedSportCategory[
                                            'categoryName'] ==
                                        'Outdoor'
                                    ? tS * 14
                                    : tS * 14,
                              ),
                              TextWidget(
                                title: '*',
                                color: Color(0xffD84848),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Container(
                                width: dW * 0.23,
                                margin: EdgeInsets.only(right: dW * 0.03),
                                child: TextFormField(
                                  initialValue:
                                      //  sport['quantity']
                                      widget.sportQuantity[i]['quantity'] == 0
                                          ? ''
                                          :
                                          //  sport['quantity']
                                          widget.sportQuantity[i]['quantity']
                                              .toString(),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  style: TextStyle(
                                    fontSize: tS * 14,
                                    letterSpacing: .30,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                  decoration: InputDecoration(
                                    hintStyle: Theme.of(context)
                                        .textTheme
                                        .headlineSmall!
                                        .copyWith(
                                            fontSize: tS * 12,
                                            color: Colors.grey.shade400),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: dW * 0.02, vertical: 0),
                                    hintText: widget.selectedSportCategory[
                                                'categoryName'] !=
                                            'Outdoor'
                                        ? 'Quantity'
                                        : widget.isNet
                                            ? 'Quantity'
                                            : 'Quantity',
                                    counterText: "",
                                    fillColor: Colors.transparent,
                                    border: textFormBorder(context),
                                    focusedErrorBorder: textFormBorder(context),
                                    focusedBorder: textFormBorder(context),
                                    enabledBorder: textFormBorder(context),
                                    filled: true,
                                  ),
                                  maxLength: 8,
                                  cursorColor: Colors.black,
                                  autovalidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  keyboardType: TextInputType.number,
                                  textInputAction: TextInputAction.next,
                                  onChanged: (value) {
                                    // if (value.length != 0) {
                                    //   sport['quantity'] = int.parse(value);
                                    //   print(sport['quantity']);
                                    // }
                                    if (value.isEmpty) {
                                      setState(() {
                                        // Clear the label fields
                                        widget.sportQuantity[i]['quantity'] = 0;
                                      });
                                    } else {
                                      int parsedValue =
                                          int.tryParse(value) ?? 0;
                                      setState(() {
                                        widget.sportQuantity[i]['quantity'] =
                                            parsedValue;
                                      });
                                    }
                                  },
                                ),
                              ),
                              // Container(
                              //   width: dW * 0.23,
                              //   child: TextFormField(
                              //     initialValue:
                              //         //  sport['label']
                              //         widget.sportQuantity[i]['label'] == ''
                              //             ? ''
                              //             : widget.sportQuantity[i]['label']

                              //     // sport['label']
                              //     ,
                              //     style: TextStyle(
                              //       fontSize: tS * 14,
                              //       letterSpacing: .30,
                              //       fontWeight: FontWeight.w600,
                              //     ),
                              //     textAlign: TextAlign.center,
                              //     decoration: InputDecoration(
                              //       hintStyle: Theme.of(context)
                              //           .textTheme
                              //           .headlineSmall!
                              //           .copyWith(
                              //               fontSize: tS * headline9,
                              //               color: Colors.grey.shade400),
                              //       contentPadding: EdgeInsets.symmetric(
                              //         horizontal: dW * 0.02,
                              //         vertical: 0,
                              //       ),
                              //       hintText: widget.selectedSportCategory[
                              //                   'categoryName'] !=
                              //               'Outdoor'
                              //           ? 'Label'
                              //           : widget.isNet
                              //               ? 'Label'
                              //               : 'Label',
                              //       counterText: "",
                              //       fillColor: Colors.transparent,
                              //       border: textFormBorder(context),
                              //       focusedErrorBorder: textFormBorder(context),
                              //       focusedBorder: textFormBorder(context),
                              //       enabledBorder: textFormBorder(context),
                              //       filled: true,
                              //     ),
                              //     textInputAction: TextInputAction.next,
                              //     cursorColor: Colors.black,
                              //     autovalidateMode:
                              //         AutovalidateMode.onUserInteraction,
                              //     keyboardType: TextInputType.text,
                              //     onChanged: (value) {
                              //       if (value.length != 0) {
                              //         // sport['label']
                              //         widget.sportQuantity[i]['label'] =
                              //             value.trim();
                              //         // print(sport['label']);
                              //       }
                              //     },
                              //   ),
                              // ),
                            ],
                          ),
                        ],
                      ),
                      // if (widget.sportQuantity[i]['quantity'] > 0)
                      //   labelFields[i]
                      if (widget.sportQuantity[i]['quantity'] > 0)
                        _generateLabelFields(i),
                    ],
                  ),
                ),
              ],
            ),
          // ),
        ],
      ),
    );
  }
}
