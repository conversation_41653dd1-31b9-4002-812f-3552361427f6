// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step4/cancellation_template_bottomsheet.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/custom_text_field.dart';
import '../../../../commonWidgets/text_widget.dart';
import '../../../widgets/addCancellationBottomsheet.dart';
import '../../../widgets/cancellationWidget.dart';

class ChildStep4SecondWidget extends StatelessWidget {
  final List cancellationCharges;
  final Function addOrRemoveCancellation;
  final Function editCancellation;
  final TextEditingController cancellationController;

  ChildStep4SecondWidget({
    required this.cancellationCharges,
    required this.addOrRemoveCancellation,
    required this.editCancellation,
    required this.cancellationController,
  });

  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  openTemplateBottomSheet(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) => CancellationTemplateBottomSheet(),
    ).then((value) {
      if (value != null) {
        cancellationController.text = value;
      }
    });
  }

  addCancellationBottomSheet(
      {required BuildContext context, Map? data, int? index}) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddCancellationBottomSheet(
          cancellationCharges: cancellationCharges,
          data: data,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        if (data != null) {
          editCancellation(data: value, index: index);
        } else {
          addOrRemoveCancellation(
            data: value,
            index: cancellationCharges.isEmpty ? 0 : cancellationCharges.length,
            add: true,
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Let’s set cancellation charges & cancellation policy',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'You’ll need to set turf’s cancellation charges and cancellation policy for your minimum loss.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.08),
          TextWidget(
            title: 'Cancellation Charges',
            fontSize: 14,
            color: Color(0xff9798A3),
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.05),
          if (cancellationCharges.isNotEmpty)
            ...cancellationCharges
                .asMap()
                .map(
                  (i, data) => MapEntry(
                    i,
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        CancellationWidget(
                          data: data,
                          deviceWidth: dW,
                          textScaleFactor: tS,
                        ),
                        Positioned(
                          right: -10,
                          child: PopupMenuButton(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(7),
                            ),
                            icon: Icon(
                              Icons.more_vert,
                              color: Colors.black,
                            ),
                            itemBuilder: (BuildContext bc) => [
                              PopupMenuItem(
                                child: Text(
                                  "Edit",
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: tS * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 1,
                              ),
                              PopupMenuItem(
                                child: Text(
                                  'Delete',
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: tS * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 2,
                              ),
                            ],
                            onSelected: (value) {
                              if (value == 1) {
                                addCancellationBottomSheet(
                                    context: context, data: data, index: i);
                              } else if (value == 2) {
                                addOrRemoveCancellation(
                                  data: data,
                                  index: i,
                                  add: false,
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .values
                .toList(),
          SizedBox(height: dW * 0.02),
          CustomButton(
            width: dW,
            height: dW * 0.12,
            buttonText:
                cancellationCharges.isEmpty ? 'Add Charges' : 'Add more',
            onPressed: () => addCancellationBottomSheet(context: context),
            fontSize: 14,
            radius: 8,
            elevation: 0,
            borderColor: getThemeColor(),
            textColor: getThemeColor(),
            buttonColor: Color(0xffF3FFEF),
          ),
          SizedBox(height: dW * 0.08),
          CustomTextFieldWithLabel(
            label: 'Cancellation Policy',
            widget: GestureDetector(
              onTap: () => openTemplateBottomSheet(context),
              child: AssetSvgIcon(
                iconName: 'info_rounded',
                color: getThemeColor(),
              ),
            ),
            controller: cancellationController,
            hintText: 'Enter cancellation policy',
            borderRadius: 5,
            labelFS: 14,
            labelColor: Color(0xff9798A3),
            labelFW: FontWeight.w500,
            hintColor: Colors.black,
            borderColor: Color(0xffACACB4),
            fillColor: Color(0xffF8F9FD),
            counterText: '${cancellationController.text.trim().length}/600',
            maxLines: 8,
            maxLength: 600,
            textFS: 14,
            textCapitalization: TextCapitalization.sentences,
            inputType: TextInputType.streetAddress,
            inputAction: TextInputAction.newline,
          ),
        ],
      ),
    );
  }
}
