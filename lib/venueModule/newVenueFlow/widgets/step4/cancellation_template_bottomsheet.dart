// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../authModule/providers/auth.dart';
import '../../../../commonWidgets/custom_button.dart';
import '../../../../commonWidgets/divider_widget.dart';
import '../../../../commonWidgets/text_widget.dart';
import '../../../../common_function.dart';
import '../../../../navigators.dart';

class CancellationTemplateBottomSheet extends StatelessWidget {
  CancellationTemplateBottomSheet({Key? key});

  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      height: dH * 0.6,
      child: Column(
        children: [
          SizedBox(height: dW * 0.01),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: 'Cancellation Policy Template',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                IconButton(
                  padding: EdgeInsets.all(0),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  icon: Icon(Icons.clear),
                )
              ],
            ),
          ),
          DividerWidget(top: 0),
          Expanded(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    title: Provider.of<Auth>(context, listen: false)
                        .cancellationTemplate,
                    fontWeight: FontWeight.w500,
                  ),
                  SizedBox(height: dW * 0.08),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomButton(
                  width: dW * 0.4,
                  height: dW * 0.12,
                  fontSize: 16,
                  radius: 7,
                  borderColor: getThemeColor(),
                  textColor: getThemeColor(),
                  buttonColor: Colors.white,
                  buttonText: 'Close',
                  onPressed: pop,
                ),
                CustomButton(
                  width: dW * 0.45,
                  height: dW * 0.12,
                  fontSize: 16,
                  radius: 7,
                  buttonText: 'Copy',
                  onPressed: () => pop(Provider.of<Auth>(context, listen: false)
                      .cancellationTemplate),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
