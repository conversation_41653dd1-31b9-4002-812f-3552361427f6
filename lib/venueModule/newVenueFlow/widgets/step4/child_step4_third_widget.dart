// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/custom_text_field.dart';
import '../../../../commonWidgets/text_widget.dart';

class ChildStep4ThirdWidget extends StatefulWidget {
  final bool isBusinessGSTRegistered;
  final String selectedGstType;
  final TextEditingController gstNoController;
  final Function setBusinessGSTRegister;
  final Function setGstType;
  ChildStep4ThirdWidget({
    required this.isBusinessGSTRegistered,
    required this.selectedGstType,
    required this.gstNoController,
    required this.setBusinessGSTRegister,
    required this.setGstType,
  });

  @override
  State<ChildStep4ThirdWidget> createState() => _ChildStep4ThirdWidgetState();
}

class _ChildStep4ThirdWidgetState extends State<ChildStep4ThirdWidget> {
  double dW = 0.0;

  double tS = 0.0;

  Widget buildRadioSelection({
    required String title,
    required bool isSelected,
    required BuildContext context,
    required Function onTap,
  }) {
    return GestureDetector(
      onTap: () => onTap(),
      child: CustomContainer(
        hPadding: .035,
        boxShadow: [],
        borderColor: isSelected ? getThemeColor() : Colors.grey.shade300,
        bgColor: isSelected ? getThemeColor() : Colors.transparent,
        width: dW * 0.42,
        vPadding: .045,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: dW * 0.25),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: TextWidget(
                  title: title,
                  fontSize: 16,
                  color: isSelected ? Colors.white : Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Container(
              height: dW * 0.05,
              padding: EdgeInsets.all(2.5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.white : Color(0xffB6B7BA),
                ),
              ),
              child: CircleAvatar(
                radius: 8,
                backgroundColor: isSelected ? Colors.white : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Is your business GST registered?',
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.1),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildRadioSelection(
                title: 'Yes',
                isSelected: widget.isBusinessGSTRegistered,
                context: context,
                onTap: () => widget.setBusinessGSTRegister(true),
              ),
              buildRadioSelection(
                title: 'No',
                isSelected: !widget.isBusinessGSTRegistered,
                context: context,
                onTap: () => widget.setBusinessGSTRegister(false),
              )
            ],
          ),
          if (widget.isBusinessGSTRegistered) ...[
            DividerWidget(top: dW * 0.05, bottom: dW * 0.04),
            TextWidget(
              title: 'GST Type',
              color: getThemeColor(),
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            SizedBox(height: dW * 0.035),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildRadioSelection(
                  title: 'Regular',
                  isSelected: widget.selectedGstType == 'Regular',
                  context: context,
                  onTap: () => widget.setGstType('Regular'),
                ),
                buildRadioSelection(
                  title: 'Composite',
                  isSelected: widget.selectedGstType == 'Composite',
                  context: context,
                  onTap: () => widget.setGstType('Composite'),
                )
              ],
            ),
            SizedBox(height: dW * 0.07),
            CustomTextFieldWithLabel(
              label: 'GST No.',
              controller: widget.gstNoController,
              hintText: 'Enter GST No.',
              borderRadius: 5,
              labelFS: 14,
              labelColor: Color(0xff9798A3),
              labelFW: FontWeight.w500,
              hintColor: Colors.black,
              borderColor: Color(0xffACACB4),
              fillColor: Color(0xffF8F9FD),
              onChanged: (value) => setState(() {}),
              inputAction: TextInputAction.done,
            ),
          ]
        ],
      ),
    );
  }
}
