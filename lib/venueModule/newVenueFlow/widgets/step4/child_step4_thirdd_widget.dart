// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../commonWidgets/text_widget.dart';
import '../../../../common_function.dart';

class ChildStep4ThirddWidget extends StatefulWidget {
  final TextEditingController termsConditionController;

  ChildStep4ThirddWidget({required this.termsConditionController});

  @override
  State<ChildStep4ThirddWidget> createState() => _ChildStep4ThirddWidgetState();
}

class _ChildStep4ThirddWidgetState extends State<ChildStep4ThirddWidget> {
  double dW = 0.0;
  double dH = 0.0;

  double tS = 0.0;

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(4),
    );
  }

  String addLineNumbers(String text) {
    if (text.isEmpty) return '';

    final lines = text.split('\n');
    final lineCount = lines.length;
    final maxLength = lineCount.toString().length;

    // Add line numbers to each line
    final numberedLines = lines.asMap().entries.map((entry) {
      final lineNumber = entry.key + 1;
      final paddedLineNumber = lineNumber.toString().padLeft(maxLength);
      final lineText = entry.value;

      // Check if line is overflowed
      final textOverflow = entry.key != lineCount - 1 &&
          lineText.isNotEmpty &&
          !lineText.startsWith('\t');

      // Add space after line number if line is not overflowed
      final spaceAfterNumber = textOverflow ? '' : ' ';

      return '$paddedLineNumber.$spaceAfterNumber$lineText';
    }).toList();

    return numberedLines.join('\n');
  }

  @override
  void initState() {
    super.initState();
    if (widget.termsConditionController.text.isEmpty) {
      widget.termsConditionController.text =
          '1. By accessing and using the turf, users agree to abide by these terms and any additional rules or guidelines posted on-site \n2. The turf is intended for sports activities, events\n3. Users are responsible for their safety and the safety of others while on the turf.\n4. Proper equipment and attire must be worn as per the designated sport.\n5. The turf has specific operating hours. Users must adhere to these hours unless prior arrangements have been made.\n6. The turf owner/operator is not liable for any injuries, accidents, or damages that may occur on the premises.\n7. Only approved equipment is allowed on the turf. Users must not bring equipment that may damage the turf surface.\n8. he turf owner/operator reserves the right to terminate or restrict access to any user who violates these terms and conditions.';
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;

    tS = MediaQuery.of(context).textScaleFactor;
    String numberedText = addLineNumbers(widget.termsConditionController.text);
    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: "Set up your venue's terms & conditions now",
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.025),
          TextWidget(
            title:
                "Craft your venue's terms and conditions. Edit or enhance at any time for clarity and compliance. Your venue, your rules",
            fontSize: 12,
          ),
          SizedBox(
            height: dW * 0.1,
          ),
          TextWidget(
            title: 'Terms & Conditions',
            fontWeight: FontWeight.w500,
            color: Color(0xff9798A3),
          ),
          SizedBox(
            height: dW * 0.02,
          ),
          TextFormField(
            style: TextStyle(
                fontSize: tS * 12,
                letterSpacing: .30,
                height: 1.6,
                color: Color(0xff5E5E5E),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500),
            // textAlign: TextAlign.center,
            // inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            // maxLength: 5,
            maxLines: 22,
            decoration: InputDecoration(
              hintStyle: TextStyle(fontSize: 12, color: Color(0xff5E5E5E)),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: dW * 0.03, vertical: dW * 0.03),
              hintText: '',
              counterText: "",
              fillColor: Colors.transparent,
              border: textFormBorder(context),
              focusedErrorBorder: textFormBorder(context),
              focusedBorder: textFormBorder(context),
              enabledBorder: textFormBorder(context),
              filled: true,
            ),
            textInputAction: TextInputAction.next,

            cursorColor: Colors.black,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            keyboardType: TextInputType.text,
            controller: widget.termsConditionController,
            // TextEditingController(text: numberedText),

            onChanged: (value) {
              setState(() {});
            },
          ),
        ],
      ),
    );
  }
}
