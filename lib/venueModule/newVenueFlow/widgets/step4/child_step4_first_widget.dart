// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/new_colors.dart';
import 'package:flutter/material.dart';

import '../../../../bulkBookingModule/widgets/select_count_bottomsheet.dart';
import '../../../../bulkBookingModule/widgets/select_duration_bottomsheet.dart';
import '../../../../commonWidgets/text_widget.dart';
import '../../../../common_function.dart';

class ChildStep4FirstWidget extends StatelessWidget {
  int durationCount;
  String period;
  final Function changeDuration;
  ChildStep4FirstWidget({
    required this.period,
    required this.durationCount,
    required this.changeDuration,
  });

  double dW = 0.0;
  double tS = 0.0;

  Widget buildDuration({
    required BuildContext context,
    required String title,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: title,
          color: Color(0xff9798A3),
          letterSpacing: 0.4,
          fontWeight: FontWeight.w500,
        ),
        SizedBox(height: dW * 0.025),
        GestureDetector(
          onTap: () {
            selectDurationOrCountDropDown(context, title);
          },
          child: CustomContainer(
            boxShadow: [],
            hPadding: .035,
            vPadding: .03,
            borderColor: getThemeColor(),
            bgColor: Color(0xffF8F9FD),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: value,
                  fontSize: 16,
                  letterSpacing: 0.3,
                  color: Color(0xff3E3E3E),
                  fontWeight: FontWeight.w500,
                ),
                Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  selectDurationOrCountDropDown(BuildContext context, duration) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: duration == 'Period'
            ? SelectDurationBottomSheet(
                selectedDuration: period,
                addDays: true,
              )
            : SelectCountBottomSheet(
                selectedDuration: period,
                selectedCount: durationCount,
              ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        changeDuration(duration, value);
      }
    });
  }

  Widget getAvailabilityCount({
    required String givenPeriod,
    required List<int> listOfCount,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: givenPeriod,
          color: Color(0xff9798A3),
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: dW * 0.02),
        Wrap(
          children: [
            ...listOfCount.map(
              (count) => GestureDetector(
                onTap: () {
                  changeDuration('Period', givenPeriod);
                  changeDuration('Duration', count);
                },
                child: Container(
                  margin: EdgeInsets.only(right: dW * 0.03, bottom: dW * 0.03),
                  width: dW * 0.27,
                  padding: EdgeInsets.symmetric(
                      vertical: dW * 0.025, horizontal: dW * 0.03),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: count == durationCount && period == givenPeriod
                        ? getThemeColor()
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(color: getThemeColor()),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: TextWidget(
                      title: '$count $givenPeriod',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: count == durationCount && period == givenPeriod
                          ? Colors.white
                          : Color(0xff5e5e5e),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
        SizedBox(height: dW * 0.02),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Now set your venue\'s booking availability',
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.03),
          TextWidget(
            title:
                'You\'ll need to add venue\'s booking availability duration.',
            fontSize: 12,
            color: Color(0xff21272A),
          ),
          SizedBox(height: dW * 0.08),
          // buildDuration(context: context, title: 'Period', value: period),
          // SizedBox(height: dW * 0.06),
          // buildDuration(
          //   context: context,
          //   title: 'Duration',
          //   value: durationCount.toString(),
          // ),
          getAvailabilityCount(givenPeriod: 'Days', listOfCount: [7, 15, 21]),
          getAvailabilityCount(
            givenPeriod: 'Month',
            listOfCount: List<int>.generate(11, (i) => i + 1),
          ),
          getAvailabilityCount(
            givenPeriod: 'Year',
            listOfCount: [1, 2, 3, 4],
          ),
        ],
      ),
    );
  }
}
