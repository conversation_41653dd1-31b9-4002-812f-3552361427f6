// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';

import '../../../../commonWidgets/text_widget.dart';

class ChildStep4FourthWidget extends StatelessWidget {
  final bool isCommissionAgreed;
  final Function setCommission;

  ChildStep4FourthWidget({
    required this.isCommissionAgreed,
    required this.setCommission,
  });

  double dW = 0.0;
  double tS = 0.0;

  Widget buildRadioSelection({
    required String title,
    required bool isSelected,
    required BuildContext context,
    required Function onTap,
  }) {
    return GestureDetector(
      onTap: () => onTap(),
      child: CustomContainer(
        hPadding: .035,
        boxShadow: [],
        borderColor: isSelected ? getThemeColor() : Colors.grey.shade300,
        bgColor: isSelected ? getThemeColor() : Colors.transparent,
        vPadding: .05,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FittedBox(
              fit: BoxFit.scaleDown,
              child: TextWidget(
                title: title,
                fontSize: 16,
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              height: dW * 0.05,
              padding: EdgeInsets.all(2.5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.white : Color(0xffB6B7BA),
                ),
              ),
              child: CircleAvatar(
                radius: 8,
                backgroundColor: isSelected ? Colors.white : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: dW * 0.05),
          TextWidget(
            title: 'Are you agreeing to the commission and onboarding field?',
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: dW * 0.1),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                title: 'Commission & Onboarding',
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              SizedBox(
                height: dW * 0.04,
              ),
              TextWidget(
                  fontSize: 12,
                  title:
                      'BookYourSlot charges a commission of 5%-10% in developing markets community.\n\nYou agree to register for the first time for BookYourSlot membership, BoolYourSlot verifies credentials from RBI. As the registration process also takes the involvement of RBI.'),
              // buildRadioSelection(
              //   title: 'Accept',
              //   isSelected: isCommissionAgreed,
              //   context: context,
              //   onTap: () => setCommission(true),
              // ),
              // SizedBox(height: dW * 0.05),
              // buildRadioSelection(
              //   title: 'Decline',
              //   isSelected: !isCommissionAgreed,
              //   context: context,
              //   onTap: () => setCommission(false),
              // )
            ],
          ),
        ],
      ),
    );
  }
}
