import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/homeModule/models/bookingModel.dart';
import 'package:bys_business/venueModule/models/venue_model.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/new_venue_review_indoor_screen.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step2/child_step2_fifth_widget.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step3/child_step3_fourth_widget.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step3/child_step3_second_widget.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step3/child_step3_third_widget.dart';
import 'package:bys_business/venueModule/newVenueFlow/widgets/step4/child_step4_first_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:video_compress/video_compress.dart';

import '../../../commonWidgets/custom_dialog.dart';
import '../../../commonWidgets/new_appbar.dart';
import '../../../commonWidgets/new_check_box_widget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../homeModule/providers/homeProvider.dart';
import '../../../navigators.dart';
import '../../../new_colors.dart';
import '../widgets/info_quatityLabel_bottomSheet.dart';
import '../widgets/step1/child_step1_first_widget.dart';
import '../widgets/step1/child_step1_fourth_widget.dart';
import '../widgets/step1/child_step1_second_widget.dart';
import '../widgets/step1/child_step1_third_widget.dart';
import '../widgets/step2/child_step2_first_widget.dart';
import '../widgets/step2/child_step2_fourth_indoor_widget.dart';
import '../widgets/step2/child_step2_fourth_widget.dart';
import '../widgets/step2/child_step2_second_widget.dart';
import '../widgets/step2/child_step2_sixth_widget.dart';
import '../widgets/step2/child_step2_third _indoor_widget.dart';
import '../widgets/step2/child_step2_third_widget.dart';
import '../widgets/step3/child_step3_first_widget.dart';
import '../widgets/step3/child_step3_fifth_widget.dart';
import '../widgets/step3/child_step3_sixth_widget.dart';
import '../widgets/step4/child_step4_thirdd_widget.dart';
import '../widgets/step4/child_step4_fourth_widget.dart';
import '../widgets/step4/child_step4_second_widget.dart';
import 'new_venue_review_screen.dart';

class AddVenueDetailsScreen extends StatefulWidget {
  final Venue? venue;
  final bool duplicateVenue;
  const AddVenueDetailsScreen({
    Key? key,
    this.duplicateVenue = false,
    this.venue,
  }) : super(key: key);

  @override
  AddVenueDetailsScreenState createState() => AddVenueDetailsScreenState();
}

class AddVenueDetailsScreenState extends State<AddVenueDetailsScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  var fileMedia1;
  var fileMedia2;
  var fileMedia3;
  var fileMedia4;
  var fileMedia5;

  String imagePath1 = '';
  String imagePath2 = '';
  String imagePath3 = '';
  String imagePath4 = '';

  bool isLoading = false;
  late UserModal user;

  bool isCompressingVideo = false;
  bool isAutoFillPrice = false;

  void toggleAutoFillPrice() {
    setState(() {
      isAutoFillPrice = !isAutoFillPrice;
    });
  }

  ScrollController scrollController = ScrollController();

  List<String> selectedImagePaths = ['', ''];

  String videoPath = '';
  String videoThumbnail = '';

  Address? venueAddress;
  TextEditingController landmarkController = TextEditingController();

  bool isBusinessGSTRegistered = false;
  String selectedGstType = '';
  TextEditingController gstNoController = TextEditingController();

  bool isCommissionAgreed = true;

  setCommission(bool value) {
    isCommissionAgreed = value;
    setState(() {});
  }

  setBusinessGSTRegister(bool value) {
    isBusinessGSTRegistered = value;
    setState(() {});
  }

  setGstType(String gstType) {
    selectedGstType = gstType;
    setState(() {});
  }

  setVenueAddress(Address address) {
    venueAddress = address;
    setState(() {});
  }

  pickAndSetImage(ImageSource source, int index) async {
    pop(true);
    XFile? pickedFile = await pickCropImage(imageSource: source);
    if (pickedFile != null) {
      var bytes = await File(pickedFile.path).readAsBytes();
      final kb = (bytes.length) / 1024;
      final size = kb / 1024;

      if (size > 5) {
        return showSnackbar('Image size should be less than 5MB');
      }
      final jpegData =
          await convertToJpeg(File(pickedFile.path), pickedFile.name);

      selectedImagePaths[index] = jpegData.path;

      setState(() {});
    }
  }

  pickAndSetImageForEquipment(ImageSource source, int index) async {
    pop(true);
    XFile? pickedFile = await pickCropImage(imageSource: source);
    if (pickedFile != null) {
      var bytes = await File(pickedFile.path).readAsBytes();
      final kb = (bytes.length) / 1024;
      final size = kb / 1024;

      if (size > 5) {
        return showSnackbar('Image size should be less than 5MB');
      }
      final jpegData =
          await convertToJpeg(File(pickedFile.path), pickedFile.name);

      equipments[index]['image'] = jpegData.path;

      setState(() {});
    }
  }

  removeSelectedOrVideoForEquipment(int index, {bool isImage = true}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      equipments[index]['image'] = '';
    }
    setState(() {});
  }

  imagePickerBottomSheetForEquipment(int index) {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              pop(false);
              return true;
            },
            child: SizedBox(
              height: dH * .2,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: dW * .05),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: dW * .02),
                    const Text(
                      'Select Photo from',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: dW * .03),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (equipments[index]['image'] != '') ...[
                          GestureDetector(
                            onTap: () {
                              equipments[index]['image'] = '';
                              setState(() {});
                              pop(true);
                            },
                            child: Column(
                              children: [
                                CircleAvatar(
                                  radius: dW * .08,
                                  backgroundColor: Colors.grey.shade300,
                                  child: Icon(
                                    Icons.delete,
                                    color: getRedColor1(context),
                                  ),
                                ),
                                SizedBox(height: dW * .02),
                                const Text('Remove '),
                                const Text('Photo')
                              ],
                            ),
                          ),
                          SizedBox(width: dW * .05),
                        ],
                        GestureDetector(
                          onTap: () => pickAndSetImageForEquipment(
                              ImageSource.gallery, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.image,
                                  color: Colors.purple,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Gallery')
                            ],
                          ),
                        ),
                        SizedBox(width: dW * .05),
                        GestureDetector(
                          onTap: () => pickAndSetImageForEquipment(
                              ImageSource.camera, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.camera_alt_rounded,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Camera')
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        }).then((value) {
      if (value != null && !value && index >= 2) {
        equipments.removeAt(index);
        setState(() {});
      }
    });
  }

  imagePickerBottomSheet(int index) {
    if (index > selectedImagePaths.length - 1) {
      selectedImagePaths.add('');
      setState(() {});
    }
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              pop(false);
              return true;
            },
            child: SizedBox(
              height: dH * .2,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: dW * .05),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: dW * .02),
                    const Text(
                      'Select Photo from',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: dW * .03),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (selectedImagePaths[index] != '') ...[
                          GestureDetector(
                            onTap: () {
                              selectedImagePaths[index] = '';
                              setState(() {});
                              pop(true);
                            },
                            child: Column(
                              children: [
                                CircleAvatar(
                                  radius: dW * .08,
                                  backgroundColor: Colors.grey.shade300,
                                  child: Icon(
                                    Icons.delete,
                                    color: getRedColor1(context),
                                  ),
                                ),
                                SizedBox(height: dW * .02),
                                const Text('Remove '),
                                const Text('Photo')
                              ],
                            ),
                          ),
                          SizedBox(width: dW * .05),
                        ],
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.gallery, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.image,
                                  color: Colors.purple,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Gallery')
                            ],
                          ),
                        ),
                        SizedBox(width: dW * .05),
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.camera, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.camera_alt_rounded,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Camera')
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        }).then((value) {
      if (value != null && !value && index >= 2) {
        selectedImagePaths.removeAt(index);
        setState(() {});
      }
    });
  }

  bool isImageListNotEmpty(String sportName, String imageKey, int index) {
    for (var entry in setCourtsAndTableTypes) {
      final String? entrySportName = entry['sport'] as String?;
      if (entrySportName != null && entrySportName == sportName) {
        if (entry.containsKey(imageKey)) {
          final imageList = entry[imageKey][index] as String;
          if (imageList == '') {
            return false;
          }
        }
      }
    }
    return true;
  }

  void imagePickerIndoorWithKey(int index, String sportName, String imageKey) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            Navigator.pop(context, false);
            return true;
          },
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.2,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: dW * .05),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: dW * .02),
                  const Text(
                    'Select Photo from',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: dW * .03),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (isImageListNotEmpty(sportName, imageKey, index)) ...[
                        GestureDetector(
                          onTap: () {
                            removeSelectedOrVideoIndoor(index,
                                sportName: sportName, imageValue: imageKey);
                            setState(() {});
                            Navigator.pop(context, true);
                          },
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: Icon(
                                  Icons.delete,
                                  color: getRedColor1(context),
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Remove '),
                              const Text('Photo')
                            ],
                          ),
                        ),
                        SizedBox(width: dW * .05),
                      ],
                      GestureDetector(
                        onTap: () => pickAndSetImageIndoor(
                            ImageSource.gallery, index, sportName, imageKey),
                        child: Column(
                          children: [
                            CircleAvatar(
                              radius: dW * .08,
                              backgroundColor: Colors.grey.shade300,
                              child: const Icon(
                                Icons.image,
                                color: Colors.purple,
                              ),
                            ),
                            SizedBox(height: dW * .02),
                            const Text('Gallery')
                          ],
                        ),
                      ),
                      SizedBox(width: dW * .05),
                      GestureDetector(
                        onTap: () => pickAndSetImageIndoor(
                            ImageSource.camera, index, sportName, imageKey),
                        child: Column(
                          children: [
                            CircleAvatar(
                              radius: dW * .08,
                              backgroundColor: Colors.grey.shade300,
                              child: const Icon(
                                Icons.camera_alt_rounded,
                                color: Colors.blue,
                              ),
                            ),
                            SizedBox(height: dW * .02),
                            const Text('Camera')
                          ],
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) {
      if (value != null && !value && index >= 2) {
        selectedImagePaths.removeAt(index);
        String selectedTitle = ''; // Initialize with a default value
        for (var entry in setCourtsAndTableTypes) {
          final titleList = entry['title'] as List<String>;
          if (titleList.length > index) {
            selectedTitle = titleList[index];
            break;
          }
        }
        final imageKey = '$selectedTitle image';
        // Remove the image path from setCourtsAndTableTypes
        for (var entry in setCourtsAndTableTypes) {
          if (entry.containsKey(imageKey)) {
            final imageList = entry[imageKey] as List<String>;
            if (imageList.length > index) {
              imageList.removeAt(index);
            }
            break;
          }
        }
        setState(() {});
      }
    });
  }

  void pickAndSetImageIndoor(
      ImageSource source, int index, String sportName, String imageKey) async {
    pop(true);
    XFile? pickedFile = await pickCropImage(imageSource: source);
    if (pickedFile != null) {
      var bytes = await File(pickedFile.path).readAsBytes();
      final kb = (bytes.length) / 1024;
      final size = kb / 1024;

      if (size > 5) {
        return showSnackbar('Image size should be less than 5MB');
      }
      final jpegData =
          await convertToJpeg(File(pickedFile.path), pickedFile.name);
      if (selectedSportCategory['categoryName'] == 'Indoor') {
        // Update the image path in the corresponding image list based on sportName and imageKey
        for (var entry in setCourtsAndTableTypes) {
          if (entry['sport'] == sportName && entry.containsKey(imageKey)) {
            final imageList = entry[imageKey] as List<String>;
            if (imageList.length > index) {
              imageList[index] = jpegData.path;
            }
            break;
          }
        }
      }

      setState(() {});
    }
  }

  removeSelectedOrVideo(int index, {bool isImage = true}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      if (index < 2) {
        selectedImagePaths[index] = '';
      } else {
        selectedImagePaths.removeAt(index);
      }
    }
    setState(() {});
  }

  void removeSelectedOrVideoIndoor(int index,
      {bool isImage = true, String? sportName, String? imageValue}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      // Remove the image path from setCourtsAndTableTypes if applicable
      if (selectedSportCategory['categoryName'] == 'Indoor' &&
          sportName != null &&
          imageValue != null) {
        // Find the entry in setCourtsAndTableTypes corresponding to the sportName
        final entryToRemove = setCourtsAndTableTypes.firstWhere(
          (entry) => entry['sport'] == sportName,
          orElse: () => {},
        );

        if (entryToRemove != null) {
          // Construct the image key based on the imageValue
          final imageKey = '$imageValue';

          // Check if the entry contains the imageKey
          if (entryToRemove.containsKey(imageKey)) {
            final imageList = entryToRemove[imageKey] as List<String>;

            // Check if the index is valid
            if (index >= 0 && index < imageList.length) {
              print(
                  'Before removal - Image path at index $index: ${imageList[index]}');
              imageList[index] = '';
              print(
                  'After removal - Image path at index $index: ${imageList[index]}');

              // Log sport name and image value
              print('Sport Name: $sportName, Image Value: $imageValue');
            }
          }
        }
      }
    }
    setState(() {});
  }

  videoPickerBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          height: dH * .2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: dW * .05),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: dW * .02),
                const Text(
                  'Profile Video',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: dW * .02),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    if (videoPath != '') ...[
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            videoPath = '';
                            videoThumbnail = '';
                          });
                          pop();
                        },
                        child: Column(
                          children: [
                            CircleAvatar(
                              radius: dW * .08,
                              backgroundColor: Colors.grey.shade300,
                              child: Icon(
                                Icons.delete,
                                color: getRedColor1(context),
                              ),
                            ),
                            SizedBox(height: dW * .02),
                            const Text('Remove '),
                            const Text('Video')
                          ],
                        ),
                      ),
                      SizedBox(width: dW * .05),
                    ],
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.gallery),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: dW * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.video_library,
                              color: Colors.purple,
                            ),
                          ),
                          SizedBox(height: dW * .02),
                          const Text('Gallery')
                        ],
                      ),
                    ),
                    SizedBox(width: dW * .05),
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.camera),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: dW * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.videocam,
                              color: Colors.blue,
                            ),
                          ),
                          SizedBox(height: dW * .02),
                          const Text('Camera')
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  pickVideo(ImageSource source) async {
    try {
      ImagePicker picker = ImagePicker();
      XFile? pickedFile = await picker.pickVideo(
        source: source,
        maxDuration: const Duration(seconds: 30),
      );

      if (pickedFile != null) {
        setVideoData(pickedFile.path);
      }
      pop();
    } catch (e) {
      print(e);
    }
  }

  setVideoData(String path) async {
    try {
      setState(() => isCompressingVideo = true);
      final videoInfo = FlutterVideoInfo();

      File selectedFile = File(path);

      String videoFilePath = selectedFile.path;
      var info = await videoInfo.getVideoInfo(videoFilePath);

      MediaInfo? compressedVideo = await VideoCompress.compressVideo(path);

      if (info != null) {
        double size = (info.filesize! / (1024 * 1024));

        if (size > 10) {
          showSnackbar('File Size should be less than 10MB');
          return;
        }

        if (info.duration == null ||
            info.width == null ||
            info.height == null) {
          return showSnackbar('Cannot upload this video');
        }
        var seconds = info.duration! / 1000;
        var maxDuration = 30;
        if (seconds > maxDuration) {
          return showSnackbar('Video must be less than or equal to 30 seconds');
        } else {
          var mediaPath = Uri.decodeComponent(selectedFile.path);
          File thumbnailData = await VideoCompress.getFileThumbnail(mediaPath,
              quality: 100, // default(100)
              position: -1 // default(-1)
              );
          videoThumbnail = thumbnailData.path;
          if (videoThumbnail == '') {
            showSnackbar('Cannot upload this video');
          }

          videoPath = compressedVideo?.path ?? mediaPath;

          if (compressedVideo != null) {
            videoPath = compressedVideo.path ?? mediaPath;
          } else {
            videoPath = mediaPath;
          }
          setState(() {});
        }
      } else {
        return showSnackbar('Cannot upload this video');
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() => isCompressingVideo = false);
    }
  }

  //Step3
  TextEditingController aboutTurfController = TextEditingController();
  TextEditingController advanceAmountController = TextEditingController();
  TextEditingController advanceAmountForOneOnController =
      TextEditingController();

  //Cancellation Charges
  List cancellationCharges = [];
  int durationCount = 1;
  String period = 'Year';
  TextEditingController cancellationController = TextEditingController();

//Step1
  TimeOfDay startTime = TimeOfDay(hour: 05, minute: 00);
  TimeOfDay endTime = TimeOfDay(hour: 23, minute: 00);

  TextEditingController venueNameController = TextEditingController();
  TextEditingController venuePhoneController = TextEditingController();
  TextEditingController termsConditionController = TextEditingController();

  // String selectedSports = '';
  List selectedSports = [];
  List selectedWeekDays = [];
  List selectedWeekEnds = [];
  List selectedTurfSize = [];
  List selectedTurfSizeWithSlots = [];
  List selectedSlots = [];
  List turfSize = [];
  List sportQuantity = [];
  List<Map<String, dynamic>> equipments = [];
  List selectedSportsForEquip = [];
  List selectedFacilities = [];
  List clickedImages = [];
  // List selectedCourtsAndTables = [];
  int selectedSlotDuration = 30;
  bool step1Completed = false;
  bool isNet = false;
  String option = 'Non Netting';
  bool usePreviousValueForPrice = false;

  int parentSteps = 1;
  int childSteps1 = 0;
  int childSteps2 = 0;
  int childSteps3 = 0;
  int childSteps4 = 0;
  int childSteps5 = 0;

  List<Map<String, dynamic>> listOfSportOfferings = [
    {
      'title': 'Badminton',
      'type': [
        {
          'Singles': false,
        },
        {
          'Doubles': false,
        }
      ],
    },
    // {
    //   'title': 'Squash',
    //   'type': [
    //     {
    //       'Singles': false,
    //     },
    //     {
    //       'Doubles': false,
    //     }
    //   ],
    // },
    {
      'title': 'Table Tennis',
      'type': [
        {
          'Singles': false,
        },
        {
          'Doubles': false,
        }
      ],
    },
  ];

  void setSportOfferingTypes(String sport, String typeName, bool isChecked) {
    setState(() {
      // Find the sport offering with the specified title
      var sportOffering = listOfSportOfferings.firstWhere(
        (offering) => offering['title'] == sport,
        orElse: () => {'type': []},
      );

      // Update the checked state of the type
      sportOffering['type'].forEach((type) {
        if (type.keys.first == typeName) {
          type[typeName] = isChecked;
          usePreviousValueForPrice = true;
        }
      });
      print(listOfSportOfferings);
    });
  }

  List listOfTurfOption = [
    {
      'title': 'Netting',
      'isSelected': false,
      'id': '1',
      'isNet': true,
      'label': "Turf with netting system",
    },
    {
      'title': 'Non Netting',
      'isSelected': true,
      'id': '2',
      'isNet': false,
      'label': "Turf without Net",
    },
    {
      'title': 'One On',
      'isSelected': false,
      'id': '3',
      'isNet': true,
      'label': "Turf with Cricket Net",
    },
  ];
  List listOfFacilities = [
    {
      'title': 'Washroom',
      'isSelected': false,
      'id': 'F1',
    },
    {
      'title': 'Cafe & Food court',
      'isSelected': false,
      'id': 'F2',
    },
    {
      'title': 'Power Backup',
      'isSelected': false,
      'id': 'F3',
    },
    {
      'title': 'Changing Room',
      'isSelected': false,
      'id': 'F4',
    },
    {
      'title': 'First Aid',
      'isSelected': false,
      'id': 'F5',
    },
    {
      'title': 'Parking',
      'isSelected': false,
      'id': 'F6',
    },
    {
      'title': 'Covered',
      'isSelected': false,
      'id': 'F7',
    },
  ];

  List listOfSports = [];
  List listofSportCategory = [];
  Map selectedSportCategory = {};

  addOrRemoveCancellation({
    required Map data,
    required int index,
    required bool add,
  }) {
    if (add) {
      cancellationCharges.add(data);
    } else {
      cancellationCharges.removeAt(index);
    }
    setState(() {});
  }

  changeDuration(String duration, value) {
    if (duration == 'Period') {
      period = value;
      if (value == 'Days') {
        durationCount = 7;
      } else {
        durationCount = 1;
      }
    } else if (duration == 'Count' || duration == 'Duration') {
      durationCount = value;
    }
    setState(() {});
  }

  editCancellation({required Map data, required int index}) {
    cancellationCharges[index]['start'] = data['start'];
    cancellationCharges[index]['end'] = data['end'];
    cancellationCharges[index]['percentage'] = data['percentage'];
    setState(() {});
  }

  selectSportCategory(String id) {
    listofSportCategory.forEach((cat) {
      if (cat['id'] == id) {
        selectedSportCategory = {
          'categoryId': cat['id'],
          'categoryName': cat['title'],
        };
        cat['isSelected'] = true;
        if (cat['title'] == 'Indoor' || cat['title'] == 'Esport') {
          option = 'Non Netting';
          listOfTurfOption.forEach((data) {
            if (data['option'] == option) {
              data['isSelected'] = true;
            } else {
              data['isSelected'] = false;
            }
          });
        }
      } else {
        cat['isSelected'] = false;
      }
    });
    setState(() {
      selectedSports = [];
      // selectedSports = '';

      listOfSports.forEach((sport) {
        sport['isSelected'] = false;
      });
    });
  }

  List listOfSlots = [
    {
      'title': 'Morning',
      'isSelected': false,
      'id': 'Slot1',
      'startTime': TimeOfDay(hour: 5, minute: 00),
      'endTime': TimeOfDay(hour: 12, minute: 0),
      'priceAndQuantity': [],
    },
    {
      'title': 'Afternoon',
      'isSelected': false,
      'id': 'Slot2',
      'startTime': TimeOfDay(hour: 12, minute: 00),
      'endTime': TimeOfDay(hour: 16, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Evening',
      'isSelected': false,
      'id': 'Slot3',
      'startTime': TimeOfDay(hour: 16, minute: 00),
      'endTime': TimeOfDay(hour: 20, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Night',
      'isSelected': false,
      'id': 'Slot4',
      'startTime': TimeOfDay(hour: 20, minute: 00),
      'endTime': TimeOfDay(hour: 24, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Late Night',
      'isSelected': false,
      'id': 'Slot5',
      'startTime': TimeOfDay(hour: 00, minute: 00),
      'endTime': TimeOfDay(hour: 05, minute: 0),
      'priceAndQuantity': [],
    },
  ];

  // List listOfCourtsAndTables = [
  //   {
  //     'sport': 'Badminton',
  //     'title': 'Synthetic Court',
  //     'isSelected': false,
  //     'id': 'C1',
  //   },
  //   {
  //     'sport': 'Badminton',
  //     'title': 'Mat Surface',
  //     'isSelected': false,
  //     'id': 'C2',
  //   },
  //   {
  //     'sport': 'Badminton',
  //     'title': 'Wooden Court',
  //     'isSelected': false,
  //     'id': 'C3',
  //   },
  //   {
  //     'sport': 'Snooker',
  //     'title': 'English Table',
  //     'isSelected': false,
  //     'id': 'T1',
  //   },
  //   {
  //     'sport': 'Snooker',
  //     'title': 'French Table',
  //     'isSelected': false,
  //     'id': 'T2',
  //   },
  //   {
  //     'sport': 'Snooker',
  //     'title': 'Default Table',
  //     'isSelected': false,
  //     'id': 'T3',
  //   },
  // ];

  List listOfCourtsAndTables = [
    {
      'sport': 'Badminton',
      'title': 'Default Court',
      'isSelected': false,
      'id': 'C1',
    },
    {
      'sport': 'Snooker',
      'title': 'Snooker Table',
      'isSelected': false,
      'id': 'T1',
    },
    {
      'sport': 'Snooker',
      'title': 'Pool Table',
      'isSelected': false,
      'id': 'T2',
    },
  ];

  List listOfDays = [
    {
      'title': 'Mon',
      'isSelected': false,
      'id': 'D1',
    },
    {
      'title': 'Tue',
      'isSelected': false,
      'id': 'D2',
    },
    {
      'title': 'Wed',
      'isSelected': false,
      'id': 'D3',
    },
    {
      'title': 'Thu',
      'isSelected': false,
      'id': 'D4',
    },
    {
      'title': 'Fri',
      'isSelected': false,
      'id': 'D5',
    },
    {
      'title': 'Sat',
      'isSelected': false,
      'id': 'D6',
    },
    {
      'title': 'Sun',
      'isSelected': false,
      'id': 'D7',
    },
  ];
  List<Map<String, dynamic>> setCourtsAndTableTypes = [];

  void addCourtsAndTableType(String sport, String title) {
    setState(() {
      var existingSportIndex = setCourtsAndTableTypes.indexWhere(
        (element) => element['sport'] == sport,
      );

      if (existingSportIndex != -1) {
        // Sport already exists in the list, update the titles
        var titles = setCourtsAndTableTypes[existingSportIndex]['title'];
        if (titles.contains(title)) {
          titles.remove(title);

          // Set isSelected to false in listOfCourtsAndTables
          listOfCourtsAndTables.forEach((courtOrTable) {
            if (courtOrTable['sport'] == sport &&
                courtOrTable['title'] == title) {
              courtOrTable['isSelected'] = false;
              usePreviousValueForPrice = true;
            }
          });
          // Remove the entire sport entry if the titles list becomes empty
          if (titles.isEmpty) {
            setCourtsAndTableTypes.removeAt(existingSportIndex);
            usePreviousValueForPrice = true;
          }
        } else {
          titles.add(title);
          setCourtsAndTableTypes[existingSportIndex]['$title image'] = ['', ''];
          usePreviousValueForPrice = true;

          // Set isSelected to true in listOfCourtsAndTables
          listOfCourtsAndTables.forEach((courtOrTable) {
            if (courtOrTable['sport'] == sport &&
                courtOrTable['title'] == title) {
              courtOrTable['isSelected'] = true;
              // usePreviousValueForPrice = true;
            }
            // usePreviousValueForPrice = true;
          });
        }
      } else {
        // Sport doesn't exist in the list, add it with the title
        setCourtsAndTableTypes.add({
          'sport': sport,
          'title': [title],
          '$title image': ['', '']
        });
        usePreviousValueForPrice = true;

        // Set isSelected to true in listOfCourtsAndTables
        listOfCourtsAndTables.forEach((courtOrTable) {
          if (courtOrTable['sport'] == sport &&
              courtOrTable['title'] == title) {
            courtOrTable['isSelected'] = true;
          }
        });
      }

      print(setCourtsAndTableTypes);
    });
  }

  setTurfOption(String title) {
    usePreviousValueForPrice = false;
    selectedTurfSize = [];
    listOfTurfSizeAndPrice.forEach((element) {
      element['isSelected'] = false;
    });
    if (title == 'One On') {
      selectedTurfSize.add('1:1');
      listOfTurfSizeAndPrice.forEach((size) {
        if (size['title'] == '1:1') {
          size['isSelected'] = true;
        }
      });
    }
    listOfTurfOption.forEach((data) {
      if (data['title'] == title) {
        setState(() {
          data['isSelected'] = true;
          option = data['title'];
          isNet = data['isNet'];
        });
      } else {
        setState(() {
          data['isSelected'] = false;
        });
      }
    });
  }

  List listOfTurfSizeAndPrice = [
    {
      'title': '11:11',
      'price': 0,
      'isSelected': false,
      'id': 'S11',
    },
    {
      'title': '9:9',
      'price': 0,
      'isSelected': false,
      'id': 'S4',
    },
    {
      'title': '8:8',
      'price': 0,
      'isSelected': false,
      'id': 'S3',
    },
    {
      'title': '7:7',
      'price': 0,
      'isSelected': false,
      'id': 'S6',
    },
    {
      'title': '6:6',
      'price': 0,
      'isSelected': false,
      'id': 'S2',
    },
    {
      'title': '5:5',
      'price': 0,
      'isSelected': false,
      'id': 'S1',
    },
    {
      'title': '1:1',
      'price': 0,
      'isSelected': false,
      'id': 'S12',
    },
  ];

  List listofSlotTime = [
    {
      'slot': 30,
      'isSelected': false,
      'id': 'ST1',
    },
    {
      'slot': 60,
      'isSelected': false,
      'id': 'ST2',
    },
  ];

  setIsNetValue(bool value) {
    setState(() {
      isNet = value;
      usePreviousValueForPrice = false;
      selectedTurfSize = [];
      listOfTurfSizeAndPrice.forEach((element) {
        element['isSelected'] = false;
      });
      // selectedSports = [];
      // listOfSports.forEach((sport) {
      //   sport['isSelected'] = false;
      // });
    });
  }

  // void setListOfSlots() {
  //   selectedSlots.forEach((element) {
  //     element['priceAndQuantity'] = [];
  //     usePreviousValueForPrice = false;
  //   });

  //   if (selectedSportCategory['categoryName'] != 'Outdoor') {
  //     selectedSlots.forEach((slot) {
  //       slot['priceAndQuantity'].add({
  //         'title': selectedSports,
  //         'quantity': 0,
  //         'price': 0,
  //         'id': '${slot['title']}$selectedSports',
  //         'weekendPrice': 0,
  //         'isSelected': false,
  //         'controller': TextEditingController(),
  //         'weekendController': TextEditingController(),
  //       });
  //     });
  //   } else {
  //     turfSize = [];
  //     selectedSlots.forEach((slot) {
  //       selectedTurfSize.forEach((size) {
  //         slot['priceAndQuantity'].add({
  //           'title': size,
  //           'price': 0,
  //           'weekendPrice': 0,
  //           'isSelected': false,
  //           'id': '${slot['title']}$size',
  //           'controller': TextEditingController(),
  //           'weekendController': TextEditingController(),
  //         });
  //       });
  //     });
  //   }

  //   selectedSlots
  //       .sort((a, b) => a["startTime"].hour.compareTo(b["startTime"].hour));
  //   setState(() {});
  // }

  setListOfSlots() {
    selectedSlots.forEach((element) {
      element['priceAndQuantity'] = [];
      usePreviousValueForPrice = false;
    });

    // if (selectedSportCategory['categoryName'] != 'Outdoor') {
    //   selectedSlots.forEach((slot) {
    //     selectedSports.forEach((sport) {
    //       slot['priceAndQuantity'].add({
    //         'title': sport,
    //         'quantity': 0,
    //         'price': 0,
    //         'id': '${slot['title']}$sport',
    //         'weekendPrice': 0,
    //         'isSelected': false,
    //         'controller': TextEditingController(),
    //         'weekendController': TextEditingController(),
    //       });
    //     });
    //   });
    // }
    if (selectedSportCategory['categoryName'] != 'Outdoor') {
      selectedSlots.forEach((slot) {
        selectedSports.forEach((sport) {
          if (sport != 'Snooker') {
            final sportOffering = listOfSportOfferings.firstWhere(
              (offering) => offering['title'] == sport,
              orElse: () => {'type': []},
            );
            final singles = sportOffering['type'].firstWhere(
              (type) => type.containsKey('Singles') && type['Singles'] == true,
              orElse: () => <String, bool>{},
            );
            final doubles = sportOffering['type'].firstWhere(
              (type) => type.containsKey('Doubles') && type['Doubles'] == true,
              orElse: () => <String, bool>{},
            );

            if (singles != null &&
                singles.containsKey('Singles') &&
                singles['Singles'] == true) {
              slot['priceAndQuantity'].add({
                'sport': sport,
                'title': 'Singles',
                'quantity': 0,
                'price': 0,
                'id': '${slot['title']} $sport Singles',
                'weekendPrice': 0,
                'isSelected': false,
                'controller': TextEditingController(),
                'weekendController': TextEditingController(),
              });
            }

            if (doubles != null &&
                doubles.containsKey('Doubles') &&
                doubles['Doubles'] == true) {
              slot['priceAndQuantity'].add({
                'sport': sport,
                'title': 'Doubles',
                'quantity': 0,
                'price': 0,
                'id': '${slot['title']} $sport Doubles',
                'weekendPrice': 0,
                'isSelected': false,
                'controller': TextEditingController(),
                'weekendController': TextEditingController(),
              });
            }
          } else if (sport == 'Snooker') {
            bool snookerExistsInSetCourts = setCourtsAndTableTypes
                .any((entry) => entry['sport'] == 'Snooker');
            for (var entry in setCourtsAndTableTypes) {
              if (entry['sport'] == 'Snooker') {
                final List<String> snookerTables = entry['title'];
                if (snookerTables != null) {
                  for (var title in snookerTables) {
                    slot['priceAndQuantity'].add({
                      'sport': sport,
                      'title': title,
                      'quantity': 0,
                      'price': 0,
                      'id': '${slot['title']}-$title',
                      'weekendPrice': 0,
                      'isSelected': false,
                      'controller': TextEditingController(),
                      'weekendController': TextEditingController(),
                    });
                  }
                }
              }
              // else if (!snookerExistsInSetCourts) {
              //   slot['priceAndQuantity'].add({
              //     'sport': 'Snooker',
              //     'title': 'Snooker Table',
              //     'quantity': 0,
              //     'price': 0,
              //     'id': '${slot['title']}-Snooker Table',
              //     'weekendPrice': 0,
              //     'isSelected': false,
              //     'controller': TextEditingController(),
              //     'weekendController': TextEditingController(),
              //   });
              // }
            }
            // if (setCourtsAndTableTypes.isEmpty) {
            //   slot['priceAndQuantity'].add({
            //     'sport': 'Snooker',
            //     'title': 'Snooker Table',
            //     'quantity': 0,
            //     'price': 0,
            //     'id': '${slot['title']}-Snooker Table',
            //     'weekendPrice': 0,
            //     'isSelected': false,
            //     'controller': TextEditingController(),
            //     'weekendController': TextEditingController(),
            //   });
            // }
          }
        });
      });
    } else {
      turfSize = [];
      // if (option == 'One On') {
      //   int index = selectedTurfSize.indexWhere((data) => data == '1:1');
      //   if (index == -1) {
      //     selectedTurfSize.add('1:1');
      //   }
      // }
      selectedSlots.forEach((slot) {
        selectedSports.forEach((sport) {
          // Darshan added
          selectedTurfSize.forEach((size) {
            slot['priceAndQuantity'].add(
              {
                'title': size,
                'sport': sport, // Darshan added
                'price': 0,
                'weekendPrice': 0,
                'isSelected': false,
                'id': '${slot['title']}$size',
                'controller': TextEditingController(),
                'weekendController': TextEditingController(),
              },
            );
          });
        });
      });
    }
    selectedSlots
        .sort((a, b) => a["startTime"].hour.compareTo(b["startTime"].hour));
    setState(() {});
  }

  setUsePreviousValueForPrice() {
    setState(() {
      usePreviousValueForPrice = false;
    });
  }

  goToReviewScreen() {
    if (cancellationController.text.trim().isEmpty) {
      return showSnackbar('Enter Cancellation Policy');
    }

    List selectedImages = [];

    selectedImagePaths.forEach((image) {
      if (image != '') selectedImages.add(image);
    });

    push(
      selectedSportCategory['categoryName'] == 'Outdoor'
          ? NewVenueReviewScreen(
              equipments: equipments,
              selectedSports: selectedSports,
              selectedDays: selectedWeekDays,
              selectedTurfSizeAndPrice: selectedSlots,
              startTime: startTime,
              endTime: endTime,
              timeSlotDifference: selectedSlotDuration,
              listOfImages: selectedImages,
              selectedFacilities: selectedFacilities,
              venueDescription: aboutTurfController.text.trim(),
              venueName: venueNameController.text.trim(),
              venueContact: venuePhoneController.text.trim(),
              advanceAmount: 0,
              advanceAmountForOneOn: 0,
              isNet: isNet,
              sportQuantity: sportQuantity,
              selectedSportCategory: selectedSportCategory,
              option: option,
              durationCount: durationCount,
              period: period,
              cancellationCharges: cancellationCharges,
              cancellationPolicy: cancellationController.text.trim(),
              selectedWeekends: selectedWeekEnds,
              videoPath: videoPath,
              videoThumbnail: videoThumbnail,
              venueAddress: venueAddress!,
              landmark: landmarkController.text.trim(),
              venueId: widget.venue == null || widget.duplicateVenue
                  ? ''
                  : widget.venue!.id,
            )
          : NewIndoorVenueReviewScreen(
              selectedSports: selectedSports,
              equipments: equipments,
              termsAndCondition: termsConditionController.text.trim(),
              selectedDays: selectedWeekDays,
              selectedTurfSizeAndPrice: selectedSlots,
              startTime: startTime,
              endTime: endTime,
              timeSlotDifference: selectedSlotDuration,
              listOfImages: selectedImages,
              selectedFacilities: selectedFacilities,
              venueDescription: aboutTurfController.text.trim(),
              venueName: venueNameController.text.trim(),
              venueContact: venuePhoneController.text.trim(),
              advanceAmount: 0,
              advanceAmountForOneOn: 0,
              isNet: isNet,
              sportQuantity: sportQuantity,
              selectedSportCategory: selectedSportCategory,
              option: option,
              durationCount: durationCount,
              period: period,
              cancellationCharges: cancellationCharges,
              cancellationPolicy: cancellationController.text.trim(),
              selectedWeekends: selectedWeekEnds,
              videoPath: videoPath,
              videoThumbnail: videoThumbnail,
              venueAddress: venueAddress!,
              landmark: landmarkController.text.trim(),
              venueId: widget.venue == null || widget.duplicateVenue
                  ? ''
                  : widget.venue!.id,
            ),
    ).then((value) {
      if (value != null && value) pop(true);
    });
  }

  selectAndUnselectDays({required bool isWeekdays, required days}) {
    try {
      if (isWeekdays) {
        selectedWeekDays = [...days];
      } else {
        selectedWeekEnds = [...days];
      }
      usePreviousValueForPrice = false;
      setState(() {});
    } catch (e) {
      print(e);
    }
  }

  selectAndUnselectSports({required sports}) {
    try {
      selectedSportsForEquip = [...sports];

      usePreviousValueForPrice = false;
      setState(() {});
    } catch (e) {
      print(e);
    }
  }

  // selectAndUnSelectType(
  //   String id,
  //   bool isSelected,
  //   List listToIterate,
  //   String listToSet,
  //   bool turfSize,
  // ) {
  //   print('www');
  //   listToIterate.forEach((data) {
  //     if (data['id'] == id) {
  //       if (isSelected) {
  //         setState(() {
  //           data['isSelected'] = false;
  //         });
  //         if (turfSize) {
  //           setState(() {
  //             usePreviousValueForPrice = false;
  //           });
  //         } else {
  //           if (selectedSportCategory['categoryName'] != 'Outdoor') {
  //             setState(() {
  //               usePreviousValueForPrice = false;
  //             });
  //           }
  //         }
  //         return;
  //       } else if (listOfSlots.isNotEmpty) {
  //         setState(() {
  //           data['isSelected'] = true;
  //           listToSet = data['title'];
  //         });
  //         if (turfSize) {
  //           setState(() {
  //             usePreviousValueForPrice = false;
  //           });
  //           listToSet = data;
  //         } else {
  //           if (selectedSportCategory['categoryName'] != 'Outdoor') {
  //             setState(() {
  //               usePreviousValueForPrice = false;
  //             });
  //           }
  //           listToSet = data['title'];
  //         }
  //         return;
  //       }
  //     }
  //   });
  // }

  selectAndUnSelectType(
    String id,
    bool isSelected,
    List listToIterate,
    List listToSet,
    bool turfSize,
  ) {
    print('www');
    listToIterate.forEach((data) {
      if (data['id'] == id) {
        if (isSelected) {
          setState(() {
            data['isSelected'] = false;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.removeWhere((value) => value['title'] == data['title']);
          } else {
            if (selectedSportCategory['categoryName'] != 'Outdoor') {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.removeWhere((value) => value == data['title']);
          }
          return;
        } else {
          setState(() {
            data['isSelected'] = true;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.add(data);
          } else {
            if (selectedSportCategory['categoryName'] != 'Outdoor') {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.add(data['title']);
          }
          return;
        }
      }
    });
  }

  // selectAndUnSelectType(
  //   String id,
  //   bool isSelected,
  //   List listToIterate,
  //   List listToSet,
  //   bool turfSize,
  // ) {
  //   print('www');
  //   listToIterate.forEach((data) {
  //     if (data['id'] == id) {
  //       if (isSelected) {
  //         // Unselecting the item
  //         setState(() {
  //           data['isSelected'] = false;
  //         });
  //         if (turfSize) {
  //           setState(() {
  //             usePreviousValueForPrice = false;
  //           });
  //           listToSet.removeWhere((value) => value['title'] == data['title']);
  //         } else {
  //           if (selectedSportCategory['categoryName'] != 'Outdoor') {
  //             setState(() {
  //               usePreviousValueForPrice = false;
  //             });
  //           }
  //           listToSet.removeWhere((value) => value == data['title']);
  //         }
  //       } else {
  //         // Selecting the item
  //         setState(() {
  //           data['isSelected'] = true;
  //         });
  //         if (turfSize) {
  //           setState(() {
  //             usePreviousValueForPrice = false;
  //           });
  //           listToSet.add(data);
  //         } else {
  //           if (selectedSportCategory['categoryName'] != 'Outdoor') {
  //             setState(() {
  //               usePreviousValueForPrice = false;
  //             });
  //           }
  //           listToSet.add(data['title']);
  //         }
  //       }
  //       return;
  //     }
  //   });
  // }

  setQuantity(List quantity) {
    if (selectedSportCategory['categoryName'] == 'Outdoor') {
      if (!usePreviousValueForPrice) {
        Future.delayed(Duration(microseconds: 0)).then((value) {
          sportQuantity = quantity;
          setState(() {});
        });
      }
    } else if (selectedSportCategory['categoryName'] == 'Indoor') {
      if (!usePreviousValueForPrice) {
        Future.delayed(Duration(microseconds: 0)).then((value) {
          sportQuantity = quantity;
          setState(() {});
        });
      }
    }
  }

  addOrRemovePrice(List listToIterate, String sizeId, bool isSelected) {
    listToIterate.forEach((slot) {
      if (isSelected) {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = true;
            final index = turfSize.indexWhere((turf) => turf == slot['title']);
            if (index == -1) {
              turfSize.add(slot['title']);
            }
          });
        }
      } else {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = false;
            slot['price'] = 0;
            slot['controller'] = TextEditingController();
            slot['weekendController'] = TextEditingController();
            turfSize.removeWhere((turf) => turf == slot['title']);
          });
        }
      }
    });
  }

  addOrRemoveSport(List listToIterate, String sizeId, bool isSelected) {
    listToIterate.forEach((slot) {
      if (isSelected) {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = true;
          });
        }
      } else {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = false;
            slot['price'] = 0;
            slot['controller'] = TextEditingController();
            slot['weekendController'] = TextEditingController();
            turfSize.removeWhere((turf) => turf == slot['title']);
          });
        }
      }
    });
  }

  selectSlotDuration(id) {
    listofSlotTime.forEach((slot) {
      if (slot['id'] == id) {
        setState(() {
          slot['isSelected'] = true;
          selectedSlotDuration = slot['slot'];
        });
      } else {
        setState(() {
          slot['isSelected'] = false;
        });
      }
    });
  }

  addOrRemoveImages(bool add, String imagePath) {
    if (add) {
      clickedImages.add(imagePath);
      print(clickedImages);
    } else {
      clickedImages.removeWhere((image) => image == imagePath);
      print(clickedImages);
    }
  }

  selectTime(bool isStartTime, String slotId, startTime) async {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onChanged: (date) {
        updateTime(date, isStartTime, slotId);
      },
      onConfirm: (date) {
        updateTime(date, isStartTime, slotId);
      },
      currentTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        startTime.hour,
        startTime.minute,
      ),
      showSecondsColumn: false,
    );
  }

  // selectTime2(bool isStartTime, String slotId, startTime) async {
  //   var slotTitle =
  //       selectedSlots.firstWhere((element) => element['id'] == slotId)['title'];
  //   DateTime minTime;
  //   DateTime maxTime;

  //   if (slotTitle == 'Morning') {
  //     minTime = DateTime(
  //         DateTime.now().year, DateTime.now().month, DateTime.now().day, 5, 0);
  //     maxTime = DateTime(DateTime.now().year, DateTime.now().month,
  //         DateTime.now().day, 11, 59);
  //     // Set initial time to the start time of the morning slot
  //     startTime = TimeOfDay(hour: 5, minute: 0);
  //   } else if (slotTitle == 'Afternoon') {
  //     minTime = DateTime(
  //         DateTime.now().year, DateTime.now().month, DateTime.now().day, 12, 0);
  //     maxTime = DateTime(DateTime.now().year, DateTime.now().month,
  //         DateTime.now().day, 15, 59);
  //     // Set initial time to the start time of the afternoon slot
  //     startTime = TimeOfDay(hour: 12, minute: 0);
  //   } else if (slotTitle == 'Evening') {
  //     minTime = DateTime(
  //         DateTime.now().year, DateTime.now().month, DateTime.now().day, 16, 0);
  //     maxTime = DateTime(DateTime.now().year, DateTime.now().month,
  //         DateTime.now().day, 19, 59);
  //     // Set initial time to the start time of the evening slot
  //     startTime = TimeOfDay(hour: 16, minute: 0);
  //   } else if (slotTitle == 'Night') {
  //     minTime = DateTime(
  //         DateTime.now().year, DateTime.now().month, DateTime.now().day, 20, 0);
  //     maxTime = DateTime(DateTime.now().year, DateTime.now().month,
  //         DateTime.now().day, 23, 59);
  //     // Set initial time to the start time of the night slot
  //     startTime = TimeOfDay(hour: 20, minute: 0);
  //   } else {
  //     // Default to allow selection from 12:00 AM to 11:59 PM
  //     minTime = DateTime(
  //         DateTime.now().year, DateTime.now().month, DateTime.now().day, 0, 0);
  //     maxTime = DateTime(DateTime.now().year, DateTime.now().month,
  //         DateTime.now().day, 23, 59);
  //   }

  //   TimeOfDay initialTime =
  //       TimeOfDay(hour: startTime.hour, minute: startTime.minute);

  //   TimeOfDay? selectedTime = await showTimePicker(
  //     context: context,
  //     initialTime: initialTime,
  //     builder: (BuildContext context, Widget? child) {
  //       return MediaQuery(
  //         data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
  //         child: child!,
  //       );
  //     },
  //     helpText: 'Select time',
  //     cancelText: 'Cancel',
  //     confirmText: 'OK',
  //     initialEntryMode: TimePickerEntryMode.dial,
  //     errorInvalidText: 'Invalid time',
  //   );

  //   if (selectedTime != null) {
  //     // Handle selectedTime
  //     // You may want to call updateTime method here with the selectedTime
  //   }
  // }

  updateTime(value, isStartTime, slotId) {
    if (value != null) {
      selectedSlots.forEach((slot) {
        if (slot['id'] == slotId) {
          setState(() {
            if (isStartTime) {
              slot['startTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            } else {
              slot['endTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            }
          });
        }
      });
    }
  }

  incrementAndDecrementStep(bool increment) {
    if (increment) {
      setState(() {
        parentSteps += 1;
        step1Completed = true;
      });
    } else {
      setState(() {
        parentSteps -= 1;
      });
    }
  }

  addDataToFields(Venue venue) {
    if (childSteps1 == 1) {
      venueNameController.text = venue.name;
      venuePhoneController.text = venue.mobileNo;
      aboutTurfController.text = venue.description!;
    }
    if (childSteps1 == 2) {
      venueAddress = venue.address;
      landmarkController.text = venue.address.landmark;
    }
    if (childSteps1 == 3) {
      for (SportType sport in venue.sportsType!) {
        listOfSports.add(sport.sport);
      }

      selectedSportCategory = {
        'categoryId': venue.sportCategory!.id,
        'categoryName': venue.sportCategory!.categoryName,
      };
    }
    if (childSteps1 == 4) {
      venue.sportsType!.forEach((sport) {
        listOfSports.forEach((list) {
          if (sport.id == list['id']) {
            list['isSelected'] = true;
            selectedSports.add(list['title']);
          }
        });
      });
    }
    if (childSteps2 == 1) {
      selectedWeekDays = venue.days!.toList();
      selectedWeekEnds = venue.weekends!.toList();
    }

    if (childSteps2 == 2) {
      selectedSlotDuration = venue.slotTimeDifference == null
          ? 30
          : venue.slotTimeDifference!.toInt();

      List<Slot> slots = getVenueSlot(venue);

      slots.forEach((slot) {
        listOfSlots.forEach((element) {
          if (slot.session == element['title']) {
            element['isSelected'] = true;
            element['startTime'] = TimeOfDay(
              hour: int.parse(slot.startTime.toStringAsFixed(2).split('.')[0]),
              minute:
                  int.parse(slot.startTime.toStringAsFixed(2).split('.')[1]),
            );
            element['endTime'] = TimeOfDay(
              hour: int.parse(slot.endTime.toStringAsFixed(2).split('.')[0]),
              minute: int.parse(slot.endTime.toStringAsFixed(2).split('.')[1]),
            );
            selectedSlots.add(element);
          }
        });
        slot.priceAndQuantity.forEach((size) {
          listOfTurfSizeAndPrice.forEach((e) {
            if (size.title == e['title']) {
              e['isSelected'] = true;
              e['price'] = size.price;
              e['weekendPrice'] = size.weekendPrice;
              TextEditingController advanceAmount = TextEditingController();
              advanceAmount.text = size.advanceAmount.toStringAsFixed(0);
              var index = sportQuantity
                  .indexWhere((value) => value['title'] == size.title);
              if (index == -1) {
                sportQuantity.add({
                  'title': size.title,
                  'quantity': size.quantity,
                  'label': size.label,
                  'advanceAmount': advanceAmount,
                });
              }
            }
          });
        });
      });
    }
    if (childSteps2 == 3) {
      listOfSportOfferings.forEach((element) {
        venue.sportsType!.forEach((element2) {
          venue.slots!.forEach((sport) {
            sport.priceAndQuantity.forEach((element3) {
              if (element['title'] == element3.sport) {
                // Find the type in the listOfSportOfferings and update its value
                var types = element['type'];
                for (var type in types) {
                  if (type.containsKey(element3.title)) {
                    type[element3.title] = true;
                    break; // Exit loop since we found and updated the type
                  }
                }
              }
            });
          });
        });
      });
    }
//     if (childSteps2 == 4) {
//       for(Slot slot in venue.slots!){
//         for
//       }
// setCourtsAndTableTypes = {
//   'sport' : venue.slots.first.
// }
//     }
  }

  myInit() {
    Venue venue = widget.venue!;
    venue.sportsType!.forEach((sport) {
      usePreviousValueForPrice = true;
      listOfSports.forEach((list) {
        if (sport.id == list['id']) {
          list['isSelected'] = true;
          selectedSports.add(list['title']);
          // selectedSports = list['title'];
        }
      });
    });

    selectedSportCategory = {
      'categoryId': venue.sportCategory!.id,
      'categoryName': venue.sportCategory!.categoryName,
    };

    // venue.slots!.forEach((slot) {
    //   slot.priceAndQuantity.forEach((pq) {
    //     selectedSports.add(pq.courtOrTable);
    //   });
    // });

    // listOfCourtsAndTables.forEach((element) {
    //   if (selectedSports.contains(element['title'])) {
    //     element['isSelected'] = true;
    //   } else {
    //     element['isSelected'] = false;
    //   }
    // });

    // listOfCourtsAndTables.forEach((element) {
    //   if (selectedSports.contains(element['sport'])) {
    //     element['isSelected'] = true;
    //   }
    // });

    listofSportCategory.forEach((sport) {
      if (sport['id'] == venue.sportCategory!.id) {
        sport['isSelected'] = true;
      } else {
        sport['isSelected'] = false;
      }
    });
    isNet = venue.isNet!;

    listOfSportOfferings.forEach((element) {
      venue.sportsType!.forEach((element2) {
        venue.slots!.forEach((sport) {
          sport.priceAndQuantity.forEach((element3) {
            if (element['title'] == element3.sport) {
              // Find the type in the listOfSportOfferings and update its value
              var types = element['type'];
              for (var type in types) {
                if (type.containsKey(element3.title)) {
                  type[element3.title] = true;
                  break; // Exit loop since we found and updated the type
                }
              }
            }
          });
        });
      });
    });

    // listOfSportOfferings.forEach((element) {
    //   if (element['title'] == 'Badminton' || element['title'] == 'Snooker') {
    //     String sport = element['title'];
    //     bool addedMatSurface =
    //         false; // Flag to track if "mat surface" has been added

    //     Map<String, dynamic> sportData = {
    //       'sport': sport,
    //       'title': [],
    //     };

    //     venue.sportsType!.forEach((element2) {
    //       venue.slots!.forEach((sport) {
    //         sport.priceAndQuantity.forEach((element3) {
    //           if (element['title'] == element3.sport && !addedMatSurface) {
    //             if (element3.courtOrTable.contains('')) {
    //               sportData['title'].add('mat surface');

    //               addedMatSurface = true;
    //             } else {
    //               sportData['title'].add(element3.courtOrTable);
    //             }
    //           }
    //         });
    //       });
    //     });

    //     setCourtsAndTableTypes.add(sportData);
    //   }
    // });

    // print(setCourtsAndTableTypes);

    selectedWeekDays = venue.days!.toList();
    selectedWeekEnds = venue.weekends!.toList();

    // venue.days!.forEach((day) {
    //   listOfDays.forEach((list) {
    //     if (day == list['title']) {
    //       list['isSelected'] = true;
    //     }
    //   });
    // });

    option = venue.option!;
    listOfTurfOption.forEach((opt) {
      if (opt['title'] == option) {
        isNet = opt['isNet'];
        opt['isSelected'] = true;
      } else {
        opt['isSelected'] = false;
      }
    });

    selectedSlotDuration = venue.slotTimeDifference == null
        ? 30
        : venue.slotTimeDifference!.toInt();

    List<Slot> slots = getVenueSlot(venue);

    slots.forEach((slot) {
      listOfSlots.forEach((element) {
        if (slot.session == element['title']) {
          element['isSelected'] = true;
          element['startTime'] = TimeOfDay(
            hour: int.parse(slot.startTime.toStringAsFixed(2).split('.')[0]),
            minute: int.parse(slot.startTime.toStringAsFixed(2).split('.')[1]),
          );
          element['endTime'] = TimeOfDay(
            hour: int.parse(slot.endTime.toStringAsFixed(2).split('.')[0]),
            minute: int.parse(slot.endTime.toStringAsFixed(2).split('.')[1]),
          );
          selectedSlots.add(element);
        }
      });
      slot.priceAndQuantity.forEach((size) {
        listOfTurfSizeAndPrice.forEach((e) {
          if (size.title == e['title']) {
            e['isSelected'] = true;
            e['price'] = size.price;
            e['weekendPrice'] = size.weekendPrice;
            TextEditingController advanceAmount = TextEditingController();
            advanceAmount.text = size.advanceAmount.toStringAsFixed(0);
            var index = sportQuantity
                .indexWhere((value) => value['title'] == size.title);
            if (index == -1) {
              sportQuantity.add({
                'title': size.title,
                'quantity': size.quantity,
                'label': size.label,
                'advanceAmount': advanceAmount,
              });
            }
          }
        });
      });
    });

    if (venue.sportCategory!.categoryName == 'Outdoor') {
      listOfTurfSizeAndPrice.forEach((e) {
        if (e['isSelected']) {
          selectedTurfSize.add(e['title']);
        }
      });
      turfSize = selectedTurfSize;

      selectedSlots.forEach((slot) {
        selectedTurfSize.forEach((size) {
          slot['priceAndQuantity'].add(
            {
              'title': size,
              'price': 0,
              'weekendPrice': 0,
              'isSelected': true,
              'id': '${slot['title']}$size',
            },
          );
        });
      });
      selectedSlots.forEach((element) {
        element['priceAndQuantity'].forEach((size) {
          listOfTurfSizeAndPrice.forEach((price) {
            TextEditingController controller = TextEditingController();
            TextEditingController weekendController = TextEditingController();
            TextEditingController advanceAmount = TextEditingController();

            if (price['title'] == size['title']) {
              controller.text = price['price'].toDouble().toStringAsFixed(0);
              weekendController.text =
                  price['weekendPrice'].toDouble().toStringAsFixed(0);
              size['price'] =
                  int.parse(price['price'].toDouble().toStringAsFixed(0));
              size['weekendPrice'] = int.parse(
                  price['weekendPrice'].toDouble().toStringAsFixed(0));
              size['controller'] = controller;
              size['weekendController'] = weekendController;
              size['advanceAmount'] = advanceAmount;
            }
          });
        });
      });
    } else {
      selectedSlots.forEach((selected) {
        setCourtsAndTableTypes.forEach((element) {
          slots.forEach((slot) {
            slot.priceAndQuantity.forEach((price) {
              TextEditingController controller = TextEditingController();
              TextEditingController weekendController = TextEditingController();
              TextEditingController advanceAmount = TextEditingController();
              if (selected['title'] == slot.session) {
                var data = {};
                controller.text = price.price.toDouble().toStringAsFixed(0);
                weekendController.text =
                    price.weekendPrice.toDouble().toStringAsFixed(0);
                data['price'] =
                    int.parse(price.price.toDouble().toStringAsFixed(0));
                data['weekendPrice'] =
                    int.parse(price.weekendPrice.toDouble().toStringAsFixed(0));
                advanceAmount.text =
                    price.advanceAmount.toDouble().toStringAsFixed(0);
                data['controller'] = controller;
                data['weekendController'] = weekendController;
                data['isSelected'] = true;
                data['id'] = price.id;
                data['title'] = price.title;
                var index = sportQuantity
                    .indexWhere((value) => value['title'] == price.title);
                if (index == -1) {
                  sportQuantity.add({
                    'title': price.title,
                    // 'type' :
                    'quantity': price.quantity,
                    'label': price.label,
                    'advanceAmount': advanceAmount,
                  });
                }
                selected['priceAndQuantity'].add(data);
              }
            });
          });
        });
      });
    }
    for (var i = 0; i < venue.images!.length; i++) {
      if (i <= 1) {
        selectedImagePaths[i] = venue.images![i];
      } else {
        selectedImagePaths.add(venue.images![i]);
      }
    }
    if (venue.video != null) {
      videoPath = venue.video!.url;
      videoThumbnail = venue.video!.thumbnail;
    }
    venue.facilities!.forEach((value) {
      listOfFacilities.forEach((facility) {
        if (facility['title'] == value) {
          facility['isSelected'] = true;
          selectedFacilities.add(facility['title']);
        }
      });
    });
    aboutTurfController.text = venue.description!;
    cancellationController.text = venue.cancellationPolicy;
    advanceAmountController.text =
        venue.advanceAmount == 0 ? '' : venue.advanceAmount.toString();
    advanceAmountForOneOnController.text = venue.advanceAmountForOneOn == 0
        ? ''
        : venue.advanceAmountForOneOn.toString();

    if (venue.availability != null) {
      period = venue.availability!.duration;
      durationCount = venue.availability!.count;
    }

    if (venue.cancellationCharges.isNotEmpty) {
      venue.cancellationCharges.forEach((data) {
        cancellationCharges.add({
          'start': data.start.toString(),
          'end': data.end.toString(),
          'percentage': data.percentage.toString(),
        });
      });
    }

    if (!widget.duplicateVenue) {
      venueNameController.text = venue.name;
    }
    venuePhoneController.text = venue.mobileNo;
    venueAddress = venue.address;
    landmarkController.text = venue.address.landmark;
  }

  fetchSports() async {
    try {
      if (Provider.of<Auth>(context, listen: false).listOfSports.length == 0) {
        setState(() {
          isLoading = true;
        });
        await Provider.of<Auth>(context, listen: false).fetchSport();
        listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
        listofSportCategory =
            Provider.of<Auth>(context, listen: false).listOfSportCategory;

        if (listofSportCategory.length > 0) {
          listofSportCategory[0]['isSelected'] = true;
          selectedSportCategory = {
            'categoryId': listofSportCategory[0]['id'],
            'categoryName': listofSportCategory[0]['title'],
          };
        }

        setState(() {
          isLoading = false;
        });
      } else {
        listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
        listofSportCategory =
            Provider.of<Auth>(context, listen: false).listOfSportCategory;
        listOfSports.forEach((sport) {
          sport['isSelected'] = false;
        });
        listofSportCategory.forEach((sport) {
          sport['isSelected'] = false;
        });
        if (listofSportCategory.length > 0) {
          listofSportCategory[0]['isSelected'] = true;
          selectedSportCategory = {
            'categoryId': listofSportCategory[0]['id'],
            'categoryName': listofSportCategory[0]['title'],
          };
        }
      }
      if (widget.venue != null) myInit();
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  List<RentalItem> rentalItems = [];

  fetchRentalItems() async {
    final List<String> selectedSportIds = listOfSports
        .where((sport) => sport['isSelected'] == true)
        .map((sport) => sport['id'].toString())
        .toList();

    final response = await Provider.of<HomeProvider>(context, listen: false)
        .fetchRentalItemBySelectedSport(
      accessToken: user.accessToken,
      body: {
        'sport': selectedSportIds,
      },
    );

    if (response['success']) {}
  }

  exitFunction() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title:
                'Are you sure you want to exit. All the entered information will be lost.',
            noText: 'Exit',
            yesText: 'Cancel',
            noFunction: () {
              pop(true);
              pop(true);
            },
            yesFunction: () => pop(),
          )),
    );
  }

  childStep1Validation1() {
    if (venueNameController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter venue name');
    } else if (venuePhoneController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter enter phone number');
    } else if (venuePhoneController.text.length != 10) {
      return showSnackbar('Enter enter valid phone number');
    } else if (aboutTurfController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter venue description');
    } else {
      if (childSteps1 == 4) {
        parentSteps = 2;
        childSteps2 = 0;
      } else {
        childSteps1 += 1;
      }
    }
    // } else if (selectedSports.length == 0) {
    //   showSnackbar('Select Sports');
    //   return;
    // } else {
    //   incrementAndDecrementStep(true);
    // }
  }

  childStep1Validation2() {
    if (venueAddress == null) {
      return showSnackbar('Select venue address');
    } else if (landmarkController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter venue landmark');
    } else {
      if (childSteps1 == 4) {
        parentSteps = 2;
        childSteps2 = 0;
      } else {
        childSteps1 += 1;
      }
    }
    // } else if (selectedSports.length == 0) {
    //   showSnackbar('Select Sports');
    //   return;
    // } else {
    //   incrementAndDecrementStep(true);
    // }
  }

  childStep1Validation4() {
    if (selectedSports.isEmpty) {
      showSnackbar('Select Sports');
      return;
    } else {
      if (childSteps1 == 4) {
        parentSteps = 2;
        childSteps2 = 0;
      } else {
        childSteps1 += 1;
      }
    }
  }

  step2Validation() {
    if (selectedWeekDays.length == 0) {
      showSnackbar('Select Weekdays');
      return;
    } else if (selectedWeekEnds.length == 0) {
      showSnackbar('Select Weekends');
      return;
    } else if (selectedSlots.length == 0) {
      showSnackbar('Select slots');
      return;
    } else if (selectedSlotDuration == 0) {
      showSnackbar('Select slot timing');
      return;
    } else if (selectedTurfSize.length == 0 &&
        selectedSportCategory['categoryName'] == 'Outdoor') {
      showSnackbar('Select turf size');
      return;
    } else {
      incrementAndDecrementStep(true);
      setListOfSlots();
    }
  }

  childStep2Validation1() {
    if (selectedWeekDays.length == 0) {
      showSnackbar('Select Weekdays');
      return;
    } else if (selectedWeekEnds.length == 0) {
      showSnackbar('Select Weekends');
      return;
    } else {
      if (childSteps1 == 6) {
        parentSteps = 3;
        childSteps2 = 0;
      } else {
        childSteps2 += 1;
      }
    }
  }

  childStep2Validation2() {
    if (selectedSlots.length == 0) {
      showSnackbar('Select slots');
      return;
    } else {
      if (childSteps1 == 6) {
        parentSteps = 3;
        childSteps3 = 0;
      } else {
        if (selectedSportCategory['categoryName'] == 'Outdoor' ||
            selectedSportCategory['categoryName'] == 'Indoor' &&
                childSteps2 == 2) {
          childSteps2 = 3;
        }
      }
    }
  }

  childStep2Validation2Indoor() {
    if (selectedSlots.length == 0) {
      showSnackbar('Select slots');
      return;
    } else {
      if (childSteps1 == 6) {
        parentSteps = 3;
        childSteps3 = 0;
      } else {
        if (selectedSportCategory['categoryName'] == 'Outdoor' ||
            selectedSportCategory['categoryName'] == 'Indoor' &&
                childSteps2 == 2 &&
                !selectedSports.contains('Badminton') &&
                !selectedSports.contains('Table Tennis') &&
                !selectedSports.contains('Squash')) {
          childSteps2 = 4;
        } else {
          childSteps2 = 3;

          // childSteps2 = 5;
        }
      }
    }
  }

  childStep2Validation3Indoor() {
    bool isValid = true;

    for (var offering in listOfSportOfferings) {
      bool hasTrueType = false;
      bool isOfferingSelected = selectedSports.contains(offering['title']);

      if (isOfferingSelected) {
        for (var type in offering['type']) {
          if (type.values.first == true) {
            hasTrueType = true;
            break;
          }
        }
        if (!hasTrueType) {
          isValid = false;
          break;
        }
      }
    }

    if (!isValid) {
      showSnackbar('At least one type should be selected for each offering');
    } else {
      if (selectedSportCategory['categoryName'] == 'Indoor' &&
          usePreviousValueForPrice) {
        setListOfSlots();
      }
      if (selectedSports.contains('Badminton') ||
          selectedSports.contains('Snooker')) {
        childSteps2 = 4;
      } else {
        childSteps2 = 5;
      }
    }
  }

  // childStep2Validation3Indoor() {
  //   bool isValid = true;

  //   for (var offering in listOfSportOfferings) {
  //     bool hasTrueType = false;

  //     for (var selectedSport in selectedSports) {
  //       if (selectedSport == offering['title']) {
  //         for (var type in offering['type']) {
  //           if (type.values.first == true) {
  //             hasTrueType = true;
  //             break;
  //           }
  //         }
  //         break;
  //       }
  //     }
  //     if (!hasTrueType) {
  //       isValid = false;
  //       break;
  //     }
  //   }

  //   if (!isValid) {
  //     showSnackbar('At least one type should be selected for each offering');
  //   } else {
  //     childSteps2 = 4;
  //   }
  // }

  childStep2Validation3Outdoor() {
    if (!listOfTurfOption.any((option) => option['isSelected'] == true)) {
      showSnackbar('Please select any 1 option');
    } else {
      childSteps2 = 4;
    }
  }

  childStep2Validation4() {
    if (selectedTurfSize.length == 0 &&
        selectedSportCategory['categoryName'] == 'Outdoor') {
      showSnackbar('Select turf size');
      return;
    } else if (selectedSportCategory['categoryName'] == 'Indoor' &&
        setCourtsAndTableTypes.length == 0) {
      showSnackbar('Select courts and tables');
      return;
    } else {
      if (childSteps1 == 6) {
        parentSteps = 3;
        childSteps3 = 0;
      } else {
        childSteps2 = 5;
        if (selectedSportCategory['categoryName'] == 'Outdoor' &&
            !usePreviousValueForPrice) {
          setListOfSlots();
        }
        if (selectedSportCategory['categoryName'] == 'Indoor' &&
            usePreviousValueForPrice) {
          setListOfSlots();
        }
      }
    }
  }

  childStep2Validation5() {
    for (var slot in selectedSlots) {
      String title = slot['title'];
      TimeOfDay startTime = slot['startTime'];
      TimeOfDay endTime = slot['endTime'];

      if (title == "Morning") {
        // Morning: 5 am to 12 pm
        if ((startTime.hour < 5 || startTime.hour >= 12) &&
            (endTime.hour < 5 || endTime.hour >= 12)) {
          showSnackbar("Invalid time for Morning slot");
          return;
        }
      } else if (title == "Afternoon") {
        // Afternoon: 12 pm to 4 pm
        if ((startTime.hour < 12 || startTime.hour >= 16) &&
            (endTime.hour < 12 || endTime.hour >= 16)) {
          showSnackbar("Invalid time for Afternoon slot");
          return;
        }
      } else if (title == "Evening") {
        // Evening: 4 pm to 8 pm
        if ((startTime.hour < 16 || startTime.hour >= 20) &&
            (endTime.hour < 16 || endTime.hour >= 20)) {
          showSnackbar("Invalid time for Evening slot");
          return;
        }
      } else if (title == "Night") {
        // Night: 8 pm to 12 am
        if ((startTime.hour < 20 || startTime.hour >= 24) &&
            (endTime.hour < 20 || endTime.hour >= 24)) {
          showSnackbar("Invalid time for Night slot");
          return;
        }
      } else if (title == "Late Night") {
        // Night: 12 am to 5 am
        if (startTime.hour >= 5 || endTime.hour > 5) {
          showSnackbar("Invalid time for Late Night slot");
          return;
        }
      }
    }
    childSteps2 = 6;
  }

  step3Validation() {
    incrementAndDecrementStep(true);
  }

  step4Validation() {
    int index = selectedImagePaths.indexWhere((image) => image.trim() != '');

    if (index == -1) {
      return showSnackbar('You have to add atleast 1 image');
    }
    setQuantity([]);
    incrementAndDecrementStep(true);
  }

  childStep3Validation1() {
    int index = selectedImagePaths.indexWhere((image) => image.trim() != '');

    if (index == -1) {
      return showSnackbar('You have to add atleast 1 image');
    } else {
      childSteps3 += 1;
      setQuantity([]);
    }
  }

  childStep3Validation2() {
    for (var i = 0; i < sportQuantity.length; i++) {
      if (sportQuantity[i]['quantity'] == 0) {
        return showSnackbar(
          selectedSportCategory['categoryName'] != 'Outdoor'
              ? 'Please enter the turf Quantity'
              : isNet
                  ? 'Please enter the turf relation'
                  : 'Please enter the turf quantity',
        );
      }
    }

    childSteps3 += 1;
  }

  void childStep3Validation3() {
    bool allControllersNotEmpty = true;
    for (var item in sportQuantity) {
      if (item['advanceAmount'] is List) {
        for (var amount in item['advanceAmount']) {
          if (amount is Map) {
            if (amount.values.first.text.isEmpty) {
              showSnackbar(
                  'Please enter advance amount for ${item['title'] ?? ''}');
              allControllersNotEmpty = false;
              break;
            }
          }
        }
      } else if (item['advanceAmount'] is TextEditingController) {
        if (item['advanceAmount'].text.isEmpty) {
          showSnackbar(
              'Please enter advance amount for ${item['title'] ?? ''}');
          allControllersNotEmpty = false;
          break;
        }
      }
    }
    if (allControllersNotEmpty) {
      usePreviousValueForPrice = true;
      childSteps3 += 1;
    }
  }

  void childStep3Validation4() {
    childSteps3 += 1;
  }

  // childStep3Validation3() {
  //   for (var i = 0; i < sportQuantity.length; i++) {
  //     if (sportQuantity[i]['advanceAmount'].text.isEmpty) {
  //       return showSnackbar(
  //           'Please enter advance amount for ${sportQuantity[i]['title'] ?? ''}');
  //     }
  //   }
  //   usePreviousValueForPrice = true;
  //   childSteps3 += 1;
  // }

  step5Validation() {
    if (selectedFacilities.length == 0) {
      showSnackbar('Select available facilities');
      return;
    } else if (aboutTurfController.text.length == 0) {
      showSnackbar('Write venue description');
      return;
    } else {
      for (var i = 0; i < sportQuantity.length; i++) {
        if (sportQuantity[i]['quantity'] == 0) {
          return showSnackbar(
            selectedSportCategory['categoryName'] != 'Outdoor'
                ? 'Please enter the turf Quantity'
                : isNet
                    ? 'Please enter the turf relation'
                    : 'Please enter the turf quantity',
          );
        } else if (sportQuantity[i]['advanceAmount'].text.isEmpty) {
          return showSnackbar(
              'Please enter advance amount for ${sportQuantity[i]['title'] ?? ''}');
        }
      }
      incrementAndDecrementStep(true);
    }
  }

  onSubmit() {
    scrollController.jumpTo(0);
    hideKeyBoard(context);
    // if (parentSteps == 1) {
    //   step1Validation();
    // } else if (parentSteps == 2) {
    //   step2Validation();
    // } else if (parentSteps == 3) {
    //   step3Validation();
    // } else if (parentSteps == 4) {
    //   step4Validation();
    // } else if (parentSteps == 5) {
    //   step5Validation();
    // } else if (parentSteps == 6) {
    //   goToReviewScreen();
    // }
  }

  getStepsInfoWidget({
    required int step,
    required String title,
    required String subTitle,
    required String image,
  }) {
    return Padding(
      padding: EdgeInsets.only(top: dW * 0.35),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.center,
            child: Image.asset("assets/images/$image.png", scale: 2.7),
          ),
          SizedBox(height: dW * .15),
          TextWidget(title: 'Step $step', fontWeight: FontWeight.w500),
          SizedBox(height: dW * .03),
          TextWidget(title: title, fontSize: 20, fontWeight: FontWeight.w600),
          SizedBox(height: dW * .03),
          TextWidget(title: subTitle, fontSize: 12),
        ],
      ),
    );
  }

  childStep4Validation3() {
    if (isBusinessGSTRegistered) {
      if (selectedGstType == '') {
        return showSnackbar('Please select GST type');
      } else if (gstNoController.text.trim().isEmpty) {
        return showSnackbar('Please enter GST Number');
      } else {
        childSteps4 += 1;
      }
    } else {
      childSteps4 += 1;
    }
  }

  nextFunction() {
    scrollController.jumpTo(0);
    hideKeyBoard(context);
    if (parentSteps == 1) {
      if (childSteps1 == 0) {
        childSteps1 += 1;
      } else {
        if (childSteps1 == 1) {
          childStep1Validation1();
        } else if (childSteps1 == 2) {
          childStep1Validation2();
        } else if (childSteps1 == 3) {
          childSteps1 += 1;
          setState(() {});
        } else if (childSteps1 == 4) {
          childStep1Validation4();
          fetchRentalItems();
        }
      }
    } else if (parentSteps == 2) {
      if (childSteps2 == 0) {
        childSteps2 += 1;
      } else {
        if (childSteps2 == 1) {
          childStep2Validation1();
        } else if (childSteps2 == 2) {
          selectedSportCategory['categoryName'] == 'Outdoor'
              ? childStep2Validation2()
              : childStep2Validation2Indoor();
        } else if (childSteps2 == 3) {
          selectedSportCategory['categoryName'] != 'Outdoor'
              ? childStep2Validation3Indoor()
              : childStep2Validation3Outdoor();
          // childSteps2 = 4;
        } else if (childSteps2 == 4) {
          childStep2Validation4();
        } else if (childSteps2 == 5) {
          childStep2Validation5();
          // childSteps2 = 6;
          // if (!usePreviousValueForPrice) setListOfSlots();
        } else if (childSteps2 == 6) {
          parentSteps = 3;
          childSteps3 = 0;
        }
      }
    } else if (parentSteps == 3) {
      if (childSteps3 == 0) {
        childSteps3 += 1;
      } else {
        if (childSteps3 == 1) {
          childStep3Validation1();
        } else if (childSteps3 == 2) {
          childStep3Validation2();
        } else if (childSteps3 == 3) {
          childStep3Validation3();
        } else if (childSteps3 == 4) {
          childStep3Validation4();
        } else if (childSteps3 == 5) {
          if (selectedFacilities.length == 0) {
            return showSnackbar('Select available facilities');
          } else {
            childSteps3 += 1;
          }
        } else if (childSteps3 == 6) {
          parentSteps = 4;
          childSteps4 = 0;
        }
        // else {
        //   parentSteps = 4;
        //   childSteps4 = 0;
        // }
      }
    } else if (parentSteps == 4) {
      if (childSteps4 == 0) {
        childSteps4 += 1;
      } else {
        if (childSteps4 == 1) {
          childSteps4 += 1;
        } else if (childSteps4 == 2) {
          if (cancellationController.text.trim().isEmpty) {
            return showSnackbar('Please enter cancellation policy');
          }
          childSteps4 += 1;
        }
        //  else if (childSteps4 == 3) {
        //   childStep4Validation3();
        // }
        //  else if (childSteps4 == 4 && isCommissionAgreed) {
        else if (childSteps4 == 3) {
          childSteps4 += 1;
        } else if (childSteps4 == 4 && isCommissionAgreed) {
          goToReviewScreen();
        }
      }
    }
    setState(() {});
  }

  backFunction() {
    if (parentSteps == 1) {
      if (childSteps1 == 0) {
        return pop();
      }
      childSteps1 -= 1;
    } else if (parentSteps == 2) {
      if (childSteps2 <= 0) {
        parentSteps = 1;
      }
      if (selectedSportCategory['categoryName'] == 'Outdoor' ||
          selectedSportCategory['categoryName'] == 'Indoor') {
        if (selectedSportCategory['categoryName'] == 'Indoor' &&
            !selectedSports.contains('Badminton') &&
            !selectedSports.contains('Table Tennis') &&
            !selectedSports.contains('Squash') &&
            childSteps2 == 4) {
          childSteps2 -= 2;
        } else {
          if (selectedSports.contains('Badminton') ||
              selectedSports.contains('Snooker')) {
            childSteps2 -= 1;
          } else {
            //childSteps2 -= 2;
            childSteps2 -= 1;
          }
        }
      } else {
        if (childSteps2 > 4) {
          childSteps2 = 2;
        } else {
          childSteps2 -= 1;
        }
      }
    } else if (parentSteps == 3) {
      if (childSteps3 <= 0) {
        parentSteps = 2;
      }
      childSteps3 -= 1;
    } else if (parentSteps == 4) {
      if (childSteps4 <= 0) {
        parentSteps = 3;
      }
      childSteps4 -= 1;
    }
    setState(() {});
  }

  Widget getStepperWidget({required int currentPosition, required int length}) {
    return SizedBox(
      width: dW * 0.21,
      height: dH * 0.01,
      child: Stack(
        children: [
          Container(
              width: dW * 0.21,
              height: dH * 0.015,
              decoration: BoxDecoration(
                border: Border.all(color: Color(0xffDBDBE3)),
                color: Color(0xffDBDBE3),
                borderRadius: BorderRadius.circular(20),
              )),
          FractionallySizedBox(
              widthFactor: currentPosition < 0 ? 0 : currentPosition / length,
              child: Container(
                width: dW * 0.21,
                height: dH * 0.015,
                decoration: BoxDecoration(
                  color: getThemeColor(),
                  borderRadius: BorderRadius.circular(20),
                ),
              ))
        ],
      ),
    );
  }

  openInfo() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      constraints: BoxConstraints(maxHeight: dH * 0.7),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => InfoQuantityLabelBottomSheet(),
    );
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchSports();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;
    rentalItems = Provider.of<HomeProvider>(context, listen: false).rentalItems;

    return WillPopScope(
      onWillPop: () async {
        backFunction();
        return false;
      },
      child: Scaffold(
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
          height: dH,
          width: dW,
          child: Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
            child: Column(
              children: [
                
                NewAppBar(
                  dW: dW,
                  title: 'Exit Listing',
                  suffixWidget: childSteps3 == 2
                      ? GestureDetector(
                          onTap: openInfo,
                          child: AssetSvgIcon(
                            iconName: 'info_rounded',
                            height: 25,
                            color: getThemeColor(),
                          ),
                        )
                      : null,
                  onTap: exitFunction,
                  leadingIcon: 'cross',
                ),
                SizedBox(height: dW * 0.07),
                if ((parentSteps == 1 && childSteps1 != 0) ||
                    (parentSteps == 2 && childSteps2 != 0) ||
                    (parentSteps == 3 && childSteps3 != 0) ||
                    (parentSteps == 4 && childSteps4 != 0))
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        getStepperWidget(currentPosition: childSteps1, length: 4),
                        getStepperWidget(currentPosition: childSteps2, length: 6),
                        getStepperWidget(currentPosition: childSteps3, length: 6),
                        getStepperWidget(currentPosition: childSteps4, length: 4),
                      ],
                    ),
                  ),
                Expanded(
                  child: isLoading
                      ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                      : SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                          controller: scrollController,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: dW * 0.02),
                              if (parentSteps == 1) ...[
                                if (childSteps1 == 0)
                                  getStepsInfoWidget(
                                    step: 1,
                                    title: 'Tell us about your venue',
                                    subTitle:
                                        'Share your venue\'s name, description, location, type (indoor/outdoor), and the sports played at your extraordinary space',
                                    image: 'venueStep1',
                                  ),
                                if (childSteps1 == 1)
                                  ChildStep1FirstWidget(
                                    venueNameController: venueNameController,
                                    venuePhoneController: venuePhoneController,
                                    aboutTurfController: aboutTurfController,
                                  ),
                                if (childSteps1 == 2)
                                  ChildStep1SecondWidget(
                                    venueAddress: venueAddress,
                                    setVenueAddress: setVenueAddress,
                                    landmarkController: landmarkController,
                                  ),
                                if (childSteps1 == 3)
                                  ChildStep1ThirdWidget(
                                    listOfSports: listOfSports,
                                    listofSportCategory: listofSportCategory,
                                    selectedSportCategory: selectedSportCategory,
                                    selectSportCategory: selectSportCategory,
                                  ),
                                if (childSteps1 == 4)
                                  ChildStep1FourthWidget(
                                    listOfSports: listOfSports,
                                    selectedSports: selectedSports,
                                    listofSportCategory: listofSportCategory,
                                    selectedSportCategory: selectedSportCategory,
                                    selectAndUnSelectType: selectAndUnSelectType,
                                  )
                              ],
                              if (parentSteps == 2) ...[
                                if (childSteps2 == 0)
                                  getStepsInfoWidget(
                                    step: 2,
                                    title: 'Sports and venue configuration',
                                    subTitle:
                                        'Provide essential details about your venue, capacity, timings. Also, add facilities and any additional equipment needed.',
                                    image: 'venueStep2',
                                  ),
                                if (childSteps2 == 1)
                                  ChildStep2FirstWidget(
                                    listOfDays: listOfDays,
                                    selectedWeekDays: selectedWeekDays,
                                    selectedWeekEnds: selectedWeekEnds,
                                    selectAndUnselectDays: selectAndUnselectDays,
                                  ),
                                if (childSteps2 == 2)
                                  ChildStep2SecondWidget(
                                    listOfSlots: listOfSlots,
                                    selectedSlots: selectedSlots,
                                    selectAndUnSelectType: selectAndUnSelectType,
                                  ),
                                if (childSteps2 == 3)
                                  selectedSportCategory['categoryName'] ==
                                          'Outdoor'
                                      ? ChildStep2ThirdWidget(
                                          listOfTurfOption: listOfTurfOption,
                                          setTurfOption: setTurfOption,
                                        )
                                      : ChildStep2ThirdIndoorWidget(
                                          selectedSports: selectedSports,
                                          listOfSports: listOfSports,
                                          listOfSportOfferings:
                                              listOfSportOfferings,
                                          setSportOfferingTypes:
                                              setSportOfferingTypes,
                                        ),
                                if (childSteps2 == 4)
                                  selectedSportCategory['categoryName'] ==
                                          'Outdoor'
                                      ? ChildStep2FourthWidget(
                                          option: option,
                                          isNet: isNet,
                                          selectedTurfSize: selectedTurfSize,
                                          listOfTurfSizeAndPrice:
                                              listOfTurfSizeAndPrice,
                                          setUsePreviousValueForPrice:
                                              setUsePreviousValueForPrice,
                                          selectAndUnSelectType:
                                              selectAndUnSelectType,
                                        )
                                      : ChildStep2FourthIndoorWidget(
                                          listOfCourtsAndTables:
                                              listOfCourtsAndTables,
                                          listOfSports: selectedSports,
                                          setCourtsAndTableTypes:
                                              addCourtsAndTableType,
                                        ),
                                if (childSteps2 == 5)
                                  ChildStep2FifthWidget(
                                    selectedSlots: selectedSlots,
                                    listofSlotTime: listofSlotTime,
                                    selectedSlotDuration: selectedSlotDuration,
                                    selectTime: selectTime,
                                    selectSlotDuration: selectSlotDuration,
                                  ),
                                if (childSteps2 == 6)
                                  ChildStep2SixthWidget(
                                    selectedSports: selectedSports,
                                    selectedSlots: selectedSlots,
                                    selectedSportCategory: selectedSportCategory,
                                    selectedSlotDuration: selectedSlotDuration,
                                    addOrRemovePrice: addOrRemovePrice,
                                    // selectSlotDuration: selectSlotDuration,
                                    isAutoFillPrice: isAutoFillPrice,
                                    toggleAutoFillPrice: toggleAutoFillPrice,
                                  )
                              ],
                              if (parentSteps == 3) ...[
                                if (childSteps3 == 0)
                                  getStepsInfoWidget(
                                    step: 3,
                                    title: 'Showcase your venue attractively',
                                    subTitle:
                                        'Present your venue with images. Specify about facilities, amenities, and inventory available at your venue.',
                                    image: 'venueStep3',
                                  ),
                                if (childSteps3 == 1)
                                  ChildStep3FirstWidget(
                                    selectedSportCategory: selectedSportCategory,
                                    setCourtsAndTableTypes:
                                        setCourtsAndTableTypes,
                                    selectedImagePaths: selectedImagePaths,
                                    videoPath: videoPath,
                                    videoThumbnail: videoThumbnail,
                                    imagePickerBottomSheet:
                                        imagePickerBottomSheet,
                                    imagePickerIndoorWithKey:
                                        imagePickerIndoorWithKey,
                                    removeSelectedOrVideoIndoor:
                                        removeSelectedOrVideoIndoor,
                                    videoPickerBottomSheet:
                                        videoPickerBottomSheet,
                                    removeSelectedOrVideo: removeSelectedOrVideo,
                                    isCompressingVideo: isCompressingVideo,
                                  ),
                                if (childSteps3 == 2)
                                  ChildStep3SecondWidget(
                                    selectedSportCategory: selectedSportCategory,
                                    selectedSports: selectedSports,
                                    listOfSportOfferings: listOfSportOfferings,
                                    sportQuantity: sportQuantity,
                                    setCourtsAndTableTypes:
                                        setCourtsAndTableTypes,
                                    turfSize: turfSize,
                                    setQuantity: setQuantity,
                                    isNet: isNet,
                                    option: option,
                                  ),
                                if (childSteps3 == 3)
                                  ChildStep3ThirdWidget(
                                    selectedSportCategory: selectedSportCategory,
                                    sportQuantity: sportQuantity,
                                    listOfSportOfferings: listOfSportOfferings,
                                    setCourtsAndTableTypes:
                                        setCourtsAndTableTypes,
                                  ),
                                if (childSteps3 == 4)
                                  ChildStep3FourthWidget(
                                    selectedSportCategory: selectedSportCategory,
                                    sportQuantity: sportQuantity,
                                    listOfSportOfferings: listOfSportOfferings,
                                    setCourtsAndTableTypes:
                                        setCourtsAndTableTypes,
                                  ),
                                if (childSteps3 == 5)
                                  ChildStep3FifthWidget(
                                    selectedSportCategory: selectedSportCategory,
                                    listOfFacilities: listOfFacilities,
                                    selectAndUnSelectType: selectAndUnSelectType,
                                    selectedFacilities: selectedFacilities,
                                  ),
                                if (childSteps3 == 6)
                                  // ChildStep3SixthWidget(
                                  //   equipments: equipments,
                                  //   listOfSports: listOfSports,
                                  //   selectedSports: selectedSports,
                                  //   selectAndUnselectSports:
                                  //       selectAndUnselectSports,
                                  //   selectedSportsForEquip:
                                  //       selectedSportsForEquip,
                                  //   imagePickerBottomSheetForEquipment:
                                  //       imagePickerBottomSheetForEquipment,
                                  //   removeSelectedOrVideoForEquipment:
                                  //       removeSelectedOrVideoForEquipment,
                                  // ),
                                  ChildStep3SixthWidget(
                                    rentalItems: rentalItems,
                                    onItemsSelected: (selectedRentalItems) {
                                      equipments = selectedRentalItems;
                                      print(equipments);
                                    },
                                  ),
                              ],
                              if (parentSteps == 4) ...[
                                if (childSteps4 == 0)
                                  getStepsInfoWidget(
                                    step: 4,
                                    title: 'Ownership & policies',
                                    subTitle:
                                        'Share your contact information and banking details as the owner. Set booking availability duration and specify your cancellation policies.',
                                    image: 'venueStep4',
                                  ),
                                if (childSteps4 == 1)
                                  ChildStep4FirstWidget(
                                    period: period,
                                    durationCount: durationCount,
                                    changeDuration: changeDuration,
                                  ),
                                if (childSteps4 == 2)
                                  ChildStep4SecondWidget(
                                    cancellationCharges: cancellationCharges,
                                    addOrRemoveCancellation:
                                        addOrRemoveCancellation,
                                    editCancellation: editCancellation,
                                    cancellationController:
                                        cancellationController,
                                  ),
                                if (childSteps4 == 3)
                                  ChildStep4ThirddWidget(
                                    termsConditionController:
                                        termsConditionController,
                                  ),
                                //   ChildStep4ThirdWidget(
                                //     isBusinessGSTRegistered:
                                //         isBusinessGSTRegistered,
                                //     selectedGstType: selectedGstType,
                                //     gstNoController: gstNoController,
                                //     setBusinessGSTRegister:
                                //         setBusinessGSTRegister,
                                //     setGstType: setGstType,
                                //   ),
                                if (childSteps4 == 4)
                                  ChildStep4FourthWidget(
                                    isCommissionAgreed: isCommissionAgreed,
                                    setCommission: setCommission,
                                  ),
                              ],
                              SizedBox(height: dW * 0.1),
                            ],
                          ),
                        ),
                ),
                BottomAlignedWidget(
                  dW: dW,
                  dH: dH,
                  child: Column(
                    children: [
                      if (childSteps4 == 4)
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.005),
                              child: CheckBoxWidget(
                                activeBorderColor: getThemeColor(),
                                activeColor: getThemeColor(),
                                active: isCommissionAgreed,
                                borderRadius: 3,
                                height: dW * 0.05,
                                iconSize: 14,
                                constraintWidth: null,
                                onTap: () {
                                  isCommissionAgreed = !isCommissionAgreed;
                                  setState(() {});
                                },
                              ),
                            ),
                            SizedBox(width: dW * 0.04),
                            Expanded(
                              child: GestureDetector(
                                  onTap: () {
                                    isCommissionAgreed = !isCommissionAgreed;
            
                                    setState(() {});
                                  },
                                  child: IntrinsicWidth(
                                    child: Container(
                                      width: dW * 0.9,
                                      child: TextWidget(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xff5E5E5E),
                                          title:
                                              'By clicking here, you accept the commission & onboarding policy'),
                                    ),
                                  )),
                            ),
                          ],
                        ),
                      SizedBox(height: dW * 0.04),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          CustomButton(
                            width: dW * 0.35,
                            height: dW * 0.12,
                            fontSize: 16,
                            radius: 7,
                            borderColor: getThemeColor(),
                            textColor: getThemeColor(),
                            buttonColor: Colors.white,
                            buttonText: 'Back',
                            onPressed: backFunction,
                          ),
                          CustomButton(
                            width: dW * 0.35,
                            height: dW * 0.12,
                            fontSize: 16,
                            radius: 7,
                            buttonText: 'Next',
                            onPressed: parentSteps == 4 &&
                                    childSteps4 == 4 &&
                                    !isCommissionAgreed
                                ? null
                                : nextFunction,
                          )
                        ],
                      ),
                      SizedBox(height: dW * 0.04),
                      TextWidget(
                        title:
                            'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      )
                    ],
                  ),
                ),
              ],
            ),
          )),
    );
  }
}
