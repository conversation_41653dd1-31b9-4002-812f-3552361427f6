import 'dart:convert';
import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/new_check_box_widget.dart';
import 'package:bys_business/commonWidgets/open_media_full_screen.dart';
import 'package:bys_business/commonWidgets/policyWidget.dart';
import 'package:bys_business/commonWidgets/raisedButton.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/venue_success_screen.dart';
import 'package:bys_business/venueModule/screens/turf_details_Screen.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../authModule/modals/userModel.dart';
import '../../../authModule/providers/auth.dart';
import '../../../commonWidgets/bottom_aligned_widget.dart';
import '../../../commonWidgets/checkBoxWidget.dart';
import '../../../commonWidgets/custom_button.dart';
import '../../../commonWidgets/custom_container.dart';
import '../../../commonWidgets/divider_widget.dart';
import '../../../commonWidgets/policy_text_widget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import '../../../fontSizes.dart';
import '../../../navigators.dart';
import '../../models/venue_model.dart';
import '../../providers/turfProvider.dart';
import '../../widgets/cancellationWidget.dart';

class NewVenueReviewScreen extends StatefulWidget {
  final List selectedSports;
  // final String selectedSports;

  final List selectedDays;
  final List selectedTurfSizeAndPrice;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final int timeSlotDifference;
  final List listOfImages;
  final List selectedFacilities;
  final String venueDescription;
  final int advanceAmount;
  final int advanceAmountForOneOn;
  final bool isNet;
  final List sportQuantity;
  final Map selectedSportCategory;
  final String venueName;
  final String venueContact;
  final String option;
  final int durationCount;
  final String period;
  final List cancellationCharges;
  final String cancellationPolicy;
  final List selectedWeekends;
  final String videoPath;
  final String videoThumbnail;
  final Address venueAddress;
  final String landmark;
  final String venueId;
  final List equipments;
  const NewVenueReviewScreen({
    Key? key,
    required this.equipments,
    required this.selectedSports,
    required this.selectedDays,
    required this.selectedTurfSizeAndPrice,
    required this.startTime,
    required this.endTime,
    required this.timeSlotDifference,
    required this.listOfImages,
    required this.selectedFacilities,
    required this.venueDescription,
    required this.advanceAmount,
    required this.isNet,
    required this.advanceAmountForOneOn,
    required this.sportQuantity,
    required this.selectedSportCategory,
    required this.venueName,
    required this.venueContact,
    required this.option,
    required this.durationCount,
    required this.period,
    required this.cancellationCharges,
    required this.cancellationPolicy,
    required this.selectedWeekends,
    required this.videoPath,
    required this.videoThumbnail,
    required this.venueAddress,
    required this.landmark,
    this.venueId = '',
  }) : super(key: key);

  @override
  _NewVenueReviewScreenState createState() => _NewVenueReviewScreenState();
}

class _NewVenueReviewScreenState extends State<NewVenueReviewScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;
  bool isLoading = false;
  bool policyAccepted = false;

  late UserModal user;

  saveDetails() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);

      List slot = [];
      widget.selectedTurfSizeAndPrice.forEach((size) {
        List priceQuantity = [];
        size['priceAndQuantity'].forEach((price) {
          priceQuantity.add({
            "title": price['title'],
            "price": double.parse(price['price'].toString()),
            "weekendPrice": double.parse(price['weekendPrice'].toString()),
          });
        });
        widget.sportQuantity.forEach((data) {
          priceQuantity.forEach((size) {
            if (data['title'] == size['title']) {
              size['quantity'] = data['quantity'];
              size['label'] = data['label'];
              size['advanceAmount'] = data['advanceAmount'] == null
                  ? 0
                  : double.parse(data['advanceAmount'].text);
            }
          });
        });
        slot.add(
          {
            "session": size['title'],
            "startTime": double.parse(size['startTime']
                .format(context)
                .toString()
                .replaceAll(':', '.')),
            "endTime": double.parse(size['endTime']
                .format(context)
                .toString()
                .replaceAll(':', '.')),
            "priceAndQuantity": priceQuantity,
          },
        );
      });
      List listSport = Provider.of<Auth>(context, listen: false).listOfSports;

      List<SportType> selectedSports = [];
      widget.selectedSports.forEach((selected) {
        listSport.forEach((sport) {
          if (selected == sport['title']) {
            selectedSports.add(
              SportType(
                id: sport['id'],
                sport: sport['title'],
                image: sport['image'] ?? '',
              ),
            );
          }
        });
      });

      Map<String, String> body = {};
      Map<String, String> files = {};

      List sports = [];
      selectedSports.forEach((sport) => sports.add(sport.id));

      List networkImage = [];
      for (int i = 0; i < widget.listOfImages.length; i++) {
        String image = widget.listOfImages[i];
        if (!image.contains('https')) {
          files['image$i'] = image;
        } else {
          networkImage.add({'url': image});
        }
      }

      if (widget.videoThumbnail != '' &&
          !widget.videoThumbnail.contains('https')) {
        files['videoThumbnail'] = widget.videoThumbnail;
      }

      if (widget.videoPath != '' && !widget.videoPath.contains('https')) {
        files['videoPath'] = widget.videoPath;
      }
      Address venueAddress = widget.venueAddress;

      Map address = {
        "fullAddress": venueAddress.fullAddress,
        'area': venueAddress.area,
        'streetName': venueAddress.streetName,
        "landmark": widget.landmark,
        "city": venueAddress.city,
        "state": venueAddress.state,
        "pincode": venueAddress.pincode,
        'location': {
          'type': "Point",
          'coordinates': [
            venueAddress.coordinates.longitude,
            venueAddress.coordinates.latitude
          ],
        },
      };

      body = {
        "turfId": widget.venueId,
        'equipments': json.encode(widget.equipments),
        "venueName": widget.venueName,
        'venueContact': widget.venueContact,
        'address': json.encode(address),
        "businessId": user.businessId,
        "sportsType": json.encode(sports),
        "days": json.encode(widget.selectedDays),
        "weekends": json.encode(widget.selectedWeekends),
        "priceAndQuantity": json.encode(slot),
        "slotTimeDifference": widget.timeSlotDifference.toString(),
        "facilities": json.encode(widget.selectedFacilities),
        "description": widget.venueDescription,
        "option": widget.option,
        "cancellationPolicy": widget.cancellationPolicy,
        "advanceAmount": widget.advanceAmount.toString(),
        "advanceAmountForOneOn": widget.advanceAmountForOneOn.toString(),
        "isNet": widget.isNet.toString(),
        "networkImage": json.encode(networkImage),
        "sportCategory": widget.selectedSportCategory['categoryId'],
        "cancellationCharges": json.encode(widget.cancellationCharges),
        "availability": json
            .encode({'duration': widget.period, 'count': widget.durationCount}),
        'videoPath': widget.videoPath,
        'videoThumbnail': widget.videoThumbnail,
      };

      final data = await Provider.of<TurfProvider>(context, listen: false)
          .addVenueAndDetailsV2(
        accessToken: user.accessToken,
        body: body,
        files: files,
      );
      if (data) {
        Provider.of<Auth>(context, listen: false).resetSports();
        if (widget.venueId != '') {
          pop(true);
        } else {
          pushAndRemoveUntil(VenueSuccessScreen(
            role: user.role,
          ));
        }
        showSnackbar(
          widget.venueId == ''
              ? 'Venue added successfully'
              : 'Venue updated successfully',
          color: greenPrimary,
        );
      } else {
        showSnackbar('Unable to add venue');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    if (widget.venueId != '') {
      policyAccepted = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  Widget screenBody() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: dW * 0.07),
                TextWidget(
                  title: 'Review your venue listing',
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(height: dW * 0.03),
                TextWidget(
                  title:
                      'Here’s what we’’ll show ot to the users. make sure everything looks good',
                  fontSize: 12,
                  color: Color(0xff21272A),
                ),
                SizedBox(height: dW * 0.08),
                CustomContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                          title: 'Venue Details',
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: getThemeColor()),
                      SizedBox(height: dW * 0.05),
                      TextWidget(title: 'Name'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueName,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: dW * 0.045),
                      TextWidget(title: 'Phone No.'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueContact,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: dW * 0.045),
                      TextWidget(title: 'About Venue'),
                      CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.02),
                        vPadding: 0.03,
                        boxShadow: [],
                        borderColor: getThemeColor(),
                        child: TextWidget(
                          title: widget.venueDescription,
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      // SizedBox(height: dW * 0.045),

                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Address'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: widget.venueAddress.fullAddress
                                    .trim()
                                    .isEmpty
                                ? '${widget.venueAddress.area.isEmpty ? widget.venueAddress.streetName : widget.venueAddress.area}, ${widget.venueAddress.landmark}, ${widget.venueAddress.city}, ${widget.venueAddress.state}, ${widget.venueAddress.pincode}.'
                                : '${widget.venueAddress.fullAddress}.',
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Sport Category'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: widget.selectedSportCategory['categoryName'],
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Sport Type'),
                          SizedBox(height: dW * 0.02),
                          Wrap(
                            crossAxisAlignment: WrapCrossAlignment.start,
                            runAlignment: WrapAlignment.spaceBetween,
                            children: [
                              ...widget.selectedSports.map(
                                (sport) => CustomContainer(
                                  width: widget.selectedSports == 'Badminton'
                                      ? dW * 0.35
                                      : dW * 0.32,
                                  boxShadow: [],
                                  borderColor: getThemeColor(),
                                  vPadding: 0.02,
                                  hPadding: 0.02,
                                  radius: 8,
                                  margin: EdgeInsets.only(
                                    bottom: dW * 0.03,
                                    right: dW * 0.03,
                                  ),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      if (Provider.of<Auth>(context,
                                                  listen: false)
                                              .getImageBySportName(sport
                                                  // widget.selectedSports
                                                  ) !=
                                          '')
                                        Image.network(
                                          Provider.of<Auth>(context,
                                                  listen: false)
                                              .getImageBySportName(sport
                                                  // widget.selectedSports
                                                  ),
                                          height: 30,
                                        ),
                                      SizedBox(width: dW * .02),
                                      ConstrainedBox(
                                        constraints:
                                            BoxConstraints(maxWidth: dW * 0.18),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: TextWidget(
                                            title:
                                                // widget.selectedSports
                                                sport,
                                            fontWeight: FontWeight.w500,
                                            textAlign: TextAlign.left,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.05),
                CustomContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            title: 'Venue & Sports Configuration',
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: getThemeColor(),
                          ),
                          SizedBox(height: dW * 0.05),
                          TextWidget(title: 'Weekdays'),
                          SizedBox(height: dW * 0.03),
                          Wrap(
                            children: [
                              ...widget.selectedDays
                                  .asMap()
                                  .map(
                                    (i, day) => MapEntry(
                                      i,
                                      TextWidget(
                                        title:
                                            '${getFullWeekDays(day)}${i == widget.selectedDays.length - 1 ? '' : ','} ',
                                        fontWeight: FontWeight.w500,
                                        textAlign: TextAlign.center,
                                        color: Color(0xff9798A3),
                                      ),
                                    ),
                                  )
                                  .values
                                  .toList()
                              // ...widget.selectedDays.map(
                              //   (day) =>
                              //       // Container(
                              //       //   constraints:
                              //       //       BoxConstraints(minWidth: dW * 0.15),
                              //       //   decoration: BoxDecoration(
                              //       //     color: getThemeColor(),
                              //       //     borderRadius: BorderRadius.circular(20),
                              //       //     border: Border.all(color: getThemeColor()),
                              //       //   ),
                              //       //   padding: EdgeInsets.all(dW * 0.02),
                              //       //   margin: EdgeInsets.only(
                              //       //     bottom: dW * 0.025,
                              //       //     right: dW * 0.025,
                              //       //   ),
                              //       //   child:
                              //       TextWidget(
                              //     title: '${getFullWeekDays(day)}, ',
                              //     fontWeight: FontWeight.w500,
                              //     textAlign: TextAlign.center,
                              //     color: Color(0xff9798A3),
                              //   ),
                              //   // ),
                              // ),
                            ],
                          ),
                          SizedBox(height: dW * 0.03),
                          TextWidget(title: 'Weekends'),
                          SizedBox(height: dW * 0.03),
                          Wrap(
                            children: [
                              ...widget.selectedWeekends
                                  .asMap()
                                  .map(
                                    (i, day) => MapEntry(
                                      i,
                                      TextWidget(
                                        title:
                                            '${getFullWeekDays(day)}${i == widget.selectedWeekends.length - 1 ? '' : ','} ',
                                        fontWeight: FontWeight.w500,
                                        textAlign: TextAlign.center,
                                        color: Color(0xff9798A3),
                                      ),
                                    ),
                                  )
                                  .values
                                  .toList()
                              //   ...widget.selectedWeekends.map(
                              //     (day) => Container(
                              //       constraints:
                              //           BoxConstraints(minWidth: dW * 0.15),
                              //       decoration: BoxDecoration(
                              //         color: getThemeColor(),
                              //         borderRadius: BorderRadius.circular(20),
                              //         border: Border.all(color: getThemeColor()),
                              //       ),
                              //       padding: EdgeInsets.all(dW * 0.02),
                              //       margin: EdgeInsets.only(
                              //         bottom: dW * 0.025,
                              //         right: dW * 0.025,
                              //       ),
                              //       child: TextWidget(
                              //         title: day,
                              //         color: Colors.white,
                              //         fontWeight: FontWeight.w500,
                              //         textAlign: TextAlign.center,
                              //       ),
                              //     ),
                              //   ),
                            ],
                          ),
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Time Slot Difference'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: '${widget.timeSlotDifference} minutes',
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Selected Slots'),
                          SizedBox(height: dW * 0.03),
                          ...widget.selectedTurfSizeAndPrice
                              .asMap()
                              .map(
                                (i, slot) => MapEntry(
                                  i,
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      TextWidget(
                                        title:
                                            '${i + 1}. ${slot['title']} Slot',
                                        color: getThemeColor(),
                                        fontWeight: FontWeight.w500,
                                      ),
                                      SizedBox(height: dW * 0.03),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          TextWidget(
                                              title: 'Selected Slot Timing'),
                                          SizedBox(height: dW * 0.03),
                                          buildSlotTimingWidget(
                                            dW: dW,
                                            tS: tS,
                                            title: 'Start Time :',
                                            value:
                                                '${slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                          ),
                                          SizedBox(height: dW * 0.05),
                                          buildSlotTimingWidget(
                                            dW: dW,
                                            tS: tS,
                                            title: 'End Time :',
                                            value:
                                                '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: dW * 0.05),
                                      TextWidget(
                                        title: widget.selectedSportCategory[
                                                    'categoryName'] !=
                                                'Outdoor'
                                            ? 'Selected Sport & Pricing'
                                            : 'Selected Turf Sizes & Pricing',
                                        fontSize: 16,
                                        color: Color(0xff636363),
                                        fontWeight: FontWeight.w500,
                                      ),
                                      SizedBox(height: dW * 0.01),
                                      TextWidget(
                                        title:
                                            'Price for ${widget.timeSlotDifference} minutes (Inclusive GST).',
                                        fontSize: 12,
                                        color: Color(0xff636363),
                                      ),
                                      SizedBox(height: dW * 0.03),
                                      CustomContainer(
                                        radius: 8,
                                        hPadding: 0.03,
                                        vPadding: 0.015,
                                        boxShadow: [],
                                        borderColor: getThemeColor(),
                                        child: Column(
                                          children: [
                                            SizedBox(height: dW * 0.03),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                ...[
                                                  widget.selectedSportCategory[
                                                              'categoryName'] ==
                                                          'Outdoor'
                                                              'Outdoor'
                                                      ? 'Sport'
                                                      : 'Turf Size',
                                                  'Weekdays',
                                                  'Weekends'
                                                ].map(
                                                  (type) => Container(
                                                    alignment:
                                                        Alignment.topLeft,
                                                    width: type == 'Weekends' ||
                                                            type == 'Weekdays'
                                                        ? dW * 0.21
                                                        : dW * .24,
                                                    child: FittedBox(
                                                      fit: BoxFit.scaleDown,
                                                      child: TextWidget(
                                                        title: type,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: dW * 0.03),
                                            ...slot['priceAndQuantity'].map(
                                              (sport) => Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            vertical:
                                                                dW * 0.02),
                                                    child: TextWidget(
                                                      title:
                                                          sport['sport'] == null
                                                              ? sport['title']
                                                              : sport['sport'],
                                                      color: getThemeColor(),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      ...[1, 2, 3].map(
                                                        (index) => Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  bottom: dW *
                                                                      0.03),
                                                          alignment: index == 1
                                                              ? Alignment
                                                                  .topLeft
                                                              : Alignment
                                                                  .center,
                                                          width: index != 1
                                                              ? dW * 0.21
                                                              : dW * .24,
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                                  vertical: 7),
                                                          decoration: index == 1
                                                              ? null
                                                              : BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              5),
                                                                  border: Border
                                                                      .all(
                                                                          color:
                                                                              getThemeColor()),
                                                                ),
                                                          child: FittedBox(
                                                            fit: BoxFit
                                                                .scaleDown,
                                                            child: TextWidget(
                                                              title: index == 1
                                                                  ? sport[
                                                                      'title']
                                                                  : index == 2
                                                                      ? sport['price'] ==
                                                                              0
                                                                          ? 'N.A'
                                                                          : '\u20b9 ${sport['price']}'
                                                                      : sport['weekendPrice'] ==
                                                                              0
                                                                          ? 'N.A'
                                                                          : '\u20b9 ${sport['weekendPrice']}',
                                                              // color:
                                                              //     getThemeColor(),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: dW * 0.05)
                                    ],
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          // SizedBox(height: dW * 0.03),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.05),
                CustomContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        title: 'Ownership & Policy',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: getThemeColor(),
                      ),
                      SizedBox(height: dW * 0.05),
                      if (widget.cancellationCharges.isNotEmpty)
                        Container(
                          margin: EdgeInsets.only(bottom: dW * 0.03),
                          child: TextWidget(
                            title: 'Cancellation Charges',
                            color: Colors.black,
                          ),
                        ),
                      if (widget.cancellationCharges.isNotEmpty)
                        ...widget.cancellationCharges
                            .asMap()
                            .map(
                              (i, data) => MapEntry(
                                i,
                                CancellationWidget(
                                    data: data,
                                    deviceWidth: dW,
                                    textScaleFactor: tS),
                              ),
                            )
                            .values
                            .toList(),
                      if (widget.cancellationPolicy.isNotEmpty) ...[
                        Container(
                          margin: EdgeInsets.only(bottom: dW * 0.03),
                          child: TextWidget(
                            title: 'Cancellation Policy',
                            color: Colors.black,
                          ),
                        ),
                        CustomContainer(
                          margin: EdgeInsets.only(bottom: dW * 0.02),
                          boxShadow: [],
                          borderColor: getThemeColor(),
                          vPadding: .03,
                          child: Text(
                            widget.cancellationPolicy,
                            style: TextStyle(
                              fontSize: tS * 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.05),
                CustomContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        title: 'Venue Presentation',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: getThemeColor(),
                      ),
                      SizedBox(height: dW * 0.05),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Booking Availability Duration'),
                          SizedBox(height: dW * 0.02),
                          TextWidget(
                            title: '${widget.durationCount} ${widget.period}',
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                      SizedBox(height: dW * 0.05),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            title:
                                widget.selectedSportCategory['categoryName'] !=
                                        'Outdoor'
                                    ? 'Quantity'
                                    : widget.isNet
                                        ? 'Turf Relation & Label'
                                        : 'Turf Quantity & Label',
                          ),
                          SizedBox(height: dW * 0.03),
                          ...widget.sportQuantity.map((sport) {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                      right: dW * 0.03, bottom: dW * 0.03),
                                  alignment: Alignment.center,
                                  width: dW * 0.2,
                                  padding: EdgeInsets.symmetric(
                                    vertical: dW * 0.025,
                                    horizontal: dW * 0.02,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  child: TextWidget(
                                    title: sport['title'],
                                    fontSize: widget.selectedSportCategory[
                                                'categoryName'] ==
                                            'Outdoor'
                                        ? tS * 14
                                        : tS * 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    textOverflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(width: dW * 0.035),
                                Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.symmetric(
                                      vertical: dW * 0.02,
                                      horizontal: dW * 0.02),
                                  child: TextWidget(
                                    title:
                                        'x   ${sport['quantity'] > 9 ? '' : '0'}${sport['quantity']} ${sport['label'].trim() == '' ? '' : '(${sport['label']})'}',
                                    fontSize: tS * displayLarge,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                      DividerWidget(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(title: 'Advance Amount'),
                          SizedBox(height: dW * 0.02),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextWidget(
                                  title: widget.selectedSportCategory[
                                              'categoryName'] !=
                                          'Outdoor'
                                      ? 'Sport'
                                      : 'Turf Size',
                                  color: Color(0xff636363)),
                              TextWidget(
                                  title: 'Advance Amount',
                                  color: Color(0xff636363)),
                            ],
                          ),
                          SizedBox(height: dW * 0.03),
                          ...widget.sportQuantity.map(
                            (sport) => Padding(
                              padding: EdgeInsets.only(bottom: dW * 0.04),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  ConstrainedBox(
                                    constraints:
                                        BoxConstraints(maxWidth: dW * 0.4),
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      child: TextWidget(
                                        title: sport['title'],
                                        color: getThemeColor(),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  CustomContainer(
                                    width: dW * 0.31,
                                    boxShadow: [],
                                    vPadding: 0.025,
                                    radius: 7,
                                    borderColor: getThemeColor(),
                                    child: TextWidget(
                                      title: sport['advanceAmount'] is List
                                          ? '\u20b9 ${sport['advanceAmount'][0]['Singles'].text}'
                                          : '\u20b9 ${sport['advanceAmount'].text}',
                                      color: getThemeColor(),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      DividerWidget(),
                      TextWidget(title: 'Amenities'),
                      SizedBox(height: dW * 0.03),
                      ...widget.selectedFacilities.map(
                        (facility) => Padding(
                          padding: EdgeInsets.only(bottom: dW * 0.035),
                          child: Row(
                            children: [
                              OldCheckBoxWidget(dW, true),
                              SizedBox(width: dW * 0.035),
                              TextWidget(
                                title: facility,
                                fontWeight: FontWeight.w500,
                              )
                            ],
                          ),
                        ),
                      ),
                      DividerWidget(),
                      TextWidget(title: 'Venue Images'),
                      SizedBox(height: dW * 0.03),
                      ...widget.listOfImages.map(
                        (image) => GestureDetector(
                          onTap: () => push(OpenMediaFullScreen(
                            type: 'Image',
                            isLocal: !image.contains('https'),
                            url: image,
                          )),
                          child: Container(
                            margin: EdgeInsets.only(bottom: dW * 0.035),
                            child: Container(
                              width: dW,
                              height: dW * 0.45,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: getThemeColor()),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: image.contains('https')
                                    ? CachedNetworkImage(
                                        fit: BoxFit.cover,
                                        imageUrl: image,
                                        placeholder: (_, __) => Image.asset(
                                          'assets/images/placeholder.jpg',
                                          fit: BoxFit.cover,
                                        ),
                                      )
                                    : Image.file(File(image),
                                        fit: BoxFit.cover),
                              ),
                            ),
                          ),
                        ),
                      ),
                      if (widget.videoPath != '') ...[
                        SizedBox(height: dW * 0.03),
                        TextWidget(title: 'Venue Video'),
                        SizedBox(height: dW * 0.03),
                        GestureDetector(
                          onTap: () {
                            push(
                              OpenMediaFullScreen(
                                  type: 'Video',
                                  isLocal: !widget.videoPath.contains('https'),
                                  url: widget.videoPath),
                            );
                          },
                          child: Stack(
                            clipBehavior: Clip.none,
                            alignment: Alignment.center,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: widget.videoThumbnail.contains('https')
                                    ? Image.network(
                                        widget.videoThumbnail,
                                        width: dW,
                                        height: dW * 0.5,
                                        fit: BoxFit.cover,
                                      )
                                    : Image.file(
                                        File(widget.videoThumbnail),
                                        width: dW,
                                        height: dW * 0.5,
                                        fit: BoxFit.cover,
                                      ),
                              ),
                              Icon(
                                Icons.play_circle_outline_rounded,
                                color: Colors.white,
                                size: dW * 0.1,
                              ),
                            ],
                          ),
                        ),
                      ]
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.15),
              ],
            ),
          ),
        ),
        BottomAlignedWidget(
          dW: dW,
          dH: dH,
          child: Column(
            children: [
              SizedBox(height: dW * 0.01),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.005),
                    child: CheckBoxWidget(
                      activeBorderColor: getThemeColor(),
                      activeColor: getThemeColor(),
                      active: policyAccepted,
                      borderRadius: 3,
                      height: dW * 0.05,
                      iconSize: 14,
                      constraintWidth: null,
                      onTap: () {
                        policyAccepted = !policyAccepted;
                        setState(() {});
                      },
                    ),
                  ),
                  SizedBox(width: dW * 0.025),
                  GestureDetector(
                    onTap: () {
                      policyAccepted = !policyAccepted;
                      setState(() {});
                    },
                    child: PolicyTextWidget(dW: dW, tS: tS, fromVenue: true),
                  ),
                ],
              ),
              SizedBox(height: dW * 0.033),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomButton(
                    width: dW * 0.4,
                    height: dW * 0.12,
                    fontSize: 16,
                    radius: 7,
                    borderColor: getThemeColor(),
                    textColor: getThemeColor(),
                    buttonColor: Colors.white,
                    buttonText: 'Back',
                    onPressed: isLoading ? () {} : pop,
                  ),
                  CustomButton(
                    width: dW * 0.45,
                    height: dW * 0.12,
                    fontSize: 16,
                    radius: 7,
                    buttonText: widget.venueId == ''
                        ? 'Submit For Review'
                        : 'Update Venue',
                    onPressed: policyAccepted ? saveDetails : null,
                    isLoading: isLoading,
                  )
                ],
              ),
              SizedBox(height: dW * 0.04),
              TextWidget(
                title:
                    'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                fontSize: 12,
                fontWeight: FontWeight.w500,
              )
            ],
          ),
        ),
      ],
    );
  }
}

getTurfOption(String option) {
  if (option == 'One On') {
    return 'Turf with Cricket Net';
  } else if (option == 'Netting') {
    return 'Turf with netting system';
  } else if (option == 'Non Netting') {
    return 'Turf without Net';
  } else {
    return '';
  }
}
