import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/venue_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../authModule/providers/auth.dart';
import '../../../commonWidgets/circular_loader.dart';
import '../../../commonWidgets/custom_button.dart';
import '../../../commonWidgets/new_appbar.dart';
import '../../../common_function.dart';
import '../../../navigators.dart';
import 'add_venue_details_screen.dart';

class VenueInstructionScreen extends StatefulWidget {
  const VenueInstructionScreen({Key? key}) : super(key: key);

  @override
  VenueInstructionScreenState createState() => VenueInstructionScreenState();
}

class VenueInstructionScreenState extends State<VenueInstructionScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Map language = {};

  bool isLoading = false;
  late UserModal user;

  List<Map<String, String>> listOfSteps = [
    {
      'title': 'Tell us about your venue',
      'subTitle':
          'Provide essential details about your venue, such as location, venue name , description.',
      'image': 'venueStep1',
    },
    {
      'title': 'Sports & venue configuration',
      'subTitle':
          'Provide sports details like turf quantity, venue availability and timings.',
      'image': 'venueStep2',
    },
    {
      'title': 'Showcase your venue attractively',
      'subTitle':
          'Upload venue images to showcase your turf. Share sports quantity and pricing details.',
      'image': 'venueStep3',
    },
    {
      'title': 'Ownership & Policies',
      'subTitle': 'Enter owner details and booking policies.',
      'image': 'venueStep4',
    },
    {
      'title': 'Review & Publish',
      'subTitle': 'Review your listing and submit it for review.',
      'image': 'venueStep5',
    },
  ];

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
                 
              NewAppBar(dW: dW, title: ''),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: isLoading
                    ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                    : SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SizedBox(height: dW * 0.02),
                            TextWidget(
                              title: 'Host Your Venues Seamlessly On BYS 🏟️',
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                            ),
                            SizedBox(height: dW * 0.07),
                            ...listOfSteps
                                .asMap()
                                .map(
                                  (i, step) => MapEntry(
                                    i,
                                    Container(
                                      padding: EdgeInsets.only(
                                        bottom: dW * 0.045,
                                        top: i == 0 ? 0 : dW * 0.04,
                                      ),
                                      decoration: BoxDecoration(
                                        border: i == listOfSteps.length - 1
                                            ? null
                                            : Border(
                                                bottom: BorderSide(
                                                    color: Color(0xffD9D9D9)),
                                              ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              TextWidget(
                                                  title: '${i + 1}',
                                                  fontWeight: FontWeight.w500),
                                              SizedBox(width: dW * 0.035),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  ConstrainedBox(
                                                    constraints: BoxConstraints(
                                                        maxWidth: dW * 0.6),
                                                    child: TextWidget(
                                                      title: '${step['title']}',
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                  ),
                                                  SizedBox(height: dW * 0.015),
                                                  ConstrainedBox(
                                                    constraints: BoxConstraints(
                                                        maxWidth: dW * 0.6),
                                                    child: TextWidget(
                                                      title:
                                                          '${step['subTitle']}',
                                                      fontSize: 10,
                                                      color: Color(0xff9798A3),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          Image.asset(
                                            'assets/images/${step['image']}.png',
                                            height: dW * 0.15,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                                .values
                                .toList(),
                            SizedBox(height: dW * 0.015),
                          ],
                        ),
                      ),
              ),
              BottomAlignedWidget(
                dW: dW,
                dH: dH,
                child: CustomButton(
                  width: dW * 0.9,
                  height: dW * 0.12,
                  fontSize: 16,
                  buttonText: 'Get Started',
                  onPressed: () => push(AddVenueDetailsScreen()),
                ),
              ),
            ],
          ),
        ));
  }
}
