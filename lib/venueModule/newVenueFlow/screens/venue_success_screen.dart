// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/employeeModule/screens/booking_screen.dart';
import 'package:bys_business/homeModule/screens/homeScreen.dart';

import '../../../authModule/providers/auth.dart';
import '../../../commonWidgets/custom_button.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import '../../../navigators.dart';

class VenueSuccessScreen extends StatefulWidget {
  final String role;
  VenueSuccessScreen({required this.role});

  @override
  State<VenueSuccessScreen> createState() => _VenueSuccessScreenState();
}

class _VenueSuccessScreenState extends State<VenueSuccessScreen> {
  double dH = 0.0;

  double dW = 0.0;

  double tS = 0.0;

  TextTheme customTextTheme = const TextTheme();

  Map language = {};

  bool isLoading = false;

  goToHomeScreen() {
    if (widget.role == 'Employee') {
      pushAndRemoveUntil(BookingScreen(
        user: Provider.of<Auth>(context, listen: false).user,
      ));
    } else {
      pushAndRemoveUntil(HomeScreen());
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    customTextTheme = Theme.of(context).textTheme;

    return WillPopScope(
      onWillPop: () async {
        goToHomeScreen();
        return true;
      },
      child: Scaffold(
        body: iOSCondition(dH)
            ? screenBody(context)
            : SafeArea(child: screenBody(context)),
      ),
    );
  }

  screenBody(BuildContext context) {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Image.asset("assets/images/venueSuccess.png",
                        scale: 2.2),
                  ),
                  SizedBox(height: dW * .09),
                  TextWidget(
                    title: 'Congratulations!',
                    fontSize: 20,
                    color: getThemeColor(),
                    fontWeight: FontWeight.w500,
                  ),
                  SizedBox(height: dW * .05),
                  TextWidget(
                    title:
                        'Your venue has been successfully submitted. Our team will review it shortly. ',
                    color: Color(0xff21272A),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: dW * .035),
                  TextWidget(
                    title: 'Thank you for joining us!',
                    fontWeight: FontWeight.w500,
                  ),
                ],
              ),
            ),
          ),
          BottomAlignedWidget(
            dW: dW,
            dH: dH,
            child: CustomButton(
              width: dW * 0.35,
              height: dW * 0.12,
              fontSize: 16,
              radius: 7,
              buttonText: 'Go To Home',
              onPressed: goToHomeScreen,
            ),
          )
        ],
      ),
    );
  }
}
