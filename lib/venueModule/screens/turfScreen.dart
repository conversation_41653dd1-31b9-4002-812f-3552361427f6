import 'dart:io';

import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/venue_instruction_screen.dart';
import 'package:bys_business/venueModule/widgets/group_widget.dart';
import '../../add_edit_snooker_pool_screen.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_container.dart';
import '../../common_function.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group_model.dart';
import '../models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../../venueModule/widgets/turfWidget.dart';
import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../widgets/add_venue_bottom_sheet.dart';
import 'add_edit_group_screen.dart';

class TurfScreen extends StatefulWidget {
  const TurfScreen({Key? key}) : super(key: key);

  @override
  _TurfScreenState createState() => _TurfScreenState();
}

class _TurfScreenState extends State<TurfScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  List<Venue> listOfTurfs = [];
  bool isLoading = false;
  int currentIndex = 1;
  late UserModal user;

  late TurfProvider turfProvider;
  List<Group> listOfGroups = [];

  String adminContact = '';

  List statusType = [
    {
      'title': "Active",
      'isSelected': true,
      'index': 1,
    },
    {
      'title': "Pending",
      'isSelected': false,
      'index': 2,
    },
    {
      'title': "Groups",
      'isSelected': false,
      'index': 3,
    },
  ];

  selectStatusType(String title) {
    statusType.forEach((type) {
      if (type['title'] == title) {
        type['isSelected'] = true;
        currentIndex = type['index'];
      } else {
        type['isSelected'] = false;
      }
    });
    if (title == 'Groups') fetchGroups();
    setState(() {});
  }

  resetStatusType() {
    statusType[0]['isSelected'] = false;
    statusType[1]['isSelected'] = true;
    currentIndex = 2;
    setState(() {});
  }

  Future<void> fetchTurf() async {
    try {
      setState(() {
        isLoading = true;
      });
      await Provider.of<TurfProvider>(context, listen: false)
          .fetchTurfsByBusinessId(user.accessToken, user.businessId);
      listOfTurfs = Provider.of<TurfProvider>(context, listen: false).turfs;
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  fetchGroups() async {
    try {
      if (turfProvider.groups.isEmpty) {
        setState(() => isLoading = true);
        await turfProvider.fetchAllGroups(user.accessToken);
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  addVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddVenueBottomSheet(listOfVenues: listOfTurfs),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  featureUnavailableDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Feature Unavailable',
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: tS * 16,
                        color: const Color(0XFF1D2739),
                      ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    'This feature is not currently available to you. Please contact the admin.',
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF667185),
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    adminContact,
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: getThemeColor(),
                        ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    launchCall(adminContact);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: dW * 0.05),
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Call',
                      style: Theme.of(context).textTheme.displayLarge!.copyWith(
                            fontSize: tS * 16,
                            color: getThemeColor(),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  fetchAdminContact() {
    var auth = Provider.of<Auth>(context, listen: false);
    adminContact = auth.adminContact.toString();
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    turfProvider = Provider.of<TurfProvider>(context, listen: false);
    if (user.businessStatus == "PENDING") {
      statusType[2]['isSelected'] = false;
      statusType[1]['isSelected'] = true;
      statusType[0]['isSelected'] = false;
      currentIndex = 2;
    } else {
      statusType[0]['isSelected'] = true;
      statusType[1]['isSelected'] = false;
      statusType[2]['isSelected'] = false;

      currentIndex = 1;
    }
    fetchTurf();
    fetchAdminContact();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfTurfs =
        Provider.of<TurfProvider>(context).getVerifiedTurf(currentIndex);
    listOfGroups = Provider.of<TurfProvider>(context).groups;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      backgroundColor: Colors.white,
    );
  }

  Widget screenBody() {
    bool isGroupsSelected = statusType.any((element) =>
        element['title'] == 'Groups' && element['isSelected'] == true);

    return Padding(
      padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // SizedBox(height: dW * 0.05),
          NewAppBar(dW: dW, title: 'Venues'),
          SizedBox(height: dW * 0.05),
          CustomContainer(
            hPadding: .03,
            margin: EdgeInsets.symmetric(horizontal: dW * 0.04),
            vPadding: .02,
            borderColor: Color(0xffF3F4F9),
            radius: 10,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ...statusType.map(
                  (type) => Expanded(
                    child: GestureDetector(
                      onTap: () => selectStatusType(type['title']),
                      child: Container(
                        width: dW * 0.38,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          vertical: dW * 0.03,
                        ),
                        decoration: BoxDecoration(
                          color: type['isSelected']
                              ? getThemeColor()
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${type['title']}',
                          style: TextStyle(
                            color:
                                !type['isSelected'] ? Colors.black : Colors.white,
                            fontSize: type['isSelected'] ? tS * 15 : tS * 14,
                            fontWeight: type['isSelected']
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: isLoading
                ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                : RefreshIndicator(
                    onRefresh: () => fetchTurf(),
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
                      physics: BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: Column(
                        children: [
                          SizedBox(height: dW * 0.07),
                          if (listOfTurfs.isEmpty && !isGroupsSelected)
                            EmptyListWidget(
                              text: 'Venue not found!',
                              topPadding: .6,
                            ),
                          if (listOfTurfs.isNotEmpty && !isGroupsSelected)
                            ...listOfTurfs.map(
                              (turf) => TurfWidget(
                                dW: dW,
                                tS: tS,
                                turf: turf,
                                user: user,
                              ),
                            ),
                          if (isGroupsSelected)
                            listOfGroups.isEmpty
                                ? EmptyListWidget(
                                    text: 'Groups not found!!!', topPadding: .5)
                                : Column(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(dW * 0.03),
                                        decoration: BoxDecoration(
                                          color: Color(0xffE3FEDB),
                                          border:
                                              Border.all(color: getThemeColor()),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: TextWidget(
                                            color: Color(0xff636363),
                                            fontWeight: FontWeight.w400,
                                            fontSize: 11,
                                            title:
                                                "*Created venue groups for locations or venues near each other, they'll seamlessly appear on the user app for easy discovery. Your venues will shine together, attracting more users."),
                                      ),
                                      SizedBox(height: dW * 0.06),
                                      ...listOfGroups
                                          .asMap()
                                          .map(
                                            (i, group) => MapEntry(
                                              i,
                                              GroupWidget(
                                                  group: group, dW: dW, index: i),
                                            ),
                                          )
                                          .values
                                          .toList()
                                    ],
                                  ),
                          SizedBox(height: dW * 0.13),
                        ],
                      ),
                    ),
                  ),
          ),
          // BottomAlignedWidget(
          //   dW: dW,
          //   dH: dW * 0.13,
          //   child: CustomButton(
          //     width: dW,
          //     height: dW * 0.13,
          //     radius: 8,
          //     fontSize: 16,
          //     buttonText: isGroupsSelected ? 'Add New Group' : 'Add New Venue',
          //     onPressed: isGroupsSelected
          //         ? () {
          //             push(AddEditGroupScreen());
          //           }
          //         : listOfTurfs.isEmpty
          //             ? () => push(VenueInstructionScreen())
          //             : addVenueBottomSheet,
          //   ),
          // )
          BottomAlignedWidget(
            dW: dW,
            dH: dW * 0.13,
            child: CustomButton(
                buttonColor:
                    isGroupsSelected ? getThemeColor() : Color(0XFFC0C0C0),
                width: dW,
                height: dW * 0.13,
                radius: 8,
                fontSize: 16,
                buttonText: isGroupsSelected ? 'Add New Group' : 'Add New Venue',
                onPressed: isGroupsSelected
                    ? () {
                        push(AddEditGroupScreen());
                      }
                    : () => featureUnavailableDialog()),
          )
          // BottomAlignedWidget(
          //   dW: dW,
          //   dH: dW * 0.13,
          //   child: CustomButton(
          //     width: dW,
          //     height: dW * 0.13,
          //     radius: 8,
          //     fontSize: 16,
          //     buttonText: isGroupsSelected ? 'Add New Group' : 'Add New Venue',
          //     onPressed: () {
          //       showModalBottomSheet(
          //         context: context,
          //         builder: (BuildContext context) {
          //           return Padding(
          //             padding: const EdgeInsets.all(16.0),
          //             child: Column(
          //               mainAxisSize: MainAxisSize.min,
          //               children: [
          //                 CustomButton(
          //                   width: dW,
          //                   height: dW * 0.13,
          //                   radius: 8,
          //                   fontSize: 16,
          //                   buttonText: isGroupsSelected
          //                       ? 'Add New Group'
          //                       : 'e',
          //                   onPressed: isGroupsSelected
          //                       ? () {
          //                           pop();
          //                           push(AddEditGroupScreen());
          //                         }
          //                       : listOfTurfs.isEmpty
          //                           ? () {
          //                               pop();
          //                               push(VenueInstructionScreen());
          //                             }
          //                           : () {
          //                               pop();
          //                               addVenueBottomSheet();
          //                             },
          //                 ),
          //                 Divider(),
          //                 CustomButton(
          //                   width: dW,
          //                   height: dW * 0.13,
          //                   radius: 8,
          //                   fontSize: 16,
          //                   buttonText: 'Add Snooker Pool Table',
          //                   onPressed: () {
          //                     pop();
          //                     push(AddEditSnookerPoolScreen());
          //                   },
          //                 ),
          //               ],
          //             ),
          //           );
          //         },
          //       );
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }
}
