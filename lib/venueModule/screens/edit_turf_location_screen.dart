import 'dart:async';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/materialCircularLoader.dart';
import 'package:bys_business/venueModule/models/venue_model.dart';
import 'package:bys_business/venueModule/providers/turfProvider.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../common_function.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/text_widget.dart';

class EditTurfLocationScreen extends StatefulWidget {
  final Venue turf;
  EditTurfLocationScreen({
    Key? key,
    required this.turf,
  }) : super(key: key);

  @override
  State<EditTurfLocationScreen> createState() => _EditTurfLocationScreenState();
}

class _EditTurfLocationScreenState extends State<EditTurfLocationScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  bool searchCity = false;
  late UserModal user;

  GlobalKey<FormState> _formKey = GlobalKey();

  TextEditingController streetNameController = TextEditingController();
  TextEditingController landMarkController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController pincodeController = TextEditingController();

  Timer? _debounce;

  searchCityState() {
    setState(() {
      searchCity = true;
    });
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      final response = await Provider.of<Auth>(context, listen: false)
          .getCityStateData(pincodeController.text);
      if (response != null) {
        cityController.text = response['city'] ?? '';
        stateController.text = response['state'] ?? '';
        setState(() {
          searchCity = false;
        });
      } else {
        callToastMessage('Invalid Pincode');
        setState(() {
          searchCity = false;
        });
      }
    });
  }

  proceed() async {
    hideKeyBoard(context);
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      if (isLoading) {
        return;
      }
      setState(() {
        isLoading = true;
      });

      Map<String, String> body = {
        "streetName": streetNameController.text.trim(),
        "landmark": landMarkController.text.trim(),
        "pincode": pincodeController.text,
        "state": stateController.text,
        "city": cityController.text,
        "turfId": widget.turf.id,
      };

      final result = await Provider.of<TurfProvider>(context, listen: false)
          .updateVenueLocation(
        accessToken: Provider.of<Auth>(context, listen: false).user.accessToken,
        body: body,
      );

      if (result) {
        pop();
        showSnackbar('Location updated successfully', color: greenPrimary);
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      showSnackbar('Unable to edit location');
      print(e);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    streetNameController.text = widget.turf.address.streetName;
    landMarkController.text = widget.turf.address.landmark;
    pincodeController.text = widget.turf.address.pincode.toString();
    stateController.text = widget.turf.address.state;
    cityController.text = widget.turf.address.city;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      appBar: CustomAppBar(dW: dW, title: 'Edit Venue Location'),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: dW * 0.05),
                      TextWidget(title: 'Add Location Details', fontSize: 16),
                      SizedBox(height: dW * 0.05),
                      CustomContainer(
                        vPadding: 0.045,
                        hPadding: .03,
                        borderColor: Colors.transparent,
                        child: Column(
                          children: [
                            CustomTextFieldWithLabel(
                              label: 'Street Address',
                              controller: streetNameController,
                              hintText: 'Enter street address',
                              inputType: TextInputType.streetAddress,
                              inputAction: TextInputAction.next,
                              textCapitalization: TextCapitalization.sentences,
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please enter street address';
                                }
                              },
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Landmark',
                              controller: landMarkController,
                              textCapitalization: TextCapitalization.sentences,
                              inputAction: TextInputAction.next,
                              hintText: 'Enter landmark',
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Please enter landmark';
                                }
                              },
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'Pincode',
                              controller: pincodeController,
                              hintText: 'Enter pincode',
                              inputType: TextInputType.number,
                              inputAction: TextInputAction.done,
                              inputFormatter: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              maxLength: 6,
                              onChanged: (value) {
                                if (value.length == 6) {
                                  searchCityState();
                                } else {
                                  cityController.clear();
                                  stateController.clear();
                                }
                              },
                              validator: (value) {
                                if (value!.trim().isEmpty) {
                                  return 'Please enter pincode';
                                } else if (value.trim().length < 6) {
                                  return 'Please enter valid pincode';
                                }
                              },
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                              label: 'State',
                              controller: stateController,
                              textCapitalization: TextCapitalization.words,
                              hintText: 'State',
                              enabled: false,
                              inputType: TextInputType.text,
                              widget: searchCity
                                  ? circularForButton(300, color: greenPrimary)
                                  : null,
                            ),
                            SizedBox(height: dW * 0.05),
                            CustomTextFieldWithLabel(
                                label: 'City',
                                controller: cityController,
                                textCapitalization: TextCapitalization.words,
                                hintText: 'City',
                                enabled: false,
                                inputType: TextInputType.text,
                                widget: searchCity
                                    ? circularForButton(300,
                                        color: greenPrimary)
                                    : null),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.1),
                    ],
                  ),
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: CustomButton(
                width: dW,
                height: dW * 0.14,
                buttonText: 'Update',
                onPressed: proceed,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
