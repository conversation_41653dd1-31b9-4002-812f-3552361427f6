// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/materialCircularLoader.dart';
import 'package:bys_business/venueModule/providers/turfProvider.dart';
import 'package:bys_business/venueModule/screens/add_edit_group_screen.dart';
import 'package:bys_business/venueModule/widgets/turfWidget.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../navigators.dart';
import '../models/group_model.dart';

class GroupDetailsScreen extends StatefulWidget {
  Group group;
  final int index;
  GroupDetailsScreen({
    Key? key,
    required this.group,
    required this.index,
  }) : super(key: key);

  @override
  GroupDetailsScreenState createState() => GroupDetailsScreenState();
}

class GroupDetailsScreenState extends State<GroupDetailsScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Map language = {};

  bool isLoading = false;
  late UserModal user;
  late TurfProvider turfProvider;

  showConfirmationDialog() {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            alignment: Alignment.centerLeft,
            titleAlign: TextAlign.left,
            subTitleAlign: TextAlign.left,
            subTitle: 'Are you sure you want to remove this group?',
            noText: 'Yes, Remove',
            yesText: 'No, Cancel',
            noFunction: () {
              pop();
              deleteGroup();
            },
            yesFunction: pop,
          )),
    );
  }

  deleteGroup() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);

      final result = await Provider.of<TurfProvider>(context, listen: false)
          .deleteGroup(user.accessToken, widget.group.id);
      if (result) {
        pop();
        showSnackbar('Group deleted successfully', color: greenPrimary);
      } else {
        showSnackbar('Unable to delete this group');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    turfProvider = Provider.of<TurfProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NewAppBar(dW: dW, title: ''),
                  isLoading
                      ? Padding(
                          padding: EdgeInsets.only(right: dW * 0.05),
                          child: circularForButton(dW, color: getThemeColor()),
                        )
                      : PopupMenuButton(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(7),
                          ),
                          icon: Icon(Icons.more_vert, color: Colors.black),
                          itemBuilder: (BuildContext bc) => [
                            popupMenuItem(
                              position: 1,
                              title: 'Edit',
                              icon: 'edit',
                              dW: dW,
                            ),
                            popupMenuItem(
                              position: 2,
                              title: 'Remove',
                              icon: 'delete1',
                              dW: dW,
                            ),
                          ],
                          onSelected: (value) {
                            if (value == 1) {
                              push(AddEditGroupScreen(group: widget.group))
                                  .then((value) {
                                if (value != null) {
                                  widget.group = value;
                                  setState(() {});
                                }
                              });
                            } else if (value == 2) {
                              showConfirmationDialog();
                            }
                          },
                        ),
                ],
              ),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: dW * 0.02),
                      TextWidget(
                        title: 'Group ${widget.index + 1}',
                        color: getThemeColor(),
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(height: dW * 0.015),
                      TextWidget(
                        title:
                            '${widget.group.name}, ${widget.group.primaryVenue.address.city}',
                        color: Color(0xff3E3E3E),
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                      ),
                      DividerWidget(),
                      TurfWidget(
                        dW: dW,
                        tS: tS,
                        turf: widget.group.primaryVenue,
                        user: user,
                      ),
                      ...widget.group.venues.map(
                        (venue) => TurfWidget(
                          dW: dW,
                          tS: tS,
                          turf: venue,
                          user: user,
                        ),
                      ),
                      SizedBox(height: dW * 0.09)
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
