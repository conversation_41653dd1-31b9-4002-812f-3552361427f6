import 'dart:io';

import 'package:bys_business/commonWidgets/materialCircularLoader.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/add_venue_details_screen.dart';

import '../../authModule/providers/auth.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../commonWidgets/open_media_full_screen.dart';
import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import '../../venueModule/screens/edit_turf_location_screen.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/checkBoxWidget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_function.dart';
import '../../services/dynamic_link_api.dart';
import '../models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../../venueModule/screens/addTurfDetailScreen.dart';
import '../../venueModule/screens/inactiveTurfScreen.dart';
import '../../venueModule/screens/viewImage.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../widgets/cancellationWidget.dart';

class TurfDetailsScreen extends StatefulWidget {
  final Venue turf;
  final UserModal user;
  TurfDetailsScreen({
    Key? key,
    required this.turf,
    required this.user,
  }) : super(key: key);

  @override
  _TurfDetailsScreenState createState() => _TurfDetailsScreenState();
}

class _TurfDetailsScreenState extends State<TurfDetailsScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  String adminContact = '';
  List turfQuanity = [];

  bool isSharing = false;
  bool isLoading = false;

  Venue? turf;

  shareVenue() async {
    setState(() => isSharing = true);
    await shareTurf(context, turf!);
    setState(() => isSharing = false);
  }

  setSportQuantity() {
    turf = Provider.of<TurfProvider>(context, listen: false)
        .findTurfById(widget.turf.id);
    turfQuanity = [];
    if (turf != null) {
      turf!.slots!.forEach((slot) {
        slot.priceAndQuantity.forEach((size) {
          var index = turfQuanity
              .indexWhere((element) => element['title'] == size.title);
          if (index == -1) {
            turfQuanity.add({
              'title': size.title,
              'quantity': size.quantity,
              'label': size.label,
              'advanceAmount': size.advanceAmount,
            });
          }
        });
      });
    }
    if (mounted) setState(() {});
  }

  showConfirmationDialog() {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            alignment: Alignment.centerLeft,
            titleAlign: TextAlign.left,
            subTitleAlign: TextAlign.left,
            subTitle:
                'Are you sure you want to remove this venue? Booking which is created till now will not be deleted.',
            noText: 'Yes, Remove',
            yesText: 'No, Cancel',
            noFunction: () {
              pop();
              deleteVenue();
            },
            yesFunction: pop,
          )),
    );
  }

  deleteVenue() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);

      final result = await Provider.of<TurfProvider>(context, listen: false)
          .deleteVenue(widget.user.accessToken, turf!.id);
      if (result) {
        pop();
        showSnackbar('Venue deleted successfully', color: greenPrimary);
      } else {
        showSnackbar('Unable to delete this venue');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  featureUnavailableDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Feature Unavailable',
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: tS * 16,
                        color: const Color(0XFF1D2739),
                      ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    'This feature is not currently available to you. Please contact the admin.',
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF667185),
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    adminContact,
                    style: Theme.of(context).textTheme.displaySmall!.copyWith(
                          fontSize: tS * 14,
                          color: getThemeColor(),
                        ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    launchCall(adminContact);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: dW * 0.05),
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Call',
                      style: Theme.of(context).textTheme.displayLarge!.copyWith(
                            fontSize: tS * 16,
                            color: getThemeColor(),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  fetchAdminContact() {
    var auth = Provider.of<Auth>(context, listen: false);
    adminContact = auth.adminContact.toString();
  }

  @override
  void initState() {
    super.initState();
    setSportQuantity();
    fetchAdminContact();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    turf = Provider.of<TurfProvider>(context).findTurfById(widget.turf.id);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return turf == null
        ? CircularLoader(android: dW * 0.002, iOS: dW * 0.035)
        : Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
              children: [
                // SizedBox(height: dW * 0.05),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NewAppBar(dW: dW, title: 'Venue Details'),
                    Row(
                      children: [
                        if (turf!.status == 'APPROVED')
                          isSharing
                              ? Padding(
                                  padding: EdgeInsets.only(right: dW * 0.02),
                                  child: circularForButton(dW,
                                      color: getThemeColor()),
                                )
                              : IconButton(
                                  onPressed: shareVenue,
                                  icon: AssetSvgIcon(iconName: 'share1'),
                                ),
                        isLoading
                            ? Padding(
                                padding: EdgeInsets.only(
                                    right: dW * 0.05, left: dW * 0.03),
                                child:
                                    circularForButton(dW, color: getThemeColor()),
                              )
                            : PopupMenuButton(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(7),
                                ),
                                icon: Icon(Icons.more_vert, color: Colors.black),
                                itemBuilder: (BuildContext bc) => [
                                  popupMenuItem(
                                    position: 1,
                                    title: 'Edit Venue Details',
                                    icon: 'edit',
                                    dW: dW,
                                  ),
                                  // popupMenuItem(
                                  //   position: 3,
                                  //   title: 'Edit Location',
                                  //   icon: '',
                                  //   dW: dW,
                                  // ),
                                  if (turf!.status == 'APPROVED')
                                    popupMenuItem(
                                      position: 2,
                                      title: "Inactive Venue",
                                      icon: 'active',
                                      dW: dW,
                                    ),
                                  if (turf!.status == 'APPROVED' &&
                                      widget.user.role == 'Business')
                                    popupMenuItem(
                                      position: 3,
                                      title: "Remove Venue",
                                      icon: 'delete1',
                                      dW: dW,
                                    ),
                                ],
                                onSelected: (value) {
                                  if (value == 1) {
                                    // push(AddTurfDetailScreen(turf: turf!));
                                    // push(AddVenueDetailsScreen(venue: turf!))
                                    //     .then((value) {
                                    //   if (value != null && value) {
                                    //     setSportQuantity();
                                    //   }
                                    // });
                                    featureUnavailableDialog();
                                  } else if (value == 2) {
                                    push(InactiveTurf(
                                        turf: turf!, user: widget.user));
                                  } else if (value == 3) {
                                    showConfirmationDialog();
                                    // push(EditTurfLocationScreen(turf: turf!));
                                  }
                                },
                              ),
                      ],
                    )
                  ],
                ),
                SizedBox(height: dW * 0.05),
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomContainer(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                  title: 'Venue Details',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  color: getThemeColor()),
                              SizedBox(height: dW * 0.05),
                              TextWidget(title: 'Name'),
                              CustomContainer(
                                margin: EdgeInsets.only(top: dW * 0.02),
                                vPadding: 0.03,
                                boxShadow: [],
                                borderColor: getThemeColor(),
                                child: TextWidget(
                                  title: turf!.name,
                                  fontSize: 14.5,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: dW * 0.045),
                              TextWidget(title: 'Phone No.'),
                              CustomContainer(
                                margin: EdgeInsets.only(top: dW * 0.02),
                                vPadding: 0.03,
                                boxShadow: [],
                                borderColor: getThemeColor(),
                                child: TextWidget(
                                  title: turf!.mobileNo,
                                  fontSize: 14.5,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: dW * 0.045),
                              if (turf != null &&
                                  turf!.description != null &&
                                  turf!.description!.isNotEmpty)
                                TextWidget(title: 'About Venue'),
                              if (turf != null &&
                                  turf!.description != null &&
                                  turf!.description!.isNotEmpty)
                                CustomContainer(
                                  margin: EdgeInsets.only(top: dW * 0.02),
                                  vPadding: 0.03,
                                  boxShadow: [],
                                  borderColor: getThemeColor(),
                                  child: TextWidget(
                                    title: turf!.description.toString(),
                                    fontSize: 14.5,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              // SizedBox(height: dW * 0.045),
          
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Address'),
                                  SizedBox(height: dW * 0.02),
                                  TextWidget(
                                    title: turf!.address.fullAddress
                                            .trim()
                                            .isEmpty
                                        ? '${turf!.address.area.isEmpty ? turf!.address.streetName : turf!.address.area}, ${turf!.address.landmark}, ${turf!.address.city}, ${turf!.address.state}, ${turf!.address.pincode}.'
                                        : '${turf!.address.fullAddress}',
                                    fontSize: 14.5,
                                    fontWeight: FontWeight.w500,
                                  )
                                ],
                              ),
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Sport Category'),
                                  SizedBox(height: dW * 0.02),
                                  TextWidget(
                                    title: turf!.sportCategory!.categoryName,
                                    fontSize: 14.5,
                                    fontWeight: FontWeight.w500,
                                  )
                                ],
                              ),
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Sport Type'),
                                  SizedBox(height: dW * 0.02),
                                  Wrap(
                                    crossAxisAlignment: WrapCrossAlignment.start,
                                    runAlignment: WrapAlignment.spaceBetween,
                                    children: [
                                      ...turf!.sportsType!.map(
                                        (sport) => CustomContainer(
                                          width: sport.sport == 'Badminton'
                                              ? dW * 0.35
                                              : dW * 0.32,
                                          boxShadow: [],
                                          borderColor: getThemeColor(),
                                          vPadding: 0.02,
                                          hPadding: 0.02,
                                          radius: 8,
                                          margin: EdgeInsets.only(
                                            bottom: dW * 0.03,
                                            right: dW * 0.03,
                                          ),
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              CachedNetworkImage(
                                                repeat: ImageRepeat.repeat,
                                                fit: BoxFit.cover,
                                                height: 30,
                                                imageUrl: sport.image,
                                                placeholder: (_, __) =>
                                                    Image.asset(
                                                  'assets/images/placeholder.jpg',
                                                  fit: BoxFit.cover,
                                                  height: 30,
                                                ),
                                              ),
                                              // Image.network(sport.image,
                                              //     height: 30),
                                              SizedBox(width: dW * .02),
                                              ConstrainedBox(
                                                constraints: BoxConstraints(
                                                    maxWidth: dW * 0.18),
                                                child: FittedBox(
                                                  fit: BoxFit.scaleDown,
                                                  child: TextWidget(
                                                    title: sport.sport,
                                                    fontWeight: FontWeight.w500,
                                                    textAlign: TextAlign.left,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),
                        CustomContainer(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    title: 'Venue & Sports Configuration',
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                    color: getThemeColor(),
                                  ),
                                  SizedBox(height: dW * 0.05),
                                  TextWidget(title: 'Weekdays'),
                                  SizedBox(height: dW * 0.03),
                                  Wrap(
                                    children: [
                                      ...turf!.days!
                                          .asMap()
                                          .map(
                                            (i, day) => MapEntry(
                                              i,
                                              TextWidget(
                                                title:
                                                    '${getFullWeekDays(day)}${i == turf!.days!.length - 1 ? '' : ','} ',
                                                fontWeight: FontWeight.w500,
                                                textAlign: TextAlign.center,
                                                color: Color(0xff9798A3),
                                              ),
                                            ),
                                          )
                                          .values
                                          .toList()
                                    ],
                                  ),
                                  SizedBox(height: dW * 0.03),
                                  TextWidget(title: 'Weekends'),
                                  SizedBox(height: dW * 0.03),
                                  Wrap(
                                    children: [
                                      ...turf!.weekends!
                                          .asMap()
                                          .map(
                                            (i, day) => MapEntry(
                                              i,
                                              TextWidget(
                                                title:
                                                    '${getFullWeekDays(day)}${i == turf!.weekends!.length - 1 ? '' : ','} ',
                                                fontWeight: FontWeight.w500,
                                                textAlign: TextAlign.center,
                                                color: Color(0xff9798A3),
                                              ),
                                            ),
                                          )
                                          .values
                                          .toList()
                                    ],
                                  ),
                                ],
                              ),
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Time Slot Difference'),
                                  SizedBox(height: dW * 0.02),
                                  TextWidget(
                                    title: '${turf!.slotTimeDifference} minutes',
                                    fontSize: 14.5,
                                    fontWeight: FontWeight.w500,
                                  )
                                ],
                              ),
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Selected Slots'),
                                  SizedBox(height: dW * 0.03),
                                  ...turf!.slots!
                                      .asMap()
                                      .map(
                                        (i, slot) => MapEntry(
                                          i,
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              TextWidget(
                                                title:
                                                    '${i + 1}. ${slot.session} Slot',
                                                color: getThemeColor(),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              SizedBox(height: dW * 0.03),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  TextWidget(
                                                      title:
                                                          'Selected Slot Timing'),
                                                  SizedBox(height: dW * 0.03),
                                                  buildSlotTimingWidget(
                                                    dW: dW,
                                                    tS: tS,
                                                    title: 'Start Time :',
                                                    value:
                                                        '${slot.startTime} ${getTimePeriod(slot.startTime)}',
                                                  ),
                                                  SizedBox(height: dW * 0.05),
                                                  buildSlotTimingWidget(
                                                    dW: dW,
                                                    tS: tS,
                                                    title: 'End Time :',
                                                    value:
                                                        '${slot.endTime} ${getTimePeriod(slot.endTime)}',
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: dW * 0.05),
                                              TextWidget(
                                                title: turf!.sportCategory!
                                                            .categoryName !=
                                                        'Outdoor'
                                                    ? 'Selected Sport & Pricing'
                                                    : 'Selected Turf Sizes & Pricing',
                                                fontSize: 16,
                                                color: Color(0xff636363),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              SizedBox(height: dW * 0.01),
                                              TextWidget(
                                                title:
                                                    'Price for ${turf!.slotTimeDifference} minutes (Inclusive GST).',
                                                fontSize: 12,
                                                color: Color(0xff636363),
                                              ),
                                              SizedBox(height: dW * 0.03),
                                              CustomContainer(
                                                radius: 8,
                                                hPadding: 0.03,
                                                vPadding: 0.015,
                                                boxShadow: [],
                                                borderColor: getThemeColor(),
                                                child: Column(
                                                  children: [
                                                    SizedBox(height: dW * 0.03),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        ...[
                                                          turf!.sportCategory!
                                                                      .categoryName !=
                                                                  'Outdoor'
                                                                      'Outdoor'
                                                              ? 'Sport'
                                                              : 'Turf Size',
                                                          'Weekdays',
                                                          'Weekends'
                                                        ].map(
                                                          (type) => Container(
                                                            alignment:
                                                                Alignment.topLeft,
                                                            width: type ==
                                                                        'Weekends' ||
                                                                    type ==
                                                                        'Weekdays'
                                                                ? dW * 0.21
                                                                : dW * .24,
                                                            child: FittedBox(
                                                              fit: BoxFit
                                                                  .scaleDown,
                                                              child: TextWidget(
                                                                title: type,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(height: dW * 0.03),
                                                    ...slot.priceAndQuantity.map(
                                                      (sport) => Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          ...[1, 2, 3].map(
                                                            (index) => Container(
                                                              margin:
                                                                  EdgeInsets.only(
                                                                      bottom: dW *
                                                                          0.03),
                                                              alignment:
                                                                  index == 1
                                                                      ? Alignment
                                                                          .topLeft
                                                                      : Alignment
                                                                          .center,
                                                              width: index != 1
                                                                  ? dW * 0.21
                                                                  : dW * .24,
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      vertical:
                                                                          7),
                                                              decoration: index ==
                                                                      1
                                                                  ? null
                                                                  : BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius
                                                                              .circular(5),
                                                                      border: Border.all(
                                                                          color:
                                                                              getThemeColor()),
                                                                    ),
                                                              child: FittedBox(
                                                                fit: BoxFit
                                                                    .scaleDown,
                                                                child: TextWidget(
                                                                  title: index ==
                                                                          1
                                                                      ? sport
                                                                          .title
                                                                      : index == 2
                                                                          ? sport.price ==
                                                                                  0
                                                                              ? 'N.A'
                                                                              : '\u20b9 ${sport.price}'
                                                                          : sport.weekendPrice ==
                                                                                  0
                                                                              ? 'N.A'
                                                                              : '\u20b9 ${sport.weekendPrice}',
                                                                  color:
                                                                      getThemeColor(),
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: dW * 0.05)
                                            ],
                                          ),
                                        ),
                                      )
                                      .values
                                      .toList(),
                                  // SizedBox(height: dW * 0.03),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),
                        CustomContainer(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                title: 'Ownership & Policy',
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: getThemeColor(),
                              ),
                              SizedBox(height: dW * 0.05),
                              if (turf!.cancellationCharges.isNotEmpty)
                                Container(
                                  margin: EdgeInsets.only(bottom: dW * 0.03),
                                  child: TextWidget(
                                    title: 'Cancellation Charges',
                                    color: Colors.black,
                                  ),
                                ),
                              if (turf!.cancellationCharges.isNotEmpty)
                                ...turf!.cancellationCharges
                                    .asMap()
                                    .map(
                                      (i, data) => MapEntry(
                                        i,
                                        CancellationWidget(
                                          data: {
                                            'start': data.start.toString(),
                                            'end': data.end.toString(),
                                            'percentage':
                                                data.percentage.toString(),
                                          },
                                          deviceWidth: dW,
                                          textScaleFactor: tS,
                                        ),
                                      ),
                                    )
                                    .values
                                    .toList(),
                              if (turf!.cancellationPolicy.isNotEmpty) ...[
                                Container(
                                  margin: EdgeInsets.only(bottom: dW * 0.03),
                                  child: TextWidget(
                                    title: 'Cancellation Policy',
                                    color: Colors.black,
                                  ),
                                ),
                                CustomContainer(
                                  margin: EdgeInsets.only(bottom: dW * 0.02),
                                  boxShadow: [],
                                  borderColor: getThemeColor(),
                                  vPadding: .03,
                                  child: Text(
                                    turf!.cancellationPolicy,
                                    style: TextStyle(
                                      fontSize: tS * 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),
                        CustomContainer(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                title: 'Venue Presentation',
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: getThemeColor(),
                              ),
                              // SizedBox(height: dW * 0.05),
                              if (turf!.availability != null)
                                Container(
                                  margin: EdgeInsets.only(top: dW * 0.05),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      TextWidget(
                                          title: 'Booking Availability Duration'),
                                      SizedBox(height: dW * 0.02),
                                      TextWidget(
                                        title:
                                            '${turf!.availability!.count} ${turf!.availability!.duration}',
                                        fontSize: 14.5,
                                        fontWeight: FontWeight.w500,
                                      )
                                    ],
                                  ),
                                ),
                              SizedBox(height: dW * 0.05),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    title: turf!.sportCategory!.categoryName !=
                                            'Outdoor'
                                        ? 'Quantity'
                                        : turf!.isNet!
                                            ? 'Turf Relation & Label'
                                            : 'Turf Quantity & Label',
                                  ),
                                  SizedBox(height: dW * 0.03),
                                  ...turfQuanity.map(
                                    (sport) => Row(
                                      children: [
                                        if (turf!.sportCategory!.categoryName ==
                                            'Outdoor')
                                          Container(
                                            margin: EdgeInsets.only(
                                                right: dW * 0.03,
                                                bottom: dW * 0.03),
                                            alignment: Alignment.center,
                                            width: dW * 0.2,
                                            padding: EdgeInsets.symmetric(
                                              vertical: dW * 0.025,
                                              horizontal: dW * 0.02,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  Theme.of(context).primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              border: Border.all(
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                            ),
                                            child: TextWidget(
                                              title: sport['title'],
                                              fontSize: turf!.sportCategory!
                                                          .categoryName ==
                                                      'Outdoor'
                                                  ? tS * 14
                                                  : tS * 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w600,
                                              textOverflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        SizedBox(width: dW * 0.035),
                                        if (turf!.sportCategory!.categoryName ==
                                            'Outdoor')
                                          Container(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.symmetric(
                                                vertical: dW * 0.02,
                                                horizontal: dW * 0.02),
                                            child: TextWidget(
                                              title:
                                                  'x   ${sport['quantity'] > 9 ? '' : '0'}${sport['quantity']} ${sport['label'] == '' ? '' : '(${sport['label']})'}',
                                              fontSize: tS * displayLarge,
                                              color: Colors.black,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          )
                                      ],
                                    ),
                                  ),
                                  if (turf!.sportCategory!.categoryName !=
                                      'Outdoor')
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: dW * 0.02,
                                          horizontal: dW * 0.02),
                                      child: TextWidget(
                                        title: '${turfQuanity.first['quantity']}',
                                        fontSize: tS * displayLarge,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    )
                                ],
                              ),
                              DividerWidget(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(title: 'Advance Amount'),
                                  SizedBox(height: dW * 0.02),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      TextWidget(
                                          title:
                                              turf!.sportCategory!.categoryName !=
                                                      'Outdoor'
                                                  ? 'Sport'
                                                  : 'Turf Size',
                                          color: Color(0xff636363)),
                                      TextWidget(
                                          title: 'Advance Amount',
                                          color: Color(0xff636363)),
                                    ],
                                  ),
                                  SizedBox(height: dW * 0.03),
                                  ...turfQuanity.map(
                                    (sport) => Padding(
                                      padding: EdgeInsets.only(bottom: dW * 0.04),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          ConstrainedBox(
                                            constraints: BoxConstraints(
                                                maxWidth: dW * 0.4),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: TextWidget(
                                                title: sport['title'],
                                                color: getThemeColor(),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                          CustomContainer(
                                            width: dW * 0.31,
                                            boxShadow: [],
                                            vPadding: 0.025,
                                            radius: 7,
                                            borderColor: getThemeColor(),
                                            child: TextWidget(
                                              title:
                                                  '\u20b9 ${sport['advanceAmount']}',
                                              color: getThemeColor(),
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              DividerWidget(),
                              TextWidget(title: 'Amenities'),
                              SizedBox(height: dW * 0.03),
                              ...turf!.facilities!.map(
                                (facility) => Padding(
                                  padding: EdgeInsets.only(bottom: dW * 0.035),
                                  child: Row(
                                    children: [
                                      OldCheckBoxWidget(dW, true),
                                      SizedBox(width: dW * 0.035),
                                      TextWidget(
                                        title: facility,
                                        fontWeight: FontWeight.w500,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              DividerWidget(),
                              TextWidget(title: 'Venue Images'),
                              SizedBox(height: dW * 0.03),
                              ...turf!.images!.map(
                                (image) => GestureDetector(
                                  onTap: () => push(OpenMediaFullScreen(
                                    type: 'Image',
                                    isLocal: !image.contains('https'),
                                    url: image,
                                  )),
                                  child: Container(
                                    margin: EdgeInsets.only(bottom: dW * 0.035),
                                    child: Container(
                                      width: dW,
                                      height: dW * 0.45,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border:
                                            Border.all(color: getThemeColor()),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: image.contains('https')
                                            ? safeNetworkImage(
                                                imageUrl: image,
                                                fit: BoxFit.cover,
                                                placeholder: Image.asset(
                                                  'assets/images/placeholder.jpg',
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            : safeLoadImage(image, fit: BoxFit.cover),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (turf!.video != null &&
                                  turf!.video!.url != '') ...[
                                SizedBox(height: dW * 0.03),
                                TextWidget(title: 'Venue Video'),
                                SizedBox(height: dW * 0.03),
                                GestureDetector(
                                  onTap: () {
                                    push(
                                      OpenMediaFullScreen(
                                        type: 'Video',
                                        isLocal:
                                            !turf!.video!.url.contains('https'),
                                        url: turf!.video!.url,
                                      ),
                                    );
                                  },
                                  child: Stack(
                                    clipBehavior: Clip.none,
                                    alignment: Alignment.center,
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: turf!.video!.thumbnail
                                                .contains('https')
                                            ? Image.network(
                                                turf!.video!.thumbnail,
                                                width: dW,
                                                height: dW * 0.5,
                                                fit: BoxFit.cover,
                                              )
                                            : Image.file(
                                                File(turf!.video!.thumbnail),
                                                width: dW,
                                                height: dW * 0.5,
                                                fit: BoxFit.cover,
                                              ),
                                      ),
                                      Icon(
                                        Icons.play_circle_outline_rounded,
                                        color: Colors.white,
                                        size: dW * 0.1,
                                      ),
                                    ],
                                  ),
                                ),
                              ]
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.15),
                      ],
                    ),
                  ),
                )
              ],
            ),
        );
  }
}

Widget buildSlotTimingWidget({
  required double dW,
  required double tS,
  required String title,
  required String value,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        children: [
          Icon(Icons.watch_later_outlined, color: getThemeColor()),
          SizedBox(width: dW * 0.02),
          TextWidget(
            title: title,
            fontSize: tS * displayLarge,
            color: Colors.black,
            fontWeight: FontWeight.w400,
          ),
        ],
      ),
      Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 7),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: getThemeColor()),
        ),
        constraints: BoxConstraints(maxWidth: dW * 0.35),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: TextWidget(
            title: value,
            fontWeight: FontWeight.w500,
          ),
        ),
      )
    ],
  );
}
