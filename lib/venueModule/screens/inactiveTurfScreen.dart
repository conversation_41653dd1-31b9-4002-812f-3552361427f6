import 'dart:io';

import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/dialogBox.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../../venueModule/widgets/inactiveBottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/andoridAppBar.dart';

class InactiveTurf extends StatefulWidget {
  final Venue turf;
  final UserModal user;
  const InactiveTurf({
    Key? key,
    required this.turf,
    required this.user,
  }) : super(key: key);

  @override
  _InactiveTurfState createState() => _InactiveTurfState();
}

class _InactiveTurfState extends State<InactiveTurf> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  inactiveBottomSheet() {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: InactiveBottomSheet(
          deviceWidth: deviceWidth,
          deviceHeight: deviceHeight,
          textScaleFactor: textScaleFactor,
          user: widget.user,
          turf: widget.turf,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        setState(() {
          widget.turf.pauseDates = value;
          sortDate();
        });
      }
    });
  }

  showDateDialog(textScaleFactor, date, int index) {
    dialogBoxCustom(
      context: context,
      cancelBtnPress: () {
        Navigator.of(context).pop(false);
      },
      cancelBtnText: 'No',
      okBtnPress: () async {
        Navigator.of(context).pop(true);
        widget.turf.pauseDates!.removeAt(index);
        sortDate();
        setState(() {});
        await Provider.of<TurfProvider>(context, listen: false)
            .pauseAndUnPauseTurf(
          turf: widget.turf,
          accessToken: widget.user.accessToken,
          action: 'Unpause',
          dates: [date],
        );
      },
      okBtnText: 'Yes',
      platform: TargetPlatform.android,
      title: 'Remove',
      content: 'Are you sure you want to remove this date?',
      titleStyle: TextStyle(
        fontSize: textScaleFactor * 15.5,
        fontWeight: FontWeight.w600,
        color: Colors.black,
      ),
      contentStyle: TextStyle(
        fontSize: textScaleFactor * 14.5,
        color: Colors.black,
      ),
      cancelBtnStyle: TextStyle(
        fontSize: textScaleFactor * 15,
        color: Colors.black,
        fontWeight: FontWeight.w600,
      ),
      okBtnStyle: TextStyle(
        fontSize: textScaleFactor * 15,
        color: Colors.grey,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    sortDate();
  }

  sortDate() {
    widget.turf.pauseDates!.sort((a, b) {
      return a['start'].compareTo(b['start']);
    });
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
        child: CustomButton(
          width: deviceWidth * 0.4,
          height: deviceWidth * 0.12,
          buttonText: 'Set Inactive Dates',
          onPressed: inactiveBottomSheet,
        ),
      ),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: deviceHeight,
      width: deviceWidth,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
        child: Column(
          children: [
            // SizedBox(height: deviceWidth * 0.05),
            NewAppBar(dW: deviceWidth, title: 'Inactive Dates'),
            SizedBox(height: deviceWidth * 0.03),
            Expanded(
              child: widget.turf.pauseDates!.length == 0
                  ? EmptyListWidget(
                      text: 'Inactive dates not found.',
                      topPadding: 0.8,
                    )
                  : SingleChildScrollView(
                      padding:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                      physics: BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: deviceWidth * 0.02),
                          ...widget.turf.pauseDates!
                              .asMap()
                              .map(
                                (index, date) => MapEntry(
                                  index,
                                  Container(
                                    child: Card(
                                      elevation: 2,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      margin: EdgeInsets.only(
                                        bottom: deviceWidth * 0.03,
                                      ),
                                      child: ListTile(
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: deviceWidth * 0.04),
                                        title: Text(
                                          '${DateFormat('dd MMM yyyy').format(DateTime.parse(date['start']))} - ${DateFormat('hh:mm a').format(DateTime.parse(date['start']))} to ${DateFormat('hh:mm a').format(DateTime.parse(date['end']))}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .displayLarge!
                                              .copyWith(
                                                fontSize: textScaleFactor * 13,
                                                color: Colors.black,
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                        trailing: GestureDetector(
                                          onTap: () => showDateDialog(
                                              textScaleFactor, date, index),
                                          child: Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                          SizedBox(height: deviceWidth * 0.18),
                        ],
                      ),
                    ),
            )
          ],
        ),
      ),
    );
  }
}
