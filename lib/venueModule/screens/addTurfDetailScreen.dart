import 'dart:io';

import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:video_compress/video_compress.dart';

import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../navigators.dart';
import '../../new_colors.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../widgets/venueDetailsWidget/new_step1_widget.dart';
import '../widgets/venueDetailsWidget/new_step2_widget.dart';
import '../widgets/venueDetailsWidget/new_step3_widget.dart';
import '../widgets/venueDetailsWidget/new_step4_widget.dart';
import '../widgets/venueDetailsWidget/new_step5_widget.dart';
import '../widgets/venueDetailsWidget/new_step6_widget.dart';
import '../models/venue_model.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../venueModule/screens/turfDetailSummaryScreen.dart';

class AddTurfDetailScreen extends StatefulWidget {
  final Venue turf;
  const AddTurfDetailScreen({
    Key? key,
    required this.turf,
  }) : super(key: key);

  @override
  _AddTurfDetailScreenState createState() => _AddTurfDetailScreenState();
}

class _AddTurfDetailScreenState extends State<AddTurfDetailScreen> {
  bool isLoading = false;
  //Step2
  var fileMedia1;
  var fileMedia2;
  var fileMedia3;
  var fileMedia4;
  var fileMedia5;

  String imagePath1 = '';
  String imagePath2 = '';
  String imagePath3 = '';
  String imagePath4 = '';
  String imagePath5 = '';

  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isCompressingVideo = false;

  ScrollController controller = ScrollController();

  List<String> selectedImagePaths = ['', ''];

  String videoPath = '';
  String videoThumbnail = '';

  pickAndSetImage(ImageSource source, int index) async {
    pop(true);
    XFile? pickedFile = await pickCropImage(imageSource: source);
    if (pickedFile != null) {
      var bytes = await File(pickedFile.path).readAsBytes();
      final kb = (bytes.length) / 1024;
      final size = kb / 1024;

      if (size > 5) {
        return showSnackbar('Image size should be less than 5MB');
      }
      final jpegData =
          await convertToJpeg(File(pickedFile.path), pickedFile.name);
      selectedImagePaths[index] = jpegData.path;
      setState(() {});
    }
  }

  imagePickerBottomSheet(int index) {
    if (index > selectedImagePaths.length - 1) {
      selectedImagePaths.add('');
      setState(() {});
    }
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              pop(false);
              return true;
            },
            child: SizedBox(
              height: deviceHeight * .2,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: deviceWidth * .05),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: deviceWidth * .02),
                    const Text(
                      'Select Photo from',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: deviceWidth * .03),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (selectedImagePaths[index] != '') ...[
                          GestureDetector(
                            onTap: () {
                              selectedImagePaths[index] = '';
                              setState(() {});
                              pop(true);
                            },
                            child: Column(
                              children: [
                                CircleAvatar(
                                  radius: deviceWidth * .08,
                                  backgroundColor: Colors.grey.shade300,
                                  child: Icon(
                                    Icons.delete,
                                    color: getRedColor1(context),
                                  ),
                                ),
                                SizedBox(height: deviceWidth * .02),
                                const Text('Remove '),
                                const Text('Photo')
                              ],
                            ),
                          ),
                          SizedBox(width: deviceWidth * .05),
                        ],
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.gallery, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: deviceWidth * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.image,
                                  color: Colors.purple,
                                ),
                              ),
                              SizedBox(height: deviceWidth * .02),
                              const Text('Gallery')
                            ],
                          ),
                        ),
                        SizedBox(width: deviceWidth * .05),
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.camera, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: deviceWidth * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.camera_alt_rounded,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: deviceWidth * .02),
                              const Text('Camera')
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        }).then((value) {
      if (value != null && !value && index >= 2) {
        selectedImagePaths.removeAt(index);
        setState(() {});
      }
    });
  }

  removeSelectedOrVideo(int index, {bool isImage = true}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      if (index < 2) {
        selectedImagePaths[index] = '';
      } else {
        selectedImagePaths.removeAt(index);
      }
    }
    setState(() {});
  }

  videoPickerBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          height: deviceHeight * .2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: deviceWidth * .05),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * .02),
                const Text(
                  'Profile Video',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: deviceWidth * .02),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    if (videoPath != '') ...[
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            videoPath = '';
                            videoThumbnail = '';
                          });
                          pop();
                        },
                        child: Column(
                          children: [
                            CircleAvatar(
                              radius: deviceWidth * .08,
                              backgroundColor: Colors.grey.shade300,
                              child: Icon(
                                Icons.delete,
                                color: getRedColor1(context),
                              ),
                            ),
                            SizedBox(height: deviceWidth * .02),
                            const Text('Remove '),
                            const Text('Video')
                          ],
                        ),
                      ),
                      SizedBox(width: deviceWidth * .05),
                    ],
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.gallery),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: deviceWidth * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.video_library,
                              color: Colors.purple,
                            ),
                          ),
                          SizedBox(height: deviceWidth * .02),
                          const Text('Gallery')
                        ],
                      ),
                    ),
                    SizedBox(width: deviceWidth * .05),
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.camera),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: deviceWidth * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.videocam,
                              color: Colors.blue,
                            ),
                          ),
                          SizedBox(height: deviceWidth * .02),
                          const Text('Camera')
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  pickVideo(ImageSource source) async {
    try {
      ImagePicker picker = ImagePicker();
      XFile? pickedFile = await picker.pickVideo(
        source: source,
        maxDuration: const Duration(seconds: 30),
      );

      if (pickedFile != null) {
        setVideoData(pickedFile.path);
      }
      pop();
    } catch (e) {
      print(e);
    }
  }

  setVideoData(String path) async {
    try {
      setState(() => isCompressingVideo = true);
      final videoInfo = FlutterVideoInfo();

      File selectedFile = File(path);

      String videoFilePath = selectedFile.path;
      var info = await videoInfo.getVideoInfo(videoFilePath);

      MediaInfo? compressedVideo = await VideoCompress.compressVideo(path);

      if (info != null) {
        double size = (info.filesize! / (1024 * 1024));

        if (size > 5) {
          showSnackbar('File Size should be less than 5MB');
          return;
        }

        if (info.duration == null ||
            info.width == null ||
            info.height == null) {
          return showSnackbar('Cannot upload this video');
        }
        var seconds = info.duration! / 1000;
        var maxDuration = 30;
        if (seconds > maxDuration) {
          return showSnackbar('Video must be less than or equal to 30 seconds');
        } else {
          var mediaPath = Uri.decodeComponent(selectedFile.path);
          File thumbnailData = await VideoCompress.getFileThumbnail(mediaPath,
              quality: 100, // default(100)
              position: -1 // default(-1)
              );
          videoThumbnail = thumbnailData.path;
          if (videoThumbnail == '') {
            showSnackbar('Cannot upload this video');
          }

          videoPath = compressedVideo?.path ?? mediaPath;

          if (compressedVideo != null) {
            videoPath = compressedVideo.path ?? mediaPath;
          } else {
            videoPath = mediaPath;
          }
          setState(() {});
        }
      } else {
        return showSnackbar('Cannot upload this video');
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() => isCompressingVideo = false);
    }
  }

  //Step3
  TextEditingController aboutTurfController = TextEditingController();
  TextEditingController advanceAmountController = TextEditingController();
  TextEditingController advanceAmountForOneOnController =
      TextEditingController();

  //Cancellation Charges
  List cancellationCharges = [];
  int durationCount = 1;
  String period = 'Year';
  TextEditingController cancellationController = TextEditingController();

//Step1
  TimeOfDay startTime = TimeOfDay(hour: 05, minute: 00);
  TimeOfDay endTime = TimeOfDay(hour: 23, minute: 00);
  TextEditingController venueNameController = TextEditingController();
  List selectedSports = [];
  List selectedWeekDays = [];
  List selectedWeekEnds = [];
  List selectedTurfSize = [];
  List selectedTurfSizeWithSlots = [];
  List selectedSlots = [];
  List turfSize = [];
  List sportQuantity = [];
  List selectedFacilities = [];
  List clickedImages = [];
  int selectedSlotDuration = 30;
  int step = 1;
  bool step1Completed = false;
  bool isNet = false;
  String option = 'Non Netting';
  bool usePreviousValueForPrice = false;

  List listOfTurfOption = [
    {
      'title': 'Netting',
      'isSelected': false,
      'id': '1',
      'isNet': true,
      'label': "Turf with netting system",
    },
    {
      'title': 'Non Netting',
      'isSelected': true,
      'id': '2',
      'isNet': false,
      'label': "Turf without Net",
    },
    {
      'title': 'One On',
      'isSelected': false,
      'id': '3',
      'isNet': true,
      'label': "Turf with Cricket Net",
    },
  ];
  List listOfFacilities = [
    {
      'title': 'Washroom',
      'isSelected': false,
      'id': 'F1',
    },
    {
      'title': 'Cafe & Food court',
      'isSelected': false,
      'id': 'F2',
    },
    {
      'title': 'Power Backup',
      'isSelected': false,
      'id': 'F3',
    },
    {
      'title': 'Changing Room',
      'isSelected': false,
      'id': 'F4',
    },
    {
      'title': 'First Aid',
      'isSelected': false,
      'id': 'F5',
    },
    {
      'title': 'Parking',
      'isSelected': false,
      'id': 'F6',
    },
  ];

  List listOfSports = [];
  List listofSportCategory = [];
  Map selectedSportCategory = {};

  addOrRemoveCancellation({
    required Map data,
    required int index,
    required bool add,
  }) {
    if (add) {
      cancellationCharges.add(data);
    } else {
      cancellationCharges.removeAt(index);
    }
    setState(() {});
  }

  changeDuration(String duration, value) {
    if (duration == 'Period') {
      period = value;
      if (value == 'Days') {
        durationCount = 7;
      } else {
        durationCount = 1;
      }
    } else if (duration == 'Count') {
      durationCount = value;
    }
    setState(() {});
  }

  editCancellation({required Map data, required int index}) {
    cancellationCharges[index]['start'] = data['start'];
    cancellationCharges[index]['end'] = data['end'];
    cancellationCharges[index]['percentage'] = data['percentage'];
    setState(() {});
  }

  selectSportCategory(String id) {
    listofSportCategory.forEach((cat) {
      if (cat['id'] == id) {
        selectedSportCategory = {
          'categoryId': cat['id'],
          'categoryName': cat['title'],
        };
        cat['isSelected'] = true;
      } else {
        cat['isSelected'] = false;
      }
    });
    setState(() {
      selectedSports = [];
      listOfSports.forEach((sport) {
        sport['isSelected'] = false;
      });
    });
  }

  List listOfSlots = [
    // {
    //   'title': 'Midnight',
    //   'isSelected': false,
    //   'id': 'Slot6',
    //   'startTime': TimeOfDay(hour: 00, minute: 00),
    //   'endTime': TimeOfDay(hour: 05, minute: 0),
    //   'priceAndQuantity': [],
    // },
    {
      'title': 'Morning',
      'isSelected': false,
      'id': 'Slot1',
      'startTime': TimeOfDay(hour: 5, minute: 00),
      'endTime': TimeOfDay(hour: 12, minute: 0),
      'priceAndQuantity': [],
    },
    {
      'title': 'Afternoon',
      'isSelected': false,
      'id': 'Slot2',
      'startTime': TimeOfDay(hour: 12, minute: 00),
      'endTime': TimeOfDay(hour: 16, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Evening',
      'isSelected': false,
      'id': 'Slot3',
      'startTime': TimeOfDay(hour: 16, minute: 00),
      'endTime': TimeOfDay(hour: 20, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Night',
      'isSelected': false,
      'id': 'Slot4',
      'startTime': TimeOfDay(hour: 20, minute: 00),
      'endTime': TimeOfDay(hour: 24, minute: 00),
      'priceAndQuantity': [],
    },
  ];
  List listOfDays = [
    {
      'title': 'Mon',
      'isSelected': false,
      'id': 'D1',
    },
    {
      'title': 'Tue',
      'isSelected': false,
      'id': 'D2',
    },
    {
      'title': 'Wed',
      'isSelected': false,
      'id': 'D3',
    },
    {
      'title': 'Thu',
      'isSelected': false,
      'id': 'D4',
    },
    {
      'title': 'Fri',
      'isSelected': false,
      'id': 'D5',
    },
    {
      'title': 'Sat',
      'isSelected': false,
      'id': 'D6',
    },
    {
      'title': 'Sun',
      'isSelected': false,
      'id': 'D7',
    },
  ];

  setTurfOption(String title) {
    usePreviousValueForPrice = false;
    selectedTurfSize = [];
    listOfTurfSizeAndPrice.forEach((element) {
      element['isSelected'] = false;
    });
    if (title == 'One On') {
      selectedTurfSize.add('1:1');
      listOfTurfSizeAndPrice.forEach((size) {
        if (size['title'] == '1:1') {
          size['isSelected'] = true;
        }
      });
    }
    listOfTurfOption.forEach((data) {
      if (data['title'] == title) {
        setState(() {
          data['isSelected'] = true;
          option = data['title'];
          isNet = data['isNet'];
        });
      } else {
        setState(() {
          data['isSelected'] = false;
        });
      }
    });
  }

  List listOfTurfSizeAndPrice = [
    {
      'title': '11:11',
      'price': 0,
      'isSelected': false,
      'id': 'S11',
    },
    {
      'title': '9:9',
      'price': 0,
      'isSelected': false,
      'id': 'S4',
    },
    {
      'title': '8:8',
      'price': 0,
      'isSelected': false,
      'id': 'S3',
    },
    {
      'title': '7:7',
      'price': 0,
      'isSelected': false,
      'id': 'S6',
    },
    {
      'title': '6:6',
      'price': 0,
      'isSelected': false,
      'id': 'S2',
    },
    {
      'title': '5:5',
      'price': 0,
      'isSelected': false,
      'id': 'S1',
    },
    {
      'title': '1:1',
      'price': 0,
      'isSelected': false,
      'id': 'S12',
    },
  ];

  List listofSlotTime = [
    {
      'slot': 30,
      'isSelected': false,
      'id': 'ST1',
    },
    {
      'slot': 60,
      'isSelected': false,
      'id': 'ST2',
    },
  ];

  setIsNetValue(bool value) {
    setState(() {
      isNet = value;
      usePreviousValueForPrice = false;
      selectedTurfSize = [];
      listOfTurfSizeAndPrice.forEach((element) {
        element['isSelected'] = false;
      });
      // selectedSports = [];
      // listOfSports.forEach((sport) {
      //   sport['isSelected'] = false;
      // });
    });
  }

  setListOfSlots() {
    selectedSlots.forEach((element) {
      element['priceAndQuantity'] = [];
      usePreviousValueForPrice = false;
    });
    if (selectedSportCategory['categoryName'] != 'Outdoor') {
      selectedSlots.forEach((slot) {
        selectedSports.forEach((sport) {
          slot['priceAndQuantity'].add({
            'title': sport,
            'quantity': 0,
            'price': 0,
            'id': '${slot['title']}$sport',
            'weekendPrice': 0,
            'isSelected': false,
            'controller': TextEditingController(),
            'weekendController': TextEditingController(),
          });
        });
      });
    } else {
      turfSize = [];
      // if (option == 'One On') {
      //   int index = selectedTurfSize.indexWhere((data) => data == '1:1');
      //   if (index == -1) {
      //     selectedTurfSize.add('1:1');
      //   }
      // }
      selectedSlots.forEach((slot) {
        selectedTurfSize.forEach((size) {
          slot['priceAndQuantity'].add(
            {
              'title': size,
              'price': 0,
              'weekendPrice': 0,
              'isSelected': false,
              'id': '${slot['title']}$size',
              'controller': TextEditingController(),
              'weekendController': TextEditingController(),
            },
          );
        });
      });
    }
    selectedSlots
        .sort((a, b) => a["startTime"].hour.compareTo(b["startTime"].hour));
  }

  setUsePreviousValueForPrice() {
    setState(() {
      usePreviousValueForPrice = false;
    });
  }

  goToReviewScreen() {
    if (cancellationController.text.trim().isEmpty) {
      return showSnackbar('Enter Cancellation Policy');
    }

    List selectedImages = [];

    selectedImagePaths.forEach((image) {
      if (image != '') selectedImages.add(image);
    });

    push(
      TurfDetailReviewScreen(
        selectedSports: selectedSports,
        selectedDays: selectedWeekDays,
        selectedTurfSizeAndPrice: selectedSlots,
        startTime: startTime,
        endTime: endTime,
        timeSlotDifference: selectedSlotDuration,
        listOfImages: selectedImages,
        selectedFacilities: selectedFacilities,
        venueDescription: aboutTurfController.text.trim(),
        venueName: venueNameController.text.trim(),
        advanceAmount: 0,
        advanceAmountForOneOn: 0,
        turf: widget.turf,
        isNet: isNet,
        sportQuantity: sportQuantity,
        selectedSportCategory: selectedSportCategory,
        option: option,
        durationCount: durationCount,
        period: period,
        cancellationCharges: cancellationCharges,
        cancellationPolicy: cancellationController.text.trim(),
        selectedWeekends: selectedWeekEnds,
        videoPath: videoPath,
        videoThumbnail: videoThumbnail,
      ),
    ).then((value) {
      if (value) pop(true);
    });
  }

  selectAndUnselectDays({required bool isWeekdays, required days}) {
    try {
      if (isWeekdays) {
        selectedWeekDays = [...days];
      } else {
        selectedWeekEnds = [...days];
      }
      setState(() {});
    } catch (e) {
      print(e);
    }
  }

  selectAndUnSelectType(
    String id,
    bool isSelected,
    List listToIterate,
    List listToSet,
    bool turfSize,
  ) {
    listToIterate.forEach((data) {
      if (data['id'] == id) {
        if (isSelected) {
          setState(() {
            data['isSelected'] = false;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.removeWhere((value) => value['title'] == data['title']);
          } else {
            if (selectedSportCategory['categoryName'] != 'Outdoor') {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.removeWhere((value) => value == data['title']);
          }
          return;
        } else {
          setState(() {
            data['isSelected'] = true;
          });
          if (turfSize) {
            setState(() {
              usePreviousValueForPrice = false;
            });
            listToSet.add(data);
          } else {
            if (selectedSportCategory['categoryName'] != 'Outdoor') {
              setState(() {
                usePreviousValueForPrice = false;
              });
            }
            listToSet.add(data['title']);
          }
          return;
        }
      }
    });
  }

  setQuantity(List quantity) {
    if (!usePreviousValueForPrice) {
      Future.delayed(Duration(microseconds: 0)).then((value) {
        sportQuantity = quantity;
        setState(() {});
      });
    }
  }

  addOrRemovePrice(List listToIterate, String sizeId, bool isSelected) {
    listToIterate.forEach((slot) {
      if (isSelected) {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = true;
            final index = turfSize.indexWhere((turf) => turf == slot['title']);
            if (index == -1) {
              turfSize.add(slot['title']);
            }
          });
        }
      } else {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = false;
            slot['price'] = 0;
            slot['controller'] = TextEditingController();
            slot['weekendController'] = TextEditingController();
            turfSize.removeWhere((turf) => turf == slot['title']);
          });
        }
      }
    });
  }

  addOrRemoveSport(List listToIterate, String sizeId, bool isSelected) {
    listToIterate.forEach((slot) {
      if (isSelected) {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = true;
          });
        }
      } else {
        if (slot['id'] == sizeId) {
          setState(() {
            slot['isSelected'] = false;
            slot['price'] = 0;
            slot['controller'] = TextEditingController();
            slot['weekendController'] = TextEditingController();
            turfSize.removeWhere((turf) => turf == slot['title']);
          });
        }
      }
    });
  }

  selectSlotDuration(id) {
    listofSlotTime.forEach((slot) {
      if (slot['id'] == id) {
        setState(() {
          slot['isSelected'] = true;
          selectedSlotDuration = slot['slot'];
        });
      } else {
        setState(() {
          slot['isSelected'] = false;
        });
      }
    });
  }

  addOrRemoveImages(bool add, String imagePath) {
    if (add) {
      clickedImages.add(imagePath);
      print(clickedImages);
    } else {
      clickedImages.removeWhere((image) => image == imagePath);
      print(clickedImages);
    }
  }

  selectTime(bool isStartTime, String slotId, startTime) async {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onChanged: (date) {
        updateTime(date, isStartTime, slotId);
      },
      onConfirm: (date) {
        updateTime(date, isStartTime, slotId);
      },
      currentTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        startTime.hour,
        startTime.minute,
      ),
      showSecondsColumn: false,
    );
  }

  updateTime(value, isStartTime, slotId) {
    if (value != null) {
      selectedSlots.forEach((slot) {
        if (slot['id'] == slotId) {
          setState(() {
            if (isStartTime) {
              slot['startTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            } else {
              slot['endTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            }
          });
        }
      });
    }
  }

  incrementAndDecrementStep(bool increment) {
    if (increment) {
      setState(() {
        step += 1;
        step1Completed = true;
      });
    } else {
      setState(() {
        step -= 1;
      });
    }
  }

  fetchSports() async {
    try {
      if (Provider.of<Auth>(context, listen: false).listOfSports.length == 0) {
        setState(() {
          isLoading = true;
        });
        await Provider.of<Auth>(context, listen: false).fetchSport();
        listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
        listofSportCategory =
            Provider.of<Auth>(context, listen: false).listOfSportCategory;

        if (listofSportCategory.length > 0) {
          listofSportCategory[0]['isSelected'] = true;
          selectedSportCategory = {
            'categoryId': listofSportCategory[0]['id'],
            'categoryName': listofSportCategory[0]['title'],
          };
        }
        if (widget.turf.days != null && widget.turf.days!.length != 0) {
          myInit();
        }
        setState(() {
          isLoading = false;
        });
      } else {
        listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
        listofSportCategory =
            Provider.of<Auth>(context, listen: false).listOfSportCategory;
        listOfSports.forEach((sport) {
          sport['isSelected'] = false;
        });
        listofSportCategory.forEach((sport) {
          sport['isSelected'] = false;
        });
        if (listofSportCategory.length > 0) {
          listofSportCategory[0]['isSelected'] = true;
          selectedSportCategory = {
            'categoryId': listofSportCategory[0]['id'],
            'categoryName': listofSportCategory[0]['title'],
          };
        }
        if (widget.turf.days != null && widget.turf.days!.length != 0) {
          myInit();
        }
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  exitFunction(textScaleFactor) async {
    if (step > 1 || step1Completed) {
      return showDialog(
        context: context,
        builder: ((context) => CustomDialog(
              title:
                  'Are you sure you want to exit. All the entered information will be lost.',
              noText: 'Exit',
              yesText: 'Cancel',
              noFunction: () {
                pop(true);
                pop(true);
              },
              yesFunction: () => pop(),
            )),
      );
    } else {
      pop(true);
    }
  }

  step1Validation() {
    if (venueNameController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter venue name');
    } else if (aboutTurfController.text.trimLeft().isEmpty) {
      return showSnackbar('Enter about venue');
    } else if (selectedSports.length == 0) {
      showSnackbar('Select Sports');
      return;
    } else {
      incrementAndDecrementStep(true);
    }
  }

  step2Validation() {
    if (selectedWeekDays.length == 0) {
      showSnackbar('Select Weekdays');
      return;
    } else if (selectedWeekEnds.length == 0) {
      showSnackbar('Select Weekends');
      return;
    } else if (selectedSlots.length == 0) {
      showSnackbar('Select slots');
      return;
    } else if (selectedSlotDuration == 0) {
      showSnackbar('Select slot timing');
      return;
    } else if (selectedTurfSize.length == 0 &&
        selectedSportCategory['categoryName'] == 'Outdoor') {
      showSnackbar('Select turf size');
      return;
    } else {
      incrementAndDecrementStep(true);
      setListOfSlots();
    }
  }

  step3Validation() {
    incrementAndDecrementStep(true);
  }

  step4Validation() {
    int index = selectedImagePaths.indexWhere((image) => image.trim() != '');

    if (index == -1) {
      return showSnackbar('You have to add atleast 1 image');
    }
    setQuantity([]);
    incrementAndDecrementStep(true);
  }

  step5Validation() {
    if (selectedFacilities.length == 0) {
      showSnackbar('Select available facilities');
      return;
    } else if (aboutTurfController.text.length == 0) {
      showSnackbar('Write venue description');
      return;
    } else {
      for (var i = 0; i < sportQuantity.length; i++) {
        if (sportQuantity[i]['quantity'] == 0) {
          return showSnackbar(
            selectedSportCategory['categoryName'] != 'Outdoor'
                ? 'Please enter the turf Quantity'
                : isNet
                    ? 'Please enter the turf relation'
                    : 'Please enter the turf quantity',
          );
        } else if (sportQuantity[i]['advanceAmount'].text.isEmpty) {
          return showSnackbar(
              'Please enter advance amount for ${sportQuantity[i]['title'] ?? ''}');
        }
      }
      incrementAndDecrementStep(true);
    }
  }

  onSubmit() {
    controller.jumpTo(0);
    hideKeyBoard(context);
    if (step == 1) {
      step1Validation();
    } else if (step == 2) {
      step2Validation();
    } else if (step == 3) {
      step3Validation();
    } else if (step == 4) {
      step4Validation();
    } else if (step == 5) {
      step5Validation();
    } else if (step == 6) {
      goToReviewScreen();
    }
  }

  myInit() {
    widget.turf.sportsType!.forEach((sport) {
      usePreviousValueForPrice = true;
      listOfSports.forEach((list) {
        if (sport.id == list['id']) {
          list['isSelected'] = true;
          selectedSports.add(list['title']);
        }
      });
    });

    selectedSportCategory = {
      'categoryId': widget.turf.sportCategory!.id,
      'categoryName': widget.turf.sportCategory!.categoryName,
    };

    listofSportCategory.forEach((sport) {
      if (sport['id'] == widget.turf.sportCategory!.id) {
        sport['isSelected'] = true;
      } else {
        sport['isSelected'] = false;
      }
    });

    isNet = widget.turf.isNet!;
    // widget.turf.days!.forEach((day) {
    //   listOfDays.forEach((list) {
    //     if (day == list['title']) {
    //       list['isSelected'] = true;
    //       selectedWeekDays.add(list['title']);
    //     }
    //   });
    // });
    selectedWeekDays = widget.turf.days!.toList();
    selectedWeekEnds = widget.turf.weekends!.toList();
    widget.turf.days!.forEach((day) {
      listOfDays.forEach((list) {
        if (day == list['title']) {
          list['isSelected'] = true;
          selectedWeekDays.add(list['title']);
        }
      });
    });

    option = widget.turf.option!;
    listOfTurfOption.forEach((opt) {
      if (opt['title'] == option) {
        isNet = opt['isNet'];
        opt['isSelected'] = true;
      } else {
        opt['isSelected'] = false;
      }
    });

    selectedSlotDuration = widget.turf.slotTimeDifference == null
        ? 30
        : widget.turf.slotTimeDifference!.toInt();

    widget.turf.slots!.forEach((slot) {
      listOfSlots.forEach((element) {
        if (slot.session == element['title']) {
          element['isSelected'] = true;
          element['startTime'] = TimeOfDay(
            hour: int.parse(slot.startTime.toStringAsFixed(2).split('.')[0]),
            minute: int.parse(slot.startTime.toStringAsFixed(2).split('.')[1]),
          );
          element['endTime'] = TimeOfDay(
            hour: int.parse(slot.endTime.toStringAsFixed(2).split('.')[0]),
            minute: int.parse(slot.endTime.toStringAsFixed(2).split('.')[1]),
          );
          selectedSlots.add(element);
        }
      });
      slot.priceAndQuantity.forEach((size) {
        listOfTurfSizeAndPrice.forEach((e) {
          if (size.title == e['title']) {
            e['isSelected'] = true;
            e['price'] = size.price;
            e['weekendPrice'] = size.weekendPrice;
            var index = sportQuantity
                .indexWhere((value) => value['title'] == size.title);
            if (index == -1) {
              sportQuantity.add({
                'title': size.title,
                'quantity': size.quantity,
                'label': size.label,
              });
            }
          }
        });
      });
    });

    if (widget.turf.sportCategory!.categoryName == 'Outdoor') {
      listOfTurfSizeAndPrice.forEach((e) {
        if (e['isSelected']) {
          selectedTurfSize.add(e['title']);
        }
      });
      turfSize = selectedTurfSize;

      selectedSlots.forEach((slot) {
        selectedTurfSize.forEach((size) {
          slot['priceAndQuantity'].add(
            {
              'title': size,
              'price': 0,
              'weekendPrice': 0,
              'isSelected': true,
              'id': '${slot['title']}$size',
            },
          );
        });
      });
      selectedSlots.forEach((element) {
        element['priceAndQuantity'].forEach((size) {
          listOfTurfSizeAndPrice.forEach((price) {
            TextEditingController controller = TextEditingController();
            TextEditingController weekendController = TextEditingController();
            TextEditingController advanceAmount = TextEditingController();

            if (price['title'] == size['title']) {
              controller.text = price['price'].toDouble().toStringAsFixed(0);
              weekendController.text =
                  price['weekendPrice'].toDouble().toStringAsFixed(0);
              size['price'] =
                  int.parse(price['price'].toDouble().toStringAsFixed(0));
              size['weekendPrice'] = int.parse(
                  price['weekendPrice'].toDouble().toStringAsFixed(0));
              size['controller'] = controller;
              size['weekendController'] = weekendController;
            }
          });
        });
      });
    } else {
      selectedSlots.forEach((selected) {
        widget.turf.slots!.forEach((slot) {
          slot.priceAndQuantity.forEach((price) {
            TextEditingController controller = TextEditingController();
            TextEditingController weekendController = TextEditingController();
            TextEditingController advanceAmount = TextEditingController();
            if (selected['title'] == slot.session) {
              var data = {};
              controller.text = price.price.toDouble().toStringAsFixed(0);
              weekendController.text =
                  price.weekendPrice.toDouble().toStringAsFixed(0);
              data['price'] =
                  int.parse(price.price.toDouble().toStringAsFixed(0));
              data['weekendPrice'] =
                  int.parse(price.weekendPrice.toDouble().toStringAsFixed(0));
              advanceAmount.text =
                  price.advanceAmount.toDouble().toStringAsFixed(0);
              data['controller'] = controller;
              data['weekendController'] = weekendController;
              data['isSelected'] = true;
              data['id'] = price.id;
              data['title'] = price.title;
              var index = sportQuantity
                  .indexWhere((value) => value['title'] == price.title);
              if (index == -1) {
                sportQuantity.add({
                  'title': price.title,
                  'quantity': price.quantity,
                  'label': price.label,
                  'advanceAmount': advanceAmount,
                });
              }
              selected['priceAndQuantity'].add(data);
            }
          });
        });
      });
    }
    print(selectedSlots);
    print(sportQuantity);
    for (var i = 0; i < widget.turf.images!.length; i++) {
      if (i <= 1) {
        selectedImagePaths[i] = widget.turf.images![i];
      } else {
        selectedImagePaths.add(widget.turf.images![i]);
      }
    }
    if (widget.turf.video != null) {
      videoPath = widget.turf.video!.url;
      videoThumbnail = widget.turf.video!.thumbnail;
    }
    widget.turf.facilities!.forEach((value) {
      listOfFacilities.forEach((facility) {
        if (facility['title'] == value) {
          facility['isSelected'] = true;
          selectedFacilities.add(facility['title']);
        }
      });
    });
    aboutTurfController.text = widget.turf.description!;
    cancellationController.text = widget.turf.cancellationPolicy;
    advanceAmountController.text = widget.turf.advanceAmount == 0
        ? ''
        : widget.turf.advanceAmount.toString();
    advanceAmountForOneOnController.text =
        widget.turf.advanceAmountForOneOn == 0
            ? ''
            : widget.turf.advanceAmountForOneOn.toString();

    if (widget.turf.availability != null) {
      period = widget.turf.availability!.duration;
      durationCount = widget.turf.availability!.count;
    }

    if (widget.turf.cancellationCharges.isNotEmpty) {
      widget.turf.cancellationCharges.forEach((data) {
        cancellationCharges.add({
          'start': data.start.toString(),
          'end': data.end.toString(),
          'percentage': data.percentage.toString(),
        });
      });
    }
  }

  @override
  void initState() {
    super.initState();
    venueNameController.text = widget.turf.name;
    fetchSports();
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return WillPopScope(
      onWillPop: () => step > 1
          ? incrementAndDecrementStep(false)
          : exitFunction(textScaleFactor),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: iOSCondition(deviceWidth)
            ? screenBody()
            : SafeArea(child: screenBody()),
      ),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: deviceHeight,
        width: deviceWidth,
        child: isLoading
            ? CircularLoader(
                android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
            : Padding(
              padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        NewAppBar(
                          dW: deviceWidth,
                          title: 'Add Venue Details',
                          onTap: () => step > 1
                              ? incrementAndDecrementStep(false)
                              : exitFunction(textScaleFactor),
                        ),
                        Container(
                          alignment: Alignment.bottomRight,
                          margin: EdgeInsets.only(right: deviceWidth * 0.05),
                          child: TextWidget(
                            title: '$step/6',
                            fontSize: 14,
                            color: Color(0xff0F402C),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: deviceWidth * 0.06),
                    Expanded(
                      child: SingleChildScrollView(
                        controller: controller,
                        physics: BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            if (step == 1)
                              NewStep1Widget(
                                listOfSports: listOfSports,
                                listofSportCategory: listofSportCategory,
                                selectAndUnSelectType: selectAndUnSelectType,
                                selectedSports: selectedSports,
                                selectedSportCategory: selectedSportCategory,
                                selectSportCategory: selectSportCategory,
                                isNet: isNet,
                                listOfSlots: listOfSlots,
                                venueNameController: venueNameController,
                                aboutTurfController: aboutTurfController,
                              ),
                            if (step == 2)
                              NewStep2Widget(
                                selectAndUnSelectType: selectAndUnSelectType,
                                listOfDays: listOfDays,
                                selectedWeekDays: selectedWeekDays,
                                selectedWeekEnds: selectedWeekEnds,
                                listOfTurfSizeAndPrice: listOfTurfSizeAndPrice,
                                selectedTurfSize: selectedTurfSize,
                                selectedSportCategory: selectedSportCategory,
                                selectTime: selectTime,
                                listofSlotTime: listofSlotTime,
                                selectSlotDuration: selectSlotDuration,
                                selectSportCategory: selectSportCategory,
                                incrementAndDecrementStep:
                                    incrementAndDecrementStep,
                                selectedSlotDuration: selectedSlotDuration,
                                isNet: isNet,
                                option: option,
                                listOfSlots: listOfSlots,
                                selectedSlots: selectedSlots,
                                setIsNetValue: setIsNetValue,
                                setListOfSlots: usePreviousValueForPrice
                                    ? () {}
                                    : setListOfSlots,
                                setUsePreviousValueForPrice:
                                    setUsePreviousValueForPrice,
                                setTurfOption: setTurfOption,
                                listOfTurfOption: listOfTurfOption,
                                selectAndUnselectDays: selectAndUnselectDays,
                              ),
                            if (step == 3)
                              NewStep3Widget(
                                selectedSlots: selectedSlots,
                                selectTime: selectTime,
                                isNet: isNet,
                                addOrRemovePrice: addOrRemovePrice,
                                incrementAndDecrementStep:
                                    incrementAndDecrementStep,
                                selectedSportCategory: selectedSportCategory,
                                selectedSlotDuration: selectedSlotDuration,
                                listofSlotTime: listofSlotTime,
                                selectSlotDuration: selectSlotDuration,
                              ),
                            if (step == 4)
                              NewStep4Widget(
                                selectedImagePaths: selectedImagePaths,
                                videoPath: videoPath,
                                videoThumbnail: videoThumbnail,
                                imagePickerBottomSheet: imagePickerBottomSheet,
                                videoPickerBottomSheet: videoPickerBottomSheet,
                                removeSelectedOrVideo: removeSelectedOrVideo,
                                isCompressingVideo: isCompressingVideo,
                              ),
                            if (step == 5)
                              NewStep5Widget(
                                listOfFacilities: listOfFacilities,
                                selectedSports: selectedSports,
                                selectAndUnSelectType: selectAndUnSelectType,
                                selectedFacilities: selectedFacilities,
                                advanceAmountController: advanceAmountController,
                                isNet: isNet,
                                turfSize: turfSize,
                                sportQuantity: sportQuantity,
                                setQuantity: setQuantity,
                                selectedSportCategory: selectedSportCategory,
                                option: option,
                                selectedSlots: selectedSlots,
                                advanceAmountForOneOnController:
                                    advanceAmountForOneOnController,
                              ),
                            if (step == 6)
                              NewStep6Widget(
                                cancellationCharges: cancellationCharges,
                                addOrRemoveCancellation: addOrRemoveCancellation,
                                durationCount: durationCount,
                                period: period,
                                editCancellation: editCancellation,
                                changeDuration: changeDuration,
                                cancellationController: cancellationController,
                              ),
                            SizedBox(height: deviceWidth * 0.12),
                          ],
                        ),
                      ),
                    ),
                    BottomAlignedWidget(
                      dW: deviceWidth,
                      dH: deviceWidth * 0.13,
                      child: CustomButton(
                        width: deviceWidth,
                        height: deviceWidth * 0.13,
                        radius: 8,
                        fontSize: 16,
                        buttonText:
                            step == 6 ? 'Save and Review' : 'Save and Continue',
                        onPressed: onSubmit,
                      ),
                    ),
                  ],
                ),
            ),
      ),
    );
  }
}
