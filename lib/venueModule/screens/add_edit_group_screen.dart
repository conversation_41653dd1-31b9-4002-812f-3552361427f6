import 'dart:io';

import 'package:bys_business/colors.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/navigators.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../venueModule/widgets/primary_venue_bottomsheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/custom_checkbox_widget.dart';
import '../../new_colors.dart';
import '../models/group_model.dart';
import '../models/venue_model.dart';
import '../providers/turfProvider.dart';

class AddEditGroupScreen extends StatefulWidget {
  final Group? group;
  AddEditGroupScreen({Key? key, this.group}) : super(key: key);

  @override
  AddEditGroupScreenState createState() => AddEditGroupScreenState();
}

class AddEditGroupScreenState extends State<AddEditGroupScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Map language = {};

  bool isLoading = false;
  bool isEditing = false;
  late UserModal user;

  TextEditingController groupNameControlller = TextEditingController();

  List<String> selectedVenues = [];
  List<Venue> listOfVenues = [];

  Venue? primaryVenue;

  void primaryVenueBottomSheet() {
    List<Venue> venues =
        listOfVenues.where((data) => selectedVenues.contains(data.id)).toList();
    showModalBottomSheet(
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) => PrimaryVenueBottomSheet(
        listOfVenues: venues,
        primaryVenue: primaryVenue,
      ),
    ).then((value) {
      primaryVenue = value;
      setState(() {});
    });
  }

  Future<void> fetchTurf() async {
    try {
      setState(() => isLoading = true);

      listOfVenues =
          Provider.of<TurfProvider>(context, listen: false).getVerifiedTurf(1);

      // if (widget.business != null) {
      //   List<String> turfIds = [];
      //   widget.business!.turfs.forEach((turf) => turfIds.add(turf.id));
      //   listOfVenues.removeWhere((turf) => !turfIds.contains(turf.id));
      // }
      // if (listOfVenues.length == 1) selectedVenue = listOfVenues.first;
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  selectVenue(String id) {
    if (!selectedVenues.contains(id)) {
      selectedVenues.add(id);
    } else {
      selectedVenues.remove(id);
      if (primaryVenue != null && primaryVenue!.id == id) {
        primaryVenue = null;
      }
    }
    if (selectedVenues.isEmpty) {
      primaryVenue = null;
    }
    setState(() {});
  }

  bool isButtonEnabled() {
    if (groupNameControlller.text.trim().isEmpty ||
        selectedVenues.isEmpty ||
        primaryVenue == null) {
      return false;
    } else {
      return true;
    }
  }

  addEditGroup() async {
    try {
      if (isEditing) return;
      setState(() => isEditing = true);
      Map body = {
        'groupId': widget.group == null ? '' : widget.group!.id,
        'name': groupNameControlller.text.trim(),
        'venues': selectedVenues,
        'primaryVenue': primaryVenue!.id
      };
      final result = await Provider.of<TurfProvider>(context, listen: false)
          .createAndEditGroup(accessToken: user.accessToken, body: body);
      if (result != null) {
        showSnackbar(
          'Group ${widget.group == null ? 'added' : 'updated'} successfully',
          color: greenPrimary,
        );
        pop(result);
      } else {
        showSnackbar('Something went wrong');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => isEditing = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    if (widget.group != null) {
      Group group = widget.group!;
      groupNameControlller.text = group.name;
      primaryVenue = group.primaryVenue;
      selectedVenues.add(group.primaryVenue.id);
      group.venues.forEach((venue) => selectedVenues.add(venue.id));
    }
    fetchTurf();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: dH,
        width: dW,
        child: Padding(
          padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
          child: Column(
            children: [
              // SizedBox(height: dW * 0.05),
              NewAppBar(
                dW: dW,
                title: '${widget.group == null ? 'Add New' : 'Edit'} Group',
              ),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: isLoading
                    ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
                    : SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SizedBox(height: dW * 0.02),
                            CustomContainer(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextFieldWithLabel(
                                    label: 'Group Name',
                                    labelFS: 16,
                                    labelFW: FontWeight.w500,
                                    controller: groupNameControlller,
                                    hintText: 'Enter Group Name',
                                    onChanged: (value) => setState(() {}),
                                  ),
                                  SizedBox(height: dW * 0.06),
                                  Row(
                                    children: [
                                      TextWidget(
                                        title: 'Select Venues',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                      TextWidget(
                                        title: '*',
                                        fontSize: 16,
                                        color: Color(0xffFF5B5B),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: dW * 0.02, bottom: dW * 0.05),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: getThemeColor(), width: 1),
                                    ),
                                    padding: EdgeInsets.all(dW * 0.04),
                                    child: Column(
                                      children: [
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics: NeverScrollableScrollPhysics(),
                                          itemCount: listOfVenues.length,
                                          itemBuilder: (context, index) {
                                            Venue venues = listOfVenues[index];
                                            return Container(
                                              margin: EdgeInsets.only(
                                                bottom: index !=
                                                        listOfVenues.length - 1
                                                    ? dW * 0.04
                                                    : 0,
                                              ),
                                              child: CustomCheckbox(
                                                title: venues.name,
                                                textColor:
                                                    getRemainingAmountSubTitleColor(
                                                        context),
                                                fontWeight: FontWeight.w500,
                                                value: selectedVenues
                                                    .contains(venues.id),
                                                activeColor: getThemeColor(),
                                                size: 21,
                                                onChanged: () =>
                                                    selectVenue(venues.id),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  DividerWidget(top: 0),
                                  Row(
                                    children: [
                                      TextWidget(
                                        title: 'Set Primary Venue',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                      TextWidget(
                                        title: '*',
                                        fontSize: 16,
                                        color: Color(0xffFF5B5B),
                                      ),
                                    ],
                                  ),
                                  GestureDetector(
                                    onTap: selectedVenues.isNotEmpty
                                        ? primaryVenueBottomSheet
                                        : () => showSnackbar(
                                            'Please select venues to set primary venue'),
                                    child: Container(
                                      margin: EdgeInsets.only(top: dW * 0.02),
                                      padding: EdgeInsets.symmetric(
                                          vertical: dW * 0.025,
                                          horizontal: dW * 0.04),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: getThemeColor(), width: 1),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          ConstrainedBox(
                                            constraints: BoxConstraints(
                                                maxWidth: dW * 0.65),
                                            child: TextWidget(
                                              title: primaryVenue == null
                                                  ? 'Select primary venue'
                                                  : primaryVenue!.name,
                                              fontWeight: primaryVenue == null
                                                  ? FontWeight.w500
                                                  : FontWeight.w600,
                                              color: primaryVenue == null
                                                  ? Color(0xffADADAD)
                                                  : Colors.black,
                                            ),
                                          ),
                                          Icon(
                                            Icons.keyboard_arrow_down,
                                            size: 25,
                                            color: selectedVenues.isEmpty
                                                ? Color(0xffADADAD)
                                                : getThemeColor(),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
              BottomAlignedWidget(
                dW: dW,
                dH: dW * 0.13,
                child: CustomButton(
                  width: dW,
                  height: dW * 0.13,
                  radius: 8,
                  fontSize: 16,
                  buttonText:
                      widget.group == null ? 'Create Group' : 'Update Group',
                  onPressed: isButtonEnabled() ? addEditGroup : null,
                  isLoading: isEditing,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
