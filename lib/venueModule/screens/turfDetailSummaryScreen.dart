import 'dart:io';

import 'package:bys_business/commonWidgets/open_media_full_screen.dart';
import 'package:bys_business/venueModule/screens/turf_details_Screen.dart';
import 'package:bys_business/venueModule/widgets/addCancellationBottomsheet.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/checkBoxWidget.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/new_appbar.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../navigators.dart';
import '../models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../widgets/cancellationWidget.dart';

class TurfDetailReviewScreen extends StatefulWidget {
  final Venue turf;
  final List selectedSports;
  final List selectedDays;
  final List selectedTurfSizeAndPrice;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final int timeSlotDifference;
  final List listOfImages;
  final List selectedFacilities;
  final String venueDescription;
  final int advanceAmount;
  final int advanceAmountForOneOn;
  final bool isNet;
  final List sportQuantity;
  final Map selectedSportCategory;
  final String venueName;
  final String option;
  final int durationCount;
  final String period;
  final List cancellationCharges;
  final String cancellationPolicy;
  final List selectedWeekends;
  final String videoPath;
  final String videoThumbnail;
  const TurfDetailReviewScreen({
    Key? key,
    required this.selectedSports,
    required this.selectedDays,
    required this.selectedTurfSizeAndPrice,
    required this.startTime,
    required this.endTime,
    required this.timeSlotDifference,
    required this.listOfImages,
    required this.selectedFacilities,
    required this.venueDescription,
    required this.advanceAmount,
    required this.turf,
    required this.isNet,
    required this.advanceAmountForOneOn,
    required this.sportQuantity,
    required this.selectedSportCategory,
    required this.venueName,
    required this.option,
    required this.durationCount,
    required this.period,
    required this.cancellationCharges,
    required this.cancellationPolicy,
    required this.selectedWeekends,
    required this.videoPath,
    required this.videoThumbnail,
  }) : super(key: key);

  @override
  _TurfDetailReviewScreenState createState() => _TurfDetailReviewScreenState();
}

class _TurfDetailReviewScreenState extends State<TurfDetailReviewScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;
  bool isLoading = false;
  late UserModal user;
  saveDetails() async {
    try {
      if (isLoading) return;
      setState(() => isLoading = true);

      List slot = [];
      widget.selectedTurfSizeAndPrice.forEach((size) {
        List priceQuantity = [];
        size['priceAndQuantity'].forEach((price) {
          priceQuantity.add({
            "title": price['title'],
            "price": price['price'].toDouble(),
            "weekendPrice": price['weekendPrice'].toDouble(),
          });
        });
        widget.sportQuantity.forEach((data) {
          priceQuantity.forEach((size) {
            if (data['title'] == size['title']) {
              size['quantity'] = data['quantity'];
              size['label'] = data['label'];
              size['advanceAmount'] = data['advanceAmount'] == null
                  ? 0
                  : int.parse(data['advanceAmount'].text);
            }
          });
        });
        slot.add(
          {
            "session": size['title'],
            "startTime": double.parse(size['startTime']
                .format(context)
                .toString()
                .replaceAll(':', '.')),
            "endTime": double.parse(size['endTime']
                .format(context)
                .toString()
                .replaceAll(':', '.')),
            "priceAndQuantity": priceQuantity,
          },
        );
      });
      List listSport = Provider.of<Auth>(context, listen: false).listOfSports;

      List<SportType> selectedSports = [];
      widget.selectedSports.forEach((selected) {
        listSport.forEach((sport) {
          if (selected == sport['title']) {
            selectedSports.add(
              SportType(
                id: sport['id'],
                sport: sport['title'],
                image: sport['image'] ?? '',
              ),
            );
          }
        });
      });
      final data = await Provider.of<TurfProvider>(context, listen: false)
          .addTurfDetails(
        accessToken: user.accessToken,
        sportsType: selectedSports,
        venueName: widget.venueName,
        days: widget.selectedDays,
        weekends: widget.selectedWeekends,
        priceAndQuantity: slot,
        images: widget.listOfImages,
        facilities: widget.selectedFacilities,
        startTime:
            double.parse(widget.startTime.format(context).replaceAll(':', '.')),
        endTime:
            double.parse(widget.endTime.format(context).replaceAll(':', '.')),
        slotTimeDifference: widget.timeSlotDifference,
        description: widget.venueDescription,
        sportCategory: widget.selectedSportCategory,
        turf: widget.turf,
        isNet: widget.isNet,
        advanceAmount: widget.advanceAmount,
        advanceAmountForOneOn: widget.advanceAmountForOneOn,
        option: widget.option,
        cancellationCharges: widget.cancellationCharges,
        cancellationPolicy: widget.cancellationPolicy,
        availability: {
          'duration': widget.period,
          'count': widget.durationCount
        },
        videoPath: widget.videoPath,
        videoThumbnail: widget.videoThumbnail,
      );
      if (data) {
        Provider.of<Auth>(context, listen: false).resetSports();
        pop(true);
        showSnackbar(
          'Venue Details ${widget.turf.days != null && widget.turf.days!.length != 0 ? 'updated' : 'added'} successfully',
          color: getThemeColor(),
        );
        setState(() => isLoading = false);
      } else {
        showSnackbar('Unable to submit!!!');
        setState(() => isLoading = false);
      }
    } catch (e) {
      showSnackbar('Something went wrong');
      print(e);
      setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return Padding(
      padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
      child: Column(
        children: [
          SizedBox(height: dW * 0.05),
          NewAppBar(
            dW: dW,
            title: widget.turf.days != null && widget.turf.days!.length != 0
                ? 'Update Venue Details'
                : 'Add Venue Details',
          ),
          SizedBox(height: dW * 0.05),
          Expanded(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Venue Name'),
                            CustomContainer(
                              margin: EdgeInsets.only(top: dW * 0.02),
                              vPadding: 0.03,
                              boxShadow: [],
                              borderColor: getThemeColor(),
                              child: TextWidget(
                                title: widget.venueName,
                                fontSize: 14.5,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: dW * 0.045),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'About Venue'),
                            CustomContainer(
                              margin: EdgeInsets.only(top: dW * 0.02),
                              vPadding: 0.03,
                              boxShadow: [],
                              borderColor: getThemeColor(),
                              child: TextWidget(
                                title: widget.venueDescription,
                                fontSize: 14.5,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        // SizedBox(height: dW * 0.045),
                        DividerWidget(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Booking Availability Duration'),
                            SizedBox(height: dW * 0.02),
                            TextWidget(
                              title: '${widget.durationCount} ${widget.period}.',
                              fontSize: 14.5,
                              fontWeight: FontWeight.w500,
                            )
                          ],
                        ),
                        DividerWidget(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Sport Category'),
                            SizedBox(height: dW * 0.02),
                            TextWidget(
                              title: widget.selectedSportCategory['categoryName'],
                              fontSize: 14.5,
                              fontWeight: FontWeight.w500,
                            )
                          ],
                        ),
                        DividerWidget(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Sport Type'),
                            SizedBox(height: dW * 0.02),
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.start,
                              runAlignment: WrapAlignment.spaceBetween,
                              children: [
                                ...widget.selectedSports.map(
                                  (sport) => CustomContainer(
                                    width: dW * 0.32,
                                    boxShadow: [],
                                    borderColor: getThemeColor(),
                                    vPadding: 0.02,
                                    hPadding: 0.02,
                                    radius: 8,
                                    margin: EdgeInsets.only(
                                      bottom: dW * 0.03,
                                      right: dW * 0.03,
                                    ),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        if (Provider.of<Auth>(context,
                                                    listen: false)
                                                .getImageBySportName(sport) !=
                                            '')
                                          Image.network(
                                            Provider.of<Auth>(context,
                                                    listen: false)
                                                .getImageBySportName(sport),
                                            scale: 3,
                                          ),
                                        SizedBox(width: dW * .02),
                                        ConstrainedBox(
                                          constraints:
                                              BoxConstraints(maxWidth: dW * 0.18),
                                          child: FittedBox(
                                            fit: BoxFit.scaleDown,
                                            child: TextWidget(
                                              title: sport,
                                              fontWeight: FontWeight.w500,
                                              textAlign: TextAlign.left,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.05),
                  CustomContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Selected Weekdays'),
                            SizedBox(height: dW * 0.03),
                            Wrap(
                              children: [
                                ...widget.selectedDays.map(
                                  (day) => Container(
                                    constraints:
                                        BoxConstraints(minWidth: dW * 0.15),
                                    decoration: BoxDecoration(
                                      color: getThemeColor(),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(color: getThemeColor()),
                                    ),
                                    padding: EdgeInsets.all(dW * 0.02),
                                    margin: EdgeInsets.only(
                                      bottom: dW * 0.025,
                                      right: dW * 0.025,
                                    ),
                                    child: TextWidget(
                                      title: day,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: dW * 0.03),
                            TextWidget(title: 'Selected Weekends'),
                            SizedBox(height: dW * 0.03),
                            Wrap(
                              children: [
                                ...widget.selectedWeekends.map(
                                  (day) => Container(
                                    constraints:
                                        BoxConstraints(minWidth: dW * 0.15),
                                    decoration: BoxDecoration(
                                      color: getThemeColor(),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(color: getThemeColor()),
                                    ),
                                    padding: EdgeInsets.all(dW * 0.02),
                                    margin: EdgeInsets.only(
                                      bottom: dW * 0.025,
                                      right: dW * 0.025,
                                    ),
                                    child: TextWidget(
                                      title: day,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        DividerWidget(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Slot Difference'),
                            SizedBox(height: dW * 0.02),
                            TextWidget(
                              title: '${widget.timeSlotDifference} minutes',
                              fontSize: 14.5,
                              fontWeight: FontWeight.w500,
                            )
                          ],
                        ),
                        DividerWidget(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title: 'Selected Slots'),
                            SizedBox(height: dW * 0.03),
                            ...widget.selectedTurfSizeAndPrice
                                .asMap()
                                .map(
                                  (i, slot) => MapEntry(
                                    i,
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        TextWidget(
                                          title:
                                              '${i + 1}. ${slot['title']} Slot',
                                          color: getThemeColor(),
                                          fontWeight: FontWeight.w500,
                                        ),
                                        SizedBox(height: dW * 0.03),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            TextWidget(
                                                title: 'Selected Slot Timing'),
                                            SizedBox(height: dW * 0.03),
                                            buildSlotTimingWidget(
                                              dW: dW,
                                              tS: tS,
                                              title: 'Start Time :',
                                              value:
                                                  '${slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                            ),
                                            SizedBox(height: dW * 0.05),
                                            buildSlotTimingWidget(
                                              dW: dW,
                                              tS: tS,
                                              title: 'End Time :',
                                              value:
                                                  '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: dW * 0.05),
                                        TextWidget(
                                          title: widget.selectedSportCategory[
                                                      'categoryName'] !=
                                                  'Outdoor'
                                              ? 'Selected Sport & Pricing'
                                              : 'Selected Turf Sizes & Pricing',
                                          fontSize: 16,
                                          color: Color(0xff636363),
                                          fontWeight: FontWeight.w500,
                                        ),
                                        SizedBox(height: dW * 0.01),
                                        TextWidget(
                                          title:
                                              'Price for ${widget.timeSlotDifference} minutes (Inclusive GST).',
                                          fontSize: 12,
                                          color: Color(0xff636363),
                                        ),
                                        SizedBox(height: dW * 0.03),
                                        CustomContainer(
                                          radius: 8,
                                          hPadding: 0.03,
                                          vPadding: 0.015,
                                          boxShadow: [],
                                          borderColor: getThemeColor(),
                                          child: Column(
                                            children: [
                                              SizedBox(height: dW * 0.03),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  ...[
                                                    widget.selectedSportCategory[
                                                                'categoryName'] ==
                                                            'Outdoor'
                                                                'Outdoor'
                                                        ? 'Sport'
                                                        : 'Turf Size',
                                                    'Weekdays',
                                                    'Weekends'
                                                  ].map(
                                                    (type) => Container(
                                                      alignment:
                                                          Alignment.topLeft,
                                                      width: type == 'Weekends' ||
                                                              type == 'Weekdays'
                                                          ? dW * 0.21
                                                          : dW * .24,
                                                      child: FittedBox(
                                                        fit: BoxFit.scaleDown,
                                                        child: TextWidget(
                                                          title: type,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: dW * 0.03),
                                              ...slot['priceAndQuantity'].map(
                                                (sport) => Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    ...[1, 2, 3].map(
                                                      (index) => Container(
                                                        margin: EdgeInsets.only(
                                                            bottom: dW * 0.03),
                                                        alignment: index == 1
                                                            ? Alignment.topLeft
                                                            : Alignment.center,
                                                        width: index != 1
                                                            ? dW * 0.21
                                                            : dW * .24,
                                                        padding:
                                                            EdgeInsets.symmetric(
                                                                vertical: 7),
                                                        decoration: index == 1
                                                            ? null
                                                            : BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5),
                                                                border: Border.all(
                                                                    color:
                                                                        getThemeColor()),
                                                              ),
                                                        child: FittedBox(
                                                          fit: BoxFit.scaleDown,
                                                          child: TextWidget(
                                                            title: index == 1
                                                                ? sport['title']
                                                                : index == 2
                                                                    ? sport['price'] ==
                                                                            0
                                                                        ? 'N.A'
                                                                        : '\u20b9 ${sport['controller'].text}'
                                                                    : sport['weekendPrice'] ==
                                                                            0
                                                                        ? 'N.A'
                                                                        : '\u20b9 ${sport['weekendPrice']}',
                                                            color:
                                                                getThemeColor(),
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: dW * 0.05)
                                      ],
                                    ),
                                  ),
                                )
                                .values
                                .toList(),
                            // SizedBox(height: dW * 0.03),
                            DividerWidget(top: 0),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  title: widget.selectedSportCategory[
                                              'categoryName'] !=
                                          'Outdoor'
                                      ? 'Quantity'
                                      : widget.isNet
                                          ? 'Turf Relation & Label'
                                          : 'Turf Quantity & Label',
                                ),
                                SizedBox(height: dW * 0.03),
                                ...widget.sportQuantity.map(
                                  (sport) => Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(
                                            right: dW * 0.03, bottom: dW * 0.03),
                                        alignment: Alignment.center,
                                        width: dW * 0.25,
                                        padding: EdgeInsets.symmetric(
                                          vertical: dW * 0.025,
                                          horizontal: dW * 0.02,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).primaryColor,
                                          borderRadius: BorderRadius.circular(6),
                                          border: Border.all(
                                            color: Theme.of(context).primaryColor,
                                          ),
                                        ),
                                        child: TextWidget(
                                          title: sport['title'],
                                          fontSize: widget.selectedSportCategory[
                                                      'categoryName'] ==
                                                  'Outdoor'
                                              ? tS * 14
                                              : tS * 12,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                          textOverflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      SizedBox(width: dW * 0.035),
                                      Container(
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.symmetric(
                                          vertical: dW * 0.02,
                                          horizontal: dW * 0.02,
                                        ),
                                        child: TextWidget(
                                          title:
                                              'x   ${sport['quantity'] > 9 ? '' : '0'}${sport['quantity']} ${sport['label'].trim() == '' ? '' : '(${sport['label']})'}',
                                          fontSize: tS * displayLarge,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            DividerWidget(),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(title: 'Advance Amount'),
                                SizedBox(height: dW * 0.02),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    TextWidget(
                                        title: widget.turf.sportCategory!
                                                    .categoryName !=
                                                'Outdoor'
                                            ? 'Sport'
                                            : 'Turf Size',
                                        color: Color(0xff636363)),
                                    TextWidget(
                                        title: 'Advance Amount',
                                        color: Color(0xff636363)),
                                  ],
                                ),
                                SizedBox(height: dW * 0.03),
                                ...widget.sportQuantity.map(
                                  (sport) => Padding(
                                    padding: EdgeInsets.only(bottom: dW * 0.04),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        ConstrainedBox(
                                          constraints:
                                              BoxConstraints(maxWidth: dW * 0.4),
                                          child: FittedBox(
                                            fit: BoxFit.scaleDown,
                                            child: TextWidget(
                                              title: sport['title'],
                                              color: getThemeColor(),
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                        CustomContainer(
                                          width: dW * 0.31,
                                          boxShadow: [],
                                          vPadding: 0.025,
                                          radius: 7,
                                          borderColor: getThemeColor(),
                                          child: TextWidget(
                                            title:
                                                '\u20b9 ${sport['advanceAmount'].text}',
                                            color: getThemeColor(),
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.05),
                  CustomContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(title: 'Facilities'),
                        SizedBox(height: dW * 0.03),
                        ...widget.selectedFacilities.map(
                          (facility) => Padding(
                            padding: EdgeInsets.only(bottom: dW * 0.035),
                            child: Row(
                              children: [
                                OldCheckBoxWidget(dW, true),
                                SizedBox(width: dW * 0.035),
                                TextWidget(
                                  title: facility,
                                  fontWeight: FontWeight.w500,
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.05),
                  CustomContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.cancellationCharges.isNotEmpty)
                          Container(
                            margin: EdgeInsets.only(bottom: dW * 0.03),
                            child: TextWidget(
                              title: 'Cancellation Charges',
                              color: Colors.black,
                            ),
                          ),
                        if (widget.cancellationCharges.isNotEmpty)
                          ...widget.cancellationCharges
                              .asMap()
                              .map(
                                (i, data) => MapEntry(
                                  i,
                                  CancellationWidget(
                                    data: data,
                                    deviceWidth: dW,
                                    textScaleFactor: tS,
                                  ),
                                ),
                              )
                              .values
                              .toList(),
                        if (widget.cancellationPolicy.isNotEmpty) ...[
                          Container(
                            margin: EdgeInsets.only(bottom: dW * 0.03),
                            child: TextWidget(
                              title: 'Cancellation Policy',
                              color: Colors.black,
                            ),
                          ),
                          CustomContainer(
                            margin: EdgeInsets.only(bottom: dW * 0.02),
                            boxShadow: [],
                            borderColor: getThemeColor(),
                            vPadding: .03,
                            child: Text(
                              widget.cancellationPolicy,
                              style: TextStyle(
                                fontSize: tS * 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.05),
                  CustomContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(title: 'Venue Photos'),
                        SizedBox(height: dW * 0.03),
                        ...widget.listOfImages.map(
                          (image) => GestureDetector(
                            onTap: () => push(OpenMediaFullScreen(
                              type: 'Image',
                              isLocal: !image.contains('https'),
                              url: image,
                            )),
                            child: Container(
                              margin: EdgeInsets.only(bottom: dW * 0.035),
                              child: Container(
                                width: dW,
                                height: dW * 0.45,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(color: getThemeColor()),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: image.contains('https')
                                      ? CachedNetworkImage(
                                          fit: BoxFit.cover,
                                          imageUrl: image,
                                          placeholder: (_, __) => Image.asset(
                                            'assets/images/placeholder.jpg',
                                            fit: BoxFit.cover,
                                          ),
                                        )
                                      : Image.file(File(image),
                                          fit: BoxFit.cover),
                                ),
                              ),
                            ),
                          ),
                        ),
                        if (widget.videoPath != '') ...[
                          SizedBox(height: dW * 0.03),
                          TextWidget(title: 'Venue Video'),
                          SizedBox(height: dW * 0.03),
                          GestureDetector(
                            onTap: () {
                              push(
                                OpenMediaFullScreen(
                                    type: 'Video',
                                    isLocal: !widget.videoPath.contains('https'),
                                    url: widget.videoPath),
                              );
                            },
                            child: Stack(
                              clipBehavior: Clip.none,
                              alignment: Alignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: widget.videoThumbnail.contains('https')
                                      ? Image.network(
                                          widget.videoThumbnail,
                                          width: dW,
                                          height: dW * 0.5,
                                          fit: BoxFit.cover,
                                        )
                                      : Image.file(
                                          File(widget.videoThumbnail),
                                          width: dW,
                                          height: dW * 0.5,
                                          fit: BoxFit.cover,
                                        ),
                                ),
                                Icon(
                                  Icons.play_circle_outline_rounded,
                                  color: Colors.white,
                                  size: dW * 0.1,
                                ),
                              ],
                            ),
                          ),
                        ]
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.15),
                ],
              ),
            ),
          ),
          BottomAlignedWidget(
            dW: dW,
            dH: dW * 0.13,
            child: CustomButton(
              width: dW,
              height: dW * 0.13,
              radius: 8,
              buttonText: 'Submit for review',
              onPressed: saveDetails,
              isLoading: isLoading,
              fontSize: 16,
            ),
          )
        ],
      ),
    );
  }
}

getTurfOption(String option) {
  if (option == 'One On') {
    return 'Turf with Cricket Net';
  } else if (option == 'Netting') {
    return 'Turf with netting system';
  } else if (option == 'Non Netting') {
    return 'Turf without Net';
  } else {
    return '';
  }
}
