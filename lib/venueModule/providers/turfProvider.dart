import 'dart:convert';
import 'package:bys_business/venueModule/models/group_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import '../../api.dart';

import '../../authModule/modals/registration.dart';
import '../../http_helper.dart';
import '../models/venue_model.dart';

class TurfProvider with ChangeNotifier {
  // List<Venue> _turfs = [];
  List<Venue> _turfs = [];

  List<Venue> get turfs {
    return [..._turfs];
  }

  List<Venue> _searchedTurfs = [];

  List<Venue> get searchedTurfs {
    return [..._searchedTurfs];
  }

  List<Group> _groups = [];
  List<Group> get groups => [..._groups];

  emptyTurf() {
    _turfs = [];
  }

  addTurfDetails({
    required String accessToken,
    required String venueName,
    required List<SportType> sportsType,
    required List days,
    required List weekends,
    required List priceAndQuantity,
    required List images,
    required List facilities,
    required double startTime,
    required double endTime,
    required int slotTimeDifference,
    required String description,
    required String option,
    required Map sportCategory,
    required Venue turf,
    required bool isNet,
    required int advanceAmount,
    required int advanceAmountForOneOn,
    required Map availability,
    required List cancellationCharges,
    required String cancellationPolicy,
    required String videoPath,
    required String videoThumbnail,
  }) async {
    final url = '${webApi['domain']}${endPoint['addTurfDetails']}';
    var request = http.MultipartRequest('POST', Uri.parse(url));
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $accessToken'
    };

    List sports = [];
    List networkImage = [];

    sportsType.forEach((sport) {
      sports.add(sport.id);
    });

    try {
      for (int i = 0; i < images.length; i++) {
        if (!images[i].contains('https')) {
          request.files
              .add(await http.MultipartFile.fromPath('image$i', images[i]));
        } else {
          networkImage.add({'url': images[i]});
        }
      }

      if (videoThumbnail != '' && !videoThumbnail.contains('https')) {
        request.files.add(await http.MultipartFile.fromPath(
            'videoThumbnail', videoThumbnail));
      }

      if (videoPath != '' && !videoPath.contains('https')) {
        request.files
            .add(await http.MultipartFile.fromPath('videoPath', videoPath));
      }

      request.fields.addAll({
        "turfId": turf.id,
        "name": venueName,
        "businessId": turf.businessId,
        "sportsType": json.encode(sports),
        "days": json.encode(days),
        "weekends": json.encode(weekends),
        "priceAndQuantity": json.encode(priceAndQuantity),
        "slotTimeDifference": slotTimeDifference.toString(),
        "facilities": json.encode(facilities),
        "description": description,
        "option": option,
        "cancellationPolicy": cancellationPolicy,
        "advanceAmount": advanceAmount.toString(),
        "advanceAmountForOneOn": advanceAmountForOneOn.toString(),
        "isNet": isNet.toString(),
        "networkImage": json.encode(networkImage),
        "sportCategory": sportCategory['categoryId'],
        "cancellationCharges": json.encode(cancellationCharges),
        "availability": json.encode(availability),
        'videoPath': videoPath,
        'videoThumbnail': videoThumbnail,
      });
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      final respStr = await response.stream.bytesToString();
      final responseData = json.decode(respStr);
      if (responseData['success']) {
        List loadedImage = [];
        List<SportType> loadedSport = [];
        responseData['result']['images'].forEach((image) {
          loadedImage.add(image['url']);
        });
        List<Slot> loadedSlots = [];
        responseData['result']['slots'].forEach((slot) {
          List<PricingAndQuantity> sizeAndPrice = [];
          slot['priceAndQuantity'].forEach((size) {
            sizeAndPrice.add(
              PricingAndQuantity(
                id: size['_id'],
                title: size['title'],
                label: size['label'] ?? "",
                sport: size['sport'] ?? "",
                totalPlayersAllowed: size['totalPlayersAllowed'] ?? 0,
                courtOrTable: size['courtOrTable'] ?? '',
                price: size['price'] == null ? 0 : size['price'].toDouble(),
                weekendPrice: size['weekendPrice'] == null
                    ? 0
                    : size['weekendPrice'].toDouble(),
                advanceAmount: size['advanceAmount'] == null
                    ? 0
                    : size['advanceAmount'].toDouble(),
                quantity: size['quantity'].toInt(),
              ),
            );
          });
          loadedSlots.add(
            Slot(
              id: slot['_id'],
              session: slot['session'],
              startTime: slot["startTime"].toDouble(),
              endTime: slot["endTime"].toDouble(),
              priceAndQuantity: sizeAndPrice,
            ),
          );
        });
        responseData['result']['sportsType'].forEach((sport) {
          loadedSport.add(
            SportType(
              id: sport['_id'],
              sport: sport['sport'],
              image: sport['image'],
            ),
          );
        });

        List<CancellationCharge> loadedCharge = [];

        responseData['result']['cancellationCharges'].forEach((data) {
          loadedCharge.add(
            CancellationCharge(
              start: data['start'].toInt(),
              end: data['end'].toInt(),
              percentage: data['percentage'].toInt(),
            ),
          );
        });

        turf.sportsType = List.from(loadedSport);
        turf.days = responseData['result']['days'];
        turf.weekends = responseData['result']['weekends'] == null
            ? []
            : List.from(responseData['result']['weekends']);
        turf.name = venueName;
        turf.option = option;
        turf.images = List.from(loadedImage);
        turf.slots = List.from(loadedSlots);
        turf.facilities = List.from(facilities);
        turf.slotTimeDifference = slotTimeDifference;
        turf.description = description;
        turf.advanceAmount = advanceAmount;
        turf.cancellationPolicy = cancellationPolicy;
        turf.availability = Availability(
          count: responseData['result']['availability']['count'].toInt(),
          duration: responseData['result']['availability']['duration'],
        );
        turf.cancellationCharges = loadedCharge;
        turf.isNet = isNet;
        turf.pauseDates = responseData['result']['pauseDates'];
        turf.video = responseData['result']['video'] == null
            ? null
            : VideoData.jsonToVideo(responseData['result']['video']);
        turf.sportCategory = SportCategory(
          id: sportCategory['categoryId'],
          categoryName: sportCategory['categoryName'],
        );
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
    }
  }

  addNewTurf({
    required String accessToken,
    required String businessId,
    required TurfDetails turfDetails,
    required var employeeId,
    String? userId,
  }) async {
    final url = '${webApi['domain']}${endPoint['addNewTurf']}';
    var str = json.encode({
      "businessId": businessId,
      "employeeId": employeeId,
      "userId": userId,
      "turfName": turfDetails.turfName,
      "turfContact": turfDetails.mobileNo,
      "streetName": turfDetails.streetName,
      "landmark": turfDetails.landmark,
      "pincode": turfDetails.pincode.toString(),
      "city": turfDetails.city,
      "state": turfDetails.state,
    });
    print(str);
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        _turfs.insert(0, Venue.jsonToVenue(responseData['result']));
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
    }
  }

  getVerifiedTurf(int index) {
    if (index == 1) {
      return _turfs
          .where(
              (turf) => turf.status == "APPROVED" && turf.images!.length != 0)
          .toList();
    } else {
      return _turfs
          .where((turf) =>
              turf.status == "PENDING" ||
              turf.status == "REJECTED" ||
              turf.images!.length == 0)
          .toList();
    }
  }

  List<Venue> searchTurfs2({required String searchText, required int index}) {
    List<Venue> matchedTurfs = [];

    // Assuming Turf is the class of objects in _turfs
    for (Venue turf in _turfs) {
      bool meetsCondition = false;

      if (index == 1) {
        meetsCondition = turf.status == "APPROVED" && turf.images!.length != 0;
      } else {
        meetsCondition = turf.status == "PENDING" ||
            turf.status == "REJECTED" ||
            turf.images!.length == 0;
      }

      if (meetsCondition &&
          turf.name.toLowerCase().contains(searchText.toLowerCase())) {
        matchedTurfs.add(turf);
      }
    }

    return matchedTurfs;
  }

  // insertTurf(Venue turf) {
  //   _turfs.insert(0, turf);
  //   notifyListeners();
  // }

  searchTurfs({
    required String enteredText,
    required String accessToken,
  }) async {
    try {
      // User? user;

      final url = '${webApi['domain']}${endPoint['searchTurf']}';
      Map body = {
        'searchedString': enteredText,
      };

      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<Venue> fetchedTurfs = [];

        response['result'].forEach((turf) {
          fetchedTurfs.add(Venue.jsonToVenue(turf));
        });
        _searchedTurfs = fetchedTurfs;

        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'result': 'error',
        'message': 'Failed to get turfs',
      };
    }
  }

  fetchTurfsByBusinessId(String accessToken, String businessId) async {
    final url = '${webApi['domain']}${endPoint['fetchTurfsByBusinessId']}';
    var str = json.encode({"businessId": businessId});
    try {
      List<Venue> loadedVenue = [];
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $accessToken'
      });
      final responseData = json.decode(response.body);
      responseData['result'].forEach((venue) {
        loadedVenue.add(Venue.jsonToVenue(venue));
      });

      _turfs = List.from(loadedVenue);

      notifyListeners();
    } catch (error) {
      print(error);
      throw error;
    }
  }

  pauseAndUnPauseTurf({
    required Venue turf,
    required String accessToken,
    required String action,
    required List dates,
  }) async {
    final url = '${webApi['domain']}${endPoint['pauseAndUnPauseTurf']}';
    var str = json.encode({
      "turfId": turf.id,
      "dates": dates,
      "action": action,
    });
    try {
      final response = await http.post(Uri.parse(url), body: str, headers: {
        "Content-Type": "application/json",
        'Authorization': "Bearer $accessToken",
      });
      final responseData = json.decode(response.body);
      if (responseData['success']) {
        turf.pauseDates = responseData['result']['pauseDates'];
        notifyListeners();
        return responseData['result']['pauseDates'];
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  updateVenueLocation({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateVenueLocation']}';

      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        int index = _turfs.indexWhere((turf) => turf.id == body['turfId']);
        if (index != -1) {
          _turfs[index].address = Address(
            id: response['result']['address']['_id'],
            streetName: response['result']['address']['streetName'] ?? '',
            landmark: response['result']['address']['landmark'] ?? '',
            city: response['result']['address']['city'] ?? '',
            fullAddress: response['result']['address']['fullAddress'] ?? '',
            area: response['result']['address']['area'] ?? '',
            state: response['result']['address']['state'] ?? '',
            coordinates: LatLng(
              response['result']['address']['location']['coordinates'][1],
              response['result']['address']['location']['coordinates'][0],
            ),
            pincode: int.parse(response['result']['address']['pincode']),
          );
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  Venue? findTurfById(String turfId) {
    return _turfs.firstWhere((turf) => turf.id == turfId);
  }

  Future<Venue?> fetchTurfById({
    required String accessToken,
    required String turfId,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchTurfById']}';

      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: {'turfId': turfId},
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        var venue = response['result'];

        Venue loadedVenue = Venue.jsonToVenue(venue);
        return loadedVenue;
      } else {
        return null;
      }
    } catch (e) {
      print(e);
      return null;
    }
  }

  addVenueAndDetailsV2Indoor({
    required String accessToken,
    required Map<String, String> body,
    required Map<String, String> files,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['addVenueAndDetailsIndoor']}';
      final response = await RemoteServices.formDataRequest(
        method: "POST",
        url: url,
        body: body,
        files: files,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        Venue venue = Venue.jsonToVenue(response['result']);
        if (body['turfId'] != '') {
          int index = _turfs.indexWhere((venue) => venue.id == body['turfId']);
          if (index != -1) _turfs[index] = venue;
        } else {
          _turfs.insert(0, venue);
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }

  addVenueAndDetailsV2({
    required String accessToken,
    required Map<String, String> body,
    required Map<String, String> files,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['addVenueAndDetailsV2']}';
      final response = await RemoteServices.formDataRequest(
        method: "POST",
        url: url,
        body: body,
        files: files,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        Venue venue = Venue.jsonToVenue(response['result']);
        if (body['turfId'] != '') {
          int index = _turfs.indexWhere((venue) => venue.id == body['turfId']);
          if (index != -1) _turfs[index] = venue;
        } else {
          _turfs.insert(0, venue);
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }

  fetchAllGroups(String accessToken) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchAllGroups']}';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Group> loadedGroup = [];
        response['result'].forEach((group) {
          loadedGroup.add(Group.jsonToGroup(group));
        });
        _groups = List.from(loadedGroup);
        notifyListeners();
      }
    } catch (error) {
      print(error);
    }
  }

  Future<Group?> createAndEditGroup({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['createAndEditGroup']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        Group group = Group.jsonToGroup(response['result']);
        if (body['groupId'] != '') {
          int index = _groups.indexWhere((data) => data.id == body['groupId']);
          if (index != -1) {
            _groups[index] = group;
          }
        } else {
          _groups.insert(0, group);
        }
        notifyListeners();
        return group;
      } else {
        return null;
      }
    } catch (error) {
      print(error);
      return null;
    }
  }

  Future<bool> deleteGroup(String accessToken, String groupId) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['deleteGroup']}?groupId=$groupId';
      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        _groups.removeWhere((data) => data.id == groupId);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }

  Future<bool> deleteVenue(String accessToken, String venueId) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['deleteVenue']}?venueId=$venueId';
      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        _turfs.removeWhere((data) => data.id == venueId);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }
}
