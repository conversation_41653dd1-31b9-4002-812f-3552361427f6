import '../../authModule/modals/userModel.dart';
import '../../common_function.dart';

import '../../authModule/providers/auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../fontSizes.dart';
import '../models/venue_model.dart';
import '../../commonWidgets/raisedButton.dart';
import '../../venueModule/screens/addTurfDetailScreen.dart';
import '../screens/turf_details_Screen.dart';

class UnVerifiedTurfWidget extends StatelessWidget {
  final Venue turf;
  final Function resetStatusType;
  final UserModal user;
  const UnVerifiedTurfWidget({
    Key? key,
    required this.turf,
    required this.resetStatusType,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: deviceWidth * 0.03,
        horizontal: deviceWidth * 0.023,
      ),
      margin: EdgeInsets.only(bottom: deviceWidth * 0.045),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Color(0xffB6B7BA),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
            child: Text(
              '${turf.name}',
              style: Theme.of(context).textTheme.displaySmall!.copyWith(
                    fontSize: textScaleFactor * displayMedium,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
            child: Text(
              '${turf.address.streetName}, ${turf.address.landmark}, ${turf.address.city}, ${turf.address.state}, ${turf.address.pincode}',
              style: Theme.of(context).textTheme.displayLarge!.copyWith(
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                    letterSpacing: .30,
                    fontSize: textScaleFactor * displayLarge,
                  ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: deviceWidth * 0.025),
                child: Text(
                  '${DateFormat('MMMM dd, yyyy, hh:mm a').format(turf.createdAt.toLocal())}',
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                        letterSpacing: .30,
                        fontSize: textScaleFactor * headline9,
                      ),
                ),
              ),
              if (turf.status == "PENDING" && turf.images!.length != 0)
                Container(
                  margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                  child: Text(
                    'In Review',
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Color(0xffBF7E0F),
                          letterSpacing: .30,
                          fontSize: textScaleFactor * headline9,
                        ),
                  ),
                ),
            ],
          ),
          turf.status == "APPROVED"
              ? Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {},
                      child: Container(
                        margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                        child: Text(
                          'Verified',
                          style:
                              Theme.of(context).textTheme.displayLarge!.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                    letterSpacing: .30,
                                    fontSize: textScaleFactor * displayLarge,
                                  ),
                        ),
                      ),
                    ),
                  ],
                )
              : turf.status == "REJECTED"
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: deviceWidth * 0.7,
                          margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                          child: Text(
                            'Your venue request has been rejected. Please contact admin.',
                            style:
                                Theme.of(context).textTheme.displayLarge!.copyWith(
                                      fontWeight: FontWeight.normal,
                                      letterSpacing: .30,
                                      color: Colors.red,
                                      fontSize: textScaleFactor * headline9,
                                    ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => callLaunch(
                              '+91${Provider.of<Auth>(context, listen: false).adminContact}'),
                          child: Container(
                            margin: EdgeInsets.only(
                              bottom: deviceWidth * 0.02,
                              right: deviceWidth * 0.02,
                            ),
                            child:
                                SvgPicture.asset('assets/svgIcons/Inquiry.svg'),
                          ),
                        ),
                      ],
                    )
                  : turf.status == "PENDING" && turf.images!.length != 0
                      ? Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                GestureDetector(
                                  onTap: () => callLaunch(
                                      '+91${Provider.of<Auth>(context, listen: false).adminContact}'),
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        bottom: deviceWidth * 0.01),
                                    child: Text(
                                      'Contact Us: +91 ${Provider.of<Auth>(context, listen: false).adminContact}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .displayLarge!
                                          .copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.green,
                                            letterSpacing: .30,
                                            fontSize:
                                                textScaleFactor * headline9,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                    ),
                                  ),
                                ),
                                Container(
                                  constraints: BoxConstraints(
                                      maxWidth: deviceWidth * 0.6),
                                  child: Text(
                                    'This will take 3 to 4 hour to list your venue.',
                                    style: Theme.of(context)
                                        .textTheme
                                        .displayLarge!
                                        .copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: Colors.black,
                                          letterSpacing: .30,
                                          fontSize: textScaleFactor * 10,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            buildRaisedButton(
                              deviceWidth * 0.23,
                              deviceWidth * 0.1,
                              () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TurfDetailsScreen(
                                      turf: turf,
                                      user: user,
                                    ),
                                  ),
                                ).then((value) {
                                  if (value != null && value) {
                                    resetStatusType();
                                  }
                                });
                              },
                              Container(
                                width: deviceWidth * 0.22,
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    'View Details',
                                    style: Theme.of(context)
                                        .textTheme
                                        .displaySmall!
                                        .copyWith(
                                          fontSize: textScaleFactor * headline9,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ),
                              ),
                              TargetPlatform.android,
                              Theme.of(context).primaryColor,
                              30,
                            )
                          ],
                        )
                      : SizedBox.shrink(),
          if (turf.status == "PENDING" && turf.images!.length == 0)
            Align(
              alignment: Alignment.bottomRight,
              child: buildRaisedButton(
                deviceWidth * 0.38,
                deviceWidth * 0.10,
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddTurfDetailScreen(
                        turf: turf,
                      ),
                    ),
                  ).then((value) {
                    if (value) {
                      resetStatusType();
                    }
                  });
                },
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: deviceWidth * 0.01),
                    Container(
                      width: deviceWidth * 0.22,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          'Add Venue Details',
                          style:
                              Theme.of(context).textTheme.displaySmall!.copyWith(
                                    fontSize: textScaleFactor * headline9,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
                TargetPlatform.android,
                Theme.of(context).primaryColor,
                30,
              ),
            ),
        ],
      ),
    );
  }
}
