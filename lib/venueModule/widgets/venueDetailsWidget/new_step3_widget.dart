import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../commonWidgets/text_widget.dart';
import '../step1Widget.dart';

class NewStep3Widget extends StatefulWidget {
  final List selectedSlots;
  final List listofSlotTime;
  final Map selectedSportCategory;
  final bool isNet;
  final int selectedSlotDuration;
  final Function selectTime;
  final Function addOrRemovePrice;
  final Function incrementAndDecrementStep;
  final Function selectSlotDuration;

  NewStep3Widget({
    Key? key,
    required this.selectedSlots,
    required this.selectedSportCategory,
    required this.isNet,
    required this.selectTime,
    required this.addOrRemovePrice,
    required this.incrementAndDecrementStep,
    required this.selectedSlotDuration,
    required this.listofSlotTime,
    required this.selectSlotDuration,
  }) : super(key: key);

  @override
  State<NewStep3Widget> createState() => _NewStep3WidgetState();
}

class _NewStep3WidgetState extends State<NewStep3Widget> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  checkIsNet(String size) {
    return true;
  }

  validateSelection(sport) {
    if (!sport['isSelected']) {
      showSnackbar(
        widget.selectedSportCategory['categoryName'] != 'Outdoor'
            ? "Please select the sport"
            : 'Please select the turf size',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Time Slot
          CustomContainer(
            vPadding: 0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * 0.035),
                TextWidget(
                  title: 'Select Time Slot Duration',
                  fontWeight: FontWeight.w600,
                ),
                SizedBox(height: deviceWidth * 0.035),
                Wrap(
                  children: [
                    ...widget.listofSlotTime.map(
                      (slot) => GestureDetector(
                        onTap: () => widget.selectSlotDuration(slot['id']),
                        child: Container(
                          width: double.infinity,
                          color: Colors.transparent,
                          padding: EdgeInsets.only(bottom: deviceWidth * 0.05),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextWidget(
                                title: '${slot['slot']} mins',
                                color: Color(0xff242530),
                                fontWeight: FontWeight.w500,
                              ),
                              Container(
                                alignment: Alignment.bottomRight,
                                height: deviceWidth * 0.05,
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: slot['slot'] ==
                                            widget.selectedSlotDuration
                                        ? Theme.of(context).primaryColor
                                        : Color(0xffB6B7BA),
                                  ),
                                ),
                                child: CircleAvatar(
                                  radius: 8,
                                  backgroundColor: slot['slot'] ==
                                          widget.selectedSlotDuration
                                      ? Theme.of(context).primaryColor
                                      : Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: deviceWidth * 0.05),
          //Slot Pricing and Time
          ...widget.selectedSlots
              .asMap()
              .map(
                (i, slot) => MapEntry(
                  i,
                  CustomContainer(
                    margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          title: '${slot['title']} Slot',
                          color: getThemeColor(),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        SizedBox(height: deviceWidth * 0.03),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              title: 'Select Slot Timing',
                              fontWeight: FontWeight.w500,
                              color: Color(0xff636363),
                            ),
                            SizedBox(height: deviceWidth * 0.03),
                            buildRowCardOfTime(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              'Start Time :-',
                              '${slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                              () {
                                widget.selectTime(
                                    true, slot['id'], slot['startTime']);
                              },
                            ),
                            SizedBox(height: deviceWidth * 0.05),
                            buildRowCardOfTime(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              'End Time :-',
                              '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                              () {
                                widget.selectTime(
                                    false, slot['id'], slot['endTime']);
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: deviceWidth * 0.07),
                        TextWidget(
                          title: widget.selectedSportCategory['categoryName'] !=
                                  'Outdoor'
                              ? 'Select Sport & Enter Pricing'
                              : 'Select Turf Sizes & Enter Pricing',
                          color: Color(0xff636363),
                          fontWeight: FontWeight.w500,
                        ),
                        TextWidget(
                          title:
                              'Include the Taxes while adding price for ${widget.selectedSlotDuration} mins.',
                          fontSize: 12,
                          color: Color(0xff636363),
                        ),
                        SizedBox(height: deviceWidth * 0.03),
                        CustomContainer(
                          radius: 8,
                          hPadding: 0.03,
                          vPadding: 0.015,
                          boxShadow: [],
                          borderColor: getThemeColor(),
                          child: Column(
                            children: [
                              SizedBox(height: deviceWidth * 0.03),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  ...[
                                    widget.selectedSportCategory[
                                                'categoryName'] !=
                                            'Outdoor'
                                        ? 'Sport'
                                        : 'Turf Size',
                                    'Weekdays',
                                    'Weekends'
                                  ].map(
                                    (type) => Container(
                                      alignment: Alignment.topLeft,
                                      width: type == 'Weekends' ||
                                              type == 'Weekdays'
                                          ? deviceWidth * 0.21
                                          : deviceWidth * .24,
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: TextWidget(
                                          title: type,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: deviceWidth * 0.03),
                              ...slot['priceAndQuantity'].map(
                                (sport) => Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        widget.addOrRemovePrice(
                                          slot['priceAndQuantity'],
                                          sport['id'],
                                          sport['isSelected'] ? false : true,
                                        );
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(
                                          right: deviceWidth * 0.03,
                                          bottom: deviceWidth * 0.03,
                                        ),
                                        width: deviceWidth * 0.22,
                                        padding: EdgeInsets.symmetric(
                                          vertical: deviceWidth * 0.03,
                                          horizontal: deviceWidth * 0.02,
                                        ),
                                        decoration: BoxDecoration(
                                          color: sport['isSelected']
                                              ? getThemeColor()
                                              : Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(7),
                                          border: Border.all(
                                              color: getThemeColor()),
                                        ),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: TextWidget(
                                            title: sport['title'],
                                            fontSize:
                                                widget.selectedSportCategory[
                                                            'categoryName'] ==
                                                        'Outdoor'
                                                    ? textScaleFactor * 14
                                                    : textScaleFactor * 12,
                                            color: sport['isSelected']
                                                ? Colors.white
                                                : getThemeColor(),
                                            fontWeight: FontWeight.w600,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () => validateSelection(sport),
                                      child: SizedBox(
                                        width: deviceWidth * 0.22,
                                        child: CustomTextFieldWithLabel(
                                          label: '',
                                          controller: sport['controller'],
                                          hintText: 'Price',
                                          hintFS: 12,
                                          borderRadius: 7,
                                          textFS: 13.5,
                                          inputFormatter: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          enabled: sport['isSelected']
                                              ? true
                                              : false,
                                          maxLength: 6,
                                          inputType: TextInputType.number,
                                          onChanged: (value) {
                                            if (value.length != 0) {
                                              sport['price'] = int.parse(value);
                                              print(sport['price']);
                                            } else {
                                              sport['price'] = 0;
                                              print(sport['price']);
                                            }
                                          },
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () => validateSelection(sport),
                                      child: SizedBox(
                                        width: deviceWidth * 0.22,
                                        child: CustomTextFieldWithLabel(
                                          label: '',
                                          controller:
                                              sport['weekendController'],
                                          hintText: 'Price',
                                          borderRadius: 7,
                                          hintFS: 12,
                                          textFS: 13.5,
                                          inputFormatter: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          enabled: sport['isSelected']
                                              ? true
                                              : false,
                                          maxLength: 6,
                                          inputType: TextInputType.number,
                                          onChanged: (value) {
                                            if (value.length != 0) {
                                              sport['weekendPrice'] =
                                                  int.parse(value);
                                              print(sport['weekendPrice']);
                                            } else {
                                              sport['weekendPrice'] = 0;
                                              print(sport['weekendPrice']);
                                            }
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: deviceWidth * 0.05)
                      ],
                    ),
                  ),
                ),
              )
              .values
              .toList(),
        ],
      ),
    );
  }
}
