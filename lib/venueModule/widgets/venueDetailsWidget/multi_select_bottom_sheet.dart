import 'dart:io';

import 'package:flutter/material.dart';

import '../../../colors.dart';
import '../../../commonWidgets/custom_button.dart';
import '../../../commonWidgets/divider_widget.dart';
import '../../../commonWidgets/new_check_box_widget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import '../../../navigators.dart';

// ignore: must_be_immutable
class MultiSelectBottomSheet extends StatefulWidget {
  final List listOfFields;
  final List<String> selectedFields;
  final String title;
  final List? ListOfSport;

  MultiSelectBottomSheet({
    required this.listOfFields,
    required this.selectedFields,
    required this.title,
    this.ListOfSport,
  });

  @override
  State<MultiSelectBottomSheet> createState() => _MultiSelectBottomSheetState();
}

class _MultiSelectBottomSheetState extends State<MultiSelectBottomSheet> {
  double dW = 0;
  double dH = 0;
  double tS = 0;
  Map language = {};

  bool isLoading = false;

  List<String> selectedFields = [];
  List listOfFields = [];

  void selectGroup(String field) {
    if (selectedFields.contains(field)) {
      selectedFields.remove(field);
    } else {
      selectedFields.add(field);
    }
    setState(() {});
  }

  void selectAll() {
    if (selectedFields.length == listOfFields.length) {
      selectedFields = [];
    } else {
      selectedFields = [];
      for (var field in listOfFields) {
        selectedFields.add(field);
      }
    }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    listOfFields = widget.listOfFields;

    selectedFields = widget.selectedFields;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;

    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        width: double.infinity,
        height: dW * 1.3,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                padding: EdgeInsets.all(dW * 0.05),
                width: dW,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextWidget(
                      title: widget.title,
                      fontSize: 14,
                      color: const Color(0xff6B6C75),
                      fontWeight: FontWeight.w500,
                    ),
                    CheckBoxWidget(
                      activeBorderColor: getThemeColor(),
                      activeColor: getThemeColor(),
                      active: selectedFields.length == listOfFields.length,
                      borderRadius: 3,
                      height: dW * 0.04,
                      iconSize: 14,
                      title: selectedFields.length == listOfFields.length
                          ? 'Unselect All'
                          : 'Select All',
                      constraintWidth: null,
                      onTap: selectAll,
                      fontSize: 14,
                      textFontWeight: FontWeight.w500,
                    ),
                  ],
                )),
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: dW * 0.03),
                    ...listOfFields.map(
                      (field) => Column(
                        children: [
                          CheckBoxWidget(
                            activeBorderColor: getThemeColor(),
                            activeColor: getThemeColor(),
                            active: selectedFields.contains(field),
                            constraintWidth: 0.75,
                            borderRadius: 3,
                            height: dW * 0.04,
                            iconSize: 14,
                            title: field,
                            onTap: () => selectGroup(field),
                            fontSize: 16,
                            textFontWeight: FontWeight.w500,
                          ),
                          const DividerWidget(),
                        ],
                      ),
                    ),
                    SizedBox(height: dW * 0.08),
                  ],
                ),
              ),
            ),
            if (listOfFields.isNotEmpty)
              Container(
                decoration: BoxDecoration(color: Colors.white, boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    offset: const Offset(0, -1),
                    spreadRadius: 0,
                    blurRadius: 5,
                  )
                ]),
                padding: EdgeInsets.only(
                  top: dW * 0.02,
                  bottom: dW * (Platform.isIOS && dH > 850 ? 0.06 : 0.03),
                  left: dW * 0.04,
                  right: dW * 0.04,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: pop,
                      child: Container(
                        width: dW * 0.3,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(vertical: dW * 0.04),
                        decoration: BoxDecoration(
                            border: null,
                            borderRadius: BorderRadius.circular(8)),
                        child: TextWidget(
                          title: 'Close',
                          fontSize: tS * 16,
                          letterSpacing: 0.36,
                          color: greyTextColor3,
                        ),
                      ),
                    ),
                    CustomButton(
                      width: dW * 0.3,
                      height: dW * 0.12,
                      buttonText: 'Save',
                      onPressed: selectedFields.isEmpty
                          ? null
                          : () => pop(selectedFields),
                      radius: 8,
                    )
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
