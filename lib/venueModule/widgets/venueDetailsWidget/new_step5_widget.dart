import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:flutter/services.dart';

import '../../../commonWidgets/checkBoxWidget.dart';
import 'package:flutter/material.dart';

import '../../../fontSizes.dart';

class NewStep5Widget extends StatefulWidget {
  Map selectedSportCategory;
  List selectedSports;
  List listOfFacilities;
  List selectedFacilities;
  final bool isNet;
  List turfSize;
  List sportQuantity;
  TextEditingController advanceAmountController;
  TextEditingController advanceAmountForOneOnController;
  Function selectAndUnSelectType;
  Function setQuantity;
  final String option;
  final List selectedSlots;
  NewStep5Widget({
    Key? key,
    required this.listOfFacilities,
    required this.selectedSportCategory,
    required this.selectedSports,
    required this.turfSize,
    required this.sportQuantity,
    required this.isNet,
    required this.selectAndUnSelectType,
    required this.selectedFacilities,
    required this.setQuantity,
    required this.advanceAmountController,
    required this.advanceAmountForOneOnController,
    required this.option,
    required this.selectedSlots,
  }) : super(key: key);

  @override
  _Step4WidgetState createState() => _Step4WidgetState();
}

class _Step4WidgetState extends State<NewStep5Widget> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  List sportQuantity = [];

  @override
  void initState() {
    super.initState();

    if (widget.selectedSportCategory['categoryName'] != 'Outdoor') {
      widget.selectedSports.forEach((sport) {
        sportQuantity.add({
          'title': sport,
          'quantity': 0,
          'label': '',
          'advanceAmount': TextEditingController()
        });
      });
    } else {
      if (widget.turfSize.contains('11:11')) {
        sportQuantity.add({
          'title': '11:11',
          'quantity': 0,
          'label': '',
          'advanceAmount': TextEditingController()
        });
      }
      if (widget.turfSize.contains('9:9')) {
        sportQuantity.add({
          'title': '9:9',
          'quantity': 0,
          'label': '',
          'advanceAmount': TextEditingController()
        });
        if (widget.turfSize.contains('7:7')) {
          sportQuantity.add({
            'title': '7:7',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
      } else if (widget.turfSize.contains('7:7')) {
        sportQuantity.add({
          'title': '7:7',
          'quantity': 0,
          'label': '',
          'advanceAmount': TextEditingController()
        });
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
      } else if (widget.turfSize.contains('5:5') &&
          !widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8')) {
        sportQuantity = [
          {
            'title': '5:5',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          }
        ];
      } else if (widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8') &&
          !widget.turfSize.contains('5:5')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          }
        ];
      } else if (widget.turfSize.contains('5:5') &&
          widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          },
          {
            'title': '5:5',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          }
        ];
      } else if (widget.turfSize.contains('8:8') ||
          widget.turfSize.contains('6:6') ||
          widget.turfSize.contains('5:5')) {
        sportQuantity = [
          {
            'title': '8:8',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          }
        ];
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          });
        }
      } else if (widget.turfSize.contains('6:6')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
            'advanceAmount': TextEditingController()
          }
        ];
      }
      if (!widget.isNet) {
        if (widget.turfSize.contains('6:6')) {
          var index =
              sportQuantity.indexWhere((element) => element['title'] == '6:6');
          if (index == -1) {
            sportQuantity.add({
              'title': '6:6',
              'quantity': 0,
              'label': '',
              'advanceAmount': TextEditingController()
            });
          }
        }
        if (widget.turfSize.contains('8:8')) {
          var index =
              sportQuantity.indexWhere((element) => element['title'] == '8:8');
          if (index == -1) {
            sportQuantity.add({
              'title': '8:8',
              'quantity': 0,
              'label': '',
              'advanceAmount': TextEditingController()
            });
          }
        }
      }
    }
    if (widget.option == 'One On') {
      sportQuantity.add({
        'title': '1:1',
        'quantity': 0,
        'label': '',
        'advanceAmount': TextEditingController()
      });
    }
    // if (!widget.isNet && widget.selectedSports.contains('Badminton')) {
    //   sportQuantity.insert(0, {'title': '2:2', 'quantity': 0, 'label': ''});
    // }
    widget.turfSize.toSet();
    sportQuantity.toSet();
    widget.setQuantity(sportQuantity);
  }

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(10),
    );
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return CustomContainer(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: widget.selectedSportCategory['categoryName'] != 'Outdoor'
                ? 'Enter Quantity'
                : widget.isNet
                    ? 'Enter venue relation & label'
                    : 'Enter venue quantity & label',
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: deviceWidth * 0.04),
          ...widget.sportQuantity.map(
            (sport) => Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  margin: EdgeInsets.only(
                    right: deviceWidth * 0.03,
                    bottom: deviceWidth * 0.03,
                  ),
                  alignment: Alignment.center,
                  width: deviceWidth * 0.25,
                  padding: EdgeInsets.symmetric(
                    vertical: deviceWidth * 0.032,
                    horizontal: deviceWidth * 0.02,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(7),
                    border: Border.all(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: TextWidget(
                      title: sport['title'],
                      fontSize: widget.selectedSportCategory['categoryName'] ==
                              'Outdoor'
                          ? textScaleFactor * 14
                          : textScaleFactor * 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(width: deviceWidth * 0.02),
                Container(
                  width: deviceWidth * 0.23,
                  margin: EdgeInsets.only(right: deviceWidth * 0.03),
                  child: TextFormField(
                    initialValue: sport['quantity'] == 0
                        ? ''
                        : sport['quantity'].toString(),
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    style: TextStyle(
                      fontSize: textScaleFactor * 14,
                      letterSpacing: .30,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      hintStyle: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(
                              fontSize: textScaleFactor * 12,
                              color: Colors.grey.shade400),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.02, vertical: 0),
                      hintText: widget.selectedSportCategory['categoryName'] !=
                              'Outdoor'
                          ? 'Quantity'
                          : widget.isNet
                              ? 'Relation'
                              : 'Quantity',
                      counterText: "",
                      fillColor: Colors.transparent,
                      border: textFormBorder(context),
                      focusedErrorBorder: textFormBorder(context),
                      focusedBorder: textFormBorder(context),
                      enabledBorder: textFormBorder(context),
                      filled: true,
                    ),
                    maxLength: 8,
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    onChanged: (value) {
                      if (value.length != 0) {
                        sport['quantity'] = int.parse(value);
                        print(sport['quantity']);
                      }
                    },
                  ),
                ),
                Container(
                  width: deviceWidth * 0.23,
                  child: TextFormField(
                    initialValue: sport['label'] == '' ? '' : sport['label'],
                    style: TextStyle(
                      fontSize: textScaleFactor * 14,
                      letterSpacing: .30,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      hintStyle: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(
                              fontSize: textScaleFactor * headline9,
                              color: Colors.grey.shade400),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: deviceWidth * 0.02,
                        vertical: 0,
                      ),
                      hintText: widget.selectedSportCategory['categoryName'] !=
                              'Outdoor'
                          ? 'Label'
                          : widget.isNet
                              ? 'Label'
                              : 'Label',
                      counterText: "",
                      fillColor: Colors.transparent,
                      border: textFormBorder(context),
                      focusedErrorBorder: textFormBorder(context),
                      focusedBorder: textFormBorder(context),
                      enabledBorder: textFormBorder(context),
                      filled: true,
                    ),
                    textInputAction: TextInputAction.next,
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    keyboardType: TextInputType.text,
                    onChanged: (value) {
                      if (value.length != 0) {
                        sport['label'] = value.trim();
                        print(sport['label']);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          // SizedBox(height: deviceWidth * 0.04),
          DividerWidget(),
          TextWidget(
            title: 'Advance Amount',
            fontWeight: FontWeight.w500,
            fontSize: 15,
          ),
          SizedBox(height: deviceWidth * 0.05),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                  title:
                      widget.selectedSportCategory['categoryName'] != 'Outdoor'
                          ? 'Sport'
                          : 'Turf Size',
                  color: Color(0xff636363)),
              TextWidget(title: 'Advance Amount', color: Color(0xff636363)),
            ],
          ),
          SizedBox(height: deviceWidth * 0.03),
          ...sportQuantity.map(
            (sport) => Padding(
              padding: EdgeInsets.only(bottom: deviceWidth * 0.04),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: deviceWidth * 0.4),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: TextWidget(
                        title: sport['title'],
                        color: getThemeColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    width: deviceWidth * 0.31,
                    child: CustomTextFieldWithLabel(
                      label: '',
                      controller: sport['advanceAmount'],
                      hintText: 'Amount',
                      inputFormatter: [FilteringTextInputFormatter.digitsOnly],
                      inputType: TextInputType.number,
                      inputAction: TextInputAction.next,
                      maxLength: 10,
                      borderRadius: 6,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // if (widget.option == 'One On') ...[
          //   SizedBox(height: deviceWidth * 0.045),
          //   TextWidget(
          //     title: 'Advance Amount for 1:1',
          //     fontWeight: FontWeight.w500,
          //   ),
          //   SizedBox(height: deviceWidth * 0.02),
          //   CustomTextFieldWithLabel(
          //     label: '',
          //     controller: widget.advanceAmountForOneOnController,
          //     hintText: 'Enter advance amount',
          //     inputFormatter: [FilteringTextInputFormatter.digitsOnly],
          //     inputType: TextInputType.number,
          //     maxLength: 10,
          //   ),
          // ],
          DividerWidget(top: 0),
          TextWidget(
            title: 'Facilities',
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.04),

          ...widget.listOfFacilities.map(
            (facility) => GestureDetector(
              onTap: () => widget.selectAndUnSelectType(
                facility['id'],
                facility['isSelected'],
                widget.listOfFacilities,
                widget.selectedFacilities,
                false,
              ),
              child: Container(
                margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
                width: deviceWidth,
                color: Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: deviceWidth * 0.7,
                      child: TextWidget(
                        title: facility['title'],
                        letterSpacing: .30,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    OldCheckBoxWidget(deviceWidth, facility['isSelected']),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
