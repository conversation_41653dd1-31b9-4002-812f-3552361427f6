import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/divider_widget.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/venueModule/widgets/cancellationWidget.dart';
import 'package:bys_business/venueModule/widgets/step1Widget.dart';
import 'package:flutter/material.dart';

import '../../../bulkBookingModule/widgets/select_duration_bottomsheet.dart';
import '../../../bulkBookingModule/widgets/select_count_bottomsheet.dart';
import '../../../commonWidgets/oldtextWidget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../fontSizes.dart';
import '../addCancellationBottomsheet.dart';

class NewStep6Widget extends StatefulWidget {
  final List cancellationCharges;
  final Function addOrRemoveCancellation;
  final Function editCancellation;
  final Function changeDuration;
  int durationCount;
  String period;
  final TextEditingController cancellationController;
  NewStep6Widget({
    Key? key,
    required this.cancellationCharges,
    required this.addOrRemoveCancellation,
    required this.editCancellation,
    required this.changeDuration,
    required this.durationCount,
    required this.period,
    required this.cancellationController,
  }) : super(key: key);

  @override
  State<NewStep6Widget> createState() => _Step5WidgetState();
}

class _Step5WidgetState extends State<NewStep6Widget> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  addCancellationBottomSheet({Map? data, int? index}) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddCancellationBottomSheet(
          cancellationCharges: widget.cancellationCharges,
          data: data,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        if (data != null) {
          widget.editCancellation(data: value, index: index);
        } else {
          widget.addOrRemoveCancellation(
            data: value,
            index: widget.cancellationCharges.isEmpty
                ? 0
                : widget.cancellationCharges.length,
            add: true,
          );
        }
      }
    });
  }

  Widget buildDuration({
    required double deviceWidth,
    required double textScaleFactor,
    required String title,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: title,
          fontSize: textScaleFactor * 15,
          color: Colors.black,
          letterSpacing: 0.4,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: deviceWidth * 0.025),
        GestureDetector(
          onTap: () {
            selectDurationOrCountDropDown(title);
          },
          child: Container(
            width: deviceWidth * 0.38,
            height: deviceWidth * 0.12,
            padding: EdgeInsets.symmetric(
              horizontal: deviceWidth * 0.03,
              vertical: deviceWidth * 0.005,
            ),
            margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
            decoration: BoxDecoration(
              border: Border.all(width: 1, color: getThemeColor()),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: value,
                  fontSize: 15,
                  letterSpacing: 0.3,
                ),
                Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  selectDurationOrCountDropDown(String duration) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: duration == 'Period'
            ? SelectDurationBottomSheet(
                selectedDuration: widget.period,
                addDays: true,
              )
            : SelectCountBottomSheet(
                selectedDuration: widget.period,
                selectedCount: widget.durationCount,
              ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        widget.changeDuration(duration, value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return CustomContainer(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: 'Booking Availability Duration',
            fontSize: 15,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.04),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildDuration(
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                title: 'Period',
                value: widget.period,
              ),
              buildDuration(
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                title: 'Count',
                value: widget.durationCount.toString(),
              ),
            ],
          ),
          DividerWidget(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                    title: 'Cancellation Charges',
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: () => addCancellationBottomSheet(),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: deviceWidth * 0.035,
                        vertical: deviceWidth * 0.014,
                      ),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: Theme.of(context).primaryColor),
                        borderRadius: BorderRadius.circular(5),
                        color: Theme.of(context).primaryColor,
                      ),
                      child: TextWidget(
                        title: 'Add',
                        fontSize: 13,
                        color: Colors.white,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(height: deviceWidth * 0.015),
              TextWidget(
                title: 'Add hour range and user refund percentage.',
                fontSize: 12,
                color: Colors.black54,
              ),
            ],
          ),
          SizedBox(height: deviceWidth * 0.05),
          // if (widget.cancellationCharges.isEmpty)
          //   Container(
          //     margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
          //     child: buildText(
          //       text: 'Add your cancellation charges.',
          //       textScaleFactor: textScaleFactor,
          //       context: context,
          //       fontSize: 14.5,
          //     ),
          //   ),
          if (widget.cancellationCharges.isNotEmpty)
            ...widget.cancellationCharges
                .asMap()
                .map(
                  (i, data) => MapEntry(
                    i,
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        CancellationWidget(
                          data: data,
                          deviceWidth: deviceWidth,
                          textScaleFactor: textScaleFactor,
                        ),
                        Positioned(
                          right: -10,
                          child: PopupMenuButton(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(7),
                            ),
                            icon: Icon(
                              Icons.more_vert,
                              color: Colors.black,
                            ),
                            itemBuilder: (BuildContext bc) => [
                              PopupMenuItem(
                                child: Text(
                                  "Edit",
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: textScaleFactor * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 1,
                              ),
                              PopupMenuItem(
                                child: Text(
                                  'Delete',
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: textScaleFactor * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 2,
                              ),
                            ],
                            onSelected: (value) {
                              if (value == 1) {
                                addCancellationBottomSheet(
                                  data: data,
                                  index: i,
                                );
                              } else if (value == 2) {
                                widget.addOrRemoveCancellation(
                                  data: data,
                                  index: i,
                                  add: false,
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .values
                .toList(),
          SizedBox(height: deviceWidth * 0.035),
          TextWidget(
            title: 'Cancellation Policy',
            fontSize: 15,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.035),
          CustomTextFieldWithLabel(
            label: '',
            controller: widget.cancellationController,
            hintText: 'Write your venue cancellation policy here...',
            textCapitalization: TextCapitalization.sentences,
            inputType: TextInputType.streetAddress,
            inputAction: TextInputAction.newline,
            maxLines: 8,
            maxLength: 500,
            hintFS: 12,
            textFS: 14,
          ),
        ],
      ),
    );
  }
}
