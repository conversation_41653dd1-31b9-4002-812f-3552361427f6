// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/commonWidgets/open_media_full_screen.dart';
import 'package:bys_business/navigators.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../../commonWidgets/asset_svg_icon.dart';
import '../../../commonWidgets/materialCircularLoader.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import '../../../new_colors.dart';

class NewStep4Widget extends StatelessWidget {
  final List<String> selectedImagePaths;
  final String videoPath;
  final String videoThumbnail;
  final Function imagePickerBottomSheet;
  final Function videoPickerBottomSheet;
  final Function removeSelectedOrVideo;
  final bool isCompressingVideo;
  NewStep4Widget({
    Key? key,
    required this.selectedImagePaths,
    required this.videoPath,
    required this.videoThumbnail,
    required this.imagePickerBottomSheet,
    required this.videoPickerBottomSheet,
    required this.removeSelectedOrVideo,
    required this.isCompressingVideo,
  }) : super(key: key);

  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  List guidelines = [
    "Pixels:-2720 x 2080 pixels (17:13)ratio",
    "One image is mandatory.",
    "Max. video duration limit is 30 Sec.",
  ];

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return CustomContainer(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: 'Venue Images',
            color: Theme.of(context).primaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.04),
          TextWidget(
            title: 'Guidelines For Images & Videos',
            color: getGreyColor1(context),
          ),
          SizedBox(height: deviceWidth * 0.02),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: guidelines
                .asMap()
                .map(
                  (i, guideline) => MapEntry(
                    i,
                    Container(
                      margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
                      child: TextWidget(
                        title: '${i + 1}. $guideline',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff636363),
                      ),
                    ),
                  ),
                )
                .values
                .toList(),
          ),
          SizedBox(height: deviceWidth * 0.03),
          Row(
            children: [
              AssetSvgIcon(iconName: 'info_rounded', color: getThemeColor()),
              SizedBox(width: deviceWidth * 0.02),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: TextWidget(
                    title: 'You can add upto 5 images of the turf.',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: getGreyColor8(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: deviceWidth * 0.04),
          Row(
            children: [
              const TextWidget(
                  title: 'Upload Images', fontWeight: FontWeight.w500),
              TextWidget(title: '*', color: getRedColor1(context)),
            ],
          ),
          SizedBox(height: deviceWidth * 0.06),
          Wrap(
            spacing: 15,
            runSpacing: 15,
            children: [
              ...selectedImagePaths
                  .asMap()
                  .map(
                    (index, imagePath) => MapEntry(
                      index,
                      GestureDetector(
                        onTap: () => imagePath.isEmpty
                            ? imagePickerBottomSheet(index)
                            : null,
                        child: SizedBox(
                          height: deviceWidth * 0.48,
                          width: deviceWidth * 0.38,
                          child: imagePath.isEmpty
                              ? DottedBorder(
                                  color: getLightGreyColor1(context),
                                  dashPattern: const [10, 10],
                                  radius: const Radius.circular(8),
                                  borderType: BorderType.RRect,
                                  child: Container(
                                    width: deviceWidth,
                                    padding: EdgeInsets.symmetric(
                                        vertical: deviceWidth * .14),
                                    decoration: BoxDecoration(
                                        color: getLightGreyColor2(context)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const AssetSvgIcon(
                                            iconName: 'camera', height: 30),
                                        SizedBox(height: deviceWidth * 0.02),
                                        TextWidget(
                                          title: 'Add Photo',
                                          fontWeight: FontWeight.w400,
                                          color: getGreyColor2(context),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Positioned(
                                      top: 0,
                                      bottom: 0,
                                      right: 0,
                                      left: 0,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: imagePath.contains('https')
                                            ? Image.network(
                                                imagePath,
                                                fit: BoxFit.cover,
                                              )
                                            : Image.file(
                                                File(imagePath),
                                                fit: BoxFit.cover,
                                              ),
                                      ),
                                    ),
                                    Positioned(
                                      right: -8,
                                      top: -8,
                                      child: GestureDetector(
                                        onTap: () =>
                                            removeSelectedOrVideo(index),
                                        child: CircleAvatar(
                                          radius: 12,
                                          backgroundColor: getThemeColor(),
                                          child: Icon(Icons.clear,
                                              size: 18, color: Colors.white),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                        ),
                      ),
                    ),
                  )
                  .values
                  .toList()
            ],
          ),
          if (selectedImagePaths.length >= 2 &&
              selectedImagePaths.length < 5 &&
              !selectedImagePaths.contains(''))
            GestureDetector(
              onTap: () => imagePickerBottomSheet(selectedImagePaths.length),
              child: Container(
                margin: EdgeInsets.only(top: deviceWidth * 0.04),
                width: deviceWidth,
                height: deviceWidth * 0.12,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: getThemeColor())),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TextWidget(
                      title: 'Add more photos',
                      fontWeight: FontWeight.w600,
                      color: getThemeColor(),
                    ),
                  ],
                ),
              ),
            ),
          SizedBox(height: deviceWidth * 0.06),
          Row(
            children: [
              const TextWidget(title: 'Upload Videos'),
              TextWidget(title: '*', color: getRedColor1(context)),
            ],
          ),
          SizedBox(height: deviceWidth * 0.06),
          videoPath.isEmpty
              ? GestureDetector(
                  onTap: isCompressingVideo
                      ? null
                      : () => videoPickerBottomSheet(),
                  child: DottedBorder(
                    color: getLightGreyColor1(context),
                    dashPattern: const [10, 10],
                    radius: const Radius.circular(8),
                    borderType: BorderType.RRect,
                    child: Container(
                      width: deviceWidth,
                      padding:
                          EdgeInsets.symmetric(vertical: deviceWidth * .14),
                      decoration:
                          BoxDecoration(color: getLightGreyColor2(context)),
                      child: isCompressingVideo
                          ? Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.08),
                              child: circularForButton(deviceWidth,
                                  color: getThemeColor()),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const AssetSvgIcon(iconName: 'camera'),
                                SizedBox(height: deviceWidth * 0.02),
                                TextWidget(
                                  title: 'Add Video',
                                  fontWeight: FontWeight.w400,
                                  color: getGreyColor2(context),
                                ),
                              ],
                            ),
                    ),
                  ),
                )
              : GestureDetector(
                  onTap: () {
                    push(OpenMediaFullScreen(
                        type: 'Video', isLocal: true, url: videoPath));
                  },
                  child: Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: videoThumbnail.contains('https')
                            ? Image.network(
                                videoThumbnail,
                                width: deviceWidth,
                                height: deviceWidth * 0.5,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(videoThumbnail),
                                width: deviceWidth,
                                height: deviceWidth * 0.5,
                                fit: BoxFit.cover,
                              ),
                      ),
                      Icon(
                        Icons.play_circle_outline_rounded,
                        color: Colors.white,
                        size: deviceWidth * 0.1,
                      ),
                      Positioned(
                        right: -8,
                        top: -8,
                        child: GestureDetector(
                          onTap: () => removeSelectedOrVideo(0, isImage: false),
                          child: CircleAvatar(
                            radius: 12,
                            backgroundColor: getThemeColor(),
                            child: Icon(Icons.clear,
                                size: 18, color: Colors.white),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
        ],
      ),
    );
  }
}
