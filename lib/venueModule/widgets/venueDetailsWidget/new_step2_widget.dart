// ignore_for_file: must_be_immutable

import '../../../commonWidgets/custom_container.dart';
import '../../../commonWidgets/divider_widget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import 'package:flutter/material.dart';

import 'multi_select_bottom_sheet.dart';
import 'new_step1_widget.dart';

class NewStep2Widget extends StatelessWidget {
  List listOfSlots;
  List listOfDays;
  List selectedWeekDays;
  List selectedWeekEnds;
  List selectedSlots;
  List listOfTurfSizeAndPrice;
  List selectedTurfSize;
  List listofSlotTime;
  List listOfTurfOption;
  int selectedSlotDuration;

  bool isNet;
  String option;
  Map selectedSportCategory;
  Function selectSportCategory;
  Function setUsePreviousValueForPrice;
  Function selectAndUnSelectType;
  Function setIsNetValue;
  Function selectSlotDuration;
  Function selectTime;
  Function incrementAndDecrementStep;
  Function setListOfSlots;
  Function setTurfOption;
  Function selectAndUnselectDays;

  NewStep2Widget({
    Key? key,
    required this.listOfSlots,
    required this.selectedSlots,
    required this.selectAndUnSelectType,
    required this.setIsNetValue,
    required this.listOfDays,
    required this.selectedWeekDays,
    required this.selectedWeekEnds,
    required this.setUsePreviousValueForPrice,
    required this.isNet,
    required this.option,
    required this.listOfTurfSizeAndPrice,
    required this.selectedTurfSize,
    required this.selectedSlotDuration,
    required this.selectTime,
    required this.listofSlotTime,
    required this.selectSlotDuration,
    required this.incrementAndDecrementStep,
    required this.setListOfSlots,
    required this.selectedSportCategory,
    required this.selectSportCategory,
    required this.listOfTurfOption,
    required this.setTurfOption,
    required this.selectAndUnselectDays,
  }) : super(key: key);

  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  checkTurfSize(size) {
    if (option == 'One On') {
      if (selectedTurfSize.length == 1) {
        return false;
      } else {
        if (selectedTurfSize.contains(size)) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      if (isNet) {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else if (size == '8:8') {
          if (selectedTurfSize.contains('9:9') ||
              selectedTurfSize.contains('7:7')) {
            return true;
          } else {
            return false;
          }
        } else if (size == '9:9' || size == '7:7') {
          if (selectedTurfSize.contains('8:8')) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }

        //Blocking 6 and 5
        // if (size == '8:8') {
        //   if (selectedTurfSize.contains('9:9') ||
        //       selectedTurfSize.contains('7:7') ||
        //       selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '9:9' || size == '7:7') {
        //   if (selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '6:6') {
        //   if (selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '5:5') {
        //   if (selectedTurfSize.contains('6:6') ||
        //       selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else {
        //   return false;
        // }
      } else {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  List<String> getDays(bool isWeekdays) {
    if (isWeekdays) {
      if (selectedWeekEnds.contains('Fri')) {
        return ['Mon', 'Tue', 'Wed', 'Thu'];
      } else {
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
      }
    } else {
      if (selectedWeekDays.contains('Fri')) {
        return ['Sat', 'Sun'];
      } else {
        return [
          'Fri',
          'Sat',
          'Sun',
        ];
      }
    }
  }

  selectDaysBottomSheet({
    required BuildContext context,
    required bool isWeekdays,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => MultiSelectBottomSheet(
        listOfFields: getDays(isWeekdays),
        title: 'Select ${isWeekdays ? 'Weekdays' : 'Weekends'}',
        selectedFields: isWeekdays
            ? List<String>.from(selectedWeekDays)
            : List<String>.from(selectedWeekEnds),
      ),
    ).then((value) {
      if (value != null) {
        try {
          selectAndUnselectDays(isWeekdays: isWeekdays, days: value);
        } catch (e) {
          print(e);
        }
      }
    });
  }

  Widget buildDaysSelectionWidget({
    required BuildContext context,
    required bool isWeekdays,
    required List listOfIterate,
  }) {
    return GestureDetector(
      onTap: () =>
          selectDaysBottomSheet(context: context, isWeekdays: isWeekdays),
      child: CustomContainer(
        boxShadow: [],
        radius: 8,
        vPadding: 0.025,
        hPadding: 0,
        borderColor: getThemeColor(),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: deviceWidth * 0.7,
              child: (selectedWeekDays.isEmpty && isWeekdays) ||
                      (selectedWeekEnds.isEmpty && !isWeekdays)
                  ? Padding(
                      padding: EdgeInsets.only(left: deviceWidth * 0.03),
                      child: TextWidget(
                        title: 'Select ${isWeekdays ? 'weekdays' : 'weekends'}',
                        textAlign: TextAlign.left,
                      ),
                    )
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: BouncingScrollPhysics(),
                      child: Row(
                        children: [
                          SizedBox(width: deviceWidth * 0.03),
                          ...listOfIterate.map(
                            (day) => Container(
                              margin:
                                  EdgeInsets.only(right: deviceWidth * 0.02),
                              constraints:
                                  BoxConstraints(minWidth: deviceWidth * 0.13),
                              padding: EdgeInsets.symmetric(
                                vertical: deviceWidth * 0.015,
                                horizontal: deviceWidth * 0.02,
                              ),
                              decoration: BoxDecoration(
                                color: getThemeColor(),
                                borderRadius: BorderRadius.circular(50),
                                border: Border.all(color: getThemeColor()),
                              ),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: day,
                                  fontSize: 13,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: deviceWidth * 0.03),
                        ],
                      ),
                    ),
            ),
            Padding(
              padding: EdgeInsets.only(right: deviceWidth * 0.02),
              child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: 'Sport Availability',
            fontSize: 15,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.04),
          CustomContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //Select Weekdays

                TextWidget(
                  title: 'Select Weekdays',
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(height: deviceWidth * 0.03),
                buildDaysSelectionWidget(
                  context: context,
                  isWeekdays: true,
                  listOfIterate: selectedWeekDays,
                ),
                SizedBox(height: deviceWidth * 0.035),

                //Select Weekends

                TextWidget(
                  title: 'Select Weekends',
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(height: deviceWidth * 0.03),
                buildDaysSelectionWidget(
                  context: context,
                  isWeekdays: false,
                  listOfIterate: selectedWeekEnds,
                ),
                SizedBox(height: deviceWidth * 0.04),

                //Select Slots
                TextWidget(
                  title: 'Select Slot',
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(height: deviceWidth * 0.03),
                Wrap(
                  children: [
                    ...listOfSlots.map(
                      (slot) => buildValueContainer(
                        deviceWidth: deviceWidth,
                        title: slot['title'],
                        id: slot['id'],
                        isSelected: slot['isSelected'],
                        maxWidth: 0.32,
                        onTap: selectAndUnSelectType,
                        listToIterate: listOfSlots,
                        listToSet: selectedSlots,
                        turfSize: true,
                        image:
                            'assets/images/${slot['title'].toLowerCase()}.png',
                      ),
                    )
                  ],
                ),

                if (selectedSportCategory['categoryName'] == 'Outdoor')
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DividerWidget(),
                      //Select Turf Options

                      TextWidget(
                        title: 'Select Turf Option',
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(height: deviceWidth * 0.035),
                      ...listOfTurfOption.map(
                        (data) => GestureDetector(
                          onTap: () => setTurfOption(data['title']),
                          child: Container(
                            color: Colors.transparent,
                            padding:
                                EdgeInsets.only(bottom: deviceWidth * 0.035),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  alignment: Alignment.topLeft,
                                  width: deviceWidth * 0.72,
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: TextWidget(
                                      title: '${data['label']}',
                                    ),
                                  ),
                                ),
                                Container(
                                  alignment: Alignment.bottomRight,
                                  height: deviceWidth * 0.05,
                                  padding: EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: data['isSelected']
                                          ? Theme.of(context).primaryColor
                                          : Color(0xffB6B7BA),
                                    ),
                                  ),
                                  child: CircleAvatar(
                                    radius: 8,
                                    backgroundColor: data['isSelected']
                                        ? Theme.of(context).primaryColor
                                        : Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      //Select Turf Size

                      SizedBox(height: deviceWidth * 0.035),

                      TextWidget(
                        title: 'Select Turf Sizes',
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(height: deviceWidth * 0.035),
                      Wrap(
                        children: [
                          ...listOfTurfSizeAndPrice.map(
                            (size) => GestureDetector(
                              onTap: size['title'] == '1:1'
                                  ? null
                                  : () {
                                      if (!checkTurfSize(size['title'])) {
                                        selectAndUnSelectType(
                                          size['id'],
                                          size['isSelected'],
                                          listOfTurfSizeAndPrice,
                                          selectedTurfSize,
                                          false,
                                        );
                                        setUsePreviousValueForPrice();
                                      }
                                    },
                              child: Container(
                                margin: EdgeInsets.only(
                                  right: deviceWidth * 0.03,
                                  bottom: deviceWidth * 0.03,
                                ),
                                constraints: BoxConstraints(
                                  minWidth: size['title'] == '1:1'
                                      ? deviceWidth * 0.09
                                      : deviceWidth * 0.11,
                                ),
                                padding: EdgeInsets.symmetric(
                                  vertical: deviceWidth * 0.02,
                                  horizontal: deviceWidth * 0.04,
                                ),
                                decoration: BoxDecoration(
                                  color: checkTurfSize(size['title'])
                                      ? Colors.grey.shade300
                                      : size['isSelected']
                                          ? Theme.of(context).primaryColor
                                          : Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: checkTurfSize(size['title'])
                                        ? Colors.black12
                                        : size['isSelected']
                                            ? Theme.of(context).primaryColor
                                            : Color(0xff5E5E5E),
                                  ),
                                ),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title: size['title'],
                                    color: checkTurfSize(size['title'])
                                        ? Colors.grey.shade500
                                        : size['isSelected']
                                            ? Colors.white
                                            : Color(0xff5E5E5E),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Widget buildTitleContainer(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String title, {
  bool showSubtitle = false,
  String subTitleText = '',
  bool applybottomMargin = true,
}) {
  return Container(
    margin: EdgeInsets.only(
      bottom: applybottomMargin ? deviceWidth * 0.03 : 0,
    ),
    child: RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * 16,
                ),
          ),
          TextSpan(
            text: '*',
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * 16,
                  color: Color(0xffAA0404),
                ),
          ),
          if (showSubtitle)
            TextSpan(
              text: '\n$subTitleText',
              style: Theme.of(context).textTheme.displayLarge!.copyWith(
                    fontSize: textScaleFactor * 14,
                    color: Color(0xff242530),
                  ),
            ),
        ],
      ),
    ),
  );
}

Widget buildDivider(double deviceWidth) {
  return Container(
    margin: EdgeInsets.only(
      top: deviceWidth * 0.01,
      bottom: deviceWidth * 0.05,
    ),
    child: Divider(
      height: deviceWidth * 0.02,
      color: Colors.grey.shade200,
      thickness: 4,
    ),
  );
}

Widget buildRowCardOfTime(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String title,
  String value,
  Function selectTime, {
  bool showEditButtom = true,
}) {
  return GestureDetector(
    onTap: () {
      selectTime();
    },
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(
              Icons.watch_later_outlined,
              color: Colors.black54,
            ),
            SizedBox(width: deviceWidth * 0.02),
            Text(
              title,
              style: Theme.of(context).textTheme.displayLarge!.copyWith(
                    fontSize: textScaleFactor * 14,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ],
        ),
        Container(
          width: deviceWidth * 0.32,
          padding: EdgeInsets.symmetric(
            vertical: deviceWidth * 0.025,
            horizontal: deviceWidth * 0.03,
          ),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(100),
            border: Border.all(
              color: Colors.black,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                        fontSize: textScaleFactor * 14,
                        color: Colors.black87,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              if (showEditButtom) SizedBox(width: deviceWidth * 0.01),
              if (showEditButtom)
                Icon(
                  Icons.mode_edit_outline_outlined,
                  size: 18,
                )
            ],
          ),
        ),
      ],
    ),
  );
}
