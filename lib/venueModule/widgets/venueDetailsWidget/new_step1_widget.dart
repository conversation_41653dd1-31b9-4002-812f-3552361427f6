import '../../../commonWidgets/custom_container.dart';
import '../../../commonWidgets/custom_text_field.dart';
import '../../../commonWidgets/divider_widget.dart';
import '../../../commonWidgets/text_widget.dart';
import '../../../common_function.dart';
import 'package:flutter/material.dart';

class NewStep1Widget extends StatefulWidget {
  final List listOfSports;
  final List listofSportCategory;
  final List listOfSlots;
  final List selectedSports;
  final bool isNet;

  final Map selectedSportCategory;
  final Function selectSportCategory;
  final Function selectAndUnSelectType;

  final TextEditingController venueNameController;
  final TextEditingController aboutTurfController;

  NewStep1Widget({
    Key? key,
    required this.listOfSports,
    required this.listofSportCategory,
    required this.listOfSlots,
    required this.selectAndUnSelectType,
    required this.selectedSports,
    required this.isNet,
    required this.selectedSportCategory,
    required this.selectSportCategory,
    required this.venueNameController,
    required this.aboutTurfController,
  }) : super(key: key);

  @override
  State<NewStep1Widget> createState() => _NewStep1WidgetState();
}

class _NewStep1WidgetState extends State<NewStep1Widget> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(height: deviceWidth * 0.02),
            CustomContainer(
              child: Column(
                children: [
                  CustomTextFieldWithLabel(
                    label: 'Venue Name',
                    controller: widget.venueNameController,
                    hintText: 'Enter venue name',
                    onChanged: (value) => setState(() {}),
                    inputAction: TextInputAction.next,
                  ),
                  SizedBox(height: deviceWidth * 0.05),
                  CustomTextFieldWithLabel(
                    label: 'About Venue',
                    controller: widget.aboutTurfController,
                    hintText: 'Enter about venue.....',
                    counterText:
                        '${widget.aboutTurfController.text.trim().length}/300',
                    maxLines: 6,
                    maxLength: 300,
                    textFS: 14,
                    textCapitalization: TextCapitalization.sentences,
                    inputType: TextInputType.streetAddress,
                    inputAction: TextInputAction.newline,
                    onChanged: (value) => setState(() {}),
                  ),
                ],
              ),
            ),
            SizedBox(height: deviceWidth * 0.07),
            TextWidget(
              title: 'Sport Details',
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            SizedBox(height: deviceWidth * 0.03),
            CustomContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Sport Category
                  TextWidget(
                    title: 'Select Sport Category',
                    fontWeight: FontWeight.w500,
                  ),
                  SizedBox(height: deviceWidth * 0.03),
                  ...widget.listofSportCategory.map(
                    (cat) => GestureDetector(
                      onTap: widget.isNet && cat['title'] == '2:2'
                          ? null
                          : () => widget.selectSportCategory(cat['id']),
                      child: Container(
                        color: Colors.transparent,
                        padding:
                            EdgeInsets.symmetric(vertical: deviceWidth * 0.02),
                        margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextWidget(title: '${cat['title']}'),
                            Container(
                              alignment: Alignment.bottomRight,
                              height: deviceWidth * 0.05,
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: cat['isSelected']
                                      ? Theme.of(context).primaryColor
                                      : Color(0xffB6B7BA),
                                ),
                              ),
                              child: CircleAvatar(
                                radius: 8,
                                backgroundColor: cat['isSelected']
                                    ? Theme.of(context).primaryColor
                                    : Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  DividerWidget(top: 0),
                  //Sport Type
                  TextWidget(
                    title: 'Select Sport Type',
                    fontWeight: FontWeight.w500,
                  ),
                  SizedBox(height: deviceWidth * 0.03),
                  Wrap(
                    children: [
                      ...widget.listOfSports
                          .where((sport) =>
                              sport['categoryId'] ==
                              widget.selectedSportCategory['categoryId'])
                          .toList()
                          .map(
                            (sport) => buildValueContainer(
                              deviceWidth: deviceWidth,
                              title: sport['title'],
                              id: sport['id'],
                              image: sport['image'],
                              maxWidth: 0.35,
                              isSelected: sport['isSelected'],
                              onTap: widget.selectAndUnSelectType,
                              listToIterate: widget.listOfSports,
                              listToSet: widget.selectedSports,
                            ),
                          )
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget buildValueContainer({
  required String title,
  required double deviceWidth,
  required String id,
  String image = '',
  required bool isSelected,
  required Function onTap,
  required List listToIterate,
  required List listToSet,
  bool turfSize = false,
  bool addSlot = false,
  double maxWidth = .2,
}) {
  return GestureDetector(
    onTap: () {
      if (!addSlot) {
        onTap(
          id,
          isSelected,
          listToIterate,
          listToSet,
          turfSize,
        );
      } else {
        onTap();
      }
    },
    child: Container(
      margin: EdgeInsets.only(
        right: deviceWidth * 0.03,
        bottom: deviceWidth * 0.03,
      ),
      constraints: BoxConstraints(maxWidth: deviceWidth * maxWidth),
      padding: EdgeInsets.symmetric(
        vertical: deviceWidth * 0.025,
        horizontal: deviceWidth * 0.03,
      ),
      decoration: BoxDecoration(
        color: isSelected ? getThemeColor() : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: getThemeColor()),
      ),
      child: Row(
        children: [
          if (image != '') ...[
            !image.contains('https')
                ? Image.asset(image, scale: 2)
                : Image.network(image, scale: title == 'PC- Games' ? 5.2 : 3),
            SizedBox(width: deviceWidth * .02),
          ],
          ConstrainedBox(
            constraints:
                BoxConstraints(maxWidth: deviceWidth * (maxWidth - .04)),
            child: TextWidget(
              title: title,
              fontSize: title == 'PC- Games' ? 12 : 13.5,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : getThemeColor(),
            ),
          ),
        ],
      ),
    ),
  );
}
