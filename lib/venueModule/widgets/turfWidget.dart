import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/navigators.dart';
import 'package:bys_business/new_colors.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/add_venue_details_screen.dart';
import 'package:bys_business/venueModule/screens/addTurfDetailScreen.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/text_widget.dart';
import '../screens/turf_details_Screen.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../models/venue_model.dart';
import 'package:flutter/material.dart';

import '../../fontSizes.dart';

class TurfWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final Venue turf;
  final UserModal user;
  const TurfWidget({
    Key? key,
    required this.dW,
    required this.tS,
    required this.turf,
    required this.user,
  }) : super(key: key);

  findLowestValue(Venue turf) {
    List<double> prices = [];
    turf.slots!.forEach((slot) {
      slot.priceAndQuantity.forEach((data) {
        if (data.price != 0) {
          prices.add(data.price);
        }
        if (data.weekendPrice != 0) {
          prices.add(data.weekendPrice);
        }
      });
    });
    if (prices.length > 0) {
      return (prices[0] * 2).toStringAsFixed(2);
    } else {
      return 0.00;
    }
  }

  getSportSvg(String sport) {
    String icon = '';
    switch (sport) {
      case 'Football':
        icon = 'assets/svgIcons/Footballl.svg';
        break;
      case 'Table Tennis':
        icon = 'assets/svgIcons/Tabletennis.svg';
        break;
      case 'Cricket':
        icon = 'assets/svgIcons/Cricket.svg';
        break;
      case 'Badminton':
        icon = 'assets/svgIcons/Badminton.svg';
        break;
      case 'Snooker':
        icon = 'assets/svgIcons/Snooker.svg';
        break;
      default:
        icon = 'assets/svgIcons/Cricket.svg';
        break;
    }
    return icon;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: turf.days!.isEmpty
          ? () => push(AddVenueDetailsScreen())
          : (turf.status == "APPROVED" || turf.status == 'PENDING')
              ? () => push(TurfDetailsScreen(turf: turf, user: user))
              : null,
      child: CustomContainer(
        margin: EdgeInsets.only(bottom: dW * .05),
        vPadding: 0,
        hPadding: 0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(vertical: dW * .025),
              decoration: BoxDecoration(
                color: turf.status == 'PENDING'
                    ? getLightYellowColor1(context)
                    : turf.status == 'APPROVED'
                        ? getLightGreenColor1(context)
                        : getLightRedColor1(context),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(8), topLeft: Radius.circular(8)),
              ),
              child: TextWidget(
                title: turf.status,
                fontWeight: FontWeight.w600,
                color: turf.status == 'PENDING'
                    ? getDarkYellowColor1(context)
                    : turf.status == 'APPROVED'
                        ? getThemeColor()
                        : getRedColor2(context),
              ),
            ),
            Container(
              padding: EdgeInsets.all(dW * .04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (turf.group != null && turf.isPrimaryVenue)
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: dW * 0.03, vertical: dW * 0.01),
                      margin: EdgeInsets.only(bottom: dW * 0.03),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Color(0xffFFE796)),
                      child: Text(
                        '${turf.group!.name} - Primary Venue',
                        style: TextStyle(
                          fontFamily: 'Poppins',
                          fontStyle: FontStyle.italic,
                          fontWeight: FontWeight.w500,
                          fontSize: 11,
                          color: Color(0xff3E3E3E),
                        ),
                      ),
                    ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(
                            maxWidth: turf.group != null && !turf.isPrimaryVenue
                                ? dW * 0.6
                                : dW * 0.8),
                        child: TextWidget(
                          title: turf.name,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xff3E3E3E),
                        ),
                      ),
                      if (turf.group != null && !turf.isPrimaryVenue)
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: dW * 0.03, vertical: dW * 0.01),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: Color(0xffFFE796)),
                          child: Text(
                            turf.group!.name,
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w500,
                              fontSize: 11,
                              color: Color(0xff3E3E3E),
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: dW * .015),
                  TextWidget(
                    title: turf.address.fullAddress.trim().isEmpty
                        ? '${turf.address.area.isEmpty ? turf.address.streetName : turf.address.area}, ${turf.address.landmark}, ${turf.address.city}, ${turf.address.state}, ${turf.address.pincode}.'
                        : '${turf.address.fullAddress}',
                    fontSize: 12,
                    color: Color(0xff636363),
                  ),
                  if (turf.status != 'REJECTED' && turf.sportsType != null)
                    Padding(
                      padding:
                          EdgeInsets.only(top: dW * 0.01, bottom: dW * 0.01),
                      child: Wrap(
                        children: [
                          ...turf.sportsType!.map((sport) {
                            return Card(
                              margin: EdgeInsets.only(right: dW * 0.015),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20)),
                              child: CircleAvatar(
                                radius: 13,
                                backgroundColor: Colors.white,
                                child: CircleAvatar(
                                  backgroundColor: Color(0xffEAEAEA),
                                  radius: 10,
                                  child: CachedNetworkImage(
                                    repeat: ImageRepeat.repeat,
                                    fit: BoxFit.cover,
                                    height: 16,
                                    imageUrl: sport.image,
                                    placeholder: (_, __) => Image.asset(
                                      'assets/images/placeholder.jpg',
                                      height: 16,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  // Image.network(sport.image, height: 16),
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  if (turf.status != 'REJECTED') ...[
                    SizedBox(height: dW * .015),
                    TextWidget(
                      title: DateFormat('dd MMM yyyy, hh:mm a')
                          .format(turf.createdAt),
                      fontSize: 12,
                      color: Color(0xff636363),
                    ),
                  ],
                  // if (turf.status != 'REJECTED' && turf.images!.isEmpty)
                  //   Padding(
                  //     padding: EdgeInsets.only(top: dW * 0.05),
                  //     child: CustomButton(
                  //       radius: 8,
                  //       width: dW,
                  //       height: dW * .12,
                  //       buttonText: 'Add Venue Details',
                  //       fontSize: 14,
                  //       onPressed: () {
                  //         push(AddTurfDetailScreen(turf: turf));
                  //       },
                  //     ),
                  //   ),
                  if (turf.status == 'REJECTED')
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: dW * 0.7,
                            margin: EdgeInsets.only(bottom: dW * 0.01),
                            child: TextWidget(
                              title:
                                  'Your venue request has been rejected. Please contact admin.',
                              fontWeight: FontWeight.normal,
                              letterSpacing: .30,
                              color: Colors.red,
                              fontSize: tS * headline9,
                            ),
                          ),
                          GestureDetector(
                            onTap: () => callLaunch(
                                '+91${Provider.of<Auth>(context, listen: false).adminContact}'),
                            child: Container(
                              margin: EdgeInsets.only(
                                bottom: dW * 0.02,
                                right: dW * 0.02,
                              ),
                              child: AssetSvgIcon(iconName: 'Inquiry'),
                            ),
                          ),
                        ],
                      ),
                    )
                ],
              ),
            )
          ],
        ),
      ),

      // child: Container(
      //   color: Colors.transparent,
      //   margin: EdgeInsets.only(bottom: dW * 0.045),
      //   child: Row(
      //     children: [
      //       Container(
      //         height: dW * 0.25,
      //         width: dW * 0.37,
      //         decoration: BoxDecoration(
      //           border: Border.all(
      //             color: Color.fromRGBO(222, 223, 227, 1),
      //           ),
      //           borderRadius: BorderRadius.circular(8),
      //         ),
      //         child: ClipRRect(
      //           borderRadius: BorderRadius.circular(8),
      //           child: CachedNetworkImage(
      //             fit: BoxFit.fill,
      //             imageUrl: turf.images![0],
      //             placeholder: (_, __) => Image.asset(
      //               'assets/images/placeholder.jpg',
      //               fit: BoxFit.cover,
      //             ),
      //           ),
      //         ),
      //       ),
      //       SizedBox(width: dW * 0.04),
      //       Column(
      //         mainAxisAlignment: MainAxisAlignment.start,
      //         crossAxisAlignment: CrossAxisAlignment.start,
      //         children: [
      //           Container(
      //             alignment: Alignment.topLeft,
      //             margin: EdgeInsets.only(bottom: dW * 0.01),
      //             constraints: BoxConstraints(maxWidth: dW * 0.45),
      //             child: Text(
      //               turf.name,
      //               style: Theme.of(context).textTheme.displayLarge!.copyWith(
      //                     fontSize: tS * displayLarge,
      //                     fontWeight: FontWeight.bold,
      //                     color: Colors.black,
      //                     letterSpacing: .14,
      //                   ),
      //             ),
      //           ),
      //           Container(
      //             alignment: Alignment.topLeft,
      //             margin: EdgeInsets.only(bottom: dW * 0.01),
      //             constraints: BoxConstraints(maxWidth: dW * 0.45),
      //             child: RichText(
      //               text: TextSpan(
      //                 children: [
      //                   TextSpan(
      //                     text: '\u20b9',
      //                     style: TextStyle(
      //                       fontSize: tS * displayMedium,
      //                       color: Colors.black,
      //                       fontWeight: FontWeight.bold,
      //                     ),
      //                   ),
      //                   TextSpan(
      //                     text: '${findLowestValue(turf)}/',
      //                     style:
      //                         Theme.of(context).textTheme.displaySmall!.copyWith(
      //                               fontSize: tS * displayMedium,
      //                               fontWeight: FontWeight.bold,
      //                             ),
      //                   ),
      //                   TextSpan(
      //                     text: 'per hr',
      //                     style:
      //                         Theme.of(context).textTheme.displayLarge!.copyWith(
      //                               fontSize: tS * displayLarge,
      //                               fontWeight: FontWeight.bold,
      //                               color: Colors.black,
      //                             ),
      //                   ),
      //                 ],
      //               ),
      //             ),
      //           ),
      //           Container(
      //             alignment: Alignment.topLeft,
      //             margin: EdgeInsets.only(bottom: dW * 0.02),
      //             constraints: BoxConstraints(maxWidth: dW * 0.45),
      //             child: Text(
      //               '${turf.address.streetName}, ${turf.address.city}',
      //               style: Theme.of(context).textTheme.headlineSmall!.copyWith(
      //                     fontSize: tS * headline9,
      //                     fontWeight: FontWeight.normal,
      //                     color: Color(0xff42434C),
      //                   ),
      //               maxLines: 1,
      //               overflow: TextOverflow.ellipsis,
      //               textAlign: TextAlign.left,
      //             ),
      //           ),
      //           Container(
      //             constraints: BoxConstraints(maxWidth: dW * 0.5),
      //             child: Wrap(
      //               children: [
      //                 ...turf.sportsType!.map((sport) {
      //                   return Card(
      //                     margin: EdgeInsets.all(0),
      //                     shape: RoundedRectangleBorder(
      //                       borderRadius: BorderRadius.circular(20),
      //                     ),
      //                     child: CircleAvatar(
      //                       radius: 13,
      //                       backgroundColor: Colors.white,
      //                       child: CircleAvatar(
      //                         backgroundColor: Color(0xffEAEAEA),
      //                         radius: 10,
      //                         child: Image.network(
      //                           sport.image,
      //                           height: 16,
      //                           width: 16,
      //                         ),
      //                       ),
      //                     ),
      //                   );
      //                 }),
      //               ],
      //             ),
      //           )
      //         ],
      //       )
      //     ],
      //   ),
      // ),
    );
  }
}
