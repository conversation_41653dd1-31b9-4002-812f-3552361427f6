import 'package:flutter/services.dart';

import '../../commonWidgets/checkBoxWidget.dart';
import 'package:flutter/material.dart';

import '../../commonWidgets/raisedButton.dart';
import '../../fontSizes.dart';
import '../../venueModule/widgets/step1Widget.dart';

class Step4Widget extends StatefulWidget {
  Map selectedSportCategory;
  List selectedSports;
  List listOfFacilities;
  List selectedFacilities;
  final bool isNet;
  List turfSize;
  List sportQuantity;
  TextEditingController aboutTurfController;
  TextEditingController advanceAmountController;
  TextEditingController advanceAmountForOneOnController;
  Function selectAndUnSelectType;
  Function setQuantity;
  final String option;
  // final List cancellationCharges;
  // final Function addOrRemoveCancellation;
  final Function save;
  Step4Widget({
    Key? key,
    required this.listOfFacilities,
    required this.selectedSportCategory,
    required this.selectedSports,
    required this.turfSize,
    required this.sportQuantity,
    required this.isNet,
    required this.selectAndUnSelectType,
    required this.selectedFacilities,
    required this.setQuantity,
    required this.aboutTurfController,
    required this.advanceAmountController,
    required this.advanceAmountForOneOnController,
    required this.option,
    // required this.cancellationCharges,
    // required this.addOrRemoveCancellation,
    required this.save,
  }) : super(key: key);

  @override
  _Step4WidgetState createState() => _Step4WidgetState();
}

class _Step4WidgetState extends State<Step4Widget> {
  List sportQuantity = [];

  @override
  void initState() {
    super.initState();

    if (widget.selectedSportCategory['categoryName'] != 'Outdoor') {
      widget.selectedSports.forEach((sport) {
        sportQuantity.add({
          'title': sport,
          'quantity': 0,
          'label': '',
        });
      });
    } else {
      if (widget.turfSize.contains('11:11')) {
        sportQuantity.add({
          'title': '11:11',
          'quantity': 0,
          'label': '',
        });
      }
      if (widget.turfSize.contains('9:9')) {
        sportQuantity.add({
          'title': '9:9',
          'quantity': 0,
          'label': '',
        });
        if (widget.turfSize.contains('7:7')) {
          sportQuantity.add({
            'title': '7:7',
            'quantity': 0,
            'label': '',
          });
        }
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
          });
        }
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
          });
        }
      } else if (widget.turfSize.contains('7:7')) {
        sportQuantity.add({
          'title': '7:7',
          'quantity': 0,
          'label': '',
        });
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
          });
        }
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
          });
        }
      } else if (widget.turfSize.contains('5:5') &&
          !widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8')) {
        sportQuantity = [
          {
            'title': '5:5',
            'quantity': 0,
            'label': '',
          }
        ];
      } else if (widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8') &&
          !widget.turfSize.contains('5:5')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
          }
        ];
      } else if (widget.turfSize.contains('5:5') &&
          widget.turfSize.contains('6:6') &&
          !widget.turfSize.contains('8:8')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
          },
          {
            'title': '5:5',
            'quantity': 0,
            'label': '',
          }
        ];
      } else if (widget.turfSize.contains('8:8') ||
          widget.turfSize.contains('6:6') ||
          widget.turfSize.contains('5:5')) {
        sportQuantity = [
          {
            'title': '8:8',
            'quantity': 0,
            'label': '',
          }
        ];
        if (widget.turfSize.contains('6:6')) {
          sportQuantity.add({
            'title': '6:6',
            'quantity': 0,
            'label': '',
          });
        }
        if (widget.turfSize.contains('5:5')) {
          sportQuantity.add({
            'title': '5:5',
            'quantity': 0,
            'label': '',
          });
        }
      } else if (widget.turfSize.contains('6:6')) {
        sportQuantity = [
          {
            'title': '6:6',
            'quantity': 0,
            'label': '',
          }
        ];
      }
      if (!widget.isNet) {
        if (widget.turfSize.contains('6:6')) {
          var index =
              sportQuantity.indexWhere((element) => element['title'] == '6:6');
          if (index == -1) {
            sportQuantity.add({
              'title': '6:6',
              'quantity': 0,
              'label': '',
            });
          }
        }
        if (widget.turfSize.contains('8:8')) {
          var index =
              sportQuantity.indexWhere((element) => element['title'] == '8:8');
          if (index == -1) {
            sportQuantity.add({
              'title': '8:8',
              'quantity': 0,
              'label': '',
            });
          }
        }
      }
    }
    if (widget.option == 'One On') {
      sportQuantity.add({
        'title': '1:1',
        'quantity': 0,
        'label': '',
      });
    }
    // if (!widget.isNet && widget.selectedSports.contains('Badminton')) {
    //   sportQuantity.insert(0, {
    //     'title': '2:2',
    //     'quantity': 0,
    //     'label': '',
    //   });
    // }
    widget.turfSize.toSet();
    sportQuantity.toSet();
    widget.setQuantity(sportQuantity);
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            widget.selectedSportCategory['categoryName'] != 'Outdoor'
                ? 'Enter Quantity'
                : widget.isNet
                    ? 'Enter turf relation & label'
                    : 'Enter turf quantity & label',
          ),
          ...widget.sportQuantity.map(
            (sport) => Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(
                    right: deviceWidth * 0.03,
                    bottom: deviceWidth * 0.03,
                  ),
                  alignment: Alignment.center,
                  width: deviceWidth * 0.18,
                  padding: EdgeInsets.symmetric(
                    vertical: deviceWidth * 0.032,
                    horizontal: deviceWidth * 0.02,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  child: Text(
                    sport['title'],
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                          fontSize:
                              widget.selectedSportCategory['categoryName'] ==
                                      'Outdoor'
                                  ? textScaleFactor * 14
                                  : textScaleFactor * 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: deviceWidth * 0.02),
                Container(
                  width: deviceWidth * 0.23,
                  margin: EdgeInsets.only(right: deviceWidth * 0.03),
                  child: TextFormField(
                    initialValue: sport['quantity'] == 0
                        ? ''
                        : sport['quantity'].toString(),
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    style: TextStyle(
                      fontSize: textScaleFactor * 14,
                      letterSpacing: .30,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      hintStyle: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(fontSize: textScaleFactor * 12),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: deviceWidth * 0.02,
                        vertical: 0,
                      ),
                      hintText: widget.selectedSportCategory['categoryName'] !=
                              'Outdoor'
                          ? 'Quantity'
                          : widget.isNet
                              ? 'Relation'
                              : 'Quantity',
                      counterText: "",
                      fillColor: Colors.transparent,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade400),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      filled: true,
                    ),
                    maxLength: 8,
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      if (value.length != 0) {
                        sport['quantity'] = int.parse(value);
                        print(sport['quantity']);
                      }
                    },
                  ),
                ),
                Container(
                  width: deviceWidth * 0.23,
                  child: TextFormField(
                    initialValue: sport['label'] == '' ? '' : sport['label'],
                    style: TextStyle(
                      fontSize: textScaleFactor * 14,
                      letterSpacing: .30,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      hintStyle: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(fontSize: textScaleFactor * headline9),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: deviceWidth * 0.02,
                        vertical: 0,
                      ),
                      hintText: widget.selectedSportCategory['categoryName'] !=
                              'Outdoor'
                          ? 'Label'
                          : widget.isNet
                              ? 'Label'
                              : 'Label',
                      counterText: "",
                      fillColor: Colors.transparent,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade400),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      filled: true,
                    ),
                    cursorColor: Colors.black,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    keyboardType: TextInputType.text,
                    onChanged: (value) {
                      if (value.length != 0) {
                        sport['label'] = value.trim();
                        print(sport['label']);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: deviceWidth * 0.045),
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            'Advance Amount',
          ),
          Container(
            // width: width * 0.7,
            child: TextFormField(
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              controller: widget.advanceAmountController,
              style: TextStyle(
                fontSize: textScaleFactor * 15,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter advance amount',
                hintStyle: TextStyle(
                  fontSize: textScaleFactor * 12,
                  fontWeight: FontWeight.w500,
                ),
                counterText: '',
                contentPadding: EdgeInsets.only(left: deviceWidth * 0.03),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7),
                  borderSide: BorderSide(
                    color: Colors.black38,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7),
                  borderSide: BorderSide(
                    color: Colors.black38,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7),
                  borderSide: BorderSide(
                    color: Colors.black38,
                  ),
                ),
              ),
              keyboardType: TextInputType.number,
              maxLength: 10,
              onTap: () {
                setState(() {});
              },
            ),
          ),
          if (widget.option == 'One On')
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * 0.045),
                buildTitleContainer(
                  deviceWidth,
                  textScaleFactor,
                  context,
                  'Advance Amount For 1:1',
                ),
                Container(
                  // width: width * 0.7,
                  child: TextFormField(
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    controller: widget.advanceAmountForOneOnController,
                    style: TextStyle(
                      fontSize: textScaleFactor * 15,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: InputDecoration(
                      hintText: 'Enter advance amount',
                      hintStyle: TextStyle(
                        fontSize: textScaleFactor * 12,
                        fontWeight: FontWeight.w500,
                      ),
                      counterText: '',
                      contentPadding: EdgeInsets.only(left: deviceWidth * 0.03),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(7),
                        borderSide: BorderSide(
                          color: Colors.black38,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(7),
                        borderSide: BorderSide(
                          color: Colors.black38,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(7),
                        borderSide: BorderSide(
                          color: Colors.black38,
                        ),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    maxLength: 10,
                    onTap: () {
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
          SizedBox(height: deviceWidth * 0.05),
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            'Facilities',
          ),
          ...widget.listOfFacilities.map(
            (facility) => GestureDetector(
              onTap: () => widget.selectAndUnSelectType(
                facility['id'],
                facility['isSelected'],
                widget.listOfFacilities,
                widget.selectedFacilities,
                false,
              ),
              child: Container(
                margin: EdgeInsets.only(bottom: deviceWidth * 0.03),
                width: deviceWidth,
                color: Colors.transparent,
                child: Row(
                  children: [
                    OldCheckBoxWidget(
                      deviceWidth,
                      facility['isSelected'],
                    ),
                    Container(
                      padding: EdgeInsets.only(left: deviceWidth * 0.025),
                      width: deviceWidth * 0.7,
                      child: Text(
                        facility['title'],
                        style: Theme.of(context).textTheme.displayLarge!.copyWith(
                              fontSize: textScaleFactor * displayLarge,
                              letterSpacing: .30,
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: deviceWidth * 0.04),
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            'About Venue',
          ),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Color(0xffF9F9F9),
            ),
            constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.width * 0.6),
            child: TextFormField(
              style: TextStyle(
                // color: Color(0xff6E7271),
                fontWeight: FontWeight.w600,
                fontSize: textScaleFactor * headline9,
                letterSpacing: .30,
              ),
              decoration: InputDecoration(
                counterText: '',
                hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * headline9,
                      fontWeight: FontWeight.normal,
                      color: Color(0xff757371),
                    ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: deviceWidth * 0.034,
                  vertical: deviceWidth * 0.04,
                ),
                hintText: "Write venue description here...",
                border: InputBorder.none,
              ),
              maxLines: null,
              maxLength: 1000,
              cursorColor: Colors.black,
              textCapitalization: TextCapitalization.sentences,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              controller: widget.aboutTurfController,
              keyboardType: TextInputType.streetAddress,
              textInputAction: TextInputAction.newline,
              onChanged: (value) {
                setState(() {});
              },
              // validator: (value) {
              //   if (value!.isEmpty) {
              //     return 'Please enter venue description';
              //   }
              // },
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.09),
            alignment: Alignment.bottomRight,
            child: Text(
              '${widget.aboutTurfController.text.length}/1000 char',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: textScaleFactor * headline9,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
            ),
          ),
          buildRaisedButton(
            deviceWidth,
            deviceWidth * 0.12,
            () {
              widget.save();
            },
            Text(
              'Save and Continue',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontSize: textScaleFactor * 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: .50,
                    color: Colors.white,
                  ),
            ),
            TargetPlatform.android,
            Theme.of(context).primaryColor,
            7,
          ),
          SizedBox(height: deviceWidth * 0.12)
        ],
      ),
    );
  }
}
