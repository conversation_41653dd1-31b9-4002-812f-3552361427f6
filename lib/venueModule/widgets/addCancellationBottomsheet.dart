import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../commonWidgets/raisedButton.dart';

class AddCancellationBottomSheet extends StatefulWidget {
  final List cancellationCharges;
  final Map? data;

  const AddCancellationBottomSheet({
    Key? key,
    required this.cancellationCharges,
    this.data,
  }) : super(key: key);

  @override
  State<AddCancellationBottomSheet> createState() =>
      _AddCancellationBottomSheetState();
}

class _AddCancellationBottomSheetState
    extends State<AddCancellationBottomSheet> {
  TextEditingController startController = TextEditingController();

  TextEditingController endController = TextEditingController();

  TextEditingController percentageController = TextEditingController();

  bool isLoading = false;

  save(BuildContext context) {
    if (startController.text.trim().isEmpty) {
      callToastMessage('Please select start range');
      return;
    } else if (endController.text.trim().isEmpty) {
      callToastMessage('Please select end range');
      return;
    } else if (percentageController.text.trim().isEmpty) {
      callToastMessage('Please add refund percentage');
      return;
    }

    Navigator.of(context).pop({
      'start': startController.text.trim(),
      'end': endController.text.trim(),
      'percentage': percentageController.text.trim(),
    });
  }

  Widget buildTextField({
    required TextEditingController controller,
    required BuildContext context,
    required String hintText,
    required double textScaleFactor,
    required double width,
    bool fromCancellationBottomSheet = false,
  }) {
    return Container(
      width: width,
      child: TextFormField(
        style: TextStyle(
          fontSize: textScaleFactor * 15,
          color: Colors.black,
          letterSpacing: .30,
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
        decoration: InputDecoration(
          hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                fontSize: textScaleFactor * 14,
                color: fromCancellationBottomSheet
                    ? Colors.black45
                    : Colors.black87,
              ),
          contentPadding: EdgeInsets.all(0),
          hintText: hintText,
          counterText: "",
          fillColor: Colors.grey.shade300,
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black54),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black54),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black54),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black54),
          ),
          disabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black54),
          ),
        ),
        cursorColor: Colors.black,
        controller: controller,
        maxLength: 3,
        keyboardType: TextInputType.number,
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.data != null) {
      startController.text = widget.data!['start'].toString();
      endController.text = widget.data!['end'].toString();
      percentageController.text = widget.data!['percentage'].toString();
      startController.selection =
          TextSelection.collapsed(offset: startController.text.length);
      endController.selection =
          TextSelection.collapsed(offset: endController.text.length);
      percentageController.selection =
          TextSelection.collapsed(offset: percentageController.text.length);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Container(
      height: MediaQuery.of(context).viewInsets.bottom == 0
          // ? deviceWidth * 0.8
          ? deviceWidth * 0.9
          : deviceWidth * 1.55,
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: deviceWidth * 0.03),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildText(
                  text: 'Set Cancellation Range',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  fontWeight: FontWeight.w500,
                ),
                IconButton(
                  padding: EdgeInsets.all(0),
                  onPressed: () {
                    Navigator.of(context).pop(null);
                  },
                  icon: Icon(Icons.clear),
                )
              ],
            ),
            SizedBox(height: deviceWidth * 0.02),
            buildText(
              text:
                  'Specify your cancellation policy"s hour range:\nStart Hour - End Hour.',
              textScaleFactor: textScaleFactor,
              context: context,
              fontSize: 12,
              fontWeight: FontWeight.w200,
            ),
            SizedBox(height: deviceWidth * 0.04),
            Row(
              children: [
                buildText(
                  text: 'Hours Range',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  fontSize: 14,
                ),
                SizedBox(width: deviceWidth * 0.045),
                buildTextField(
                  controller: startController,
                  hintText: '0',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  width: deviceWidth * 0.14,
                  fromCancellationBottomSheet: true,
                ),
                SizedBox(width: deviceWidth * 0.03),
                buildText(
                  text: 'To',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  color: Colors.black45,
                ),
                SizedBox(width: deviceWidth * 0.03),
                buildTextField(
                  controller: endController,
                  hintText: '24',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  width: deviceWidth * 0.14,
                  fromCancellationBottomSheet: true,
                ),
                SizedBox(width: deviceWidth * 0.03),
                buildText(
                  text: 'Hrs',
                  textScaleFactor: textScaleFactor,
                  context: context,
                )
              ],
            ),
            SizedBox(height: deviceWidth * 0.07),
            Row(
              children: [
                buildText(
                  text: 'Add Refund Percentage',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  fontSize: 14,
                ),
                SizedBox(width: deviceWidth * 0.045),
                buildTextField(
                  controller: percentageController,
                  hintText: '%',
                  textScaleFactor: textScaleFactor,
                  context: context,
                  width: deviceWidth * 0.14,
                ),
                SizedBox(width: deviceWidth * 0.03),
                buildText(
                  text: '%',
                  textScaleFactor: textScaleFactor,
                  context: context,
                ),
              ],
            ),
            SizedBox(height: deviceWidth * 0.085),
            CustomButton(
              width: deviceWidth,
              height: deviceWidth * 0.12,
              buttonText: 'Save',
              radius: 7,
              fontSize: 15,
              onPressed: () => save(context),
            ),
          ],
        ),
      ),
    );
  }
}

Widget buildText({
  required String text,
  required double textScaleFactor,
  Color color = Colors.black,
  FontWeight fontWeight = FontWeight.w500,
  required BuildContext context,
  double fontSize = 16,
  double? width,
}) {
  return Container(
    constraints: width == null
        ? null
        : BoxConstraints(
            maxWidth: width,
          ),
    child: FittedBox(
      fit: BoxFit.scaleDown,
      child: Text(
        text,
        style: Theme.of(context).textTheme.displayLarge!.copyWith(
              fontSize: textScaleFactor * fontSize,
              color: color,
              fontWeight: fontWeight,
            ),
      ),
    ),
  );
}
