import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../commonWidgets/raisedButton.dart';
import '../models/venue_model.dart';
import '../../venueModule/providers/turfProvider.dart';
import '../../venueModule/widgets/step1Widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class InactiveBottomSheet extends StatefulWidget {
  final double deviceWidth;
  final double deviceHeight;
  final double textScaleFactor;
  final UserModal user;
  final Venue turf;
  const InactiveBottomSheet({
    Key? key,
    required this.deviceWidth,
    required this.deviceHeight,
    required this.textScaleFactor,
    required this.user,
    required this.turf,
  }) : super(key: key);

  @override
  InactiveBottomSheetState createState() => InactiveBottomSheetState();
}

class InactiveBottomSheetState extends State<InactiveBottomSheet> {
  TextEditingController startDateController = TextEditingController();
  TextEditingController endDateController = TextEditingController();

  bool isLoading = false;

  Widget? dialog;

  initialDateRange() {
    if (startDate == null && endDate == null) {
      return null;
    } else {
      return DateTimeRange(start: startDate!, end: endDate!);
    }
  }

  DateTime? startDate;
  DateTime? endDate;
  TimeOfDay? startTime;
  TimeOfDay? endTime;

  selectTime(bool start) {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onChanged: (date) {
        setState(() {
          if (start) {
            startTime = TimeOfDay(hour: date.hour, minute: date.minute);
          } else {
            endTime = TimeOfDay(hour: date.hour, minute: date.minute);
          }
        });
      },
      onConfirm: (date) {
        setState(() {
          if (start) {
            startTime = TimeOfDay(hour: date.hour, minute: date.minute);
          } else {
            endTime = TimeOfDay(hour: date.hour, minute: date.minute);
          }
        });
      },
      currentTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        startTime == null ? DateTime.now().hour : startTime!.hour,
        startTime == null ? DateTime.now().minute : startTime!.minute,
      ),
      showSecondsColumn: false,
    );
  }

  selectDate({
    required String dateType,
    required TextEditingController dateController,
  }) {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2200),
    ).then((date) {
      if (date != null) {
        setState(() {
          if (dateType == 'Start') {
            startDate = date;
          } else {
            endDate = date;
          }
          dateController.text = DateFormat('dd MMM yyyy').format(date);
        });
      }
    });
  }

  setPauseDate() async {
    try {
      if (startDate == null || endDate == null) {
        callToastMessage('Please select start and end date');
        return;
      }

      if (startTime == null || endTime == null) {
        callToastMessage('Please select start time and end time');
        return;
      }

      setState(() {
        isLoading = true;
      });

      List dates = [];
      int length = endDate!.difference(startDate!).inDays;
      for (var i = 0; i <= length; i++) {
        // if (i == 0) {
        //   dates.add(
        // startDate!
        //       .add(Duration(
        //           days: i, hours: startTime!.hour, minutes: startTime!.minute))
        //       .toString()
        // );
        // } else if (i == length) {
        //   dates.add(startDate!
        //       .add(Duration(
        //           days: i, hours: endTime!.hour, minutes: endTime!.minute))
        //       .toString());
        // } else {
        //   dates.add(startDate!.add(Duration(days: i)).toString());
        // }
        dates.add({
          'start': startDate!
              .add(Duration(
                  days: i, hours: startTime!.hour, minutes: startTime!.minute))
              .toString(),
          'end': startDate!
              .add(Duration(
                  days: i, hours: endTime!.hour, minutes: endTime!.minute))
              .toString()
        });
      }
      print(dates);
      final result = await Provider.of<TurfProvider>(context, listen: false)
          .pauseAndUnPauseTurf(
        turf: widget.turf,
        accessToken: widget.user.accessToken,
        action: 'Pause',
        dates: dates,
      );
      if (result != null) {
        Navigator.of(context).pop(result);
      } else {
        callToastMessage('Something went wrong');
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  Widget dateTextField({
    required String dateType,
    required TextEditingController dateController,
    required String hintText,
  }) {
    return Container(
      child: TextFormField(
        style: TextStyle(
          // color: Color(0xff6E7271),
          fontSize: widget.textScaleFactor * 15,
          color: Colors.black,
          letterSpacing: .30, fontWeight: FontWeight.w600,
        ),
        decoration: InputDecoration(
          suffixIcon: Icon(
            Icons.calendar_today_outlined,
            color: Colors.black54,
            size: 20,
          ),
          hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                fontSize: widget.textScaleFactor * 14,
              ),
          // contentPadding: EdgeInsets.symmetric(
          //   horizontal: widget.deviceWidth * 0.034,
          //   vertical: widget.deviceWidth * 0.04,
          // ),
          hintText: hintText,
          counterText: "",
          fillColor: Colors.grey.shade300,
          border: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade500),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade500),
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade500),
          ),
          errorBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade500),
          ),
          disabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade500),
          ),
          filled: false,
        ),
        cursorColor: Colors.black,
        readOnly: true,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        controller: dateController,
        keyboardType: TextInputType.datetime,
        onTap: () {
          selectDate(
            dateType: dateType,
            dateController: dateController,
          );
        },
      ),
    );
  }

  Widget buildTime(bool start) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: widget.deviceWidth * 0.02),
          child: Text(
            start ? 'Start Time' : 'End Time',
            style: TextStyle(
              fontSize: widget.textScaleFactor * 16,
              letterSpacing: 0.3,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            selectTime(start);
          },
          child: Container(
            height: widget.deviceWidth * 0.12,
            padding: EdgeInsets.symmetric(
              horizontal: widget.deviceWidth * 0.035,
              vertical: widget.deviceHeight * 0.005,
            ),
            margin: EdgeInsets.only(bottom: widget.deviceWidth * 0.065),
            decoration: BoxDecoration(
              color: const Color(0xffE0EFF9),
              border: Border.all(
                width: 1,
                color: const Color(0xffE0EFF9),
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  start
                      ? startTime == null
                          ? 'Start Time'
                          : '${startTime!.hour}:${startTime!.minute} ${startTime!.period == DayPeriod.pm ? 'PM' : 'AM'}'
                      : endTime == null
                          ? 'End Time'
                          : '${endTime!.hour}:${endTime!.minute} ${endTime!.period == DayPeriod.pm ? 'PM' : 'AM'}',
                  style: TextStyle(
                    fontSize: widget.textScaleFactor * 14,
                    letterSpacing: 0.3,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: widget.deviceWidth * 0.02),
                SvgPicture.asset('assets/svgIcons/Clock.svg')
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    dialog = DateRangePickerDialog(
      firstDate: DateTime.now(),
      lastDate: DateTime(2200),
      currentDate: DateTime.now(),
      initialDateRange: initialDateRange(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.deviceHeight * 0.45,
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: widget.deviceWidth * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: widget.deviceWidth * 0.01),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  padding: EdgeInsets.all(0),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  icon: Icon(Icons.clear),
                )
              ],
            ),
            buildTitleContainer(
              widget.deviceWidth,
              widget.textScaleFactor,
              context,
              'Date Range',
            ),
            GestureDetector(
              onTap: () {
                showDialog<DateTimeRange>(
                  context: context,
                  useSafeArea: false,
                  builder: (BuildContext context) {
                    return Theme(
                      data: ThemeData.light().copyWith(
                        colorScheme: ColorScheme.fromSwatch(
                          primarySwatch: Colors.green,
                          // primaryColorDark: Theme.of(context).primaryColor,
                          accentColor: Theme.of(context).primaryColor,
                        ),
                        dialogBackgroundColor: Colors.white,
                      ),
                      child: dialog!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    setState(() {
                      startDate = value.start;
                      endDate = value.end;
                    });
                  }
                });
              },
              child: Container(
                height: widget.deviceWidth * 0.12,
                padding: EdgeInsets.symmetric(
                  horizontal: widget.deviceWidth * 0.03,
                  vertical: widget.deviceHeight * 0.005,
                ),
                margin: EdgeInsets.only(bottom: widget.deviceWidth * 0.065),
                decoration: BoxDecoration(
                  color: const Color(0xffE0EFF9),
                  border: Border.all(
                    width: 1,
                    color: const Color(0xffE0EFF9),
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      startDate == null
                          ? 'Select date range'
                          : '${DateFormat('dd MMM yyyy').format(startDate!)} - ${DateFormat('dd MMM yyyy').format(endDate!)}',
                      style: TextStyle(
                        fontSize: widget.textScaleFactor * 14,
                        letterSpacing: 0.3,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SvgPicture.asset('assets/svgIcons/Calendarr.svg')
                  ],
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildTime(true),
                buildTime(false),
              ],
            ),
            SizedBox(height: widget.deviceWidth * 0.03),
            Align(
              alignment: Alignment.bottomRight,
              child: buildRaisedButton(
                widget.deviceWidth,
                widget.deviceWidth * 0.12,
                isLoading ? () {} : setPauseDate,
                isLoading
                    ? circularForButton(widget.deviceWidth)
                    : Text(
                        'Set Inactive Dates',
                        style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                              fontSize: widget.textScaleFactor * 14,
                              color: Colors.white,
                            ),
                      ),
                TargetPlatform.android,
                Theme.of(context).primaryColor,
                7,
              ),
            )
          ],
        ),
      ),
    );
  }
}
