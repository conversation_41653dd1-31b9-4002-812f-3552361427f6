// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/add_venue_details_screen.dart';

import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';

import '../models/venue_model.dart';

class SelectDuplicationVenueBottomSheet extends StatefulWidget {
  final List<Venue> listOfVenues;

  SelectDuplicationVenueBottomSheet({Key? key, required this.listOfVenues});

  @override
  State<SelectDuplicationVenueBottomSheet> createState() =>
      _SelectDuplicationVenueBottomSheetState();
}

class _SelectDuplicationVenueBottomSheetState
    extends State<SelectDuplicationVenueBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Venue? selectedVenue;

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      height: dH * 0.65,
      child: Column(
        children: [
          SizedBox(height: dW * 0.01),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: 'Choose listing',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                IconButton(
                  padding: EdgeInsets.all(0),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  icon: Icon(Icons.clear),
                )
              ],
            ),
          ),
          DividerWidget(top: 0),
          Expanded(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...widget.listOfVenues.map(
                    (venue) => GestureDetector(
                      onTap: () {
                        selectedVenue = venue;
                        setState(() {});
                      },
                      child: CustomContainer(
                        bgColor: Colors.transparent,
                        vPadding: .03,
                        margin: EdgeInsets.only(bottom: dW * 0.05),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: dW * 0.7),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    title: venue.name,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  SizedBox(height: dW * 0.01),
                                  TextWidget(
                                    title: venue.address.fullAddress
                                            .trim()
                                            .isEmpty
                                        ? '${venue.address.area.isEmpty ? venue.address.streetName : venue.address.area}, ${venue.address.landmark}, ${venue.address.city}, ${venue.address.state}, ${venue.address.pincode}.'
                                        : '${venue.address.fullAddress}.',
                                  )
                                ],
                              ),
                            ),
                            Container(
                              alignment: Alignment.bottomRight,
                              height: dW * 0.05,
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: selectedVenue != null &&
                                          selectedVenue!.id == venue.id
                                      ? Theme.of(context).primaryColor
                                      : Color(0xffB6B7BA),
                                ),
                              ),
                              child: CircleAvatar(
                                radius: 8,
                                backgroundColor: selectedVenue != null &&
                                        selectedVenue!.id == venue.id
                                    ? Theme.of(context).primaryColor
                                    : Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: dW * 0.08),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: dW * 0.05),
            child: CustomButton(
              width: dW * 0.9,
              height: dW * 0.12,
              buttonText: 'Proceed',
              fontSize: 16,
              onPressed: selectedVenue == null
                  ? null
                  : () {
                      pop();
                      pop();
                      push(
                        AddVenueDetailsScreen(
                          venue: selectedVenue,
                          duplicateVenue: true,
                        ),
                      );
                    },
            ),
          )
        ],
      ),
    );
  }
}
