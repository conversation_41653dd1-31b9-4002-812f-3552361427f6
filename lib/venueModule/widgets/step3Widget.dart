import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../commonWidgets/raisedButton.dart';
import '../../venueModule/widgets/step1Widget.dart';
import '../../fontSizes.dart';
import '../../commonWidgets/imageValidator.dart';

class Step3Widget extends StatefulWidget {
  var fileMedia1;
  var fileMedia2;
  var fileMedia3;
  var fileMedia4;
  var fileMedia5;

  List clickedImages;

  bool usePreviousValueForPrice;

  String imagePath1;
  String imagePath2;
  String imagePath3;
  String imagePath4;
  String imagePath5;

  Function captureImage;
  Function deleteImage;
  Function incrementAndDecrementStep;
  Function addOrRemoveImages;
  Function setQuantity;

  Step3Widget({
    Key? key,
    required this.fileMedia1,
    required this.fileMedia2,
    required this.fileMedia3,
    required this.fileMedia4,
    required this.fileMedia5,
    required this.imagePath1,
    required this.imagePath2,
    required this.imagePath3,
    required this.imagePath4,
    required this.imagePath5,
    required this.usePreviousValueForPrice,
    required this.clickedImages,
    required this.captureImage,
    required this.deleteImage,
    required this.incrementAndDecrementStep,
    required this.addOrRemoveImages,
    required this.setQuantity,
  }) : super(key: key);

  @override
  _Step3WidgetState createState() => _Step3WidgetState();
}

class _Step3WidgetState extends State<Step3Widget> {
  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            'Venue Images',
          ),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.01),
            child: Text(
              'Guidelines for Images',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: textScaleFactor * 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    letterSpacing: .34,
                  ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.025),
            child: Text(
              '1. Pixels: 2720x2080 pixels (17:13 ratio).\n2. Good dots per inch min 100 170 pixel gap needs to be maintained on top & bottom.\n3. Three images are mandatory.',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: textScaleFactor * headline9,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
            ),
          ),
          Row(
            // mainAxisAlignment: MainAxisAlignment.start,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.info_rounded,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: deviceWidth * 0.01),
              Container(
                constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
                child: Text(
                  'You can add upto 5 images of the turf.',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: deviceWidth * 0.05),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.08),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Upload Image 1',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        color: Colors.black,
                      ),
                ),
                SizedBox(height: deviceWidth * 0.02),
                widget.imagePath1 != ''
                    ? Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Colors.black45,
                                ),
                              ),
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              child: widget.imagePath1 != '' &&
                                      widget.imagePath1.contains('https')
                                  ? Image.network(
                                      widget.imagePath1,
                                      fit: BoxFit.cover,
                                    )
                                  : Image.file(
                                      widget.fileMedia1,
                                      fit: BoxFit.cover,
                                    ), ////////////////////
                            ),
                          ),
                          Positioned(
                            right: deviceWidth * 0.025,
                            bottom: deviceWidth * 0.025,
                            child: editAndDeleteImageRow(
                              deviceWidth,
                              () {
                                widget.captureImage(1);
                              },
                              () {
                                widget.deleteImage(1);
                              },
                            ),
                          )
                        ],
                      )
                    : Container(
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: Radius.circular(10),
                          dashPattern: [3, 3],
                          strokeCap: StrokeCap.butt,
                          color: Colors.black,
                          strokeWidth: 0.4,
                          child: GestureDetector(
                            onTap: () => widget.captureImage(1),
                            child: Container(
                              color: Colors.transparent,
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              alignment: Alignment.center,
                              child: Icon(
                                Icons.camera_alt,
                                size: 60,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                SizedBox(height: deviceWidth * 0.04),
                Text(
                  'Upload Image 2',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        color: Colors.black,
                      ),
                ),
                SizedBox(height: deviceWidth * 0.02),
                widget.imagePath2 != ''
                    ? Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Colors.black45,
                                ),
                              ),
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              child: widget.imagePath2 != '' &&
                                      widget.imagePath2.contains('https')
                                  ? Image.network(
                                      widget.imagePath2,
                                      fit: BoxFit.cover,
                                    )
                                  : Image.file(
                                      widget.fileMedia2,
                                      fit: BoxFit.cover,
                                    ), ////////////////////
                            ),
                          ),
                          Positioned(
                            right: deviceWidth * 0.025,
                            bottom: deviceWidth * 0.025,
                            child: editAndDeleteImageRow(
                              deviceWidth,
                              () {
                                widget.captureImage(2);
                              },
                              () {
                                widget.deleteImage(2);
                              },
                            ),
                          )
                        ],
                      )
                    : Container(
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: Radius.circular(10),
                          dashPattern: [3, 3],
                          strokeCap: StrokeCap.butt,
                          color: Colors.black,
                          strokeWidth: 0.4,
                          child: GestureDetector(
                            onTap: () => widget.captureImage(2),
                            child: Container(
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              alignment: Alignment.center,
                              color: Colors.transparent,
                              child: Icon(
                                Icons.camera_alt,
                                size: 60,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                SizedBox(height: deviceWidth * 0.04),
                Text(
                  'Upload Image 3',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        color: Colors.black,
                      ),
                ),
                SizedBox(height: deviceWidth * 0.02),
                widget.imagePath3 != ''
                    ? Stack(
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.black45,
                                  ),
                                ),
                                width: deviceWidth * 0.9,
                                height: deviceWidth * 0.5,
                                child: widget.imagePath3 != '' &&
                                        widget.imagePath3.contains('https')
                                    ? Image.network(
                                        widget.imagePath3,
                                        fit: BoxFit.cover,
                                      )
                                    : Image.file(
                                        widget.fileMedia3,
                                        fit: BoxFit.cover,
                                      ), ////////////////////
                              )),
                          Positioned(
                            right: deviceWidth * 0.025,
                            bottom: deviceWidth * 0.025,
                            child: editAndDeleteImageRow(
                              deviceWidth,
                              () {
                                widget.captureImage(3);
                              },
                              () {
                                widget.deleteImage(3);
                              },
                            ),
                          )
                        ],
                      )
                    : Container(
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: Radius.circular(10),
                          dashPattern: [3, 3],
                          strokeCap: StrokeCap.butt,
                          color: Colors.black,
                          strokeWidth: 0.4,
                          child: GestureDetector(
                            onTap: () => widget.captureImage(3),
                            child: Container(
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              alignment: Alignment.center,
                              color: Colors.transparent,
                              child: Icon(
                                Icons.camera_alt,
                                size: 60,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                SizedBox(height: deviceWidth * 0.04),
                Text(
                  'Upload Image 4',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        color: Colors.black,
                      ),
                ),
                SizedBox(height: deviceWidth * 0.02),
                widget.imagePath4 != ''
                    ? Stack(
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.black45,
                                  ),
                                ),
                                width: deviceWidth * 0.9,
                                height: deviceWidth * 0.5,
                                child: widget.imagePath4 != '' &&
                                        widget.imagePath4.contains('https')
                                    ? Image.network(
                                        widget.imagePath4,
                                        fit: BoxFit.cover,
                                      )
                                    : Image.file(
                                        widget.fileMedia4,
                                        fit: BoxFit.cover,
                                      ), ////////////////////
                              )),
                          Positioned(
                            right: deviceWidth * 0.025,
                            bottom: deviceWidth * 0.025,
                            child: editAndDeleteImageRow(
                              deviceWidth,
                              () {
                                widget.captureImage(4);
                              },
                              () {
                                widget.deleteImage(4);
                              },
                            ),
                          )
                        ],
                      )
                    : Container(
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: Radius.circular(10),
                          dashPattern: [3, 3],
                          strokeCap: StrokeCap.butt,
                          color: Colors.black,
                          strokeWidth: 0.4,
                          child: GestureDetector(
                            onTap: () => widget.captureImage(4),
                            child: Container(
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              alignment: Alignment.center,
                              color: Colors.transparent,
                              child: Icon(
                                Icons.camera_alt,
                                size: 60,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                SizedBox(height: deviceWidth * 0.04),
                Text(
                  'Upload Image 5',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * headline9,
                        color: Colors.black,
                      ),
                ),
                SizedBox(height: deviceWidth * 0.02),
                widget.imagePath5 != ''
                    ? Stack(
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.black45,
                                  ),
                                ),
                                width: deviceWidth * 0.9,
                                height: deviceWidth * 0.5,
                                child: widget.imagePath5 != '' &&
                                        widget.imagePath5.contains('https')
                                    ? Image.network(
                                        widget.imagePath5,
                                        fit: BoxFit.cover,
                                      )
                                    : Image.file(
                                        widget.fileMedia5,
                                        fit: BoxFit.cover,
                                      ), ////////////////////
                              )),
                          Positioned(
                            right: deviceWidth * 0.025,
                            bottom: deviceWidth * 0.025,
                            child: editAndDeleteImageRow(
                              deviceWidth,
                              () {
                                widget.captureImage(5);
                              },
                              () {
                                widget.deleteImage(5);
                              },
                            ),
                          )
                        ],
                      )
                    : Container(
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: Radius.circular(10),
                          dashPattern: [3, 3],
                          strokeCap: StrokeCap.butt,
                          color: Colors.black,
                          strokeWidth: 0.4,
                          child: GestureDetector(
                            onTap: () => widget.captureImage(5),
                            child: Container(
                              width: deviceWidth * 0.9,
                              height: deviceWidth * 0.5,
                              alignment: Alignment.center,
                              color: Colors.transparent,
                              child: Icon(
                                Icons.camera_alt,
                                size: 60,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
              ],
            ),
          ),
          buildRaisedButton(
            deviceWidth,
            deviceWidth * 0.12,
            () {
              if (widget.clickedImages.length < 3) {
                callToastMessage('You have to add atleast 3 images');
                return;
              }
              widget.setQuantity([]);
              widget.incrementAndDecrementStep(true);
            },
            Text(
              'Save and Continue',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontSize: textScaleFactor * 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: .50,
                    color: Colors.white,
                  ),
            ),
            TargetPlatform.android,
            Theme.of(context).primaryColor,
            7,
          ),
          SizedBox(height: deviceWidth * 0.08),
        ],
      ),
    );
  }
}

Widget editAndDeleteImageRow(
  double deviceWidth,
  Function editImage,
  Function deleteImage,
) {
  return Row(
    children: [
      GestureDetector(
        onTap: () => editImage(),
        child: Container(
          padding: EdgeInsets.all(deviceWidth * 0.008),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.grey.shade200,
            border: Border.all(
              color: Colors.black,
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.mode_edit_outline_outlined,
              color: Colors.black,
            ),
          ),
        ),
      ),
      SizedBox(width: deviceWidth * 0.04),
      GestureDetector(
        onTap: () => deleteImage(),
        child: Container(
          padding: EdgeInsets.all(deviceWidth * 0.008),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.grey.shade200,
            border: Border.all(
              color: Colors.black,
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.delete_outline_outlined,
              color: Colors.black,
            ),
          ),
        ),
      ),
    ],
  );
}
