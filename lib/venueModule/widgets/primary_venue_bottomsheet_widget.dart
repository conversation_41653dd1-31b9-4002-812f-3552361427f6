// ignore_for_file: must_be_immutable

import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';

import '../models/venue_model.dart';

class PrimaryVenueBottomSheet extends StatefulWidget {
  final List<Venue> listOfVenues;
  final Venue? primaryVenue;

  PrimaryVenueBottomSheet({
    Key? key,
    required this.primaryVenue,
    required this.listOfVenues,
  });

  @override
  State<PrimaryVenueBottomSheet> createState() =>
      _PrimaryVenueBottomSheetState();
}

class _PrimaryVenueBottomSheetState extends State<PrimaryVenueBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  Venue? selectedVenue;

  @override
  void initState() {
    super.initState();
    selectedVenue = widget.primaryVenue;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      height: dH * 0.55,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: dW * 0.01),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextWidget(
                        title: 'Select Primary Venue',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      IconButton(
                        padding: EdgeInsets.all(0),
                        onPressed: () {
                          Navigator.of(context).pop(false);
                        },
                        icon: Icon(Icons.clear),
                      )
                    ],
                  ),
                  DividerWidget(top: 0),
                  ...widget.listOfVenues.map(
                    (venue) => GestureDetector(
                      onTap: () {
                        selectedVenue = venue;
                        setState(() {});
                      },
                      child: Container(
                        color: Colors.transparent,
                        padding: EdgeInsets.symmetric(vertical: dW * 0.02),
                        margin: EdgeInsets.only(bottom: dW * 0.01),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: dW * 0.8),
                              child: TextWidget(
                                  title: venue.name,
                                  fontWeight: FontWeight.w600),
                            ),
                            Container(
                              alignment: Alignment.bottomRight,
                              height: dW * 0.05,
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: selectedVenue != null &&
                                          selectedVenue!.id == venue.id
                                      ? Theme.of(context).primaryColor
                                      : Color(0xffB6B7BA),
                                ),
                              ),
                              child: CircleAvatar(
                                radius: 8,
                                backgroundColor: selectedVenue != null &&
                                        selectedVenue!.id == venue.id
                                    ? Theme.of(context).primaryColor
                                    : Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: dW * 0.08),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: dW * 0.05),
            child: CustomButton(
              width: dW * 0.9,
              height: dW * 0.12,
              buttonText: 'Save',
              onPressed: () => pop(selectedVenue),
            ),
          )
        ],
      ),
    );
  }
}
