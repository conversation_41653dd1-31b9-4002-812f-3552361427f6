import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../fontSizes.dart';
import '../../commonWidgets/raisedButton.dart';

class Step1Widget extends StatelessWidget {
  List listOfSports;
  List listofSportCategory;
  List listOfSlots;
  List selectedSports;
  List listOfDays;
  List selectedWeekDays;
  List selectedSlots;
  List listOfTurfSizeAndPrice;
  List selectedTurfSize;
  List listofSlotTime;
  List listOfTurfOption;
  int selectedSlotDuration;

  bool isNet;
  String option;
  Map selectedSportCategory;
  Function selectSportCategory;
  Function setUsePreviousValueForPrice;
  // TimeOfDay startTime;
  // TimeOfDay endTime;
  Function selectAndUnSelectType;
  Function setIsNetValue;
  Function selectSlotDuration;
  Function selectTime;
  Function incrementAndDecrementStep;
  Function setListOfSlots;
  Function setTurfOption;

  TextEditingController venueNameController;

  Step1Widget({
    Key? key,
    required this.listOfSports,
    required this.listofSportCategory,
    required this.listOfSlots,
    required this.selectedSlots,
    required this.selectAndUnSelectType,
    required this.setIsNetValue,
    required this.selectedSports,
    required this.listOfDays,
    required this.selectedWeekDays,
    required this.setUsePreviousValueForPrice,
    required this.isNet,
    required this.option,
    required this.listOfTurfSizeAndPrice,
    required this.selectedTurfSize,
    required this.selectedSlotDuration,
    required this.selectTime,
    required this.listofSlotTime,
    required this.selectSlotDuration,
    required this.incrementAndDecrementStep,
    required this.setListOfSlots,
    required this.selectedSportCategory,
    required this.selectSportCategory,
    required this.venueNameController,
    required this.listOfTurfOption,
    required this.setTurfOption,
  }) : super(key: key);

  save() {
    if (selectedSports.length == 0) {
      callToastMessage('Select Sports');
      return;
    } else if (selectedWeekDays.length == 0) {
      callToastMessage('Select Weekdays');
      return;
    } else if (selectedSlots.length == 0) {
      callToastMessage('Select slots');
      return;
    } else if (selectedSlotDuration == 0) {
      callToastMessage('Select slot timing');
      return;
    } else if (selectedTurfSize.length == 0 &&
        selectedSportCategory['categoryName'] == 'Outdoor') {
      callToastMessage('Select turf size');
      return;
    } else {
      incrementAndDecrementStep(true);
      setListOfSlots();
    }
  }

  checkTurfSize(size) {
    if (option == 'One On') {
      if (selectedTurfSize.length == 1) {
        return false;
      } else {
        if (selectedTurfSize.contains(size)) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      if (isNet) {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else if (size == '8:8') {
          if (selectedTurfSize.contains('9:9') ||
              selectedTurfSize.contains('7:7')) {
            return true;
          } else {
            return false;
          }
        } else if (size == '9:9' || size == '7:7') {
          if (selectedTurfSize.contains('8:8')) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }

        //Blocking 6 and 5
        // if (size == '8:8') {
        //   if (selectedTurfSize.contains('9:9') ||
        //       selectedTurfSize.contains('7:7') ||
        //       selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '9:9' || size == '7:7') {
        //   if (selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '6:6') {
        //   if (selectedTurfSize.contains('5:5')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else if (size == '5:5') {
        //   if (selectedTurfSize.contains('6:6') ||
        //       selectedTurfSize.contains('8:8')) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // } else {
        //   return false;
        // }
      } else {
        if (size == '11:11' || size == '1:1') {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: deviceWidth * 0.7),
                child: Text(
                  'Venue Name*',
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 14,
                        color: const Color(0xff434343),
                      ),
                ),
              ),
              TextFormField(
                style: TextStyle(
                  fontSize: textScaleFactor * 16,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: textScaleFactor * 13,
                        color: Color(0xff737373),
                        fontWeight: FontWeight.normal,
                      ),
                  hintText: "Venue Name",
                  counterText: "",
                  fillColor: Colors.grey.shade300,
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  disabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                  filled: false,
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade500),
                  ),
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cursorColor: Colors.black,
                textCapitalization: TextCapitalization.words,
                controller: venueNameController,
                keyboardType: TextInputType.text,
                maxLines: null,
                validator: (value) {
                  if (value!.trim().isEmpty) {
                    return 'Please enter turf name';
                  }
                },
              ),
              SizedBox(height: deviceWidth * 0.05),
              buildTitleContainer(
                deviceWidth,
                textScaleFactor,
                context,
                'Select Sport Category',
              ),
              ...listofSportCategory.map(
                (cat) => GestureDetector(
                  onTap: isNet && cat['title'] == '2:2'
                      ? null
                      : () => selectSportCategory(cat['id']),
                  child: Container(
                    width: double.infinity,
                    color: Colors.transparent,
                    padding: EdgeInsets.only(bottom: deviceWidth * 0.03),
                    margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
                    child: Row(
                      children: [
                        Container(
                          margin: EdgeInsets.only(right: deviceWidth * 0.04),
                          alignment: Alignment.bottomRight,
                          height: deviceWidth * 0.05,
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: cat['isSelected']
                                  ? Theme.of(context).primaryColor
                                  : Color(0xffB6B7BA),
                            ),
                          ),
                          child: CircleAvatar(
                            radius: 8,
                            backgroundColor: cat['isSelected']
                                ? Theme.of(context).primaryColor
                                : Colors.white,
                          ),
                        ),
                        Container(
                          width: deviceWidth * 0.8,
                          child: Text(
                            '${cat['title']}',
                            style:
                                Theme.of(context).textTheme.displayLarge!.copyWith(
                                      fontSize: textScaleFactor * displayLarge,
                                      color: Color(0xff242530),
                                      fontWeight: FontWeight.w600,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        buildDivider(deviceWidth),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildTitleContainer(
                deviceWidth,
                textScaleFactor,
                context,
                'Sport Type',
              ),
              Wrap(
                children: [
                  ...listOfSports
                      .where((sport) =>
                          sport['categoryId'] ==
                          selectedSportCategory['categoryId'])
                      .toList()
                      .map(
                        (sport) => buildValueContainer(
                          deviceWidth: deviceWidth,
                          textScaleFactor: textScaleFactor,
                          context: context,
                          title: sport['title'],
                          id: sport['id'],
                          isSelected: sport['isSelected'],
                          onTap: selectAndUnSelectType,
                          listToIterate: listOfSports,
                          listToSet: selectedSports,
                        ),
                      )
                ],
              ),
            ],
          ),
        ),
        buildDivider(deviceWidth),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildTitleContainer(
                deviceWidth,
                textScaleFactor,
                context,
                'Select Days of Week',
              ),
              Wrap(
                children: [
                  ...listOfDays.map(
                    (day) => buildValueContainer(
                      deviceWidth: deviceWidth,
                      textScaleFactor: textScaleFactor,
                      context: context,
                      title: day['title'],
                      id: day['id'],
                      isSelected: day['isSelected'],
                      onTap: selectAndUnSelectType,
                      listToIterate: listOfDays,
                      listToSet: selectedWeekDays,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
        buildDivider(deviceWidth),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildTitleContainer(
                deviceWidth,
                textScaleFactor,
                context,
                'Select Slot Difference',
              ),
              Wrap(
                children: [
                  ...listofSlotTime.map(
                    (slot) => GestureDetector(
                      onTap: () => selectSlotDuration(slot['id']),
                      child: Container(
                        width: double.infinity,
                        color: Colors.transparent,
                        padding: EdgeInsets.only(bottom: deviceWidth * 0.03),
                        margin: EdgeInsets.only(bottom: deviceWidth * 0.005),
                        child: Row(
                          children: [
                            Container(
                              margin:
                                  EdgeInsets.only(right: deviceWidth * 0.04),
                              alignment: Alignment.bottomRight,
                              height: deviceWidth * 0.05,
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: slot['slot'] == selectedSlotDuration
                                      ? Theme.of(context).primaryColor
                                      : Color(0xffB6B7BA),
                                ),
                              ),
                              child: CircleAvatar(
                                radius: 8,
                                backgroundColor:
                                    slot['slot'] == selectedSlotDuration
                                        ? Theme.of(context).primaryColor
                                        : Colors.white,
                              ),
                            ),
                            Container(
                              width: deviceWidth * 0.8,
                              child: Text(
                                '${slot['slot']} mins',
                                style: Theme.of(context)
                                    .textTheme
                                    .displayLarge!
                                    .copyWith(
                                      fontSize: textScaleFactor * displayLarge,
                                      color: Color(0xff242530),
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        buildDivider(deviceWidth),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildTitleContainer(
                deviceWidth,
                textScaleFactor,
                context,
                'Slots',
              ),
              Wrap(
                children: [
                  ...listOfSlots.map(
                    (slot) => buildValueContainer(
                      deviceWidth: deviceWidth,
                      textScaleFactor: textScaleFactor,
                      context: context,
                      title: slot['title'],
                      id: slot['id'],
                      isSelected: slot['isSelected'],
                      onTap: selectAndUnSelectType,
                      listToIterate: listOfSlots,
                      listToSet: selectedSlots,
                      turfSize: true,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
        if (selectedSportCategory['categoryName'] == 'Outdoor')
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildDivider(deviceWidth),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    buildTitleContainer(
                      deviceWidth,
                      textScaleFactor,
                      context,
                      'Select Turf Option',
                    ),
                    ...listOfTurfOption.map(
                      (data) => GestureDetector(
                        onTap: () => setTurfOption(data['title']),
                        child: Container(
                          width: double.infinity,
                          color: Colors.transparent,
                          padding: EdgeInsets.only(bottom: deviceWidth * 0.03),
                          child: Row(
                            children: [
                              Container(
                                margin:
                                    EdgeInsets.only(right: deviceWidth * 0.04),
                                alignment: Alignment.bottomRight,
                                height: deviceWidth * 0.05,
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: data['isSelected']
                                        ? Theme.of(context).primaryColor
                                        : Color(0xffB6B7BA),
                                  ),
                                ),
                                child: CircleAvatar(
                                  radius: 8,
                                  backgroundColor: data['isSelected']
                                      ? Theme.of(context).primaryColor
                                      : Colors.white,
                                ),
                              ),
                              Container(
                                width: deviceWidth * 0.8,
                                child: Text(
                                  data['label'],
                                  style: Theme.of(context)
                                      .textTheme
                                      .displayLarge!
                                      .copyWith(
                                        fontSize: textScaleFactor * displayLarge,
                                        color: Color(0xff242530),
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                    // GestureDetector(
                    //   onTap: () => setIsNetValue(false),
                    //   child: Container(
                    //     width: double.infinity,
                    //     padding: EdgeInsets.only(bottom: deviceWidth * 0.03),
                    //     color: Colors.transparent,
                    //     child: Row(
                    //       children: [
                    //         Container(
                    //           margin:
                    //               EdgeInsets.only(right: deviceWidth * 0.04),
                    //           alignment: Alignment.bottomRight,
                    //           height: deviceWidth * 0.05,
                    //           padding: EdgeInsets.all(2),
                    //           decoration: BoxDecoration(
                    //             shape: BoxShape.circle,
                    //             border: Border.all(
                    //               color: !isNet
                    //                   ? Theme.of(context).primaryColor
                    //                   : Color(0xffB6B7BA),
                    //             ),
                    //           ),
                    //           child: CircleAvatar(
                    //             radius: 8,
                    //             backgroundColor: !isNet
                    //                 ? Theme.of(context).primaryColor
                    //                 : Colors.white,
                    //           ),
                    //         ),
                    //         Container(
                    //           width: deviceWidth * 0.8,
                    //           child: Text(
                    //             'No',
                    //             style: Theme.of(context)
                    //                 .textTheme
                    //                 .displayLarge!
                    //                 .copyWith(
                    //                   fontSize: textScaleFactor * displayLarge,
                    //                   color: Color(0xff242530),
                    //                   fontWeight: FontWeight.w600,
                    //                 ),
                    //           ),
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              buildDivider(deviceWidth),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildTitleContainer(
                      deviceWidth,
                      textScaleFactor,
                      context,
                      'Select Turf Sizes',
                    ),
                    Wrap(
                      children: [
                        ...listOfTurfSizeAndPrice.map(
                          (size) => GestureDetector(
                            onTap: size['title'] == '1:1'
                                ? null
                                : () {
                                    if (!checkTurfSize(size['title'])) {
                                      selectAndUnSelectType(
                                        size['id'],
                                        size['isSelected'],
                                        listOfTurfSizeAndPrice,
                                        selectedTurfSize,
                                        false,
                                      );
                                      setUsePreviousValueForPrice();
                                    }
                                  },
                            child: Container(
                              margin: EdgeInsets.only(
                                right: deviceWidth * 0.03,
                                bottom: deviceWidth * 0.03,
                              ),
                              constraints: BoxConstraints(
                                minWidth: size['title'] == '1:1'
                                    ? deviceWidth * 0.09
                                    : deviceWidth * 0.11,
                              ),
                              padding: EdgeInsets.symmetric(
                                vertical: deviceWidth * 0.032,
                                horizontal: deviceWidth * 0.04,
                              ),
                              decoration: BoxDecoration(
                                color: checkTurfSize(size['title'])
                                    ? Colors.grey.shade300
                                    : size['isSelected']
                                        ? Theme.of(context).primaryColor
                                        : Colors.white,
                                borderRadius: BorderRadius.circular(100),
                                border: Border.all(
                                  color: checkTurfSize(size['title'])
                                      ? Colors.black12
                                      : size['isSelected']
                                          ? Theme.of(context).primaryColor
                                          : Color(0xff707070),
                                ),
                              ),
                              child: FittedBox(
                                child: Text(
                                  size['title'],
                                  style: Theme.of(context)
                                      .textTheme
                                      .displayLarge!
                                      .copyWith(
                                        fontSize: textScaleFactor * displayLarge,
                                        color: checkTurfSize(size['title'])
                                            ? Colors.grey.shade500
                                            : size['isSelected']
                                                ? Colors.white
                                                : Color(0xff707070),
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: deviceWidth * 0.02),
                  ],
                ),
              ),
            ],
          ),
        // SizedBox(height: deviceWidth * 0.04),
        SizedBox(height: deviceWidth * 0.08),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: buildRaisedButton(
            deviceWidth,
            deviceWidth * 0.12,
            save,
            Text(
              'Save and Continue',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontSize: textScaleFactor * 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: .50,
                    color: Colors.white,
                  ),
            ),
            TargetPlatform.android,
            Theme.of(context).primaryColor,
            7,
          ),
        ),
        SizedBox(height: deviceWidth * 0.08),
      ],
    );
  }
}

Widget buildTitleContainer(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String title, {
  bool showSubtitle = false,
  String subTitleText = '',
  bool applybottomMargin = true,
}) {
  return Container(
    margin: EdgeInsets.only(
      bottom: applybottomMargin ? deviceWidth * 0.03 : 0,
    ),
    child: RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * displayMedium,
                ),
          ),
          TextSpan(
            text: '*',
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  fontSize: textScaleFactor * displayMedium,
                  color: Color(0xffAA0404),
                ),
          ),
          if (showSubtitle)
            TextSpan(
              text: '\n$subTitleText',
              style: Theme.of(context).textTheme.displayLarge!.copyWith(
                    fontSize: textScaleFactor * displayLarge,
                    color: Color(0xff242530),
                  ),
            ),
        ],
      ),
    ),
  );
}

Widget buildValueContainer({
  required double deviceWidth,
  required double textScaleFactor,
  required BuildContext context,
  required String title,
  required String id,
  required bool isSelected,
  required Function onTap,
  required List listToIterate,
  required List listToSet,
  bool turfSize = false,
  bool addSlot = false,
}) {
  return GestureDetector(
    onTap: () {
      if (!addSlot) {
        onTap(
          id,
          isSelected,
          listToIterate,
          listToSet,
          turfSize,
        );
      } else {
        onTap();
      }
    },
    child: Container(
      margin: EdgeInsets.only(
        right: deviceWidth * 0.03,
        bottom: deviceWidth * 0.03,
      ),
      constraints: BoxConstraints(minWidth: deviceWidth * 0.13),
      padding: EdgeInsets.symmetric(
        vertical: deviceWidth * 0.032,
        horizontal: deviceWidth * 0.04,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(100),
        border: Border.all(
          color:
              isSelected ? Theme.of(context).primaryColor : Color(0xff707070),
        ),
      ),
      child: FittedBox(
        child: Text(
          title,
          style: Theme.of(context).textTheme.displayLarge!.copyWith(
                fontSize: textScaleFactor * displayLarge,
                color: isSelected ? Colors.white : Color(0xff707070),
                fontWeight: FontWeight.bold,
              ),
        ),
      ),
    ),
  );
}

Widget buildDivider(double deviceWidth) {
  return Container(
    margin: EdgeInsets.only(
      top: deviceWidth * 0.01,
      bottom: deviceWidth * 0.05,
    ),
    child: Divider(
      height: deviceWidth * 0.02,
      color: Colors.grey.shade200,
      thickness: 4,
    ),
  );
}

Widget buildRowCardOfTime(
  double deviceWidth,
  double textScaleFactor,
  BuildContext context,
  String title,
  String value,
  Function selectTime, {
  bool showEditButtom = true,
}) {
  return GestureDetector(
    onTap: () {
      selectTime();
    },
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextWidget(title: title, color: Color(0xff636363)),
        Container(
          width: deviceWidth * 0.32,
          padding: EdgeInsets.symmetric(
            vertical: deviceWidth * 0.02,
            horizontal: deviceWidth * 0.03,
          ),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: getThemeColor()),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(Icons.watch_later_outlined,
                  color: getThemeColor(), size: 20),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: TextWidget(
                  title: value,
                  fontSize: textScaleFactor * displayLarge,
                  color: getThemeColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
