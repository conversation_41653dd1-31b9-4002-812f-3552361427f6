import '../../commonWidgets/raisedButton.dart';
import '../../venueModule/widgets/step1Widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../fontSizes.dart';

class Step2Widget extends StatefulWidget {
  final List selectedSlots;
  final Map selectedSportCategory;
  final bool isNet;
  final int selectedSlotDuration;
  final Function selectTime;
  final Function addOrRemovePrice;
  final Function incrementAndDecrementStep;
  final Function checkForWeekend;
  final Function checkForWeekdays;

  Step2Widget({
    Key? key,
    required this.selectedSlots,
    required this.selectedSportCategory,
    required this.isNet,
    required this.selectTime,
    required this.addOrRemovePrice,
    required this.incrementAndDecrementStep,
    required this.checkForWeekend,
    required this.selectedSlotDuration,
    required this.checkForWeekdays,
  }) : super(key: key);

  @override
  State<Step2Widget> createState() => _Step2WidgetState();
}

class _Step2WidgetState extends State<Step2Widget> {
  checkIsNet(String size) {
    // if (isNet) {
    //   if (size == '8:8' || size == '6:6') {
    //     return false;\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    //   } else {
    //     return true;
    //   }
    // } else {
    return true;
    // }
  }

  nextStep() {
    // for (var i = 0; i < widget.selectedSlots.length; i++) {
    //   for (var j = 0;
    //       j < widget.selectedSlots[i]['priceAndQuantity'].length;
    //       j++) {
    //     if (widget.checkForWeekdays()) {
    //       if (widget.selectedSlots[i]['priceAndQuantity'][j]['price'] <= 0) {
    //         callToastMessage('Please enter the weekdays price');
    //         return;
    //       }
    //     }
    //     if (widget.checkForWeekend()) {
    //       if (widget.selectedSlots[i]['priceAndQuantity'][j]['weekendPrice'] <=
    //           0) {
    //         callToastMessage('Please enter the weekends price');
    //         return;
    //       }
    //     }
    //   }
    // }
    widget.incrementAndDecrementStep(true);
    return true;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
          child: Form(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...widget.selectedSlots
                    .asMap()
                    .map(
                      (i, slot) => MapEntry(
                        i,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            buildTitleContainer(
                              deviceWidth,
                              textScaleFactor,
                              context,
                              '${i + 1}) ${slot['title']} Slot',
                              showSubtitle: false,
                            ),
                            Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.04),
                              child: buildTitleContainer(
                                deviceWidth,
                                textScaleFactor,
                                context,
                                widget.selectedSportCategory['categoryName'] !=
                                        'Outdoor'
                                    ? 'Select Sport & Enter Pricing'
                                    : 'Select Turf Sizes & Enter Pricing',
                                showSubtitle: true,
                                subTitleText:
                                    'Include the Taxes while adding price for ${widget.selectedSlotDuration} mins.',
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: deviceWidth * 0.04,
                                vertical: deviceWidth * 0.02,
                              ),
                              child: Row(
                                mainAxisAlignment: widget.checkForWeekdays() &&
                                        widget.checkForWeekend()
                                    ? MainAxisAlignment.spaceBetween
                                    : MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                      right: deviceWidth * 0.03,
                                      bottom: deviceWidth * 0.03,
                                    ),
                                    constraints: BoxConstraints(
                                      minWidth: deviceWidth * 0.13,
                                    ),
                                    // child: Text('Size'),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      left: widget.checkForWeekdays() &&
                                              widget.checkForWeekend()
                                          ? deviceWidth * 0.05
                                          : deviceWidth * 0.125,
                                    ),
                                    child: widget.checkForWeekdays()
                                        ? Text(
                                            'Weekdays',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          )
                                        : Text(''),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      right: deviceWidth * 0.03,
                                    ),
                                    child: widget.checkForWeekend()
                                        ? Text(
                                            'Weekends',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          )
                                        : Text(''),
                                  ),
                                ],
                              ),
                            ),
                            ...slot['priceAndQuantity'].map((sport) {
                              return Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: deviceWidth * 0.04),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      widget.checkForWeekdays() &&
                                              widget.checkForWeekend()
                                          ? MainAxisAlignment.spaceBetween
                                          : MainAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        widget.addOrRemovePrice(
                                          slot['priceAndQuantity'],
                                          sport['id'],
                                          sport['isSelected'] ? false : true,
                                        );
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(
                                          right: deviceWidth * 0.03,
                                          bottom: deviceWidth * 0.03,
                                        ),
                                        width: deviceWidth * 0.21,
                                        padding: EdgeInsets.symmetric(
                                          vertical: deviceWidth * 0.032,
                                          horizontal: deviceWidth * 0.04,
                                        ),
                                        decoration: BoxDecoration(
                                          color: sport['isSelected']
                                              ? Theme.of(context).primaryColor
                                              : Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(100),
                                          border: Border.all(
                                            color: sport['isSelected']
                                                ? Theme.of(context).primaryColor
                                                : Color(0xff707070),
                                          ),
                                        ),
                                        child: Text(
                                          sport['title'],
                                          style: Theme.of(context)
                                              .textTheme
                                              .displayLarge!
                                              .copyWith(
                                                fontSize:
                                                    widget.selectedSportCategory[
                                                                'categoryName'] ==
                                                            'Outdoor'
                                                        ? textScaleFactor * 15
                                                        : textScaleFactor * 13,
                                                color: sport['isSelected']
                                                    ? Colors.white
                                                    : Color(0xff707070),
                                                fontWeight: FontWeight.bold,
                                              ),
                                          softWrap: true,
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: deviceWidth * 0.02),
                                    if (widget.checkForWeekdays())
                                      GestureDetector(
                                        onTap: () {
                                          if (!sport['isSelected']) {
                                            callToastMessage(
                                              widget.selectedSportCategory[
                                                          'categoryName'] !=
                                                      'Outdoor'
                                                  ? "Please select the sport"
                                                  : 'Please select the turf size',
                                            );
                                          }
                                        },
                                        child: Container(
                                          width: deviceWidth * 0.23,
                                          margin: EdgeInsets.only(
                                              right: deviceWidth * 0.03),
                                          child: TextFormField(
                                            controller: sport['controller'],
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly,
                                            ],
                                            style: TextStyle(
                                              fontSize: textScaleFactor * 14,
                                              letterSpacing: .30,
                                              fontWeight: FontWeight.w600,
                                            ),
                                            textAlign: TextAlign.center,
                                            decoration: InputDecoration(
                                              hintStyle: Theme.of(context)
                                                  .textTheme
                                                  .headlineSmall!
                                                  .copyWith(
                                                      fontSize:
                                                          textScaleFactor * 10),
                                              contentPadding:
                                                  EdgeInsets.symmetric(
                                                horizontal: deviceWidth * 0.01,
                                                vertical: 0,
                                              ),
                                              hintText: "Enter price",
                                              counterText: "",
                                              fillColor: Colors.transparent,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                              ),
                                              disabledBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                              ),
                                              filled: true,
                                              enabled: sport['isSelected']
                                                  ? true
                                                  : false,
                                            ),
                                            maxLength: 8,
                                            cursorColor: Colors.black,
                                            autovalidateMode: AutovalidateMode
                                                .onUserInteraction,
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              if (value.length != 0) {
                                                sport['price'] =
                                                    int.parse(value);
                                                print(sport['price']);
                                              } else {
                                                sport['price'] = 0;
                                                print(sport['price']);
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                    if (widget.checkForWeekend())
                                      GestureDetector(
                                        onTap: () {
                                          if (!sport['isSelected']) {
                                            callToastMessage(
                                              widget.selectedSportCategory[
                                                          'categoryName'] !=
                                                      'Outdoor'
                                                  ? "Please select the sport"
                                                  : 'Please select the turf size',
                                            );
                                          }
                                        },
                                        child: Container(
                                          width: deviceWidth * 0.23,
                                          child: TextFormField(
                                            controller:
                                                sport['weekendController'],
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly,
                                            ],
                                            style: TextStyle(
                                              fontSize: textScaleFactor * 14,
                                              letterSpacing: .30,
                                              fontWeight: FontWeight.w600,
                                            ),
                                            textAlign: TextAlign.center,
                                            decoration: InputDecoration(
                                              hintStyle: Theme.of(context)
                                                  .textTheme
                                                  .headlineSmall!
                                                  .copyWith(
                                                      fontSize:
                                                          textScaleFactor * 10),
                                              contentPadding:
                                                  EdgeInsets.symmetric(
                                                horizontal: deviceWidth * 0.01,
                                                vertical: 0,
                                              ),
                                              hintText: "Enter price",
                                              counterText: "",
                                              fillColor: Colors.transparent,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                              ),
                                              disabledBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                              ),
                                              filled: true,
                                              enabled: sport['isSelected']
                                                  ? true
                                                  : false,
                                            ),
                                            maxLength: 8,
                                            cursorColor: Colors.black,
                                            autovalidateMode: AutovalidateMode
                                                .onUserInteraction,
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              if (value.length != 0) {
                                                sport['weekendPrice'] =
                                                    int.parse(value);
                                                print(sport['weekendPrice']);
                                              } else {
                                                sport['weekendPrice'] = 0;
                                                print(sport['weekendPrice']);
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              );
                            }),
                            SizedBox(height: deviceWidth * 0.03),
                            Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.04),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  buildTitleContainer(
                                    deviceWidth,
                                    textScaleFactor,
                                    context,
                                    'Select Slot Timing',
                                  ),
                                  buildRowCardOfTime(
                                    deviceWidth,
                                    textScaleFactor,
                                    context,
                                    'Start Time:',
                                    '${slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                    () {
                                      widget.selectTime(
                                          true, slot['id'], slot['startTime']);
                                    },
                                  ),
                                  SizedBox(height: deviceWidth * 0.03),
                                  buildRowCardOfTime(
                                    deviceWidth,
                                    textScaleFactor,
                                    context,
                                    'End Time:',
                                    '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                    () {
                                      widget.selectTime(
                                          false, slot['id'], slot['endTime']);
                                    },
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: deviceWidth * 0.07)
                          ],
                        ),
                      ),
                    )
                    .values
                    .toList(),
                SizedBox(height: deviceWidth * 0.03),
                buildRaisedButton(
                  deviceWidth,
                  deviceWidth * 0.12,
                  nextStep,
                  Text(
                    'Save and Continue',
                    style: Theme.of(context).textTheme.displayMedium?.copyWith(
                          fontSize: textScaleFactor * 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: .50,
                          color: Colors.white,
                        ),
                  ),
                  TargetPlatform.android,
                  Theme.of(context).primaryColor,
                  7,
                ),
                SizedBox(height: deviceWidth * 0.08),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
