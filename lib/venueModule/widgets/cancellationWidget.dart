import 'package:bys_business/venueModule/widgets/addCancellationBottomsheet.dart';
import 'package:flutter/material.dart';

class CancellationWidget extends StatelessWidget {
  final Map data;
  final double deviceWidth;
  final double textScaleFactor;
  const CancellationWidget({
    Key? key,
    required this.data,
    required this.deviceWidth,
    required this.textScaleFactor,
  }) : super(key: key);

  Widget buildRangeContainer({
    required double deviceWidth,
    required double textScaleFactor,
    required String text,
    required BuildContext context,
  }) {
    return Container(
      width: deviceWidth * 0.12,
      alignment: Alignment.center,
      padding: EdgeInsets.all(deviceWidth * 0.02),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.transparent),
        borderRadius: BorderRadius.circular(5),
        color: Colors.white.withOpacity(.8),
      ),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          text,
          style: TextStyle(
            fontSize: textScaleFactor * 15,
            color: Theme.of(context).primaryColor,
            letterSpacing: .30,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
      padding: EdgeInsets.symmetric(
        horizontal: deviceWidth * 0.025,
        vertical: deviceWidth * 0.052,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.15),
        border:
            Border.all(color: Theme.of(context).primaryColor.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Row(
            children: [
              buildText(
                text: 'Hours Range',
                textScaleFactor: textScaleFactor,
                context: context,
                fontSize: 13.7,
              ),
              SizedBox(width: deviceWidth * 0.045),
              buildRangeContainer(
                context: context,
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                text: data['start'],
              ),
              SizedBox(width: deviceWidth * 0.03),
              buildText(
                text: 'To',
                textScaleFactor: textScaleFactor,
                context: context,
                color: Colors.black45,
              ),
              SizedBox(width: deviceWidth * 0.03),
              buildRangeContainer(
                context: context,
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                text: data['end'],
              ),
              SizedBox(width: deviceWidth * 0.03),
              buildText(
                text: 'Hrs',
                textScaleFactor: textScaleFactor,
                context: context,
                fontSize: 14,
              )
            ],
          ),
          SizedBox(height: deviceWidth * 0.05),
          Row(
            children: [
              buildText(
                text: 'Refund Percentage',
                textScaleFactor: textScaleFactor,
                context: context,
                fontSize: 13.7,
              ),
              SizedBox(width: deviceWidth * 0.045),
              buildRangeContainer(
                context: context,
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                text: data['percentage'],
              ),
              SizedBox(width: deviceWidth * 0.03),
              buildText(
                text: '%',
                textScaleFactor: textScaleFactor,
                context: context,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
