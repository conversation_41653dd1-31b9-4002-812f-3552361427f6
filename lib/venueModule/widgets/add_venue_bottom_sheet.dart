// ignore_for_file: must_be_immutable

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/custom_container.dart';
import 'package:bys_business/venueModule/newVenueFlow/screens/venue_instruction_screen.dart';
import 'package:bys_business/venueModule/widgets/select_duplication_venue_bottomsheet.dart';

import '../../commonWidgets/custom_button.dart';
import '../../commonWidgets/divider_widget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';

import '../models/venue_model.dart';

class AddVenueBottomSheet extends StatefulWidget {
  final List<Venue> listOfVenues;

  AddVenueBottomSheet({Key? key, required this.listOfVenues});

  @override
  State<AddVenueBottomSheet> createState() => _AddVenueBottomSheetState();
}

class _AddVenueBottomSheetState extends State<AddVenueBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  int selectedIndex = 0;

  List<Map> listOfOptions = [
    {
      'icon': 'newListing',
      'text': 'Create a new listing',
      'index': 1,
    },
    {
      'icon': 'duplicate',
      'text': 'Duplicate an existing listing',
      'index': 2,
    }
  ];

  selectVenueBottomSheet() {
    showModalBottomSheet(
      enableDrag: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SelectDuplicationVenueBottomSheet(
            listOfVenues: widget.listOfVenues),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    );
  }

  navigation() {
    if (selectedIndex == 1) {
      pop();
      push(VenueInstructionScreen());
    } else if (selectedIndex == 2) {
      selectVenueBottomSheet();
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      height: dH * 0.42,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: dW * 0.01),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextWidget(
                        title: 'Initiate a new listing',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      IconButton(
                        padding: EdgeInsets.all(0),
                        onPressed: () {
                          Navigator.of(context).pop(false);
                        },
                        icon: Icon(Icons.clear),
                      )
                    ],
                  ),
                  DividerWidget(top: 0),
                  CustomContainer(
                    vPadding: .01,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...listOfOptions.map(
                          (option) => GestureDetector(
                            onTap: () {
                              selectedIndex = option['index'];
                              setState(() {});
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.symmetric(vertical: dW * 0.035),
                              color: Colors.transparent,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      AssetSvgIcon(iconName: option['icon']),
                                      SizedBox(width: dW * 0.03),
                                      ConstrainedBox(
                                        constraints:
                                            BoxConstraints(maxWidth: dW * 0.65),
                                        child:
                                            TextWidget(title: option['text']),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    alignment: Alignment.bottomRight,
                                    height: dW * 0.05,
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: selectedIndex == option['index']
                                            ? Theme.of(context).primaryColor
                                            : Color(0xffB6B7BA),
                                      ),
                                    ),
                                    child: CircleAvatar(
                                      radius: 8,
                                      backgroundColor:
                                          selectedIndex == option['index']
                                              ? Theme.of(context).primaryColor
                                              : Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.08),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: dW * 0.05),
            child: CustomButton(
              width: dW * 0.9,
              height: dW * 0.12,
              buttonText: 'Proceed',
              onPressed: selectedIndex == 0 ? null : navigation,
            ),
          )
        ],
      ),
    );
  }
}
