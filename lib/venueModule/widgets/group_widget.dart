import 'package:flutter/material.dart';

import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../navigators.dart';
import '../models/group_model.dart';
import '../screens/group_details_screen.dart';

class GroupWidget extends StatelessWidget {
  final double dW;
  final int index;
  final Group group;
  const GroupWidget({
    Key? key,
    required this.dW,
    required this.index,
    required this.group,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => push(GroupDetailsScreen(group: group, index: index)),
      child: CustomContainer(
        bgColor: Colors.transparent,
        margin: EdgeInsets.only(bottom: dW * 0.04),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: dW * 0.03, vertical: dW * 0.01),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Color(0xffFFE796)),
                  child: Text(
                    'Group ${index + 1}',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                      color: Color(0xff3E3E3E),
                    ),
                  ),
                ),
                SizedBox(height: dW * 0.025),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: dW * 0.75),
                  child: TextWidget(
                    title: '${group.name}, ${group.primaryVenue.address.city}',
                    fontWeight: FontWeight.w500,
                    color: Color(0xff3E3E3E),
                  ),
                ),
                SizedBox(height: dW * 0.01),
                TextWidget(
                  title:
                      '${(group.venues.length + 1)} ${(group.venues.length + 1) > 1 ? 'Venues' : 'Venue'}',
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff636363),
                ),
              ],
            ),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: 24,
              color: getThemeColor(),
            ),
          ],
        ),
      ),
    );
  }
}
