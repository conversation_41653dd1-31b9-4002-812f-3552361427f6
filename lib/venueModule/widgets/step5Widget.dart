import 'package:bys_business/venueModule/widgets/cancellationWidget.dart';
import 'package:bys_business/venueModule/widgets/step1Widget.dart';
import 'package:flutter/material.dart';

import '../../bulkBookingModule/widgets/select_count_bottomsheet.dart';
import '../../bulkBookingModule/widgets/select_duration_bottomsheet.dart';
import '../../commonWidgets/oldtextWidget.dart';
import '../../fontSizes.dart';
import 'addCancellationBottomsheet.dart';

class Step5Widget extends StatefulWidget {
  final List cancellationCharges;
  final Function addOrRemoveCancellation;
  final Function editCancellation;
  final Function changeDuration;
  int durationCount;
  String period;
  final TextEditingController cancellationController;
  Step5Widget({
    Key? key,
    required this.cancellationCharges,
    required this.addOrRemoveCancellation,
    required this.editCancellation,
    required this.changeDuration,
    required this.durationCount,
    required this.period,
    required this.cancellationController,
  }) : super(key: key);

  @override
  State<Step5Widget> createState() => _Step5WidgetState();
}

class _Step5WidgetState extends State<Step5Widget> {
  addCancellationBottomSheet({Map? data, int? index}) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddCancellationBottomSheet(
          cancellationCharges: widget.cancellationCharges,
          data: data,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        if (data != null) {
          widget.editCancellation(data: value, index: index);
        } else {
          widget.addOrRemoveCancellation(
            data: value,
            index: widget.cancellationCharges.isEmpty
                ? 0
                : widget.cancellationCharges.length,
            add: true,
          );
        }
      }
    });
  }

  Widget buildDuration({
    required double deviceWidth,
    required double textScaleFactor,
    required String title,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OldTextWidget(
          bottomMargin: deviceWidth * 0.02,
          context: context,
          text: title,
          fontSize: textScaleFactor * 15,
          textColor: Colors.black,
          letterSpacing: 0.4,
          fontWeight: FontWeight.w600,
        ),
        GestureDetector(
          onTap: () {
            selectDurationOrCountDropDown(title);
          },
          child: Container(
            width: deviceWidth * 0.4,
            height: deviceWidth * 0.12,
            padding: EdgeInsets.symmetric(
              horizontal: deviceWidth * 0.03,
              vertical: deviceWidth * 0.005,
            ),
            margin: EdgeInsets.only(bottom: deviceWidth * 0.04),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              border: Border.all(
                width: 1,
                color: Colors.transparent,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: textScaleFactor * 16,
                    letterSpacing: 0.3,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.black,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  selectDurationOrCountDropDown(String duration) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: duration == 'Period'
            ? SelectDurationBottomSheet(
                selectedDuration: widget.period,
                addDays: true,
              )
            : SelectCountBottomSheet(
                selectedDuration: widget.period,
                selectedCount: widget.durationCount,
              ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        widget.changeDuration(duration, value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final deviceHeight = MediaQuery.of(context).size.height;
    final deviceWidth = MediaQuery.of(context).size.width;
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildTitleContainer(
            deviceWidth,
            textScaleFactor,
            context,
            'Booking Availability Duration',
          ),
          SizedBox(height: deviceWidth * 0.02),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildDuration(
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                title: 'Period',
                value: widget.period,
              ),
              buildDuration(
                deviceWidth: deviceWidth,
                textScaleFactor: textScaleFactor,
                title: 'Count',
                value: widget.durationCount.toString(),
              ),
            ],
          ),
          SizedBox(height: deviceWidth * 0.033),
          Column(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Cancellation Charges',
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                          fontSize: textScaleFactor * 16,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  // GestureDetector(
                  //   onTap: () => addCancellationBottomSheet(),
                  //   child: CircleAvatar(
                  //     radius: 13,
                  //     backgroundColor: Theme.of(context).primaryColor,
                  //     child: Icon(
                  //       Icons.add,
                  //       color: Colors.white,
                  //       size: 20,
                  //     ),
                  //   ),
                  // ),
                  GestureDetector(
                    onTap: () => addCancellationBottomSheet(),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: deviceWidth * 0.03,
                        vertical: deviceWidth * 0.014,
                      ),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: Theme.of(context).primaryColor),
                        borderRadius: BorderRadius.circular(5),
                        color: Theme.of(context).primaryColor,
                      ),
                      child: buildText(
                          text: 'Add',
                          textScaleFactor: textScaleFactor,
                          context: context,
                          fontSize: 14.5,
                          color: Colors.white),
                    ),
                  )
                ],
              ),
              SizedBox(height: deviceWidth * 0.01),
              Text(
                'Add hour range and user refund percentage.',
                style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      fontSize: textScaleFactor * 13,
                      color: Colors.black,
                    ),
              ),
            ],
          ),
          SizedBox(height: deviceWidth * 0.05),
          // if (widget.cancellationCharges.isEmpty)
          //   Container(
          //     margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
          //     child: buildText(
          //       text: 'Add your cancellation charges.',
          //       textScaleFactor: textScaleFactor,
          //       context: context,
          //       fontSize: 14.5,
          //     ),
          //   ),
          if (widget.cancellationCharges.isNotEmpty)
            ...widget.cancellationCharges
                .asMap()
                .map(
                  (i, data) => MapEntry(
                    i,
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        CancellationWidget(
                          data: data,
                          deviceWidth: deviceWidth,
                          textScaleFactor: textScaleFactor,
                        ),
                        Positioned(
                          right: -10,
                          child: PopupMenuButton(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(7),
                            ),
                            icon: Icon(
                              Icons.more_vert,
                              color: Colors.black,
                            ),
                            itemBuilder: (BuildContext bc) => [
                              PopupMenuItem(
                                child: Text(
                                  "Edit",
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: textScaleFactor * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 1,
                              ),
                              PopupMenuItem(
                                child: Text(
                                  'Delete',
                                  style: Theme.of(context)
                                      .textTheme
                                      .displaySmall!
                                      .copyWith(
                                        fontSize: textScaleFactor * 13,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                ),
                                value: 2,
                              ),
                            ],
                            onSelected: (value) {
                              if (value == 1) {
                                addCancellationBottomSheet(
                                  data: data,
                                  index: i,
                                );
                              } else if (value == 2) {
                                widget.addOrRemoveCancellation(
                                  data: data,
                                  index: i,
                                  add: false,
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .values
                .toList(),
          SizedBox(height: deviceWidth * 0.035),
          OldTextWidget(
            context: context,
            text: 'Cancellation Policy',
            fontSize: textScaleFactor * 15,
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: deviceWidth * 0.035),
          Container(
            margin: EdgeInsets.only(bottom: deviceWidth * 0.02),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Color(0xffF9F9F9),
            ),
            constraints: BoxConstraints(minHeight: deviceWidth * 0.7),
            child: TextFormField(
              style: TextStyle(
                // color: Color(0xff6E7271),
                fontWeight: FontWeight.w600,
                fontSize: textScaleFactor * 14,
                letterSpacing: .30,
              ),
              decoration: InputDecoration(
                counterText: '',
                hintStyle: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: textScaleFactor * headline9,
                      fontWeight: FontWeight.normal,
                      color: Color(0xff757371),
                    ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: deviceWidth * 0.034,
                  vertical: deviceWidth * 0.04,
                ),
                hintText: "Write your venue cancellation policy here...",
                border: InputBorder.none,
              ),
              maxLines: null,
              cursorColor: Colors.black,
              textCapitalization: TextCapitalization.sentences,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              controller: widget.cancellationController,
              keyboardType: TextInputType.streetAddress,
              textInputAction: TextInputAction.newline,
              onChanged: (value) {
                setState(() {});
              },
              // validator: (value) {
              //   if (value!.isEmpty) {
              //     return 'Please enter venue description';
              //   }
              // },
            ),
          ),
        ],
      ),
    );
  }
}
