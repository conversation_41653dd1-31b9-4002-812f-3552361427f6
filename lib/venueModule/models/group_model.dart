import 'venue_model.dart';

class Group {
  final String id;
  final String name;
  final List<Venue> venues;
  final Venue primaryVenue;
  final DateTime createdAt;

  Group({
    required this.id,
    required this.name,
    required this.venues,
    required this.primaryVenue,
    required this.createdAt,
  });

  static Group jsonToGroup(Map group) {
    List<Venue> listOfVenues = group['venues']
        .map<Venue>((venue) => Venue.jsonToVenue(venue))
        .toList();

    int index =
        listOfVenues.indexWhere((venue) => venue.id == group['primaryVenue']);
    Venue primaryVenue = listOfVenues[index];
    listOfVenues.removeAt(index);

    return Group(
      id: group['_id'] ?? '',
      name: group['name'] ?? '',
      venues: listOfVenues,
      primaryVenue: primaryVenue,
      createdAt: DateTime.parse(group['createdAt']).toLocal(),
    );
  }
}
