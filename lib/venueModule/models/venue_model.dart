// import 'package:google_maps_flutter/google_maps_flutter.dart';

// import 'sportModels/badminton_model.dart';
// import 'sportModels/cricket_model.dart';
// import 'sportModels/football_model.dart';
// import 'sportModels/ps3_model.dart';
// import 'sportModels/ps4_model.dart';
// import 'sportModels/ps5_model.dart';
// import 'sportModels/snooker_model.dart';
// import 'sportModels/table_tennis_model.dart';
// import 'sportModels/tennis_model.dart';
// import 'sportModels/xbox1_model.dart';

// class Venue {
//   String id;
//   String businessId;
//   String user;
//   String name;
//   String mobileNo;
//   String cancellationPolicy;
//   Address address;
//   List<SportType>? sportsType;
//   List? days;
//   List? weekends;
//   List<Slot>? slots;
//   bool? isNet;

//   int? slotTimeDifference;
//   List<String>? images;
//   List? facilities;
//   String? description;
//   String? option;
//   bool block;
//   String status;
//   SportCategory? sportCategory;
//   DateTime createdAt;
//   List? pauseDates;
//   int advanceAmount;
//   int advanceAmountForOneOn;
//   double convenienceFeeForAdvancePay;
//   double convenienceFeeForFullPay;
//   Availability? availability;
//   List<CancellationCharge> cancellationCharges;
//   VideoData? video;
//   List<Equipments>? equipments;
//   //Sports Model

//   //Outdoor
//   final Cricket? cricket;
//   final Football? football;

//   //Indoor
//   final Badminton? badminton;
//   final Snooker? snooker;
//   final TableTennis? tableTennis;
//   final Tennis? tennis;

//   //Esport
//   final PS3? ps3;
//   final PS4? ps4;
//   final PS5? ps5;
//   final Xbox1? xbox1;

//   VenueGroup? group;
//   bool isPrimaryVenue;

//   Venue({
//     required this.id,
//     required this.businessId,
//     required this.user,
//     required this.name,
//     required this.mobileNo,
//     required this.address,
//     required this.cancellationPolicy,
//     required this.createdAt,
//     required this.status,
//     required this.advanceAmount,
//     required this.advanceAmountForOneOn,
//     required this.equipments,
//     this.availability,
//     required this.cancellationCharges,
//     this.pauseDates,
//     this.sportCategory,
//     this.sportsType,
//     this.isNet,
//     this.days,
//     this.weekends,
//     this.slots,
//     this.slotTimeDifference,
//     this.convenienceFeeForAdvancePay = 0,
//     this.convenienceFeeForFullPay = 0,
//     this.images,
//     this.option,
//     this.video,
//     this.facilities,
//     this.description,
//     this.block = false,
//     required this.cricket,
//     required this.football,
//     required this.badminton,
//     required this.snooker,
//     required this.tableTennis,
//     required this.tennis,
//     required this.ps3,
//     required this.ps4,
//     required this.ps5,
//     required this.xbox1,
//     required this.group,
//     required this.isPrimaryVenue,
//   });
//   static Venue jsonToVenue(Map<String, dynamic> venue) {
//     return Venue(
//       id: venue['_id'] ?? '',
//       businessId: venue['businessId'] ?? '',
//       user: venue['user'] ?? '',
//       name: venue['name'] ?? '',
//       mobileNo: venue['phone'] ?? '',
//       address: Address.jsonToAddress(venue['address']),
//       createdAt: DateTime.parse(venue['createdAt']).toLocal(),
//       status: venue['status'],
//       block: venue['block'] ?? [],
//       days: venue['days'] ?? [],
//       weekends: venue['weekends'] ?? [],
//       description: venue['description'] ?? '',
//       option: venue['option'] ?? '',
//       pauseDates: venue['pauseDates'] ?? [],
//       cancellationPolicy: venue['cancellationPolicy'] ?? '',
//       advanceAmount:
//           venue['advanceAmount'] == null ? 0 : venue['advanceAmount'].toInt(),
//       advanceAmountForOneOn: venue['advanceAmountForOneOn'] == null
//           ? 0
//           : venue['advanceAmountForOneOn'].toInt(),
//       convenienceFeeForAdvancePay: venue['convenienceFeeForAdvancePay'] == null
//           ? 0
//           : venue['convenienceFeeForAdvancePay'].toDouble(),
//       convenienceFeeForFullPay: venue['convenienceFeeForFullPay'] == null
//           ? 0
//           : venue['convenienceFeeForFullPay'].toDouble(),
//       cancellationCharges: venue['cancellationCharges']
//           .map<CancellationCharge>(
//               (charges) => CancellationCharge.jsonToCancellationCharge(charges))
//           .toList(),
//       equipments: venue['equipments'] == null
//           ? null
//           : venue['equipments']
//               .map<Equipments>(
//                   (equipment) => Equipments.jsonToEquipments(equipment))
//               .toList(),
//       availability: venue['availability'] == null
//           ? null
//           : Availability.jsonToAvailability(venue['availability']),
//       facilities: venue['facilities'] ?? [],
//       images: venue['images'] == null
//           ? []
//           : (venue['images'] as List).map<String>((data) {
//               return data['url'];
//             }).toList(),
//       isNet: venue['isNet'] ?? false,
//       slotTimeDifference: venue['slotTimeDifference'] ?? 0,
//       slots:
//           venue['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
//       sportCategory: SportCategory(
//         categoryName: venue['sportCategory'] == null
//             ? ''
//             : venue['sportCategory']['categoryName'],
//         id: venue['sportCategory'] == null ? '' : venue['sportCategory']['_id'],
//       ),
//       sportsType: venue['sportsType'] == null
//           ? []
//           : venue['sportsType']
//               .map<SportType>((sport) => SportType.jsonToSportType(sport))
//               .toList(),
//       video:
//           venue['video'] == null ? null : VideoData.jsonToVideo(venue['video']),
//       cricket: venue['cricket'] == null
//           ? null
//           : Cricket.jsonToCricket(venue['cricket']),
//       football: venue['football'] == null
//           ? null
//           : Football.jsonToFootball(venue['football']),
//       badminton: venue['badminton'] == null
//           ? null
//           : Badminton.jsonToBadminton(venue['badminton']),
//       snooker: venue['snooker'] == null
//           ? null
//           : Snooker.jsonToSnooker(venue['snooker']),
//       tableTennis: venue['tableTennis'] == null
//           ? null
//           : TableTennis.jsonToTableTennis(venue['tableTennis']),
//       tennis:
//           venue['tennis'] == null ? null : Tennis.jsonToTennis(venue['tennis']),
//       ps3: venue['ps3'] == null ? null : PS3.jsonToPS3(venue['ps3']),
//       ps4: venue['ps4'] == null ? null : PS4.jsonToPS4(venue['ps4']),
//       ps5: venue['ps5'] == null ? null : PS5.jsonToPS5(venue['ps5']),
//       xbox1: venue['xbox1'] == null ? null : Xbox1.jsonToXbox1(venue['xbox1']),
//       group: venue['group'] != null && venue['group'] is Map
//           ? VenueGroup.jsonToVenueGroup(venue['group'])
//           : null,
//       isPrimaryVenue: venue['isPrimaryVenue'] ?? false,
//     );
//   }
// }

// class Availability {
//   final String duration;
//   final int count;

//   Availability({required this.count, required this.duration});

//   static Availability jsonToAvailability(Map availability) {
//     return Availability(
//       duration: availability['duration'] ?? '',
//       count: availability['count'] ?? 0,
//     );
//   }
// }

// class CancellationCharge {
//   final int start;
//   final int end;
//   final int percentage;

//   CancellationCharge({
//     required this.start,
//     required this.end,
//     required this.percentage,
//   });

//   static CancellationCharge jsonToCancellationCharge(Map cancellationCharge) {
//     return CancellationCharge(
//       start: cancellationCharge['start'] ?? 0,
//       end: cancellationCharge['end'] ?? 0,
//       percentage: cancellationCharge['percentage'] ?? 0,
//     );
//   }
// }

// class PricingAndQuantity {
//   String id;
//   String title;
//   String label;
//   String sport;
//   int quantity;
//   double price;
//   String courtOrTable;
//   double advanceAmount;
//   int totalPlayersAllowed;
//   double weekendPrice;

//   PricingAndQuantity({
//     required this.id,
//     required this.title,
//     required this.label,
//     required this.sport,
//     required this.price,
//     required this.courtOrTable,
//     required this.weekendPrice,
//     required this.totalPlayersAllowed,
//     required this.quantity,
//     required this.advanceAmount,
//   });

//   static PricingAndQuantity jsonToPriceAndQuantity(Map json) {
//     return PricingAndQuantity(
//       id: json['_id'] ?? '',
//       title: json['title'] ?? '',
//       sport: json['sport'] ?? '',
//       courtOrTable: json['courtOrTable'] ?? '',
//       totalPlayersAllowed: json['totalPlayersAllowed'] ?? 0,

//       label: '',
//       // json['label'] == null ? '' : json['label'],
//       quantity: json['quantity'] ?? 0,
//       price: json['price'] == null ? 0 : json['price'].toDouble(),
//       weekendPrice:
//           json['weekendPrice'] == null ? 0 : json['weekendPrice'].toDouble(),
//       advanceAmount:
//           json['advanceAmount'] == null ? 0 : json['advanceAmount'].toDouble(),
//     );
//   }
// }

// class Slot {
//   String id;
//   String session;
//   double startTime;
//   double endTime;
//   List<PricingAndQuantity> priceAndQuantity;
//   Slot({
//     required this.id,
//     required this.session,
//     required this.startTime,
//     required this.endTime,
//     required this.priceAndQuantity,
//   });

//   static Slot jsonToSlot(Map<String, dynamic> json) {
//     List<dynamic> priceAndQuantityJson = json['priceAndQuantity'] ?? [];
//     List<PricingAndQuantity> priceAndQuantity = priceAndQuantityJson
//         .map((pqJson) => PricingAndQuantity.jsonToPriceAndQuantity(pqJson))
//         .toList();

//     return Slot(
//       id: json['_id'] ?? '',
//       session: json['session'] ?? '',
//       startTime: json['startTime'] == null ? 0 : json['startTime'].toDouble(),
//       // endTime: json['endTime'] == null ? 0 : json['endTime'].toDouble(),
//       // endTime: json['endTime'] == null
//       //     ? 0.0 // or any default value if endTime is absent in JSON
//       //     : json['endTime'] is int // Check if endTime is an integer
//       //         ? json['endTime'].toDouble() // Convert integer to double
//       //         : double.tryParse(json['endTime']) ?? 0.0,
//       endTime: json['endTime'] == null ? 0 : json['endTime'].toDouble(),

//       priceAndQuantity: priceAndQuantity,
//     );
//   }
// }

// class Address {
//   String id;
//   String streetName;
//   String landmark;
//   String city;
//   LatLng coordinates;
//   int pincode;
//   String fullAddress;
//   String area;
//   String state;

//   Address({
//     required this.id,
//     required this.streetName,
//     required this.landmark,
//     required this.fullAddress,
//     required this.area,
//     required this.city,
//     required this.coordinates,
//     required this.pincode,
//     required this.state,
//   });

//   static Address jsonToAddress(Map address) {
//     return Address(
//       city: address['city'] ?? '',
//       coordinates: LatLng(
//         address['location']['coordinates'][1],
//         address['location']['coordinates'][0],
//       ),
//       id: address['_id'],
//       landmark: address['landmark'] ?? '',
//       fullAddress: address['fullAddress'] ?? '',
//       area: address['area'] ?? '',
//       pincode: address['pincode'] != null ? int.parse(address['pincode']) : 0,
//       state: address['state'] ?? '',
//       streetName: address['streetName'] ?? '',
//     );
//   }
// }

// class SportType {
//   String id;
//   String sport;
//   String image;
//   SportType({
//     required this.id,
//     required this.sport,
//     required this.image,
//   });

//   static SportType jsonToSportType(Map sport) {
//     return SportType(
//       id: sport['_id'] ?? '',
//       sport: sport['sport'] ?? '',
//       image: sport['image'] ?? '',
//     );
//   }
// }

// class SportCategory {
//   String id;
//   String categoryName;
//   SportCategory({required this.id, required this.categoryName});
// }

// class TermsAndCondition {
//   String id;

//   String text;

//   TermsAndCondition({required this.id, required this.text});

//   static TermsAndCondition jsonToTermsAndCondition(Map termsAndCondition) {
//     return TermsAndCondition(
//       id: termsAndCondition['_id'] ?? '',
//       text: termsAndCondition['text'] ?? '',
//     );
//   }
// }

// class Equipments {
//   String id;
//   String name;
//   String price;
//   String image;
//   Equipments(
//       {required this.id,
//       required this.name,
//       required this.price,
//       required this.image});

//   static Equipments jsonToEquipments(Map equipment) {
//     return Equipments(
//       id: equipment['_id'] ?? '',
//       name: equipment['name'] ?? '',
//       image: equipment['image'] ?? '',
//       price: equipment['price'] ?? '',
//     );
//   }
// }

// class VideoData {
//   final String url;
//   final String thumbnail;
//   VideoData({
//     required this.url,
//     required this.thumbnail,
//   });

//   static VideoData jsonToVideo(Map video) {
//     return VideoData(
//       url: video['url'] ?? '',
//       thumbnail: video['thumbnail'] ?? '',
//     );
//   }
// }

// class VenueGroup {
//   final String id;
//   final String name;

//   VenueGroup({
//     required this.id,
//     required this.name,
//   });

//   static VenueGroup jsonToVenueGroup(Map group) {
//     return VenueGroup(id: group['_id'] ?? '', name: group['name'] ?? '');
//   }
// }

import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'sportModels/badminton_model.dart';
import 'sportModels/cricket_model.dart';
import 'sportModels/football_model.dart';
import 'sportModels/ps3_model.dart';
import 'sportModels/ps4_model.dart';
import 'sportModels/ps5_model.dart';
import 'sportModels/snooker_model.dart';
import 'sportModels/table_tennis_model.dart';
import 'sportModels/tennis_model.dart';
import 'sportModels/xbox1_model.dart';

class Venue {
  String id;
  String businessId;
  String user;
  String name;
  String mobileNo;
  String cancellationPolicy;
  Address address;
  List<SportType>? sportsType;
  List? days;
  List? weekends;
  List<Slot>? slots;
  bool? isNet;

  int? slotTimeDifference;
  List<String>? images;
  List? facilities;
  String? description;
  String? option;
  bool block;
  String status;
  SportCategory? sportCategory;
  DateTime createdAt;
  List? pauseDates;
  int advanceAmount;
  int advanceAmountForOneOn;
  double convenienceFeeForAdvancePay;
  double convenienceFeeForFullPay;
  Availability? availability;
  List<CancellationCharge> cancellationCharges;
  VideoData? video;
  List<Equipments>? equipments;
  //Sports Model

  //Outdoor
  final Cricket? cricket;
  final Football? football;

  //Indoor
  final Badminton? badminton;
  final Snooker? snooker;
  final Pool? pool;
  final Pickleball? pickleball;
  final TableTennis? tableTennis;
  final Tennis? tennis;

  //Esport
  final PS3? ps3;
  final PS4? ps4;
  final PS5? ps5;
  final Xbox1? xbox1;

  VenueGroup? group;
  bool isPrimaryVenue;

  Venue({
    required this.id,
    required this.businessId,
    required this.user,
    required this.name,
    required this.mobileNo,
    required this.address,
    required this.cancellationPolicy,
    required this.createdAt,
    required this.status,
    required this.advanceAmount,
    required this.advanceAmountForOneOn,
    required this.equipments,
    this.availability,
    required this.cancellationCharges,
    this.pauseDates,
    this.sportCategory,
    this.sportsType,
    this.isNet,
    this.days,
    this.weekends,
    this.slots,
    this.slotTimeDifference,
    this.convenienceFeeForAdvancePay = 0,
    this.convenienceFeeForFullPay = 0,
    this.images,
    this.option,
    this.video,
    this.facilities,
    this.description,
    this.block = false,
    required this.cricket,
    required this.football,
    required this.badminton,
    required this.snooker,
    required this.pool,
    required this.pickleball,
    required this.tableTennis,
    required this.tennis,
    required this.ps3,
    required this.ps4,
    required this.ps5,
    required this.xbox1,
    required this.group,
    required this.isPrimaryVenue,
  });
  static Venue jsonToVenue(Map<String, dynamic> venue) {
    return Venue(
      id: venue['_id'] ?? '',
      businessId: venue['businessId'] ?? '',
      user: venue['user'] ?? '',
      name: venue['name'] ?? '',
      mobileNo: venue['phone'] ?? '',
      address: Address.jsonToAddress(venue['address']),
      createdAt: DateTime.parse(venue['createdAt']).toLocal(),
      status: venue['status'],
      block: venue['block'] ?? [],
      days: venue['days'] ?? [],
      weekends: venue['weekends'] ?? [],
      description: venue['description'] ?? '',
      option: venue['option'] ?? '',
      pauseDates: venue['pauseDates'] ?? [],
      cancellationPolicy: venue['cancellationPolicy'] ?? '',
      advanceAmount:
          venue['advanceAmount'] == null ? 0 : venue['advanceAmount'].toInt(),
      advanceAmountForOneOn: venue['advanceAmountForOneOn'] == null
          ? 0
          : venue['advanceAmountForOneOn'].toInt(),
      convenienceFeeForAdvancePay: venue['convenienceFeeForAdvancePay'] == null
          ? 0
          : venue['convenienceFeeForAdvancePay'].toDouble(),
      convenienceFeeForFullPay: venue['convenienceFeeForFullPay'] == null
          ? 0
          : venue['convenienceFeeForFullPay'].toDouble(),
      cancellationCharges: venue['cancellationCharges']
          .map<CancellationCharge>(
              (charges) => CancellationCharge.jsonToCancellationCharge(charges))
          .toList(),
      equipments: venue['equipments'] == null
          ? null
          : venue['equipments']
              .map<Equipments>(
                  (equipment) => Equipments.jsonToEquipments(equipment))
              .toList(),
      availability: venue['availability'] == null
          ? null
          : Availability.jsonToAvailability(venue['availability']),
      facilities: venue['facilities'] ?? [],
      images: venue['images'] == null
          ? []
          : (venue['images'] as List).map<String>((data) {
              return data['url'];
            }).toList(),
      isNet: venue['isNet'] ?? false,
      slotTimeDifference: venue['slotTimeDifference'] ?? 0,
      slots:
          venue['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
      sportCategory: SportCategory(
        categoryName: venue['sportCategory'] == null
            ? ''
            : venue['sportCategory']['categoryName'],
        id: venue['sportCategory'] == null ? '' : venue['sportCategory']['_id'],
      ),
      sportsType: venue['sportsType'] == null
          ? []
          : venue['sportsType']
              .map<SportType>((sport) => SportType.jsonToSportType(sport))
              .toList(),
      video:
          venue['video'] == null ? null : VideoData.jsonToVideo(venue['video']),
      cricket: venue['cricket'] == null
          ? null
          : Cricket.jsonToCricket(venue['cricket']),
      football: venue['football'] == null
          ? null
          : Football.jsonToFootball(venue['football']),
      badminton: venue['badminton'] == null
          ? null
          : Badminton.jsonToBadminton(venue['badminton']),
      snooker: venue['snooker'] == null
          ? null
          : Snooker.jsonToSnooker(venue['snooker']),
      pool: venue['pool'] == null ? null : Pool.jsonToPool(venue['pool']),
      pickleball: venue['pickleball'] == null
          ? null
          : Pickleball.jsonToPickleball(venue['pickleball']),
      tableTennis: venue['tableTennis'] == null
          ? null
          : TableTennis.jsonToTableTennis(venue['tableTennis']),
      tennis:
          venue['tennis'] == null ? null : Tennis.jsonToTennis(venue['tennis']),
      ps3: venue['ps3'] == null ? null : PS3.jsonToPS3(venue['ps3']),
      ps4: venue['ps4'] == null ? null : PS4.jsonToPS4(venue['ps4']),
      ps5: venue['ps5'] == null ? null : PS5.jsonToPS5(venue['ps5']),
      xbox1: venue['xbox1'] == null ? null : Xbox1.jsonToXbox1(venue['xbox1']),
      group: venue['group'] != null && venue['group'] is Map
          ? VenueGroup.jsonToVenueGroup(venue['group'])
          : null,
      isPrimaryVenue: venue['isPrimaryVenue'] ?? false,
    );
  }
}

class Availability {
  final String duration;
  final int count;

  Availability({required this.count, required this.duration});

  static Availability jsonToAvailability(Map availability) {
    return Availability(
      duration: availability['duration'] ?? '',
      count: availability['count'] ?? 0,
    );
  }
}

class CancellationCharge {
  final int start;
  final int end;
  final int percentage;

  CancellationCharge({
    required this.start,
    required this.end,
    required this.percentage,
  });

  static CancellationCharge jsonToCancellationCharge(Map cancellationCharge) {
    return CancellationCharge(
      start: cancellationCharge['start'] ?? 0,
      end: cancellationCharge['end'] ?? 0,
      percentage: cancellationCharge['percentage'] ?? 0,
    );
  }
}

class PricingAndQuantity {
  String id;
  String title;
  String label;
  String sport;
  int quantity;
  double price;
  String courtOrTable;
  double advanceAmount;
  int totalPlayersAllowed;
  double weekendPrice;

  PricingAndQuantity({
    required this.id,
    required this.title,
    required this.label,
    required this.sport,
    required this.price,
    required this.courtOrTable,
    required this.weekendPrice,
    required this.totalPlayersAllowed,
    required this.quantity,
    required this.advanceAmount,
  });

  static PricingAndQuantity jsonToPriceAndQuantity(Map json) {
    return PricingAndQuantity(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      sport: json['sport'] ?? '',
      courtOrTable: json['courtOrTable'] ?? '',
      totalPlayersAllowed: json['totalPlayersAllowed'] ?? 0,

      label: '',
      // json['label'] == null ? '' : json['label'],
      quantity: json['quantity'] ?? 0,
      price: json['price'] == null ? 0 : json['price'].toDouble(),
      weekendPrice:
          json['weekendPrice'] == null ? 0 : json['weekendPrice'].toDouble(),
      advanceAmount:
          json['advanceAmount'] == null ? 0 : json['advanceAmount'].toDouble(),
    );
  }
}

class Slot {
  String id;
  String session;
  double startTime;
  double endTime;
  List<PricingAndQuantity> priceAndQuantity;
  Slot({
    required this.id,
    required this.session,
    required this.startTime,
    required this.endTime,
    required this.priceAndQuantity,
  });

  static Slot jsonToSlot(Map<String, dynamic> json) {
    List<dynamic> priceAndQuantityJson = json['priceAndQuantity'] ?? [];
    List<PricingAndQuantity> priceAndQuantity = priceAndQuantityJson
        .map((pqJson) => PricingAndQuantity.jsonToPriceAndQuantity(pqJson))
        .toList();

    return Slot(
      id: json['_id'] ?? '',
      session: json['session'] ?? '',
      startTime: json['startTime'] == null ? 0 : json['startTime'].toDouble(),
      // endTime: json['endTime'] == null ? 0 : json['endTime'].toDouble(),
      // endTime: json['endTime'] == null
      //     ? 0.0 // or any default value if endTime is absent in JSON
      //     : json['endTime'] is int // Check if endTime is an integer
      //         ? json['endTime'].toDouble() // Convert integer to double
      //         : double.tryParse(json['endTime']) ?? 0.0,
      endTime: json['endTime'] == null ? 0 : json['endTime'].toDouble(),

      priceAndQuantity: priceAndQuantity,
    );
  }
}

class Address {
  String id;
  String streetName;
  String landmark;
  String city;
  LatLng coordinates;
  int pincode;
  String fullAddress;
  String area;
  String state;

  Address({
    required this.id,
    required this.streetName,
    required this.landmark,
    required this.fullAddress,
    required this.area,
    required this.city,
    required this.coordinates,
    required this.pincode,
    required this.state,
  });

  static Address jsonToAddress(Map address) {
    return Address(
      city: address['city'] ?? '',
      coordinates: LatLng(
        address['location']['coordinates'][1],
        address['location']['coordinates'][0],
      ),
      id: address['_id'],
      landmark: address['landmark'] ?? '',
      fullAddress: address['fullAddress'] ?? '',
      area: address['area'] ?? '',
      pincode: address['pincode'] != null ? int.parse(address['pincode']) : 0,
      state: address['state'] ?? '',
      streetName: address['streetName'] ?? '',
    );
  }
}

class SportType {
  String id;
  String sport;
  String image;
  SportType({
    required this.id,
    required this.sport,
    required this.image,
  });

  static SportType jsonToSportType(Map sport) {
    return SportType(
      id: sport['_id'] ?? '',
      sport: sport['sport'] ?? '',
      image: sport['image'] ?? '',
    );
  }
}

class SportCategory {
  String id;
  String categoryName;
  SportCategory({required this.id, required this.categoryName});
}

class TermsAndCondition {
  String id;

  String text;

  TermsAndCondition({required this.id, required this.text});

  static TermsAndCondition jsonToTermsAndCondition(Map termsAndCondition) {
    return TermsAndCondition(
      id: termsAndCondition['_id'] ?? '',
      text: termsAndCondition['text'] ?? '',
    );
  }
}

class Equipments {
  String id;
  String name;
  String price;
  String image;
  Equipments(
      {required this.id,
      required this.name,
      required this.price,
      required this.image});

  static Equipments jsonToEquipments(Map equipment) {
    return Equipments(
      id: equipment['_id'] ?? '',
      name: equipment['name'] ?? '',
      image: equipment['image'] ?? '',
      price: equipment['price'] ?? '',
    );
  }
}

class VideoData {
  final String url;
  final String thumbnail;
  VideoData({
    required this.url,
    required this.thumbnail,
  });

  static VideoData jsonToVideo(Map video) {
    return VideoData(
      url: video['url'] ?? '',
      thumbnail: video['thumbnail'] ?? '',
    );
  }
}

class VenueGroup {
  final String id;
  final String name;

  VenueGroup({
    required this.id,
    required this.name,
  });

  static VenueGroup jsonToVenueGroup(Map group) {
    return VenueGroup(id: group['_id'] ?? '', name: group['name'] ?? '');
  }
}
