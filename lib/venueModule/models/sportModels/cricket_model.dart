import '../venue_model.dart';

class Cricket {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Cricket({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Cricket jsonToCricket(Map cricket) {
    return Cricket(
      id: cricket['_id'] ?? '',
      venue: cricket['venue'] ?? '',
      sport: cricket['sport'] ?? '',
      slots: cricket['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}
