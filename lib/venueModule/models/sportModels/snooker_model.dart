// import '../venue_model.dart';

// class Snooker {
//   final String id;
//   final String venue;
//   final String sport;
//   final List<Slot> slots;

//   Snooker({
//     required this.id,
//     required this.venue,
//     required this.sport,
//     required this.slots,
//   });

//   static Snooker jsonToSnooker(Map snooker) {
//     return Snooker(
//       id: snooker['_id'] ?? '',
//       venue: snooker['venue'] ?? '',
//       sport: snooker['sport'] ?? '',
//       slots: snooker['slots']
//           .map<Slot>((slots) => Slot.jsonToSlot(slots))
//           .toList(),
//     );
//   }
// }

import '../venue_model.dart';

class Snooker {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Snooker({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Snooker jsonToSnooker(Map snooker) {
    return Snooker(
      id: snooker['_id'] ?? '',
      venue: snooker['venue'] ?? '',
      sport: snooker['sport'] ?? '',
      slots: snooker['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}

class Pool {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Pool({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Pool jsonToPool(Map pool) {
    return Pool(
      id: pool['_id'] ?? '',
      venue: pool['venue'] ?? '',
      sport: pool['sport'] ?? '',
      slots:
          pool['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}

class Pickleball {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Pickleball({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Pickleball jsonToPickleball(Map pickleball) {
    return Pickleball(
      id: pickleball['_id'] ?? '',
      venue: pickleball['venue'] ?? '',
      sport: pickleball['sport'] ?? '',
      slots: pickleball['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}
