import '../venue_model.dart';

class Football {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Football({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Football jsonToFootball(Map football) {
    return Football(
      id: football['_id'] ?? '',
      venue: football['venue'] ?? '',
      sport: football['sport'] ?? '',
      slots: football['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}
