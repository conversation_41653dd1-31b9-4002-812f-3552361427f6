import '../venue_model.dart';

class PS4 {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  PS4({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static PS4 jsonToPS4(Map ps4) {
    return PS4(
      id: ps4['_id'] ?? '',
      venue: ps4['venue'] ?? '',
      sport: ps4['sport'] ?? '',
      slots: ps4['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}
