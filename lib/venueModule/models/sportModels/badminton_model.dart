import '../venue_model.dart';

class Badminton {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Badminton({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Badminton jsonToBadminton(Map badminton) {
    return Badminton(
      id: badminton['_id'] ?? '',
      venue: badminton['venue'] ?? '',
      sport: badminton['sport'] ?? '',
      slots: badminton['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}
