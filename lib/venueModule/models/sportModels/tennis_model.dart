import '../venue_model.dart';

class Tennis {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Tennis({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Tennis jsonToTennis(Map tennis) {
    return Tennis(
      id: tennis['_id'] ?? '',
      venue: tennis['venue'] ?? '',
      sport: tennis['sport'] ?? '',
      slots:
          tennis['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}
