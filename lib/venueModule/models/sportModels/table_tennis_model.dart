import '../venue_model.dart';

class TableTennis {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  TableTennis({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static TableTennis jsonToTableTennis(Map tableTennis) {
    return TableTennis(
      id: tableTennis['_id'] ?? '',
      venue: tableTennis['venue'] ?? '',
      sport: tableTennis['sport'] ?? '',
      slots: tableTennis['slots']
          .map<Slot>((slots) => Slot.jsonToSlot(slots))
          .toList(),
    );
  }
}
