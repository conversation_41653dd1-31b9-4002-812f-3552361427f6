import '../venue_model.dart';

class Xbox1 {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  Xbox1({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static Xbox1 jsonToXbox1(Map xbox1) {
    return Xbox1(
      id: xbox1['_id'] ?? '',
      venue: xbox1['venue'] ?? '',
      sport: xbox1['sport'] ?? '',
      slots:
          xbox1['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}
