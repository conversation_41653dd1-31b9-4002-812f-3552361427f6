import '../venue_model.dart';

class PS5 {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  PS5({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static PS5 jsonToPS5(Map ps5) {
    return PS5(
      id: ps5['_id'] ?? '',
      venue: ps5['venue'] ?? '',
      sport: ps5['sport'] ?? '',
      slots: ps5['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}
