import '../venue_model.dart';

class PS3 {
  final String id;
  final String venue;
  final String sport;
  final List<Slot> slots;

  PS3({
    required this.id,
    required this.venue,
    required this.sport,
    required this.slots,
  });

  static PS3 jsonToPS3(Map ps3) {
    return PS3(
      id: ps3['_id'] ?? '',
      venue: ps3['venue'] ?? '',
      sport: ps3['sport'] ?? '',
      slots: ps3['slots'].map<Slot>((slots) => Slot.jsonToSlot(slots)).toList(),
    );
  }
}
