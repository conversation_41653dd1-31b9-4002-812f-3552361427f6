import 'package:flutter/material.dart';

import 'main.dart';

Future<dynamic> push(Widget screen) => navigatorKey.currentState!
    .push(MaterialPageRoute(builder: (context) => screen));

Future<dynamic> pushReplacement(Widget screen) => navigatorKey.currentState!
    .pushReplacement(MaterialPageRoute(builder: (context) => screen));

Future<dynamic> pushAndRemoveUntil(Widget screen) =>
    navigatorKey.currentState!.pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => screen),
        (Route<dynamic> route) => false);

pop([data]) => navigatorKey.currentState!.pop(data);
