import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:bys_business/couponModule/screens/coupon_detail_screen.dart';
import 'package:bys_business/navigators.dart';
import 'package:flutter/material.dart';
import '../../commonWidgets/custom_container.dart';
import '../../common_function.dart';
import '../../new_colors.dart';
import '../model/coupon_model.dart';

class CouponWidget extends StatelessWidget {
  final double deviceHeight;
  final double deviceWidth;
  final double textScale;
  final CouponModel coupon;
  const CouponWidget({
    Key? key,
    required this.deviceHeight,
    required this.deviceWidth,
    required this.textScale,
    required this.coupon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        push(CouponDetailScreen(couponId: coupon.id));
      },
      child: Container(
        margin: EdgeInsets.only(bottom: deviceWidth * .07),
        child: CustomContainer(
          vPadding: 0,
          hPadding: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: deviceWidth * .02),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8)),
                  color:
                      !coupon.active || coupon.endDate.isBefore(DateTime.now())
                          ? getLightRedColor1(context)
                          : getLightGreenColor1(context),
                ),
                width: deviceWidth,
                alignment: Alignment.center,
                child: TextWidget(
                  title: coupon.endDate.isBefore(DateTime.now())
                      ? 'Expired'
                      : coupon.active
                          ? 'Active'
                          : 'Inactive',
                  fontWeight: FontWeight.w600,
                  color:
                      !coupon.active || coupon.endDate.isBefore(DateTime.now())
                          ? getRedColor2(context)
                          : getThemeColor(),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: deviceWidth * .05,
                  vertical: deviceWidth * 0.04,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      title: coupon.code,
                      fontWeight: FontWeight.w600,
                      color: getGreyColor2(context),
                    ),
                    SizedBox(height: deviceWidth * .04),
                    TextWidget(
                      title: coupon.description,
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: getGreyColor2(context),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget divider(double deviceWidth, double vertical) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: vertical),
    child: Divider(
      color: Colors.black26,
    ),
  );
}
