import 'package:bys_business/employeeManagement/model/employeeManagemenrModel.dart';
import 'package:bys_business/homeModule/models/bookingModel.dart';

class CouponModel {
  String id;
  String code;
  String description;
  DateTime startDate;
  DateTime endDate;
  bool active;
  int discountPercentage;
  Venues? turf;
  bool isForNewUser;
  EmployeeMapped? employee;
  final String createdSource;
  final String bookingType;
  final DateTime createdAt;

  CouponModel({
    required this.id,
    required this.code,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.active,
    required this.discountPercentage,
    required this.turf,
    required this.isForNewUser,
    this.employee,
    required this.createdSource,
    required this.bookingType,
    required this.createdAt,
  });
}
