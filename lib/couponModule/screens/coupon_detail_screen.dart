import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/custom_dialog.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import '../../couponModule/model/coupon_model.dart';
import '../../couponModule/screens/add_edit_coupon_screen.dart';
import '../../customerModule/provider/customer_provider.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_button_2.dart';
import '../../commonWidgets/oldtextWidget.dart';
import '../../commonWidgets/text_widget.dart';
import '../../common_function.dart';
import '../../new_colors.dart';

class CouponDetailScreen extends StatefulWidget {
  final String couponId;

  const CouponDetailScreen({Key? key, required this.couponId})
      : super(key: key);

  @override
  _CouponDetailScreenState createState() => _CouponDetailScreenState();
}

class _CouponDetailScreenState extends State<CouponDetailScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  late UserModal user;
  CouponModel? coupon;

  bool isLoading = false;
  bool isUpdating = false;

  Widget buildHeader({required String title}) {
    return Container(
      margin: EdgeInsets.only(bottom: deviceWidth * 0.015),
      child: Text(
        title,
        style: Theme.of(context).textTheme.headlineSmall!.copyWith(
              fontSize: textScaleFactor * 13,
              color: Colors.grey.shade500,
            ),
      ),
    );
  }

  activeOrDeleteCoupon(bool delete) async {
    try {
      setState(() {
        isUpdating = true;
      });

      final data = await Provider.of<CustomerProvider>(context, listen: false)
          .activeOrDeleteCoupon(
        accessToken: user.accessToken,
        couponId: coupon!.id,
        action: delete ? 'Delete' : 'Active',
        active: !coupon!.active,
      );

      if (data) {
        if (delete) {
          showSnackbar('Coupon Delete Successfully', color: getThemeColor());
          pop();
        } else {
          showSnackbar(
            'Coupon ${coupon!.active ? 'Activated' : 'Inactivated'} Successfully',
            color: getThemeColor(),
          );
        }
      } else {
        showSnackbar('Unable to update status');
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      if (mounted) setState(() => isUpdating = false);
    }
  }

  showConfirmationDialog({bool delete = false}) {
    if (isUpdating) return;

    return showDialog(
      context: context,
      builder: ((context) => CustomDialog(
            title:
                'Are you sure you want to ${delete ? 'delete' : coupon!.active ? 'inactive' : 'active'} this coupon?',
            noText: delete
                ? 'Yes, Delete'
                : 'Yes, ${coupon!.active ? 'Inactive' : 'Active'}',
            yesText: 'Cancel',
            noFunction: () {
              pop();
              activeOrDeleteCoupon(delete);
            },
            yesFunction: () => pop(),
          )),
    );
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    coupon = Provider.of<CustomerProvider>(context, listen: false)
        .findCouponById(widget.couponId);
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    coupon =
        Provider.of<CustomerProvider>(context).findCouponById(widget.couponId);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return isLoading || coupon == null
        ? CircularLoader(android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
        : Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: deviceWidth * 0.05),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.04),
                      child: Row(
                        children: [
                          CustomButton2(dW: deviceWidth),
                          SizedBox(width: deviceWidth * 0.05),
                          TextWidget(
                            title: 'Coupon Details',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: getThemeColor(),
                          ),
                        ],
                      ),
                    ),
                    isUpdating
                        ? Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: deviceWidth * 0.04),
                            child: circularForButton(deviceWidth,
                                color: getThemeColor()),
                          )
                        : PopupMenuButton(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(7)),
                            icon: Icon(Icons.more_vert, color: Colors.black),
                            itemBuilder: (BuildContext bc) => [
                              popupMenuItem(
                                position: 1,
                                title: 'Edit',
                                icon: 'edit',
                                dW: deviceWidth,
                              ),
                              popupMenuItem(
                                position: 2,
                                title: coupon!.active ? "Inactive" : "Active",
                                icon: 'active',
                                dW: deviceWidth,
                              ),
                              popupMenuItem(
                                position: 3,
                                title: 'Delete',
                                icon: 'delete1',
                                dW: deviceWidth,
                              ),
                            ],
                            onSelected: (value) {
                              if (value == 1) {
                                push(AddEditCouponScreen(
                                    coupon: coupon, user: user));
                              } else if (value == 2) {
                                showConfirmationDialog();
                              } else if (value == 3) {
                                showConfirmationDialog(delete: true);
                              }
                            },
                          ),
                  ],
                ),
                SizedBox(height: deviceWidth * 0.05),
                Container(
                  padding: EdgeInsets.symmetric(vertical: deviceWidth * .03),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8)),
                    color: !coupon!.active ||
                            coupon!.endDate.isBefore(DateTime.now())
                        ? getLightRedColor1(context)
                        : getLightGreenColor1(context),
                  ),
                  width: deviceWidth,
                  alignment: Alignment.center,
                  child: TextWidget(
                    title: coupon!.endDate.isBefore(DateTime.now())
                        ? 'Expired'
                        : coupon!.active
                            ? 'Active'
                            : 'Inactive',
                    fontWeight: FontWeight.w600,
                    color: !coupon!.active ||
                            coupon!.endDate.isBefore(DateTime.now())
                        ? getRedColor2(context)
                        : getThemeColor(),
                  ),
                ),
                SizedBox(height: deviceWidth * 0.05),
                Expanded(
                  child: CustomContainer(
                    margin:
                        EdgeInsets.symmetric(horizontal: deviceWidth * 0.04),
                    vPadding: 0,
                    hPadding: 0,
                    child: SingleChildScrollView(
                      padding:
                          EdgeInsets.symmetric(horizontal: deviceWidth * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: deviceWidth * 0.05),
                          buildHeader(title: 'Coupon Code'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text: coupon!.code,
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          buildHeader(title: 'Discount Percentage'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text: '${coupon!.discountPercentage} %',
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          buildHeader(title: 'Description'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text: coupon!.description,
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          buildHeader(title: 'Venue'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text: coupon!.turf!.name,
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          buildHeader(title: 'Booking Type'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text: coupon!.bookingType,
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          buildHeader(title: 'Validity'),
                          OldTextWidget(
                            bottomMargin: deviceWidth * 0.05,
                            context: context,
                            text:
                                '${DateFormat('dd MMM yyyy').format(coupon!.startDate)} to ${DateFormat('dd MMM yyyy').format(coupon!.endDate)}',
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          if (coupon!.employee != null)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                buildHeader(title: 'Created By'),
                                OldTextWidget(
                                  bottomMargin: deviceWidth * 0.05,
                                  context: context,
                                  text: coupon!.createdSource,
                                  fontSize: textScaleFactor * 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                buildHeader(title: 'Employee Name'),
                                OldTextWidget(
                                  bottomMargin: deviceWidth * 0.05,
                                  context: context,
                                  text: coupon!.employee!.name,
                                  fontSize: textScaleFactor * 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ],
                            ),
                          buildHeader(title: 'Created Date Time'),
                          OldTextWidget(
                            bottomMargin: 0,
                            context: context,
                            text:
                                '${DateFormat('dd MMM yyyy hh:mm a').format(coupon!.createdAt)}',
                            fontSize: textScaleFactor * 14,
                            fontWeight: FontWeight.w600,
                          ),
                          SizedBox(height: deviceWidth * 0.05),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: deviceWidth * 0.15),
              ],
            ),
          );
  }
}
