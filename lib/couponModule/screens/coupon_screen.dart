import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/commonWidgets/circular_loader.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/raisedButton.dart';
import 'package:bys_business/couponModule/model/coupon_model.dart';
import 'package:bys_business/couponModule/screens/add_edit_coupon_screen.dart';
import 'package:bys_business/couponModule/widgets/coupon_widget.dart';
import 'package:bys_business/customerModule/provider/customer_provider.dart';
import 'package:bys_business/navigators.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../common_function.dart';

class CouponScreen extends StatefulWidget {
  final UserModal user;

  const CouponScreen({Key? key, required this.user}) : super(key: key);

  @override
  _CouponScreenState createState() => _CouponScreenState();
}

class _CouponScreenState extends State<CouponScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isLoading = false;
  List<CouponModel> listOfCoupon = [];

  fetchCoupons() async {
    try {
      setState(() {
        isLoading = true;
      });

      List<String> turfId = [];

      if (widget.user.business != null) {
        widget.user.business!.turfs.forEach((data) => turfId.add(data.id));
      }

      await Provider.of<CustomerProvider>(context, listen: false)
          .fetchCouponsForBusiness(
        accessToken: widget.user.accessToken,
        business: widget.user.businessId,
        turfId: turfId,
        employee: widget.user.business == null ? false : true,
      );

      setState(() {
        isLoading = true;
      });
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      if (this.mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    fetchCoupons();
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;
    listOfCoupon = Provider.of<CustomerProvider>(context).coupons;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: isLoading
          ? SizedBox.shrink()
          : FloatingActionButton(
              backgroundColor: getThemeColor(),
              child: Icon(Icons.add, size: 30),
              onPressed: () {
                push(AddEditCouponScreen(user: widget.user));
              },
            ),
    );
  }

  Widget screenBody() {
    return SizedBox(
        height: deviceHeight,
        width: deviceWidth,
        child: isLoading
            ? CircularLoader(
                android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
            : Padding(
              padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
              child: Column(
                  children: [
                    // SizedBox(height: deviceWidth * 0.05),
                    NewAppBar(dW: deviceWidth, title: 'Coupons'),
                    listOfCoupon.isEmpty
                        ? EmptyListWidget(
                            text: 'No Coupon added yet!',
                            topPadding: .5,
                          )
                        : Container(
                            child: SingleChildScrollView(
                              physics: BouncingScrollPhysics(),
                              padding: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.05),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: deviceWidth * 0.065),
                                  ...listOfCoupon.map(
                                    (coupon) => CouponWidget(
                                      deviceHeight: deviceHeight,
                                      deviceWidth: deviceWidth,
                                      textScale: textScaleFactor,
                                      coupon: coupon,
                                    ),
                                  ),
                                  SizedBox(height: deviceWidth * 0.2)
                                ],
                              ),
                            ),
                          ),
                  ],
                ),
            ));
  }
}
