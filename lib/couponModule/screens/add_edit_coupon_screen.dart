import 'dart:io';

import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/commonWidgets/bottom_aligned_widget.dart';
import 'package:bys_business/commonWidgets/custom_button.dart';
import 'package:bys_business/commonWidgets/custom_text_field.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/raisedButton.dart';
import 'package:bys_business/common_function.dart';
import 'package:bys_business/couponModule/model/coupon_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_container.dart';
import '../../customerModule/provider/customer_provider.dart';
import '../../homeModule/providers/homeProvider.dart';

class AddEditCouponScreen extends StatefulWidget {
  final UserModal user;
  final CouponModel? coupon;

  const AddEditCouponScreen({Key? key, required this.user, this.coupon})
      : super(key: key);

  @override
  _AddEditCouponScreenState createState() => _AddEditCouponScreenState();
}

class _AddEditCouponScreenState extends State<AddEditCouponScreen> {
  double deviceHeight = 0;
  double deviceWidth = 0;
  double textScaleFactor = 0;

  bool isLoading = false;
  bool create = false;
  Widget? dialog;

  List venues = [];
  String turf = '0';
  String bookingType = '';
  DateTime? startDate;
  DateTime? endDate;

  TextEditingController codeController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController discountPercentageController = TextEditingController();

  GlobalKey<FormState> _formKey = GlobalKey();

  bool isForNewUser = false;

  fetchVenues() async {
    try {
      setState(() {
        isLoading = true;
      });

      final data = await Provider.of<HomeProvider>(context, listen: false)
          .fetchTurfAdmin(
        accessToken: widget.user.accessToken,
      );
      venues = List.from(data);
      venues.insert(0, {'_id': '0', 'name': 'Select Venue'});
    } catch (e) {
      print(e);
      callToastMessage('Something went wrong');
    } finally {
      if (this.mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Widget buildHeader({required String title}) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
            fontSize: textScaleFactor * 14,
            color: const Color(0xff434343),
          ),
    );
  }

  Widget buidlTextField({
    required String hintText,
    required String errorText,
    required TextEditingController controller,
    required List<TextInputFormatter> formatter,
    TextInputType inputType = TextInputType.text,
    TextInputAction inputAction = TextInputAction.next,
    TextAlign textAlign = TextAlign.left,
    int? maxLength,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: deviceWidth * 0.05),
      child: TextFormField(
        controller: controller,
        inputFormatters: formatter,
        textInputAction: inputAction,
        keyboardType: inputType,
        cursorColor: Colors.black,
        maxLength: maxLength,
        maxLines: null,
        style: TextStyle(
          fontSize: textScaleFactor * 14,
          letterSpacing: .60,
          fontWeight: FontWeight.w600,
        ),
        textAlign: textAlign,
        decoration: InputDecoration(
          hintText: hintText,
          counterText: '',
          hintStyle: Theme.of(context)
              .textTheme
              .headlineSmall!
              .copyWith(fontSize: textScaleFactor * 12.5),
          contentPadding: const EdgeInsets.all(0),
          fillColor: Colors.grey.shade100,
          border: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xffDBDBDB),
            ),
          ),
          focusedBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xffDBDBDB),
            ),
          ),
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xffDBDBDB),
            ),
          ),
          errorBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xffDBDBDB),
            ),
          ),
          filled: false,
          focusedErrorBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xffDBDBDB),
            ),
          ),
        ),
        validator: (value) {
          if (value!.trim().isEmpty) {
            return errorText;
          }
          return null;
        },
      ),
    );
  }

  initialDateRange() {
    if (startDate == null && endDate == null) {
      return null;
    } else {
      return DateTimeRange(start: startDate!, end: endDate!);
    }
  }

  addEditCoupon() async {
    try {
      if (create) return;
      hideKeyBoard(context);

      setState(() {
        create = true;
      });

      var result = Provider.of<CustomerProvider>(context, listen: false)
          .findCouponByCode(
        code: codeController.text.trim(),
        id: widget.coupon == null ? '' : widget.coupon!.id,
      );

      if (!result) {
        return showSnackbar('Coupon with this code already exists');
      } else {
        final data = await Provider.of<CustomerProvider>(context, listen: false)
            .createAndUpdateCoupon(
          accessToken: widget.user.accessToken,
          business: widget.user.businessId,
          createdSource: widget.user.business == null ? 'BUSINESS' : 'EMPLOYEE',
          code: codeController.text.trim().toUpperCase(),
          description: descriptionController.text.trim(),
          discountPercentage: discountPercentageController.text.trim(),
          bookingType: bookingType,
          startDate: startDate.toString(),
          endDate: endDate.toString(),
          turf: turf,
          employee: widget.user.business == null
              ? null
              : widget.user.business!.employee.id,
          couponId: widget.coupon == null ? '' : widget.coupon!.id,
        );

        if (data == null) {
          showSnackbar('Something went wrong');
        } else if (!data['success']) {
          if (data['message'] == 'DuplicateCode') {
            showSnackbar('Coupon Code Already Exists');
          }
        } else {
          Navigator.of(context).pop(true);
          Navigator.of(context).pop(true);
          Navigator.of(context).pop(true);
          showSnackbar(
            widget.coupon == null
                ? 'Coupon Created Successfully'
                : 'Coupon Updated Successfully',
            color: getThemeColor(),
          );
        }
      }
    } catch (e) {
      print(e);
      showSnackbar('Something went wrong');
    } finally {
      setState(() {
        create = false;
      });
    }
  }

  bool isButtonEnable() {
    if (codeController.text.trim().isEmpty ||
        descriptionController.text.trim().isEmpty ||
        discountPercentageController.text.isEmpty ||
        startDate == null ||
        endDate == null ||
        turf == '0' ||
        bookingType == '') {
      return false;
    } else {
      return true;
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.coupon != null && widget.coupon!.bookingType.isNotEmpty) {
      bookingType = widget.coupon!.bookingType;
    } else {
      bookingType = 'Single Booking';
    }
    if (widget.user.business != null) {
      venues = [];
      venues.insert(0, {'_id': '0', 'name': 'Select Venue'});
      widget.user.business!.turfs.forEach((data) {
        venues.add({'_id': data.id, 'name': data.name});
      });
    } else {
      fetchVenues();
    }

    if (widget.coupon != null) {
      codeController.text = widget.coupon!.code;
      descriptionController.text = widget.coupon!.description;
      discountPercentageController.text =
          widget.coupon!.discountPercentage.toString();
      startDate = widget.coupon!.startDate;
      endDate = widget.coupon!.endDate;
      turf = widget.coupon!.turf!.id;
    }

    dialog = DateRangePickerDialog(
      firstDate: DateTime(2018),
      lastDate: DateTime(2200),
      currentDate: DateTime.now(),
      initialDateRange: initialDateRange(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
  }

  @override
  Widget build(BuildContext context) {
    deviceHeight = MediaQuery.of(context).size.height;
    deviceWidth = MediaQuery.of(context).size.width;
    textScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(deviceHeight)
          ? screenBody()
          : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return GestureDetector(
      onTap: () => hideKeyBoard(context),
      child: SizedBox(
        height: deviceHeight,
        width: deviceWidth,
        child: isLoading
            ? CircularLoader(
                android: deviceWidth * 0.08, iOS: deviceWidth * 0.035)
            : Padding(padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: deviceHeight * 0.02) : EdgeInsets.only(top: deviceHeight*0.02 , bottom:deviceHeight * 0.02),
                child: Column(
                  children: [
                    SizedBox(height: deviceWidth * 0.05),
                    NewAppBar(
                      dW: deviceWidth,
                      title: widget.coupon == null
                          ? 'Add Coupon'
                          : 'Update Coupon',
                    ),
                    Expanded(
                      child: CustomContainer(
                        radius: 8,
                        vPadding: 0,
                        hPadding: 0.01,
                        margin: EdgeInsets.symmetric(
                          horizontal: deviceWidth * 0.04,
                          vertical: deviceWidth * 0.05,
                        ),
                        child: SingleChildScrollView(
                          physics: BouncingScrollPhysics(),
                          padding: EdgeInsets.symmetric(
                              horizontal: deviceWidth * 0.03),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: deviceWidth * 0.05),
                              CustomTextFieldWithLabel(
                                label: 'Coupon Code',
                                controller: codeController,
                                hintText: 'Please enter coupon code',
                                inputFormatter: [
                                  UpperCaseTextFormatter(),
                                  FilteringTextInputFormatter.deny(
                                    ' ',
                                    replacementString: '',
                                  ),
                                ],
                                maxLength: 25,
                                onChanged: (value) => setState(() {}),
                              ),
                              SizedBox(height: deviceWidth * 0.05),
                              CustomTextFieldWithLabel(
                                label: 'Description',
                                controller: descriptionController,
                                hintText: 'Please enter description',
                                maxLength: 400,
                                onChanged: (value) => setState(() {}),
                              ),
                              SizedBox(height: deviceWidth * 0.05),
                              CustomTextFieldWithLabel(
                                label: 'Discount Percentage',
                                controller: discountPercentageController,
                                hintText: 'Please enter discount percentage',
                                inputType: TextInputType.number,
                                inputAction: TextInputAction.done,
                                maxLength: 2,
                                onChanged: (value) => setState(() {}),
                              ),
                              SizedBox(height: deviceWidth * 0.05),
                              buildHeader(title: 'Start & End Date'),
                              SizedBox(height: deviceWidth * 0.035),
                              GestureDetector(
                                onTap: () {
                                  hideKeyBoard(context);
                                  showDialog<DateTimeRange>(
                                    context: context,
                                    useSafeArea: false,
                                    builder: (BuildContext context) {
                                      return Theme(
                                        data: ThemeData.light().copyWith(
                                          colorScheme: ColorScheme.fromSwatch(
                                            primarySwatch: Colors.green,
                                            // primaryColorDark:
                                            //     Theme.of(context).primaryColor,
                                            accentColor:
                                                Theme.of(context).primaryColor,
                                          ),
                                          dialogBackgroundColor: Colors.white,
                                        ),
                                        child: dialog!,
                                      );
                                    },
                                  ).then((value) {
                                    if (value != null) {
                                      setState(() {
                                        startDate = value.start;
                                        endDate = value.end;
                                      });
                                    }
                                  });
                                },
                                child: Container(
                                  height: deviceWidth * 0.12,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: deviceWidth * 0.03,
                                    vertical: deviceHeight * 0.005,
                                  ),
                                  margin: EdgeInsets.only(
                                      bottom: deviceWidth * 0.05),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: getThemeColor()),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        startDate == null
                                            ? 'Select start & end date'
                                            : '${DateFormat('dd MMM yyyy').format(startDate!)} - ${DateFormat('dd MMM yyyy').format(endDate!)}',
                                        style: TextStyle(
                                          fontSize: textScaleFactor * 14,
                                          letterSpacing: 0.3,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        'assets/svgIcons/Calendarr.svg',
                                        color: Colors.black,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              buildHeader(title: 'Select Venue'),
                              SizedBox(height: deviceWidth * 0.035),
                              Container(
                                height: deviceWidth * 0.12,
                                padding: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.03,
                                  vertical: deviceHeight * 0.005,
                                ),
                                margin:
                                    EdgeInsets.only(bottom: deviceWidth * 0.05),
                                decoration: BoxDecoration(
                                  border: Border.all(color: getThemeColor()),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton(
                                    value: turf,
                                    isExpanded: true,
                                    iconSize: 27,
                                    icon: Icon(
                                      Icons.keyboard_arrow_down,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    iconEnabledColor: Colors.black,
                                    hint: Text(
                                      'Select Venue:',
                                      style: TextStyle(
                                          fontSize: textScaleFactor * 13.5),
                                    ),
                                    items: venues.map(
                                      (item) {
                                        return DropdownMenuItem(
                                          value: item['_id'],
                                          child: Text(
                                            item['name'],
                                            style: TextStyle(
                                              fontSize: textScaleFactor * 14,
                                              letterSpacing: 0.3,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                    onTap: () {
                                      hideKeyBoard(context);
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        turf = value.toString();
                                      });
                                    },
                                  ),
                                ),
                              ),
                              buildHeader(title: 'Select Booking Type'),
                              SizedBox(height: deviceWidth * 0.035),
                              Container(
                                height: deviceWidth * 0.12,
                                padding: EdgeInsets.symmetric(
                                  horizontal: deviceWidth * 0.03,
                                  vertical: deviceHeight * 0.005,
                                ),
                                margin:
                                    EdgeInsets.only(bottom: deviceWidth * 0.05),
                                decoration: BoxDecoration(
                                  border: Border.all(color: getThemeColor()),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton(
                                    value: bookingType,
                                    isExpanded: true,
                                    iconSize: 27,
                                    icon: Icon(
                                      Icons.keyboard_arrow_down,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    iconEnabledColor: Colors.black,
                                    hint: Text(
                                      'Select Venue:',
                                      style: TextStyle(
                                          fontSize: textScaleFactor * 14),
                                    ),
                                    items:
                                        ['Single Booking', 'Bulk Booking'].map(
                                      (item) {
                                        return DropdownMenuItem(
                                          value: item,
                                          child: Text(
                                            item,
                                            style: TextStyle(
                                              fontSize: textScaleFactor * 14,
                                              letterSpacing: 0.3,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                    onTap: () {
                                      hideKeyBoard(context);
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        bookingType = value.toString();
                                      });
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: deviceWidth * 0.05)
                            ],
                          ),
                        ),
                      ),
                    ),
                    BottomAlignedWidget(
                      dW: deviceWidth,
                      dH: deviceWidth * 0.13,
                      child: CustomButton(
                        width: deviceWidth,
                        height: deviceWidth * 0.13,
                        radius: 8,
                        buttonText: widget.coupon == null
                            ? 'Add Coupon'
                            : 'Update Coupon',
                        onPressed: isButtonEnable() ? addEditCoupon : null,
                        isLoading: create,
                        fontSize: 16,
                      ),
                    )
                  ],
                ),
              ),
      ),
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
