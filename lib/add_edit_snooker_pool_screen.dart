// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:bys_business/authModule/modals/userModel.dart';
import 'package:bys_business/authModule/providers/auth.dart';
import 'package:bys_business/venueModule/widgets/cancellationWidget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:video_compress/video_compress.dart';
import 'colors.dart';
import 'commonWidgets/asset_svg_icon.dart';
import 'commonWidgets/bottom_aligned_widget.dart';
import 'commonWidgets/checkBoxWidget.dart';
import 'commonWidgets/custom_button.dart';
import 'commonWidgets/custom_checkbox_widget.dart';
import 'commonWidgets/custom_container.dart';
import 'commonWidgets/custom_text_field.dart';
import 'commonWidgets/divider_widget.dart';
import 'commonWidgets/materialCircularLoader.dart';
import 'commonWidgets/new_appbar.dart';
import 'commonWidgets/new_check_box_widget.dart';
import 'commonWidgets/open_media_full_screen.dart';
import 'commonWidgets/text_widget.dart';
import 'common_function.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'homeModule/models/bookingModel.dart';
import 'homeModule/providers/homeProvider.dart';
import 'navigators.dart';
import 'preview_screen_for_snooker_pool.dart';
import 'venueModule/models/venue_model.dart';
import 'venueModule/newVenueFlow/widgets/step1/search_location_bottomsheet.dart';
import 'venueModule/newVenueFlow/widgets/step4/cancellation_template_bottomsheet.dart';
import 'venueModule/widgets/addCancellationBottomsheet.dart';
import 'venueModule/widgets/venueDetailsWidget/multi_select_bottom_sheet.dart';

class AddEditSnookerPoolScreen extends StatefulWidget {
  const AddEditSnookerPoolScreen({
    Key? key,
  }) : super(key: key);

  @override
  AddEditSnookerPoolScreenState createState() =>
      AddEditSnookerPoolScreenState();
}

class AddEditSnookerPoolScreenState extends State<AddEditSnookerPoolScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  //Variables
  bool isLoading = false;
  late UserModal user;
  TextEditingController venueNameController = TextEditingController();
  TextEditingController venuePhoneController = TextEditingController();
  TextEditingController venueDescriptionController = TextEditingController();
  TextEditingController landmarkController = TextEditingController();
  Address? venueAddress;
  List selectedWeekDays = [];
  List selectedWeekEnds = [];
  List<String> getDays(bool isWeekdays) {
    if (isWeekdays) {
      if (selectedWeekEnds.contains('Fri')) {
        return ['Mon', 'Tue', 'Wed', 'Thu'];
      } else {
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
      }
    } else {
      if (selectedWeekDays.contains('Fri')) {
        return ['Sat', 'Sun'];
      } else {
        return [
          'Fri',
          'Sat',
          'Sun',
        ];
      }
    }
  }

  List listOfSlots = [
    {
      'title': 'Morning',
      'isSelected': false,
      'id': 'Slot1',
      'startTime': TimeOfDay(hour: 5, minute: 00),
      'endTime': TimeOfDay(hour: 12, minute: 0),
      'priceAndQuantity': [],
    },
    {
      'title': 'Afternoon',
      'isSelected': false,
      'id': 'Slot2',
      'startTime': TimeOfDay(hour: 12, minute: 00),
      'endTime': TimeOfDay(hour: 16, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Evening',
      'isSelected': false,
      'id': 'Slot3',
      'startTime': TimeOfDay(hour: 16, minute: 00),
      'endTime': TimeOfDay(hour: 20, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Night',
      'isSelected': false,
      'id': 'Slot4',
      'startTime': TimeOfDay(hour: 20, minute: 00),
      'endTime': TimeOfDay(hour: 24, minute: 00),
      'priceAndQuantity': [],
    },
    {
      'title': 'Late Night',
      'isSelected': false,
      'id': 'Slot5',
      'startTime': TimeOfDay(hour: 00, minute: 00),
      'endTime': TimeOfDay(hour: 05, minute: 0),
      'priceAndQuantity': [],
    },
  ];

  List selectedSlots = [];
  List listOfSports = [];

  // List listOfCourtsAndTables = [
  //   {
  //     'sport': 'Snooker',
  //     'title': 'Snooker Table',
  //     'isSelected': false,
  //     'id': 'T1',
  //   },
  //   {
  //     'sport': 'Pool',
  //     'title': 'Pool Table',
  //     'isSelected': false,
  //     'id': 'T2',
  //   },
  // ];

  List listofSlotTime = [
    {
      'slot': 30,
      'isSelected': false,
      'id': 'ST1',
    },
    {
      'slot': 60,
      'isSelected': false,
      'id': 'ST2',
    },
  ];

  int selectedSlotDuration = 30;

  List<List<TextEditingController>> snookerWeekdaysSinglesControllers = [];
  List<List<TextEditingController>> snookerWeekdaysDoublesControllers = [];
  List<List<TextEditingController>> snookerWeekendsSinglesControllers = [];
  List<List<TextEditingController>> snookerWeekendsDoublesControllers = [];

  List selectedImages = [];
  List<String> selectedImagePaths = ['', ''];
  String videoPath = '';
  String videoThumbnail = '';
  List<Map<String, dynamic>> equipments = [];
  bool isCompressingVideo = false;

  TextEditingController quantityController = TextEditingController();
  TextEditingController advanceAmountSinglesController =
      TextEditingController();
  TextEditingController advanceAmountDoublesController =
      TextEditingController();
  int fieldCount = 0;
  TextEditingController numberOfPlayersController = TextEditingController();

  List listOfFacilities = [
    {
      'title': 'Washroom',
      'isSelected': false,
      'id': 'F1',
    },
    {
      'title': 'Cafe & Food court',
      'isSelected': false,
      'id': 'F2',
    },
    {
      'title': 'Power Backup',
      'isSelected': false,
      'id': 'F3',
    },
    {
      'title': 'Changing Room',
      'isSelected': false,
      'id': 'F4',
    },
    {
      'title': 'First Aid',
      'isSelected': false,
      'id': 'F5',
    },
    {
      'title': 'Parking',
      'isSelected': false,
      'id': 'F6',
    },
  ];

  List<RentalItem> rentalItems = [];
  Set<int> selectedIndex = {};
  String period = 'Year';
  int durationCount = 1;
  List cancellationCharges = [];
  TextEditingController cancellationController = TextEditingController();
  TextEditingController termsConditionController = TextEditingController();
  bool isCommissionAgreed = true;

  //Bottom Sheets
  openSearchLocationBottomSheet() {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: SearchLocationBottomSheet(
            fullAddress: venueAddress != null ? venueAddress!.fullAddress : ''),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        setVenueAddress(value);
      }
    });
  }

  selectDaysBottomSheet({
    required BuildContext context,
    required bool isWeekdays,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => MultiSelectBottomSheet(
        listOfFields: getDays(isWeekdays),
        title: 'Select ${isWeekdays ? 'Weekdays' : 'Weekends'}',
        selectedFields: isWeekdays
            ? List<String>.from(selectedWeekDays)
            : List<String>.from(selectedWeekEnds),
      ),
    ).then((value) {
      if (value != null) {
        try {
          setState(() {
            if (isWeekdays) {
              selectedWeekDays = List<String>.from(value);
            } else {
              selectedWeekEnds = List<String>.from(value);
            }
          });
        } catch (e) {
          print(e);
        }
      }
    });
  }

  imagePickerBottomSheet(int index) {
    if (index > selectedImagePaths.length - 1) {
      selectedImagePaths.add('');
      setState(() {});
    }
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              pop(false);
              return true;
            },
            child: SizedBox(
              height: dH * .2,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: dW * .05),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: dW * .02),
                    const Text(
                      'Select Photo from',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: dW * .03),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (selectedImagePaths[index] != '') ...[
                          GestureDetector(
                            onTap: () {
                              selectedImagePaths[index] = '';
                              setState(() {});
                              pop(true);
                            },
                            child: Column(
                              children: [
                                CircleAvatar(
                                  radius: dW * .08,
                                  backgroundColor: Colors.grey.shade300,
                                  child: Icon(
                                    Icons.delete,
                                    color: redColor,
                                  ),
                                ),
                                SizedBox(height: dW * .02),
                                const Text('Remove '),
                                const Text('Photo')
                              ],
                            ),
                          ),
                          SizedBox(width: dW * .05),
                        ],
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.gallery, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.image,
                                  color: Colors.purple,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Gallery')
                            ],
                          ),
                        ),
                        SizedBox(width: dW * .05),
                        GestureDetector(
                          onTap: () =>
                              pickAndSetImage(ImageSource.camera, index),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: dW * .08,
                                backgroundColor: Colors.grey.shade300,
                                child: const Icon(
                                  Icons.camera_alt_rounded,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: dW * .02),
                              const Text('Camera')
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        }).then((value) {
      if (value != null && !value && index >= 2) {
        selectedImagePaths.removeAt(index);
        setState(() {});
      }
    });
  }

  videoPickerBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          height: dH * .2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: dW * .05),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: dW * .02),
                const Text(
                  'Profile Video',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: dW * .02),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    if (videoPath != '') ...[
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            videoPath = '';
                            videoThumbnail = '';
                          });
                          pop();
                        },
                        child: Column(
                          children: [
                            CircleAvatar(
                              radius: dW * .08,
                              backgroundColor: Colors.grey.shade300,
                              child: Icon(
                                Icons.delete,
                                color: redColor,
                              ),
                            ),
                            SizedBox(height: dW * .02),
                            const Text('Remove '),
                            const Text('Video')
                          ],
                        ),
                      ),
                      SizedBox(width: dW * .05),
                    ],
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.gallery),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: dW * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.video_library,
                              color: Colors.purple,
                            ),
                          ),
                          SizedBox(height: dW * .02),
                          const Text('Gallery')
                        ],
                      ),
                    ),
                    SizedBox(width: dW * .05),
                    GestureDetector(
                      onTap: () => pickVideo(ImageSource.camera),
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: dW * .08,
                            backgroundColor: Colors.grey.shade300,
                            child: const Icon(
                              Icons.videocam,
                              color: Colors.blue,
                            ),
                          ),
                          SizedBox(height: dW * .02),
                          const Text('Camera')
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  addCancellationBottomSheet(
      {required BuildContext context, Map? data, int? index}) {
    showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      context: (context),
      builder: (context) => GestureDetector(
        child: AddCancellationBottomSheet(
          cancellationCharges: cancellationCharges,
          data: data,
        ),
        onTap: () {},
        behavior: HitTestBehavior.opaque,
      ),
    ).then((value) {
      if (value != null) {
        if (data != null) {
          editCancellation(data: value, index: index!);
        } else {
          addOrRemoveCancellation(
            data: value,
            index: cancellationCharges.isEmpty ? 0 : cancellationCharges.length,
            add: true,
          );
        }
      }
    });
  }

  openTemplateBottomSheet(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (context) => CancellationTemplateBottomSheet(),
    ).then((value) {
      if (value != null) {
        cancellationController.text = value;
      }
    });
  }

  //Widgets
  Widget buildDaysSelectionWidget({
    required BuildContext context,
    required bool isWeekdays,
    required List listOfIterate,
  }) {
    return GestureDetector(
      onTap: () =>
          selectDaysBottomSheet(context: context, isWeekdays: isWeekdays),
      child: CustomContainer(
        boxShadow: [],
        radius: 8,
        vPadding: 0.03,
        hPadding: 0,
        borderColor: getThemeColor(),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: dW * 0.7,
              child: (selectedWeekDays.isEmpty && isWeekdays) ||
                      (selectedWeekEnds.isEmpty && !isWeekdays)
                  ? Padding(
                      padding: EdgeInsets.only(left: dW * 0.03),
                      child: TextWidget(
                        title: 'Select ${isWeekdays ? 'weekdays' : 'weekends'}',
                        textAlign: TextAlign.left,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: BouncingScrollPhysics(),
                      child: Row(
                        children: [
                          SizedBox(width: dW * 0.03),
                          ...listOfIterate.map(
                            (day) => Container(
                              margin: EdgeInsets.only(right: dW * 0.02),
                              constraints: BoxConstraints(minWidth: dW * 0.13),
                              padding: EdgeInsets.symmetric(
                                vertical: dW * 0.015,
                                horizontal: dW * 0.02,
                              ),
                              decoration: BoxDecoration(
                                color: getThemeColor(),
                                borderRadius: BorderRadius.circular(50),
                                border: Border.all(color: getThemeColor()),
                              ),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: TextWidget(
                                  title: day,
                                  fontSize: 13,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: dW * 0.03),
                        ],
                      ),
                    ),
            ),
            Padding(
              padding: EdgeInsets.only(right: dW * 0.02),
              child: Icon(Icons.keyboard_arrow_down, color: getThemeColor()),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTimeSelectionWidget(
      {required String title, required String value, required Function onTap}) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            title: title,
            color: Color(0xff9798A3),
            fontWeight: FontWeight.w500,
          ),
          SizedBox(height: dW * 0.02),
          CustomContainer(
            boxShadow: [],
            width: dW * 0.4,
            hPadding: .03,
            borderColor: Color(0xffACACB4),
            radius: 7,
            bgColor: Color(0xffF8F9FD),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  title: value,
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                AssetSvgIcon(iconName: 'clock2', height: 22)
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget getAvailabilityCount({
    required String givenPeriod,
    required List<int> listOfCount,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          title: givenPeriod,
          color: Color(0xff9798A3),
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: dW * 0.02),
        Wrap(
          children: [
            ...listOfCount.map(
              (count) => GestureDetector(
                onTap: () {
                  changeDuration('Period', givenPeriod);
                  changeDuration('Duration', count);
                },
                child: Container(
                  margin: EdgeInsets.only(right: dW * 0.03, bottom: dW * 0.03),
                  width: dW * 0.27,
                  padding: EdgeInsets.symmetric(
                      vertical: dW * 0.025, horizontal: dW * 0.03),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: count == durationCount && period == givenPeriod
                        ? getThemeColor()
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(color: getThemeColor()),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: TextWidget(
                      title: '$count $givenPeriod',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: count == durationCount && period == givenPeriod
                          ? Colors.white
                          : Color(0xff5e5e5e),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
        SizedBox(height: dW * 0.02),
      ],
    );
  }

  //Functions
  setVenueAddress(Address address) {
    venueAddress = address;
    setState(() {});
  }

  selectSlotDuration(id) {
    listofSlotTime.forEach((slot) {
      if (slot['id'] == id) {
        setState(() {
          slot['isSelected'] = true;
          selectedSlotDuration = slot['slot'];
        });
      } else {
        setState(() {
          slot['isSelected'] = false;
        });
      }
    });
  }

  selectTime(bool isStartTime, String slotId, startTime) async {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onChanged: (date) {
        updateTime(date, isStartTime, slotId);
      },
      onConfirm: (date) {
        updateTime(date, isStartTime, slotId);
      },
      currentTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        startTime.hour,
        startTime.minute,
      ),
      showSecondsColumn: false,
    );
  }

  updateTime(value, isStartTime, slotId) {
    if (value != null) {
      selectedSlots.forEach((slot) {
        if (slot['id'] == slotId) {
          setState(() {
            if (isStartTime) {
              slot['startTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            } else {
              slot['endTime'] =
                  TimeOfDay(hour: value.hour, minute: value.minute);
            }
          });
        }
      });
    }
  }

  initailizeControllers() {
    snookerWeekdaysSinglesControllers = List.generate(
      selectedSlots.length,
      (_) => List.generate(2, (_) => TextEditingController()),
    );
    snookerWeekdaysDoublesControllers = List.generate(
      selectedSlots.length,
      (_) => List.generate(2, (_) => TextEditingController()),
    );
    snookerWeekendsSinglesControllers = List.generate(
      selectedSlots.length,
      (_) => List.generate(2, (_) => TextEditingController()),
    );
    snookerWeekendsDoublesControllers = List.generate(
      selectedSlots.length,
      (_) => List.generate(2, (_) => TextEditingController()),
    );
  }

  removeSelectedOrVideoForEquipment(int index, {bool isImage = true}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      equipments[index]['image'] = '';
    }
    setState(() {});
  }

  pickAndSetImage(ImageSource source, int index) async {
    pop(true);
    XFile? pickedFile = await pickCropImage(imageSource: source);
    if (pickedFile != null) {
      var bytes = await File(pickedFile.path).readAsBytes();
      final kb = (bytes.length) / 1024;
      final size = kb / 1024;

      if (size > 5) {
        return showSnackbar('Image size should be less than 5MB');
      }
      final jpegData =
          await convertToJpeg(File(pickedFile.path), pickedFile.name);

      selectedImagePaths[index] = jpegData.path;

      setState(() {});
    }
  }

  removeSelectedOrVideo(int index, {bool isImage = true}) {
    if (!isImage) {
      videoPath = '';
      videoThumbnail = '';
    } else {
      if (index < 2) {
        selectedImagePaths[index] = '';
      } else {
        selectedImagePaths.removeAt(index);
      }
    }
    setState(() {});
  }

  pickVideo(ImageSource source) async {
    try {
      ImagePicker picker = ImagePicker();
      XFile? pickedFile = await picker.pickVideo(
        source: source,
        maxDuration: const Duration(seconds: 30),
      );

      if (pickedFile != null) {
        setVideoData(pickedFile.path);
      }
      pop();
    } catch (e) {
      print(e);
    }
  }

  setVideoData(String path) async {
    try {
      setState(() => isCompressingVideo = true);
      final videoInfo = FlutterVideoInfo();

      File selectedFile = File(path);

      String videoFilePath = selectedFile.path;
      var info = await videoInfo.getVideoInfo(videoFilePath);

      MediaInfo? compressedVideo = await VideoCompress.compressVideo(path);

      if (info != null) {
        double size = (info.filesize! / (1024 * 1024));

        if (size > 10) {
          showSnackbar('File Size should be less than 10MB');
          return;
        }

        if (info.duration == null ||
            info.width == null ||
            info.height == null) {
          return showSnackbar('Cannot upload this video');
        }
        var seconds = info.duration! / 1000;
        var maxDuration = 30;
        if (seconds > maxDuration) {
          return showSnackbar('Video must be less than or equal to 30 seconds');
        } else {
          var mediaPath = Uri.decodeComponent(selectedFile.path);
          File thumbnailData = await VideoCompress.getFileThumbnail(mediaPath,
              quality: 100, // default(100)
              position: -1 // default(-1)
              );
          videoThumbnail = thumbnailData.path;
          if (videoThumbnail == '') {
            showSnackbar('Cannot upload this video');
          }

          videoPath = compressedVideo?.path ?? mediaPath;

          if (compressedVideo != null) {
            videoPath = compressedVideo.path ?? mediaPath;
          } else {
            videoPath = mediaPath;
          }
          setState(() {});
        }
      } else {
        return showSnackbar('Cannot upload this video');
      }
    } catch (e) {
      print(e);
    } finally {
      setState(() => isCompressingVideo = false);
    }
  }

  initailizeQuantityControllers() {
    quantityController.addListener(() {
      int quantity = int.tryParse(quantityController.text) ?? 0;
      if (quantity > 0 && quantity <= 5) {
        setState(() {
          fieldCount = quantity;
        });
      } else {
        setState(() {
          fieldCount = 0;
        });
      }
    });
  }

  changeDuration(String duration, value) {
    if (duration == 'Period') {
      period = value;
      if (value == 'Days') {
        durationCount = 7;
      } else {
        durationCount = 1;
      }
    } else if (duration == 'Count' || duration == 'Duration') {
      durationCount = value;
    }
    setState(() {});
  }

  addOrRemoveCancellation({
    required Map data,
    required int index,
    required bool add,
  }) {
    if (add) {
      cancellationCharges.add(data);
    } else {
      cancellationCharges.removeAt(index);
    }
    setState(() {});
  }

  editCancellation({required Map data, required int index}) {
    cancellationCharges[index]['start'] = data['start'];
    cancellationCharges[index]['end'] = data['end'];
    cancellationCharges[index]['percentage'] = data['percentage'];
    setState(() {});
  }

  textFormBorder(context) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: getThemeColor()),
      borderRadius: BorderRadius.circular(4),
    );
  }


  //Api's Functions
  fetchSports() async {
    setState(() {
      isLoading = true;
    });

    listOfSports = Provider.of<Auth>(context, listen: false).listOfSports;
    print(listOfSports);
    // listofSportCategory =
    //     Provider.of<Auth>(context, listen: false).listOfSportCategory;
    // print(listofSportCategory);

    setState(() {
      isLoading = false;
    });
  }

  fetchRentalItems() async {
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .fetchRentalItemBySelectedSport(
      accessToken: user.accessToken,
      body: {
        'sport': ['62bd7a9709538c27c6b86bae'],
      },
    );

    if (response['success']) {}
  }

  //Init Function
  myInit() {
    fetchSports();
    initailizeQuantityControllers();
    fetchRentalItems();
    if (termsConditionController.text.isEmpty) {
      termsConditionController.text =
          '1. By accessing and using the turf, users agree to abide by these terms and any additional rules or guidelines posted on-site \n2. The turf is intended for sports activities, events\n3. Users are responsible for their safety and the safety of others while on the turf.\n4. Proper equipment and attire must be worn as per the designated sport.\n5. The turf has specific operating hours. Users must adhere to these hours unless prior arrangements have been made.\n6. The turf owner/operator is not liable for any injuries, accidents, or damages that may occur on the premises.\n7. Only approved equipment is allowed on the turf. Users must not bring equipment that may damage the turf surface.\n8. he turf owner/operator reserves the right to terminate or restrict access to any user who violates these terms and conditions.';
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    myInit();
  }

  @override
  void dispose() {
    super.dispose();
    venueNameController.dispose();
    venuePhoneController.dispose();
    venueDescriptionController.dispose();
    landmarkController.dispose();
    for (var controllers in snookerWeekdaysSinglesControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    for (var controllers in snookerWeekdaysDoublesControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    for (var controllers in snookerWeekendsSinglesControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    for (var controllers in snookerWeekendsDoublesControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    quantityController.dispose();
    advanceAmountSinglesController.dispose();
    advanceAmountDoublesController.dispose();
    cancellationController.dispose();
    termsConditionController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    rentalItems = Provider.of<HomeProvider>(context, listen: false).rentalItems;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Padding(
        padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: dW * 0.05),
              child: NewAppBar(dW: dW, title: ''),
            ),
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextWidget(
                      title: 'Lets give venue title & small description',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title: 'Now give your venue title and small description',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: CustomTextFieldWithLabel(
                        label: 'Name',
                        controller: venueNameController,
                        hintText: 'Enter venue name',
                        borderRadius: 5,
                        labelFS: 14,
                        labelColor: Color(0xff9798A3),
                        labelFW: FontWeight.w500,
                        hintColor: Colors.black,
                        borderColor: Color(0xffACACB4),
                        fillColor: Color(0xffF8F9FD),
                        onChanged: (value) => setState(() {}),
                        inputAction: TextInputAction.next,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: CustomTextFieldWithLabel(
                        label: 'Phone',
                        controller: venuePhoneController,
                        hintText: 'Enter venue phone no.',
                        borderRadius: 5,
                        labelFS: 14,
                        maxLength: 10,
                        labelColor: Color(0xff9798A3),
                        labelFW: FontWeight.w500,
                        hintColor: Colors.black,
                        borderColor: Color(0xffACACB4),
                        fillColor: Color(0xffF8F9FD),
                        onChanged: (value) => setState(() {}),
                        inputFormatter: [FilteringTextInputFormatter.digitsOnly],
                        inputType: TextInputType.number,
                        inputAction: TextInputAction.next,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: CustomTextFieldWithLabel(
                        label: 'Description',
                        controller: venueDescriptionController,
                        hintText: 'Enter description here ',
                        borderRadius: 5,
                        labelFS: 14,
                        labelColor: Color(0xff9798A3),
                        labelFW: FontWeight.w500,
                        hintColor: Colors.black,
                        borderColor: Color(0xffACACB4),
                        fillColor: Color(0xffF8F9FD),
                        counterText:
                            '${venueDescriptionController.text.trim().length}/300',
                        maxLines: 6,
                        maxLength: 300,
                        textFS: 14,
                        textCapitalization: TextCapitalization.sentences,
                        inputType: TextInputType.streetAddress,
                        inputAction: TextInputAction.newline,
                        onChanged: (value) => setState(() {}),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Where is your venue located?',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'Specify your venue\'s location with a map pinpoint and nearby landmark details.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: Row(
                        children: [
                          TextWidget(
                            title: 'Venue Address',
                            color: Color(0xff9798A3),
                            fontWeight: FontWeight.w500,
                          ),
                          TextWidget(
                            title: '*',
                            color: redColor,
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: openSearchLocationBottomSheet,
                      child: CustomContainer(
                        margin: EdgeInsets.only(top: dW * 0.025),
                        width: dW,
                        boxShadow: [],
                        borderColor: Color(0xffACACB4),
                        bgColor: Color(0xffF8F9FD),
                        radius: 5,
                        hPadding: .03,
                        vPadding: .03,
                        child: TextWidget(
                          title: venueAddress == null
                              ? 'Select Address'
                              : venueAddress!.fullAddress.trim().isEmpty
                                  ? '${venueAddress!.area.isEmpty ? venueAddress!.streetName : venueAddress!.area}, ${venueAddress!.landmark}, ${venueAddress!.city}, ${venueAddress!.state}, ${venueAddress!.pincode}.'
                                  : '${venueAddress!.fullAddress}.',
                          fontWeight: venueAddress == null
                              ? FontWeight.normal
                              : FontWeight.w600,
                          fontSize: 15,
                          color: blackColor3,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: CustomTextFieldWithLabel(
                        label: 'Landmark',
                        controller: landmarkController,
                        hintText: 'Enter landmark here',
                        borderRadius: 5,
                        labelFS: 14,
                        labelColor: Color(0xff9798A3),
                        labelFW: FontWeight.w500,
                        hintColor: Colors.black,
                        borderColor: Color(0xffACACB4),
                        fillColor: Color(0xffF8F9FD),
                        onChanged: (value) => setState(() {}),
                        inputAction: TextInputAction.next,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'Let’s describe weekdays and weekends for you venue',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'Select the venue weekends and weekdays, whether it\'s outdoor, indoor or an esports arena.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1, bottom: dW * 0.03),
                      child: TextWidget(
                        title: 'Weekdays',
                        fontWeight: FontWeight.w500,
                        color: Color(0xff9798A3),
                      ),
                    ),
                    buildDaysSelectionWidget(
                      context: context,
                      isWeekdays: true,
                      listOfIterate: selectedWeekDays,
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(top: dW * 0.075, bottom: dW * 0.03),
                      child: TextWidget(
                        title: 'Weekend Days',
                        fontWeight: FontWeight.w500,
                        color: Color(0xff9798A3),
                      ),
                    ),
                    buildDaysSelectionWidget(
                      context: context,
                      isWeekdays: false,
                      listOfIterate: selectedWeekEnds,
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Now select time slots',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'Select the time slots in which your venues are open for the users',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: Wrap(
                        children: [
                          ...listOfSlots.map(
                            (slot) => GestureDetector(
                              onTap: () {
                                setState(() {
                                  slot['isSelected'] = !slot['isSelected'];
                                  if (slot['isSelected']) {
                                    selectedSlots.add(slot);
                                    print(selectedSlots);
                                  } else {
                                    selectedSlots.remove(slot);
                                  }
                                  initailizeControllers();
                                });
                              },
                              child: IntrinsicWidth(
                                child: Container(
                                  margin: EdgeInsets.only(
                                      right: dW * 0.03, bottom: dW * 0.03),
                                  padding: EdgeInsets.symmetric(
                                      vertical: dW * 0.025,
                                      horizontal: dW * 0.03),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: slot['isSelected']
                                        ? getThemeColor()
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(color: getThemeColor()),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      TextWidget(
                                        title: slot['title'],
                                        fontSize: slot['title'] == 'Badminton'
                                            ? 16
                                            : 18,
                                        fontWeight: FontWeight.w500,
                                        color: !slot['isSelected']
                                            ? Colors.black
                                            : Colors.white,
                                      ),
                                      SizedBox(width: dW * .03),
                                      slot['title'] == 'Late Night'
                                          ? Image.asset(
                                              'assets/images/late_night.png',
                                              height: dW * 0.06,
                                            )
                                          : Image.asset(
                                              'assets/images/${slot['title'].toLowerCase()}.png',
                                              height: dW * 0.06,
                                            ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: Text(
                        'Specify the game offerings at your venue',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...listOfSports
                              .where((sport) => sport['title'] == 'Snooker')
                              .toList()
                              .asMap()
                              .entries
                              .map((entry) {
                            final sport = entry.value;
                            bool isSingle = sport['single'] ?? false;
                            bool isDouble = sport['double'] ?? false;
        
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    if (sport['image'] != '')
                                      sport['image'].contains('https')
                                          ? Container(
                                              padding: EdgeInsets.all(dW * 0.01),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: getThemeColor(),
                                                  width: 1,
                                                ),
                                              ),
                                              child: CachedNetworkImage(
                                                imageUrl: sport['image'],
                                                height: 16,
                                                fit: BoxFit.cover,
                                              ),
                                            )
                                          : Container(
                                              padding: EdgeInsets.all(dW * 0.01),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: getThemeColor(),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Image.asset(sport['image'],
                                                  height: 30),
                                            ),
                                    SizedBox(width: dW * 0.03),
                                    Text(
                                      sport['title'],
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xff3E3E3E),
                                      ),
                                    ),
                                  ],
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      sport['single'] = !isSingle;
                                    });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        bottom: dW * 0.03, top: dW * 0.03),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04,
                                      vertical: dW * 0.06,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        width: 1,
                                        color:
                                            isSingle ? Colors.green : Colors.grey,
                                      ),
                                    ),
                                    width: dW,
                                    child: Row(
                                      children: [
                                        SizedBox(width: dW * 0.04),
                                        Expanded(
                                          child: CustomCheckbox(
                                            title: 'Singles',
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                            textColor: isSingle
                                                ? getThemeColor()
                                                : Colors.black,
                                            onChanged: () {
                                              setState(() {
                                                sport['single'] = !isSingle;
                                              });
                                            },
                                            value: isSingle,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      sport['double'] = !isDouble;
                                    });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(bottom: dW * 0.06),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04,
                                      vertical: dW * 0.06,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        width: 1,
                                        color:
                                            isDouble ? Colors.green : Colors.grey,
                                      ),
                                    ),
                                    width: dW,
                                    child: Row(
                                      children: [
                                        SizedBox(width: dW * 0.04),
                                        Expanded(
                                          child: CustomCheckbox(
                                            title: 'Doubles',
                                            fontWeight: FontWeight.w500,
                                            fontSize: 16,
                                            textColor: isDouble
                                                ? getThemeColor()
                                                : Colors.black,
                                            onChanged: () {
                                              setState(() {
                                                sport['double'] = !isDouble;
                                              });
                                            },
                                            value: isDouble,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                    // Padding(
                    //   padding: EdgeInsets.only(top: dW * 0.05),
                    //   child: TextWidget(
                    //     title: 'Now add type tables available at your venue',
                    //     fontSize: 20,
                    //     fontWeight: FontWeight.w600,
                    //   ),
                    // ),
                    // Padding(
                    //   padding: EdgeInsets.only(top: dW * 0.1),
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       ...listOfCourtsAndTables.asMap().entries.map((entry) {
                    //         final sportData = entry.value;
                    //         return Column(
                    //           crossAxisAlignment: CrossAxisAlignment.start,
                    //           children: [
                    //             Row(
                    //               children: [
                    //                 Image.asset(
                    //                   'assets/images/${sportData['sport']}.png',
                    //                   height: 24,
                    //                 ),
                    //                 SizedBox(
                    //                   width: dW * 0.02,
                    //                 ),
                    //                 TextWidget(
                    //                   title: '${sportData['sport']}',
                    //                   fontWeight: FontWeight.w600,
                    //                   color: Color(0xff3E3E3E),
                    //                 ),
                    //               ],
                    //             ),
                    //             GestureDetector(
                    //               onTap: () {
                    //                 setState(() {
                    //                   sportData['isSelected'] =
                    //                       !sportData['isSelected'];
                    //                 });
                    //               },
                    //               child: Container(
                    //                 margin: EdgeInsets.only(
                    //                     bottom: dW * 0.04, top: dW * 0.04),
                    //                 padding: EdgeInsets.only(
                    //                   left: dW * 0.04,
                    //                   top: dW * 0.035,
                    //                   right: dW * 0.045,
                    //                   bottom: dW * 0.035,
                    //                 ),
                    //                 decoration: BoxDecoration(
                    //                   color: sportData['isSelected']
                    //                       ? getThemeColor()
                    //                       : whiteColor,
                    //                   border: Border.all(
                    //                     color: getThemeColor(),
                    //                   ),
                    //                   borderRadius: BorderRadius.circular(55),
                    //                 ),
                    //                 child: IntrinsicWidth(
                    //                   child: Row(
                    //                     children: [
                    //                       Text(
                    //                         sportData['title'],
                    //                         style: TextStyle(
                    //                           fontWeight: FontWeight.w500,
                    //                           fontSize: 20,
                    //                           color: sportData['isSelected']
                    //                               ? whiteColor
                    //                               : Color(0xff5E5E5E),
                    //                         ),
                    //                       ),
                    //                     ],
                    //                   ),
                    //                 ),
                    //               ),
                    //             ),
                    //           ],
                    //         );
                    //       }).toList(),
                    //     ],
                    //   ),
                    // ),
                    if (selectedSlots.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.05),
                        child: TextWidget(
                          title: 'Let’s set timings for your venue',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (selectedSlots.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.1),
                        child: TextWidget(
                          title: 'Time Slot Duration',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (selectedSlots.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.035),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ...listofSlotTime.map(
                              (slot) => GestureDetector(
                                onTap: () => selectSlotDuration(slot['id']),
                                child: CustomContainer(
                                  boxShadow: [],
                                  vPadding: .03,
                                  radius: 7,
                                  borderColor:
                                      slot['slot'] == selectedSlotDuration
                                          ? getThemeColor()
                                          : Color.fromRGBO(172, 172, 180, 1),
                                  bgColor: slot['slot'] == selectedSlotDuration
                                      ? getThemeColor()
                                      : Colors.transparent,
                                  width: dW * 0.38,
                                  child: TextWidget(
                                    title: '${slot['slot']} mins',
                                    color: slot['slot'] == selectedSlotDuration
                                        ? Colors.white
                                        : Color(0xff242530),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 17,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    DividerWidget(top: 20, bottom: 0, color: Color(0xffD9D9D9)),
                    ...selectedSlots
                        .asMap()
                        .map(
                          (i, slot) => MapEntry(
                            i,
                            Container(
                              padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                              decoration: i == selectedSlots.length - 1
                                  ? null
                                  : BoxDecoration(
                                      border: Border(
                                          bottom: BorderSide(
                                              color: Color(0xffD9D9D9)))),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    title: '${slot['title']} Slot',
                                    color: getThemeColor(),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  SizedBox(height: dW * 0.03),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      buildTimeSelectionWidget(
                                        title: 'Start Time',
                                        value:
                                            '${slot['startTime'].format(context) == '00:00' ? '12:00' : slot['startTime'].format(context)} ${slot['startTime'].period.index == 1 ? 'pm' : 'am'}',
                                        onTap: () => selectTime(
                                            true, slot['id'], slot['startTime']),
                                      ),
                                      SizedBox(height: dW * 0.05),
                                      buildTimeSelectionWidget(
                                        title: 'End Time',
                                        value:
                                            '${slot['endTime'].format(context)} ${slot['endTime'].period.index == 1 ? 'pm' : 'am'}',
                                        onTap: () => selectTime(
                                            false, slot['id'], slot['endTime']),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                        .values
                        .toList(),
                    if (selectedSlots.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.05),
                        child: TextWidget(
                          title: 'Now add sports pricing',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (selectedSlots.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.03),
                        child: TextWidget(
                          title:
                              'Include the taxes while adding price for $selectedSlotDuration mins, you can change this later.',
                          fontSize: 12,
                          color: Color(0xff21272A),
                        ),
                      ),
                    ...selectedSlots
                        .asMap()
                        .map((i, slot) {
                          return MapEntry(
                            i,
                            Container(
                              padding: EdgeInsets.symmetric(vertical: dW * 0.055),
                              decoration: i == selectedSlots.length - 1
                                  ? null
                                  : BoxDecoration(
                                      border: Border(
                                          bottom: BorderSide(
                                              color: Color(0xffD9D9D9)))),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Image.asset(
                                        'assets/images/${slot['title'].toLowerCase()}.png',
                                        height: 18,
                                      ),
                                      SizedBox(
                                        width: dW * 0.02,
                                      ),
                                      TextWidget(
                                        title: '${slot['title']} Slot',
                                        color: Color(0xff3E3E3E),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: dW * 0.06, bottom: dW * 0.02),
                                    padding: EdgeInsets.all(dW * 0.04),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Color(0xffD9D9D9),
                                        ),
                                        borderRadius: BorderRadius.circular(8)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            ...['Sport', 'Weekdays', 'Weekends']
                                                .map(
                                              (type) => Container(
                                                alignment: Alignment.topLeft,
                                                width: type == 'Weekends' ||
                                                        type == 'Weekdays'
                                                    ? dW * 0.21
                                                    : dW * .24,
                                                child: FittedBox(
                                                  fit: BoxFit.scaleDown,
                                                  child: TextWidget(
                                                    title: type,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsets.only(top: dW * 0.03),
                                          child: TextWidget(
                                            title: 'Snooker',
                                            color: getThemeColor(),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              margin:
                                                  EdgeInsets.only(top: dW * 0.05),
                                              padding: EdgeInsets.all(dW * 0.03),
                                              decoration: BoxDecoration(
                                                color: getThemeColor(),
                                                borderRadius:
                                                    BorderRadius.circular(7),
                                              ),
                                              child: TextWidget(
                                                title: 'Singles',
                                                color: Colors.white,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            SizedBox(width: dW * 0.05),
                                            Expanded(
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    top: dW * 0.04),
                                                child: CustomTextFieldWithLabel(
                                                  label: '',
                                                  controller:
                                                      snookerWeekdaysSinglesControllers[
                                                          i][0],
                                                  textAlign: TextAlign.left,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          vertical: dW * 0.04),
                                                  prefixIcon: Icon(
                                                    Icons.currency_rupee_sharp,
                                                    size: 16,
                                                    color: blackColor3,
                                                  ),
                                                  hintText: '',
                                                  hintFS: 12,
                                                  borderRadius: 7,
                                                  textFS: 13.5,
                                                  inputFormatter: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  enabled: true,
                                                  maxLength: 6,
                                                  inputType: TextInputType.number,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: dW * 0.05),
                                            Expanded(
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    top: dW * 0.04),
                                                child: CustomTextFieldWithLabel(
                                                  label: '',
                                                  controller:
                                                      snookerWeekendsSinglesControllers[
                                                          i][0],
                                                  textAlign: TextAlign.left,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          vertical: dW * 0.04),
                                                  prefixIcon: Icon(
                                                    Icons.currency_rupee_sharp,
                                                    size: 16,
                                                    color: blackColor3,
                                                  ),
                                                  hintText: '',
                                                  hintFS: 12,
                                                  borderRadius: 7,
                                                  textFS: 13.5,
                                                  inputFormatter: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  enabled: true,
                                                  maxLength: 6,
                                                  inputType: TextInputType.number,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              margin:
                                                  EdgeInsets.only(top: dW * 0.05),
                                              padding: EdgeInsets.all(dW * 0.03),
                                              decoration: BoxDecoration(
                                                color: getThemeColor(),
                                                borderRadius:
                                                    BorderRadius.circular(7),
                                              ),
                                              child: TextWidget(
                                                title: 'Doubles',
                                                color: Colors.white,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            SizedBox(width: dW * 0.05),
                                            Expanded(
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    top: dW * 0.04),
                                                child: CustomTextFieldWithLabel(
                                                  label: '',
                                                  controller:
                                                      snookerWeekdaysDoublesControllers[
                                                          i][0],
                                                  textAlign: TextAlign.left,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          vertical: dW * 0.04),
                                                  prefixIcon: Icon(
                                                    Icons.currency_rupee_sharp,
                                                    size: 16,
                                                    color: blackColor3,
                                                  ),
                                                  hintText: '',
                                                  hintFS: 12,
                                                  borderRadius: 7,
                                                  textFS: 13.5,
                                                  inputFormatter: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  enabled: true,
                                                  maxLength: 6,
                                                  inputType: TextInputType.number,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: dW * 0.05),
                                            Expanded(
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    top: dW * 0.04),
                                                child: CustomTextFieldWithLabel(
                                                  label: '',
                                                  controller:
                                                      snookerWeekendsDoublesControllers[
                                                          i][0],
                                                  textAlign: TextAlign.left,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          vertical: dW * 0.04),
                                                  prefixIcon: Icon(
                                                    Icons.currency_rupee_sharp,
                                                    size: 16,
                                                    color: blackColor3,
                                                  ),
                                                  hintText: '',
                                                  hintFS: 12,
                                                  borderRadius: 7,
                                                  textFS: 13.5,
                                                  inputFormatter: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  enabled: true,
                                                  maxLength: 6,
                                                  inputType: TextInputType.number,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        })
                        .values
                        .toList(),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Add some photos or video of your venue',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'You’ll need to add at least 1 photo and while adding video max duration should be 30 Sec.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: Row(
                        children: [
                          const TextWidget(
                              title: 'Upload Images',
                              fontWeight: FontWeight.w500),
                          TextWidget(title: '*', color: redColor),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.06),
                      child: Wrap(
                        spacing: 15,
                        runSpacing: 15,
                        children: [
                          ...selectedImagePaths
                              .asMap()
                              .map(
                                (index, imagePath) => MapEntry(
                                  index,
                                  GestureDetector(
                                    onTap: () => imagePath.isEmpty
                                        ? imagePickerBottomSheet(index)
                                        : null,
                                    child: SizedBox(
                                      height: dW * 0.48,
                                      width: dW * 0.38,
                                      child: imagePath.isEmpty
                                          ? DottedBorder(
                                              color: Colors.grey,
                                              dashPattern: const [10, 10],
                                              radius: const Radius.circular(8),
                                              borderType: BorderType.RRect,
                                              child: Container(
                                                width: dW,
                                                padding: EdgeInsets.symmetric(
                                                    vertical: dW * .12),
                                                decoration: BoxDecoration(
                                                  color: Colors.grey.shade100,
                                                ),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const AssetSvgIcon(
                                                        iconName: 'camera',
                                                        height: 30),
                                                    SizedBox(height: dW * 0.02),
                                                    TextWidget(
                                                      title: 'Add Photo',
                                                      fontWeight: FontWeight.w400,
                                                      color: Colors.grey.shade900,
                                                    ),
                                                    TextWidget(
                                                      title: '360 X 261 PX, 5 MB',
                                                      fontWeight: FontWeight.w400,
                                                      fontSize: 11,
                                                      color: Colors.grey.shade500,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                          : Stack(
                                              clipBehavior: Clip.none,
                                              children: [
                                                Positioned(
                                                  top: 0,
                                                  bottom: 0,
                                                  right: 0,
                                                  left: 0,
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(10),
                                                    child: imagePath
                                                            .contains('https')
                                                        ? Image.network(
                                                            imagePath,
                                                            fit: BoxFit.cover,
                                                          )
                                                        : Image.file(
                                                            File(imagePath),
                                                            fit: BoxFit.cover,
                                                          ),
                                                  ),
                                                ),
                                                Positioned(
                                                  right: -8,
                                                  top: -8,
                                                  child: GestureDetector(
                                                    onTap: () =>
                                                        removeSelectedOrVideo(
                                                            index),
                                                    child: CircleAvatar(
                                                      radius: 12,
                                                      backgroundColor:
                                                          getThemeColor(),
                                                      child: Icon(Icons.clear,
                                                          size: 18,
                                                          color: Colors.white),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                    ),
                                  ),
                                ),
                              )
                              .values
                              .toList()
                        ],
                      ),
                    ),
                    if (selectedImagePaths.length >= 2 &&
                        selectedImagePaths.length < 5 &&
                        !selectedImagePaths.contains(''))
                      GestureDetector(
                        onTap: () =>
                            imagePickerBottomSheet(selectedImagePaths.length),
                        child: Container(
                          margin: EdgeInsets.only(top: dW * 0.04),
                          width: dW,
                          height: dW * 0.12,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: getThemeColor())),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              TextWidget(
                                title: 'Add more photos',
                                fontWeight: FontWeight.w600,
                                color: getThemeColor(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.06, bottom: dW * 0.05),
                      child: const TextWidget(title: 'Upload Videos'),
                    ),
                    videoPath.isEmpty
                        ? GestureDetector(
                            onTap: isCompressingVideo
                                ? null
                                : () => videoPickerBottomSheet(),
                            child: DottedBorder(
                              color: Colors.grey,
                              dashPattern: const [10, 10],
                              radius: const Radius.circular(8),
                              borderType: BorderType.RRect,
                              child: Container(
                                width: dW,
                                padding: EdgeInsets.symmetric(vertical: dW * .14),
                                decoration:
                                    BoxDecoration(color: Colors.grey.shade100),
                                child: isCompressingVideo
                                    ? Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: dW * 0.08),
                                        child: circularForButton(dW,
                                            color: getThemeColor()),
                                      )
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const AssetSvgIcon(iconName: 'camera'),
                                          SizedBox(height: dW * 0.02),
                                          TextWidget(
                                            title: 'Add Video',
                                            fontWeight: FontWeight.w400,
                                            color: Colors.grey.shade900,
                                          ),
                                          TextWidget(
                                            title: '360 X 261 PX, 10 MB',
                                            fontWeight: FontWeight.w400,
                                            fontSize: 11,
                                            color: Colors.grey.shade500,
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                          )
                        : GestureDetector(
                            onTap: () {
                              push(OpenMediaFullScreen(
                                  type: 'Video', isLocal: true, url: videoPath));
                            },
                            child: Stack(
                              clipBehavior: Clip.none,
                              alignment: Alignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: videoThumbnail.contains('https')
                                      ? Image.network(
                                          videoThumbnail,
                                          width: dW,
                                          height: dW * 0.5,
                                          fit: BoxFit.cover,
                                        )
                                      : Image.file(
                                          File(videoThumbnail),
                                          width: dW,
                                          height: dW * 0.5,
                                          fit: BoxFit.cover,
                                        ),
                                ),
                                Icon(
                                  Icons.play_circle_outline_rounded,
                                  color: Colors.white,
                                  size: dW * 0.1,
                                ),
                                Positioned(
                                  right: -8,
                                  top: -8,
                                  child: GestureDetector(
                                    onTap: () =>
                                        removeSelectedOrVideo(0, isImage: false),
                                    child: CircleAvatar(
                                      radius: 12,
                                      backgroundColor: getThemeColor(),
                                      child: Icon(Icons.clear,
                                          size: 18, color: Colors.white),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
        
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Now lets enter quantity of courts & tables',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
        
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'Specify the number tables available at your venue.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05, bottom: dW * 0.1),
                      child: TextWidget(
                        title:
                            "*Note- You can't add labels for quantities exceeding 5",
                        fontSize: 12,
                        color: Color(0xffD84848),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: dW * 0.06, bottom: dW * 0.02),
                      padding: EdgeInsets.all(dW * 0.04),
                      decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffD9D9D9),
                          ),
                          borderRadius: BorderRadius.circular(8)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            title: 'Snooker',
                            color: getThemeColor(),
                            fontWeight: FontWeight.w500,
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.03),
                            child: Row(
                              children: [
                                TextWidget(
                                  title: 'Enter Quantity',
                                  color: Color(0xff636363),
                                  fontWeight: FontWeight.w500,
                                  fontSize: tS * 14,
                                ),
                                TextWidget(
                                  title: '*',
                                  color: Color(0xffD84848),
                                ),
                                const Spacer(),
                                Expanded(
                                  child: CustomTextFieldWithLabel(
                                    label: '',
                                    controller: quantityController,
                                    textAlign: TextAlign.center,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: dW * 0.04,
                                        horizontal: dW * 0.04),
                                    hintText: 'Quantity',
                                    hintFS: 12,
                                    borderRadius: 7,
                                    textFS: 13.5,
                                    inputFormatter: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    enabled: true,
                                    maxLength: 6,
                                    inputType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          for (int i = 0; i < fieldCount; i++)
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.03),
                              child: CustomTextFieldWithLabel(
                                label: '',
                                controller: TextEditingController(),
                                textAlign: TextAlign.center,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: dW * 0.04, horizontal: dW * 0.04),
                                hintText: '${i + 1}',
                                hintFS: 12,
                                borderRadius: 7,
                                textFS: 13.5,
                                enabled: true,
                              ),
                            ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Enter advance amount for sport',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: dW * 0.06, bottom: dW * 0.02),
                      padding: EdgeInsets.all(dW * 0.04),
                      decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffD9D9D9),
                          ),
                          borderRadius: BorderRadius.circular(8)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextWidget(
                                title: 'Sport',
                                fontWeight: FontWeight.w600,
                                color: Color(0xff3E3E3E),
                              ),
                              TextWidget(
                                title: 'Advance Amount',
                                fontWeight: FontWeight.w600,
                                color: Color(0xff3E3E3E),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.05),
                            child: TextWidget(
                              title: 'Snooker',
                              color: getThemeColor(),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.03),
                            child: Row(
                              children: [
                                TextWidget(
                                  title: 'Singles:',
                                  color: Color(0xff636363),
                                  fontWeight: FontWeight.w500,
                                  fontSize: tS * 14,
                                ),
                                const Spacer(),
                                Expanded(
                                  child: CustomTextFieldWithLabel(
                                    label: '',
                                    controller: advanceAmountSinglesController,
                                    textAlign: TextAlign.center,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: dW * 0.04,
                                        horizontal: dW * 0.04),
                                    hintText: '',
                                    prefixIcon: Icon(
                                      Icons.currency_rupee_sharp,
                                      size: 16,
                                      color: blackColor3,
                                    ),
                                    hintFS: 12,
                                    borderRadius: 7,
                                    textFS: 13.5,
                                    inputFormatter: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    enabled: true,
                                    maxLength: 6,
                                    inputType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.03),
                            child: Row(
                              children: [
                                TextWidget(
                                  title: 'Doubles:',
                                  color: Color(0xff636363),
                                  fontWeight: FontWeight.w500,
                                  fontSize: tS * 14,
                                ),
                                const Spacer(),
                                Expanded(
                                  child: CustomTextFieldWithLabel(
                                    label: '',
                                    controller: advanceAmountDoublesController,
                                    textAlign: TextAlign.center,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: dW * 0.04,
                                        horizontal: dW * 0.04),
                                    hintText: '',
                                    prefixIcon: Icon(
                                      Icons.currency_rupee_sharp,
                                      size: 16,
                                      color: blackColor3,
                                    ),
                                    hintFS: 12,
                                    borderRadius: 7,
                                    textFS: 13.5,
                                    inputFormatter: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    enabled: true,
                                    maxLength: 6,
                                    inputType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'What is the maximum number of players allowed per booking?',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title: "*You must fill in all of the fields",
                        fontSize: 12,
                        color: Color(0xffD84848),
                      ),
                    ),
        
                    Container(
                      margin: EdgeInsets.only(top: dW * 0.06, bottom: dW * 0.02),
                      padding: EdgeInsets.all(dW * 0.04),
                      decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffD9D9D9),
                          ),
                          borderRadius: BorderRadius.circular(8)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            title: 'Sport',
                            fontWeight: FontWeight.w600,
                            color: Color(0xff3E3E3E),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.05),
                            child: Row(
                              children: [
                                TextWidget(
                                  title: 'Snooker',
                                  color: getThemeColor(),
                                  fontWeight: FontWeight.w500,
                                ),
                                SizedBox(width: dW * 0.03),
                                TextWidget(
                                  title: '(No. of players)',
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xff3E3E3E),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.03),
                            child: CustomTextFieldWithLabel(
                              label: '',
                              controller: numberOfPlayersController,
                              textAlign: TextAlign.center,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: dW * 0.04, horizontal: dW * 0.04),
                              hintText: '',
                              hintFS: 12,
                              borderRadius: 7,
                              textFS: 13.5,
                              inputFormatter: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              enabled: true,
                              maxLength: 6,
                              inputType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05, bottom: dW * 0.1),
                      child: TextWidget(
                        title:
                            'Tell users what facilities available at your venue',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    ...listOfFacilities.map(
                      (facility) => GestureDetector(
                        onTap: () {},
                        child: Container(
                          margin: EdgeInsets.only(bottom: dW * 0.05),
                          width: dW,
                          color: Colors.transparent,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: dW * 0.7,
                                alignment: Alignment.topLeft,
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: TextWidget(
                                    title: facility['title'],
                                    letterSpacing: .30,
                                    color: Color(0xff636363),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              OldCheckBoxWidget(dW, facility['isSelected']),
                            ],
                          ),
                        ),
                      ),
                    ),
        
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'Is any Equipment available at your venue? (Optional)',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.025),
                      child: TextWidget(
                        title:
                            'Rent sports equipment and gear to enhance the player experience at your venue.',
                        fontSize: 12,
                      ),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: rentalItems.length,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, i) {
                        final item = rentalItems[i];
                        final isSelected = selectedIndex.contains(i);
        
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                selectedIndex.remove(i);
                              } else {
                                selectedIndex.add(i);
                              }
                            });
                          },
                          child: Container(
                            margin: EdgeInsets.only(top: dW * 0.1),
                            padding: EdgeInsets.symmetric(
                                vertical: dW * 0.05, horizontal: dW * 0.05),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? getThemeColor()
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: getThemeColor()),
                            ),
                            child: Row(
                              children: [
                                CachedNetworkImage(
                                  imageUrl: item.productImage,
                                  height: 30,
                                  fit: BoxFit.cover,
                                  placeholder: (_, __) => Image.asset(
                                    'assets/images/user.png',
                                    fit: BoxFit.cover,
                                    height: 35,
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(left: dW * 0.03),
                                  child: SizedBox(
                                    width: dW * 0.4,
                                    child: Text(
                                      item.productName,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 18,
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.black,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  '\u20b9 ${item.price.toStringAsFixed(1).replaceAll(RegExp(r'\.0$'), '')}/${item.duration} min',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color:
                                        isSelected ? Colors.white : Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: 'Now set your venue\'s booking availability',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
        
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.1),
                      child: TextWidget(
                        title:
                            'You\'ll need to add venue\'s booking availability duration.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    getAvailabilityCount(
                        givenPeriod: 'Days', listOfCount: [7, 15, 21]),
                    getAvailabilityCount(
                      givenPeriod: 'Month',
                      listOfCount: List<int>.generate(11, (i) => i + 1),
                    ),
                    getAvailabilityCount(
                      givenPeriod: 'Year',
                      listOfCount: [1, 2, 3, 4],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'Let’s set cancellation charges & cancellation policy',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: TextWidget(
                        title:
                            'You’ll need to set turf’s cancellation charges and cancellation policy for your minimum loss.',
                        fontSize: 12,
                        color: Color(0xff21272A),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08, bottom: dW * 0.05),
                      child: TextWidget(
                        title: 'Cancellation Charges',
                        fontSize: 14,
                        color: Color(0xff9798A3),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (cancellationCharges.isNotEmpty)
                      ...cancellationCharges
                          .asMap()
                          .map(
                            (i, data) => MapEntry(
                              i,
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  CancellationWidget(
                                    data: data,
                                    deviceWidth: dW,
                                    textScaleFactor: tS,
                                  ),
                                  Positioned(
                                    right: -10,
                                    child: PopupMenuButton(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(7),
                                      ),
                                      icon: Icon(
                                        Icons.more_vert,
                                        color: Colors.black,
                                      ),
                                      itemBuilder: (BuildContext bc) => [
                                        PopupMenuItem(
                                          child: Text(
                                            "Edit",
                                            style: Theme.of(context)
                                                .textTheme
                                                .displaySmall!
                                                .copyWith(
                                                  fontSize: tS * 13,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black,
                                                ),
                                          ),
                                          value: 1,
                                        ),
                                        PopupMenuItem(
                                          child: Text(
                                            'Delete',
                                            style: Theme.of(context)
                                                .textTheme
                                                .displaySmall!
                                                .copyWith(
                                                  fontSize: tS * 13,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black,
                                                ),
                                          ),
                                          value: 2,
                                        ),
                                      ],
                                      onSelected: (value) {
                                        if (value == 1) {
                                          addCancellationBottomSheet(
                                              context: context,
                                              data: data,
                                              index: i);
                                        } else if (value == 2) {
                                          addOrRemoveCancellation(
                                            data: data,
                                            index: i,
                                            add: false,
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                          .values
                          .toList(),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.02),
                      child: CustomButton(
                        width: dW,
                        height: dW * 0.12,
                        buttonText: cancellationCharges.isEmpty
                            ? 'Add Charges'
                            : 'Add more',
                        onPressed: () =>
                            addCancellationBottomSheet(context: context),
                        fontSize: 14,
                        radius: 8,
                        elevation: 0,
                        borderColor: getThemeColor(),
                        textColor: getThemeColor(),
                        buttonColor: Color(0xffF3FFEF),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.08),
                      child: CustomTextFieldWithLabel(
                        label: 'Cancellation Policy',
                        widget: GestureDetector(
                          onTap: () => openTemplateBottomSheet(context),
                          child: AssetSvgIcon(
                            iconName: 'info_rounded',
                            color: getThemeColor(),
                          ),
                        ),
                        controller: cancellationController,
                        hintText: 'Enter cancellation policy',
                        borderRadius: 5,
                        labelFS: 14,
                        labelColor: Color(0xff9798A3),
                        labelFW: FontWeight.w500,
                        hintColor: Colors.black,
                        borderColor: Color(0xffACACB4),
                        fillColor: Color(0xffF8F9FD),
                        counterText:
                            '${cancellationController.text.trim().length}/600',
                        maxLines: 8,
                        maxLength: 600,
                        textFS: 14,
                        textCapitalization: TextCapitalization.sentences,
                        inputType: TextInputType.streetAddress,
                        inputAction: TextInputAction.newline,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title: "Set up your venue's terms & conditions now",
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.025),
                      child: TextWidget(
                        title:
                            "Craft your venue's terms and conditions. Edit or enhance at any time for clarity and compliance. Your venue, your rules",
                        fontSize: 12,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: TextWidget(
                        title: 'Terms & Conditions',
                        fontWeight: FontWeight.w500,
                        color: Color(0xff9798A3),
                      ),
                    ),
        
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.02),
                      child: TextFormField(
                        style: TextStyle(
                            fontSize: tS * 12,
                            letterSpacing: .30,
                            height: 1.6,
                            color: Color(0xff5E5E5E),
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500),
                        maxLines: 22,
                        decoration: InputDecoration(
                          hintStyle:
                              TextStyle(fontSize: 12, color: Color(0xff5E5E5E)),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: dW * 0.03, vertical: dW * 0.03),
                          hintText: '',
                          counterText: "",
                          fillColor: Colors.transparent,
                          border: textFormBorder(context),
                          focusedErrorBorder: textFormBorder(context),
                          focusedBorder: textFormBorder(context),
                          enabledBorder: textFormBorder(context),
                          filled: true,
                        ),
                        textInputAction: TextInputAction.next,
                        cursorColor: Colors.black,
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        keyboardType: TextInputType.text,
                        controller: termsConditionController,
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05),
                      child: TextWidget(
                        title:
                            'Are you agreeing to the commission and onboarding field?',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.1),
                      child: TextWidget(
                        title: 'Commission & Onboarding',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.04),
                      child: TextWidget(
                          fontSize: 12,
                          title:
                              'BookYourSlot charges a commission of 5%-10% in developing markets community.\n\nYou agree to register for the first time for BookYourSlot membership, BoolYourSlot verifies credentials from RBI. As the registration process also takes the involvement of RBI.'),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.05, bottom: dW * 0.05),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.005),
                            child: CheckBoxWidget(
                              activeBorderColor: getThemeColor(),
                              activeColor: getThemeColor(),
                              active: isCommissionAgreed,
                              borderRadius: 3,
                              height: dW * 0.05,
                              iconSize: 14,
                              constraintWidth: null,
                              onTap: () {
                                isCommissionAgreed = !isCommissionAgreed;
                                setState(() {});
                              },
                            ),
                          ),
                          SizedBox(width: dW * 0.04),
                          Expanded(
                            child: GestureDetector(
                                onTap: () {
                                  isCommissionAgreed = !isCommissionAgreed;
        
                                  setState(() {});
                                },
                                child: IntrinsicWidth(
                                  child: Container(
                                    width: dW * 0.9,
                                    child: TextWidget(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xff5E5E5E),
                                        title:
                                            'By clicking here, you accept the commission & onboarding policy'),
                                  ),
                                )),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            BottomAlignedWidget(
              dW: dW,
              dH: dH,
              child: Column(
                children: [
                  CustomButton(
                    width: dW * 0.9,
                    height: dW * 0.12,
                    fontSize: 16,
                    buttonText: 'Next',
                    onPressed: () {
                      push(PreviewScreenForSnookerPool(
                        venueName: venueNameController.text.trim(),
                        venueContact: venuePhoneController.text.trim(),
                        venueDescription: venueDescriptionController.text.trim(),
                        venueAddress: venueAddress!,
                        selectedWeekDays: selectedWeekDays,
                        selectedWeekEnds: selectedWeekEnds,
                        selectedSlotDuration: selectedSlotDuration,
                        selectedSlots: selectedSlots,
                      ));
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: dW * 0.04),
                    child: TextWidget(
                      title:
                          'Need Help? Contact Us: ${Provider.of<Auth>(context, listen: false).adminContact}',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
