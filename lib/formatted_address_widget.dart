import 'package:flutter/material.dart';

import 'commonWidgets/asset_svg_icon.dart';
import 'commonWidgets/text_widget.dart';
import 'common_function.dart';
import 'venueModule/models/venue_model.dart';

class FormattedAddressWidget extends StatelessWidget {
  final Address fa;
  final double dW;
  final double textScale;
  final bool isForGetCurrentLocation;

  const FormattedAddressWidget({
    Key? key,
    required this.fa,
    required this.dW,
    required this.textScale,
    this.isForGetCurrentLocation = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(fa.coordinates),
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.only(
          top: dW * 0.0475,
          left: dW * 0.06,
          right: dW * 0.02,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: dW * 0.005, right: dW * 0.04),
              child: !isForGetCurrentLocation
                  ? AssetSvgIcon(
                      iconName: 'location',
                      height: 20,
                      color: getThemeColor(),
                    )
                  : Icon(
                      Icons.my_location_rounded,
                      color: Colors.blueGrey.shade600,
                    ),
            ),
            Container(
              width: dW * 0.8,
              padding: EdgeInsets.only(bottom: dW * 0.042),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                      width: !isForGetCurrentLocation ? 0.2 : 0.9,
                      color: Colors.grey.shade600),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    title: fa.streetName,
                    maxLines: 1,
                    textOverflow: TextOverflow.ellipsis,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: getThemeColor(),
                  ),
                  SizedBox(height: dW * 0.015),
                  TextWidget(
                    title: fa.fullAddress,
                    fontSize: 13,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
