import '../../commonWidgets/circle_avatar_widget.dart';
import '../../commonWidgets/custom_container.dart';
import '../../commonWidgets/text_widget.dart';
import '../../customerModule/models/customer_model.dart';
import '../../customerModule/screens/customer_booking_screen.dart';
import '../../navigators.dart';
import 'package:flutter/material.dart';

class CustomerWidget extends StatelessWidget {
  final double dW;
  final double tS;
  final CustomerModel customer;
  final int? totalBookings;
  const CustomerWidget({
    Key? key,
    required this.dW,
    required this.tS,
    required this.customer,
    required this.totalBookings,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        push(CustomerBookingScreen(customer: customer));
      },
      child: CustomContainer(
        margin: EdgeInsets.only(bottom: dW * 0.05),
        child: Row(
          children: [
            CircleAvatarWidget(
              avatar: customer.avatar,
              userName: '${customer.firstName} ${customer.lastName}',
              height: 0.15,
              parentRadius: 28,
              fontSize: 18,
            ),
            SizedBox(width: dW * 0.04),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: dW * 0.4),
                  child: TextWidget(
                    title: '${customer.firstName} ${customer.lastName}',
                    fontWeight: FontWeight.w600,
                    maxLines: 1,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: dW * 0.01),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: dW * 0.4),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: TextWidget(
                      title:
                          // totalBookings != 0
                          // ?
                          'Upcoming Booking: $totalBookings'
                      // :
                      // 'Upcoming Booking: N.A.'
                      ,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
