import 'dart:convert';

import 'package:bys_business/couponModule/model/coupon_model.dart';
import 'package:bys_business/employeeManagement/model/employeeManagemenrModel.dart';

import '../../customerModule/models/customer_model.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../venueModule/models/venue_model.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import '../../api.dart';

class CustomerProvider with ChangeNotifier {
  List<CustomerModel> _customers = [];

  List<CustomerModel> get customers {
    return [..._customers];
  }

  List<CouponModel> _coupons = [];

  List<CouponModel> get coupons {
    return [..._coupons];
  }

  int _skipCustomer = 0;
  int _limitCustomer = 12;

  num totalCustomerCount = 0;

  resetCustomer() {
    _customers = [];
    _skipCustomer = 0;
    totalCustomerCount = 0;
    _limitCustomer = 12;
  }

  fetchBusinessCustomers({
    required String accessToken,
    required String business,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchBusinessCustomers']}';

    if (_skipCustomer == 0) _customers = [];

    var str = json.encode({
      "business": business,
      'limit': _limitCustomer,
      'skip': _skipCustomer,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<CustomerModel> loadedCustomer = [];

        responseData['result'].forEach((customer) {
          if (customer['user'] != null) {
            loadedCustomer.add(
              CustomerModel(
                firstName: customer['user']['firstName'] ??
                    customer['user']['fullName'] ??
                    '',
                lastName: customer['user']['lastName'] ?? '',
                phone: customer['user']['phone'] ?? '',
                email: customer['user']['email'] ?? '',
                avatar: customer['user']['avatar'] ?? '',
                id: customer['user']['_id'],
                bookings: [],
              ),
            );
          }
        });

        if (_skipCustomer == 0) {
          _customers = List.from(loadedCustomer);
        } else {
          _customers.addAll(loadedCustomer);
        }

        if (_skipCustomer == 0 && responseData['totalCustomerCount'] != null) {
          totalCustomerCount = responseData['totalCustomerCount'];
        }

        if (loadedCustomer.isNotEmpty) {
          _skipCustomer += loadedCustomer.length;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  fetchUserBookingForBusinessNew({
    required String accessToken,
    required String userId,
    required String businessId,
  }) async {
    int index = _customers.indexWhere((customer) => customer.id == userId);

    if (index == -1) {
      return;
    }
    final url =
        '${webApi['domain']}${endPoint['fetchUserBookingForBusinessNew']}';

    if (_skipCustomer == 0) _customers = [];

    var str = json.encode({
      "userId": userId,
      "businessId": businessId,
      'limit': _customers[index].limit,
      'skip': _customers[index].skip,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success']) {
        List<BookingModel> loadedBooking = [];

        responseData['result'].forEach((booking) {
          loadedBooking.add(BookingModel.jsonToBooking(booking));
        });

        if (_customers[index].skip == 0) {
          _customers[index].bookings = List.from(loadedBooking);
        } else {
          _customers[index].bookings.addAll(loadedBooking);
        }
        if (loadedBooking.isNotEmpty) {
          _customers[index].skip += loadedBooking.length;
        }
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  getCustomerBookingById(String userId) {
    return _customers.firstWhere((customer) => customer.id == userId).bookings;
  }

  findCouponByCode({required String code, required String id}) {
    int index = _coupons.indexWhere((coupon) => coupon.code == code);
    return index == -1
        ? true
        : id == _coupons[index].id || id == ''
            ? true
            : false;
  }

  fetchCouponsForBusiness({
    required String accessToken,
    required String business,
    required List<String> turfId,
    required bool employee,
  }) async {
    final url = '${webApi['domain']}${endPoint['fetchCouponsForBusiness']}';

    var str = json.encode({
      "business": business,
      "turfId": turfId,
      "employee": employee,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken',
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success'] && responseData['result'] != null) {
        List<CouponModel> loadedCoupon = [];

        responseData['result'].forEach((coupon) {
          loadedCoupon.add(
            CouponModel(
              id: coupon['_id'],
              code: coupon['code'] ?? '',
              bookingType: coupon['bookingType'] ?? '',
              description: coupon['description'] ?? '',
              startDate: DateTime.parse(coupon['startDate']).toLocal(),
              endDate: DateTime.parse(coupon['endDate']).toLocal(),
              active: coupon['active'],
              discountPercentage: coupon['discountPercentage'] == null
                  ? 0
                  : coupon['discountPercentage'].toInt(),
              turf: coupon['turf'] == null
                  ? null
                  : Venues(
                      id: coupon['turf']['_id'],
                      name: coupon['turf']['name'],
                    ),
              employee: coupon['employee'] == null
                  ? null
                  : EmployeeMapped(
                      id: coupon['employee']['_id'],
                      name:
                          '${coupon['employee']['firstName'] ?? ''} ${coupon['employee']['lastName'] ?? ''}',
                    ),
              isForNewUser: coupon['isForNewUser'],
              createdSource: coupon['createdSource'] ?? '',
              createdAt: DateTime.parse(coupon['createdAt']).toLocal(),
            ),
          );
        });
        _coupons = List.from(loadedCoupon);
        notifyListeners();
      }
    } catch (error) {
      print(error);
      throw error;
    }
  }

  createAndUpdateCoupon({
    required String accessToken,
    required String business,
    required String createdSource,
    required String code,
    required String description,
    required String discountPercentage,
    required String startDate,
    required String endDate,
    required String turf,
    required employee,
    required String couponId,
    required String bookingType,
  }) async {
    final url = '${webApi['domain']}${endPoint['createAndUpdateCoupon']}';

    var str = json.encode({
      "business": business,
      "createdSource": createdSource,
      "code": code,
      "description": description,
      "discountPercentage": discountPercentage,
      "startDate": startDate,
      "endDate": endDate,
      "turf": turf,
      "employee": employee,
      "couponId": couponId,
      "bookingType": bookingType,
    });

    try {
      final response = await http.post(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success'] && responseData['result'] != null) {
        if (couponId == '') {
          _coupons.insert(
            0,
            CouponModel(
              id: responseData['result']['_id'],
              code: responseData['result']['code'] ?? '',
              description: responseData['result']['description'] ?? '',
              bookingType: responseData['result']['bookingType'] ?? '',
              startDate:
                  DateTime.parse(responseData['result']['startDate']).toLocal(),
              endDate:
                  DateTime.parse(responseData['result']['endDate']).toLocal(),
              active: responseData['result']['active'],
              discountPercentage:
                  responseData['result']['discountPercentage'] == null
                      ? 0
                      : responseData['result']['discountPercentage'].toInt(),
              turf: responseData['result']['turf'] == null
                  ? null
                  : Venues(
                      id: responseData['result']['turf']['_id'],
                      name: responseData['result']['turf']['name'],
                    ),
              employee: responseData['result']['employee'] == null
                  ? null
                  : EmployeeMapped(
                      id: responseData['result']['employee']['_id'],
                      name:
                          '${responseData['result']['employee']['firstName'] ?? ''} ${responseData['result']['employee']['lastName'] ?? ''}',
                    ),
              isForNewUser: responseData['result']['isForNewUser'],
              createdSource: responseData['result']['createdSource'] ?? '',
              createdAt:
                  DateTime.parse(responseData['result']['createdAt']).toLocal(),
            ),
          );
        } else {
          int index = _coupons.indexWhere((coupon) => coupon.id == couponId);
          _coupons[index].code = responseData['result']['code'] ?? '';
          _coupons[index].description =
              responseData['result']['description'] ?? '';
          _coupons[index].discountPercentage =
              responseData['result']['discountPercentage'] == null
                  ? 0
                  : responseData['result']['discountPercentage'].toInt();
          _coupons[index].startDate =
              DateTime.parse(responseData['result']['startDate']).toLocal();
          _coupons[index].endDate =
              DateTime.parse(responseData['result']['endDate']).toLocal();
          _coupons[index].turf = responseData['result']['turf'] == null
              ? null
              : Venues(
                  id: responseData['result']['turf']['_id'],
                  name: responseData['result']['turf']['name'],
                );
        }

        notifyListeners();
      }
      return responseData;
    } catch (error) {
      print(error);
      return null;
    }
  }

  findCouponById(String id) {
    int index = _coupons.indexWhere((coupon) => coupon.id == id);
    if (index == -1) {
      return null;
    } else {
      return _coupons[index];
    }
  }

  activeOrDeleteCoupon({
    required String accessToken,
    required String couponId,
    required String action,
    required bool active,
  }) async {
    final url = '${webApi['domain']}${endPoint['activeOrDeleteCoupon']}';

    var str = json.encode({
      "couponId": couponId,
      "action": action,
      "active": active,
    });

    try {
      final response = await http.put(
        Uri.parse(url),
        body: str,
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $accessToken'
        },
      );

      final responseData = json.decode(response.body);
      if (responseData['success'] && responseData['result'] != null) {
        int index = _coupons.indexWhere((coupon) => coupon.id == couponId);
        if (index != -1) {
          if (action == 'Active') {
            _coupons[index].active = active;
          } else {
            _coupons.removeAt(index);
          }
        }
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print(error);
      return false;
    }
  }
}
