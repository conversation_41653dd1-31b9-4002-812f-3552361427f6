import '../../authModule/modals/userModel.dart';
import '../../homeModule/models/bookingModel.dart';

class CustomerModel {
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String avatar;
  final String id;
  List<BookingModel> bookings;
  int skip;
  int limit;

  CustomerModel({
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.avatar,
    required this.id,
    required this.bookings,
    this.skip = 0,
    this.limit = 10,
  });
}
