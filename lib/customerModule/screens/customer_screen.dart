import 'dart:io';

import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';
import 'package:bys_business/commonWidgets/text_widget.dart';
import 'package:http/http.dart';

import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/cupertinoCircularLoader.dart';
import '../../common_function.dart';
import '../../customerModule/models/customer_model.dart';
import '../../customerModule/provider/customer_provider.dart';
import '../../customerModule/widgets/customer_widget.dart';

import '../../authModule/modals/userModel.dart';
import '../../commonWidgets/materialCircularLoader.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../homeModule/models/bookingModel.dart';

class CustomerScreen extends StatefulWidget {
  final UserModal user;
  const CustomerScreen({Key? key, required this.user}) : super(key: key);

  @override
  _CustomerScreenState createState() => _CustomerScreenState();
}

class _CustomerScreenState extends State<CustomerScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;

  List<CustomerModel> listOfCustomers = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    Provider.of<CustomerProvider>(context, listen: false).resetCustomer();
    fetchBusinessCustomers();
  }

  fetchBusinessCustomers() async {
    try {
      setState(() {
        isLoading = true;
      });
      await getCustomers();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getCustomers() async {
    await Provider.of<CustomerProvider>(context, listen: false)
        .fetchBusinessCustomers(
      accessToken: widget.user.accessToken,
      business: widget.user.businessId,
    );
  }

  getCustomerBookings({String? customerId}) async {
    await Provider.of<CustomerProvider>(context, listen: false)
        .fetchUserBookingForBusinessNew(
      accessToken: widget.user.accessToken,
      userId: customerId!,
      businessId: widget.user.businessId,
    );
  }

  fetchUserBookingForBusiness({String? customerId}) async {
    try {
      // setState(() {
      isLoading = true;
      // });
      await getCustomerBookings(customerId: customerId);
      // setState(() {
      isLoading = false;
      // });
    } catch (e) {
      print(e);
      // setState(() {
      isLoading = false;
      // });
    }
  }

  lazyLoad() async {
    setState(() {
      lazyLoading = true;
    });
    await getCustomers();
    setState(() {
      lazyLoading = false;
    });
  }

  int listOfBookings(String customerId) {
    // Filter the list of bookings to get only bookings for the specified customer ID
    List<BookingModel> customerBookings = Provider.of<CustomerProvider>(context)
        .getCustomerBookingById(customerId);

    return customerBookings.length;
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        lazyLoad();
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    listOfCustomers = Provider.of<CustomerProvider>(context).customers;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
          : Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
            child: Column(
                children: [
                  // SizedBox(height: dW * 0.05),
                  NewAppBar(dW: dW, title: 'Customers'),
                  SizedBox(height: dW * 0.03  ),
                  Expanded(
                    child: listOfCustomers.isEmpty
                        ? EmptyListWidget(
                            text: 'Customers not found!',
                            topPadding: 0.7,
                          )
                        : NotificationListener<ScrollNotification>(
                            onNotification: _handleScrollNotification,
                            child: SingleChildScrollView(
                              
                              controller: _scrollController,
                              padding:
                                  EdgeInsets.symmetric(horizontal: dW * 0.05),
                              physics: BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: dW * 0.02),
                                  TextWidget(
                                    title:
                                        '${Provider.of<CustomerProvider>(context).totalCustomerCount} Customers',
                                    fontWeight: FontWeight.w500,
                                  ),
                                  SizedBox(height: dW * 0.04),
                                  ListView.builder(
                                    padding: EdgeInsets.all(0),
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: listOfCustomers.length,
                                    itemBuilder: (context, index) {
                                      // getCustomerBookings
                                      // fetchUserBookingForBusiness
                                      getCustomerBookings(
                                          customerId: listOfCustomers[index].id);
                                      listOfBookings(listOfCustomers[index].id);
                                      return CustomerWidget(
                                        totalBookings: listOfBookings(
                                            listOfCustomers[index].id),
                                        customer: listOfCustomers[index],
                                        dW: dW,
                                        tS: tS,
                                      );
                                    },
                                  ),
                                  // ...listOfCustomers.map((customer) {
            
                                  //   return CustomerWidget(
                                  //     // totalBookings: bookings.length,
                                  //     customer: customer,
                                  //     dW: dW,
                                  //     tS: tS,
                                  //   );
                                  // }),
                                  SizedBox(height: dW * 0.04),
                                  if (lazyLoading)
                                    Center(
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          bottom: dW * 0.1,
                                        ),
                                        child: Platform.isIOS
                                            ? CupertinoCircularLoader(10.0)
                                            : MaterialCircularLoader(
                                                dW * 0.06,
                                              ),
                                      ),
                                    ),
                                  SizedBox(height: dW * 0.12),
                                ],
                              ),
                            ),
                          ),
                  )
                ],
              ),
          ),
    );
  }
}
