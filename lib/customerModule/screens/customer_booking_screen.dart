import 'dart:io';

import 'package:bys_business/commonWidgets/asset_svg_icon.dart';
import 'package:bys_business/commonWidgets/empty_list_widget.dart';
import 'package:bys_business/commonWidgets/new_appbar.dart';

import '../../authModule/modals/userModel.dart';
import '../../authModule/providers/auth.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_function.dart';
import '../../customerModule/models/customer_model.dart';
import '../../customerModule/provider/customer_provider.dart';
import '../../homeModule/models/bookingModel.dart';
import '../../homeModule/widgets/bookingWidget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CustomerBookingScreen extends StatefulWidget {
  final CustomerModel customer;
  const CustomerBookingScreen({
    Key? key,
    required this.customer,
  }) : super(key: key);

  @override
  _CustomerBookingScreenState createState() => _CustomerBookingScreenState();
}

class _CustomerBookingScreenState extends State<CustomerBookingScreen> {
  double dH = 0;
  double dW = 0;
  double tS = 0;

  bool isLoading = false;
  bool lazyLoading = false;
  List<BookingModel> listOfBookings = [];
  final ScrollController _scrollController = ScrollController();
  late UserModal user;

  @override
  void initState() {
    super.initState();
    user = Provider.of<Auth>(context, listen: false).user;
    fetchUserBookingForBusiness();
  }

  fetchUserBookingForBusiness() async {
    try {
      setState(() {
        isLoading = true;
      });
      await getCustomerBookings();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  getCustomerBookings() async {
    await Provider.of<CustomerProvider>(context, listen: false)
        .fetchUserBookingForBusinessNew(
      accessToken: user.accessToken,
      userId: widget.customer.id,
      businessId: user.businessId,
    );
  }

  lazyLoad() async {
    setState(() {
      lazyLoading = true;
    });
    await getCustomerBookings();
    setState(() {
      lazyLoading = false;
    });
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (_scrollController.position.extentAfter == 0) {
        lazyLoad();
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    user = Provider.of<Auth>(context).user;

    listOfBookings = Provider.of<CustomerProvider>(context)
        .getCustomerBookingById(widget.customer.id);

    return Scaffold(
      backgroundColor: Colors.white,
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? CircularLoader(android: dW * 0.08, iOS: dW * 0.035)
          : Padding(
            padding: Platform.isIOS ?  EdgeInsets.only(top: MediaQuery.of(context).padding.top , bottom: dH * 0.02) : EdgeInsets.only(top: dH*0.02 , bottom: dH * 0.02),
            child: Column(
                children: [
                  // SizedBox(height: dW * 0.05),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NewAppBar(dW: dW, title: 'Customer Bookings'),
                      // GestureDetector(
                      //   onTap: () {},
                      //   child: Container(
                      //     padding: EdgeInsets.symmetric(
                      //         horizontal: dW * 0.05, vertical: dW * 0.02),
                      //     color: Colors.transparent,
                      //     child: AssetSvgIcon(iconName: 'filter1'),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: dW * 0.03),
                  Expanded(
                    child: listOfBookings.isEmpty
                        ? EmptyListWidget(
                            text: 'Booking not found!',
                            topPadding: 0.7,
                          )
                        : NotificationListener<ScrollNotification>(
                            onNotification: _handleScrollNotification,
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              padding:
                                  EdgeInsets.symmetric(horizontal: dW * 0.05),
                              physics: BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: dW * 0.04),
                                  ...listOfBookings.map(
                                    (booking) => BookingWidget(
                                      booking: booking,
                                      user: user,
                                      deviceWidth: dW,
                                      textScaleFactor: tS,
                                      fromCustomer: true,
                                    ),
                                  ),
                                  SizedBox(height: dW * 0.04),
                                  if (lazyLoading) lazyLoader(dW),
                                  SizedBox(height: dW * 0.12),
                                ],
                              ),
                            ),
                          ),
                  )
                ],
              ),
          ),
    );
  }
}
