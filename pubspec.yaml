name: bys_business
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+3

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  http: ^1.4.0
  pin_code_fields: ^8.0.1
  flutter_svg: ^2.1.0
  localstorage: ^4.0.1+4
  shared_preferences:
  provider: ^6.0.3
  carousel_slider: ^5.0.0
  intl: ^0.19.0
  fluttertoast: ^8.0.8
  dotted_border: ^2.0.0+1
  image_picker: ^1.1.2
  email_validator: ^3.0.0
  url_launcher: ^6.0.20
  google_maps_flutter: ^2.0.6
  syncfusion_flutter_charts: ^29.1.41
  #for Ios
  # qr_code_scanner: ^0.5.2
  #For Android
  # qr_code_scanner: ^1.0.0
  qr_code_scanner_plus: ^2.0.10+1
  flutter_html: ^3.0.0-alpha.5
  shimmer: ^3.0.0
  firebase_messaging: ^15.2.5
  firebase_core: ^3.13.0
  firebase_dynamic_links: ^6.1.5
  flutter_local_notifications: ^19.2.0
  flutter_datetime_picker_plus: ^2.2.0
  # flutter_datetime_picker:
  #   git:
  #     url: https://github.com/Realank/flutter_datetime_picker.git
  #     ref: master
  path_provider: ^2.0.7
  open_file_safe_plus: ^0.0.6
  permission_handler: ^12.0.0+1
  dio: ^5.8.0+1
  device_info_plus: ^11.4.0
  cached_network_image: ^3.2.0
  share_plus: ^11.0.0
  date_picker_timeline: ^1.2.3
  flutter_native_contact_picker: ^0.0.10
  package_info_plus:
  video_compress: ^3.1.2
  flutter_video_info: ^1.3.1
  image_cropper: ^9.1.0
  flutter_image_compress: ^2.4.0
  video_player: ^2.4.5
  chewie: ^1.2.2
  geolocator: ^14.0.0
  app_settings: ^6.1.1  
  geocoding: ^3.0.0
  scrollable_positioned_list: ^0.3.5
  flutter_polyline_points: ^2.1.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  otp_autofill: ^4.0.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8




# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
dev_dependencies:

  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.4.6
  flutter_lints: ^5.0.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path_android: "assets/images/logo2.png"
  image_path_ios: "assets/images/logo2.png"
flutter_native_splash:
  color: "#f2f2f2"
  image: assets/images/logo2.png
  android: true
  ios: true
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/svgIcons/
    - assets/placeholders/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 900
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat.ttf

  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
